﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class test1 : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(test1));

        string IMigrationMetadata.Id => "202106281128117_test1";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
        }

        public override void Down()
        {
            DropForeignKey("dbo.SalesPriceOfferDetails", "SalesPriceOfferID", "dbo.SalesPriceOffers");
            DropForeignKey("dbo.SalesPriceOffers", "CustomerID", "dbo.Customers");
            DropIndex("dbo.SalesPriceOffers", new string[1] { "CustomerID" });
            DropIndex("dbo.SalesPriceOfferDetails", new string[1] { "SalesPriceOfferID" });
            DropColumn("dbo.UserSettingsProfiles", "ForceStartDrawerPeriod");
            DropColumn("dbo.SalesInvoices", "DropOffType");
            DropColumn("dbo.SalesInvoices", "OrderNumber");
            DropColumn("dbo.SalesInvoices", "DropOffAddress");
            DropColumn("dbo.SalesInvoices", "DropOffLocation");
            DropColumn("dbo.SalesInvoices", "PostDate");
            DropColumn("dbo.SalesInvoices", "DeliveryOrderNumber");
            DropTable("dbo.SalesPriceOffers");
            DropTable("dbo.SalesPriceOfferDetails");
        }
    }
}
