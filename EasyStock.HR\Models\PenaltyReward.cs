﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    public class PenaltyReward
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID { get; set; }

        [Display(Name = "Employé")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int EmployeeId { get; set; }

        [Display(Name = "Date")]
        [Range(typeof(DateTime), "1-1-1901", "1-1-2222", ErrorMessage = "Ce champ est obligatoire")]
        public DateTime Date { get; set; }

        [Display(Name = "Calcul")]
        public CalculationType calcType { get; set; }

        public PenaltyRewardType Type { get; set; }

        [Display(Name = "Nombre de jours")]
        public int NoOfDays { get; set; }

        [Display(Name = "Montant")]
        public double Amount { get; set; }

        [Display(Name = "Remarques")]
        [StringLength(150)]
        public string Remarks { get; set; }

        public Employee Employee { get; set; }
    }
}
