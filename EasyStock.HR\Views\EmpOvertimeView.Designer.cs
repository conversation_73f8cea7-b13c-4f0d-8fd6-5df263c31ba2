﻿namespace EasyStock.HR.Views
{
	public partial class EmpOvertimeView : global::EasyStock.HR.MainViews.MasterView
	{
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.empOvertimeDelayBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.DayDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.DurationTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.bsencePaidImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.EmpIDTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.NotesTextEdit = new DevExpress.XtraEditors.MemoEdit();
            this.RegulationIDTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.ItemForRegulationID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDayDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDuration = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForbsencePaid = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForNotes = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForEmpID = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.empOvertimeDelayBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DurationTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bsencePaidImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmpIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NotesTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.RegulationIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForRegulationID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDayDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDuration)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForbsencePaid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNotes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmpID)).BeginInit();
            this.SuspendLayout();
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.DayDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.DurationTextEdit);
            this.dataLayoutControl1.Controls.Add(this.bsencePaidImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.EmpIDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NotesTextEdit);
            this.dataLayoutControl1.Controls.Add(this.RegulationIDTextEdit);
            this.dataLayoutControl1.DataSource = this.empOvertimeDelayBindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 26);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(800, 400);
            this.dataLayoutControl1.TabIndex = 4;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(800, 400);
            this.Root.TextVisible = false;
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup2,
            this.emptySpaceItem1,
            this.emptySpaceItem2});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(780, 380);
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForRegulationID,
            this.ItemForDayDate,
            this.ItemForDuration,
            this.ItemForbsencePaid,
            this.ItemForNotes,
            this.ItemForID,
            this.ItemForEmpID});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(421, 249);
            this.layoutControlGroup2.Text = "Informations";
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(421, 0);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(359, 380);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 249);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(421, 131);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empOvertimeDelayBindingSource, "ID", true));
            this.IDTextEdit.Location = new System.Drawing.Point(128, 44);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.IDTextEdit.Properties.MaskSettings.Set("mask", "N0");
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(289, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 4;
            this.empOvertimeDelayBindingSource.DataSource = typeof(EasyStock.HR.Models.EmpOvertimeDelay);
            this.DayDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empOvertimeDelayBindingSource, "DayDate", true));
            this.DayDateDateEdit.EditValue = null;
            this.DayDateDateEdit.Location = new System.Drawing.Point(128, 116);
            this.DayDateDateEdit.Name = "DayDateDateEdit";
            this.DayDateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.DayDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DayDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DayDateDateEdit.Size = new System.Drawing.Size(289, 20);
            this.DayDateDateEdit.StyleController = this.dataLayoutControl1;
            this.DayDateDateEdit.TabIndex = 7;
            this.DurationTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empOvertimeDelayBindingSource, "Duration", true));
            this.DurationTextEdit.Location = new System.Drawing.Point(128, 140);
            this.DurationTextEdit.Name = "DurationTextEdit";
            this.DurationTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DurationTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DurationTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.DurationTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.DurationTextEdit.Properties.MaskSettings.Set("mask", "N0");
            this.DurationTextEdit.Size = new System.Drawing.Size(289, 20);
            this.DurationTextEdit.StyleController = this.dataLayoutControl1;
            this.DurationTextEdit.TabIndex = 8;
            this.bsencePaidImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empOvertimeDelayBindingSource, "bsencePaid", true));
            this.bsencePaidImageComboBoxEdit.Location = new System.Drawing.Point(128, 164);
            this.bsencePaidImageComboBoxEdit.Name = "bsencePaidImageComboBoxEdit";
            this.bsencePaidImageComboBoxEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.bsencePaidImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.bsencePaidImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.bsencePaidImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.bsencePaidImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Oui", EasyStock.HR.EAbsencePaid.Casual, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Non", EasyStock.HR.EAbsencePaid.Normal, 1)});
            this.bsencePaidImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.bsencePaidImageComboBoxEdit.Size = new System.Drawing.Size(289, 20);
            this.bsencePaidImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.bsencePaidImageComboBoxEdit.TabIndex = 9;
            this.EmpIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empOvertimeDelayBindingSource, "EmpID", true));
            this.EmpIDTextEdit.Location = new System.Drawing.Point(128, 68);
            this.EmpIDTextEdit.Name = "EmpIDTextEdit";
            this.EmpIDTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.EmpIDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.EmpIDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.EmpIDTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.EmpIDTextEdit.Properties.NullText = "";
            this.EmpIDTextEdit.Size = new System.Drawing.Size(289, 20);
            this.EmpIDTextEdit.StyleController = this.dataLayoutControl1;
            this.EmpIDTextEdit.TabIndex = 5;
            this.NotesTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empOvertimeDelayBindingSource, "Notes", true));
            this.NotesTextEdit.Location = new System.Drawing.Point(128, 188);
            this.NotesTextEdit.Name = "NotesTextEdit";
            this.NotesTextEdit.Size = new System.Drawing.Size(289, 57);
            this.NotesTextEdit.StyleController = this.dataLayoutControl1;
            this.NotesTextEdit.TabIndex = 10;
            this.RegulationIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empOvertimeDelayBindingSource, "RegulationID", true));
            this.RegulationIDTextEdit.Location = new System.Drawing.Point(128, 92);
            this.RegulationIDTextEdit.Name = "RegulationIDTextEdit";
            this.RegulationIDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.RegulationIDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.RegulationIDTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.RegulationIDTextEdit.Properties.NullText = "";
            this.RegulationIDTextEdit.Properties.ReadOnly = true;
            this.RegulationIDTextEdit.Size = new System.Drawing.Size(289, 20);
            this.RegulationIDTextEdit.StyleController = this.dataLayoutControl1;
            this.RegulationIDTextEdit.TabIndex = 6;
            this.ItemForRegulationID.Control = this.RegulationIDTextEdit;
            this.ItemForRegulationID.Location = new System.Drawing.Point(0, 48);
            this.ItemForRegulationID.Name = "ItemForRegulationID";
            this.ItemForRegulationID.Size = new System.Drawing.Size(397, 24);
            this.ItemForRegulationID.TextSize = new System.Drawing.Size(100, 13);
            this.ItemForDayDate.Control = this.DayDateDateEdit;
            this.ItemForDayDate.Location = new System.Drawing.Point(0, 72);
            this.ItemForDayDate.Name = "ItemForDayDate";
            this.ItemForDayDate.Size = new System.Drawing.Size(397, 24);
            this.ItemForDayDate.TextSize = new System.Drawing.Size(100, 13);
            this.ItemForDuration.Control = this.DurationTextEdit;
            this.ItemForDuration.Location = new System.Drawing.Point(0, 96);
            this.ItemForDuration.Name = "ItemForDuration";
            this.ItemForDuration.Size = new System.Drawing.Size(397, 24);
            this.ItemForDuration.TextSize = new System.Drawing.Size(100, 13);
            this.ItemForbsencePaid.Control = this.bsencePaidImageComboBoxEdit;
            this.ItemForbsencePaid.Location = new System.Drawing.Point(0, 120);
            this.ItemForbsencePaid.Name = "ItemForbsencePaid";
            this.ItemForbsencePaid.Size = new System.Drawing.Size(397, 24);
            this.ItemForbsencePaid.TextSize = new System.Drawing.Size(100, 13);
            this.ItemForNotes.Control = this.NotesTextEdit;
            this.ItemForNotes.Location = new System.Drawing.Point(0, 144);
            this.ItemForNotes.MaxSize = new System.Drawing.Size(397, 61);
            this.ItemForNotes.MinSize = new System.Drawing.Size(397, 61);
            this.ItemForNotes.Name = "ItemForNotes";
            this.ItemForNotes.Size = new System.Drawing.Size(397, 61);
            this.ItemForNotes.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForNotes.TextSize = new System.Drawing.Size(100, 13);
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(397, 24);
            this.ItemForID.TextSize = new System.Drawing.Size(100, 13);
            this.ItemForEmpID.Control = this.EmpIDTextEdit;
            this.ItemForEmpID.Location = new System.Drawing.Point(0, 24);
            this.ItemForEmpID.Name = "ItemForEmpID";
            this.ItemForEmpID.Size = new System.Drawing.Size(397, 24);
            this.ItemForEmpID.TextSize = new System.Drawing.Size(100, 13);
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.dataLayoutControl1);
            this.Name = "EmpOvertimeView";
            this.Text = "Heures supplémentaires de l\'employé";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.empOvertimeDelayBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DurationTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bsencePaidImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmpIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NotesTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.RegulationIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForRegulationID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDayDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDuration)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForbsencePaid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNotes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmpID)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		private global::System.ComponentModel.IContainer components = null;

		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		private global::System.Windows.Forms.BindingSource empOvertimeDelayBindingSource;

		private global::DevExpress.XtraEditors.DateEdit DayDateDateEdit;

		private global::DevExpress.XtraEditors.TextEdit DurationTextEdit;

		private global::DevExpress.XtraEditors.ImageComboBoxEdit bsencePaidImageComboBoxEdit;

		private global::DevExpress.XtraEditors.LookUpEdit EmpIDTextEdit;

		private global::DevExpress.XtraEditors.MemoEdit NotesTextEdit;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForRegulationID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDayDate;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDuration;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForbsencePaid;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForNotes;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForEmpID;

		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;

		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;

		private global::DevExpress.XtraEditors.LookUpEdit RegulationIDTextEdit;
	}
}
