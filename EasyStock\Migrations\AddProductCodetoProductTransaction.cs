﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x0200048A RID: 1162
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class AddProductCodetoProductTransaction : DbMigration, IMigrationMetadata
	{
		// Token: 0x060022FA RID: 8954 RVA: 0x000121C2 File Offset: 0x000103C2
		public override void Up()
		{
			base.AddColumn("dbo.ProductTransactions", "ProductCode", (ColumnBuilder c) => c.String(null, null, null, null, null, null, null, null, null), null);
		}

		// Token: 0x060022FB RID: 8955 RVA: 0x000121F6 File Offset: 0x000103F6
		public override void Down()
		{
			base.DropColumn("dbo.ProductTransactions", "ProductCode", null);
		}

		// Token: 0x17000B7C RID: 2940
		// (get) Token: 0x060022FC RID: 8956 RVA: 0x001F1428 File Offset: 0x001EF628
		string IMigrationMetadata.Id
		{
			get
			{
				return "202106011152127_AddProductCodetoProductTransaction";
			}
		}

		// Token: 0x17000B7D RID: 2941
		// (get) Token: 0x060022FD RID: 8957 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000B7E RID: 2942
		// (get) Token: 0x060022FE RID: 8958 RVA: 0x001F1440 File Offset: 0x001EF640
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002AFF RID: 11007
		private readonly ResourceManager Resources = new ResourceManager(typeof(AddProductCodetoProductTransaction));
	}
}
