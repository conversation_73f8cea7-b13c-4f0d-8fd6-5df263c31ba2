﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Data.Entity.Migrations.Model;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class Initial : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(Initial));

        string IMigrationMetadata.Id => "202012301807219_Initial";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.Accounts", delegate (ColumnBuilder c)
            {
                ColumnModel iD8 = c.Int(false, identity: true);
                int? maxLength8 = 50;
                return new
                {
                    ID = iD8,
                    Number = c.String(null, maxLength8),
                    Name = c.String(false, 400),
                    ParentID = c.Int(),
                    Note = c.String(),
                    Secrecy = c.Int(false),
                    CanEdit = c.<PERSON>(false),
                    AccountType = c.Int(false)
                };
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Accounts", t => t.ParentID).Index(t => t.ParentID);
            CreateTable("dbo.BillingDetails", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Name = c.String(false),
                AccountID = c.Int(false),
                BranchLink = c.Int(false),
                Notes = c.String()
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Accounts", t => t.AccountID).Index(t => t.AccountID);
            CreateTable("dbo.Bills", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Code = c.String(),
                BranchID = c.Int(false),
                Date = c.DateTime(false),
                Notes = c.String(),
                Total = c.Double(false),
                JournalID = c.Int()
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Journals", t => t.JournalID).Index(t => t.JournalID);
            CreateTable("dbo.Journals", delegate (ColumnBuilder c)
            {
                ColumnModel iD7 = c.Int(false, identity: true);
                int? maxLength7 = 50;
                return new
                {
                    ID = iD7,
                    Code = c.String(null, maxLength7),
                    Date = c.DateTime(false),
                    Note = c.String(),
                    BranchID = c.Int(false),
                    ProcessType = c.Int(false),
                    ProcessID = c.Int(false)
                };
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.JournalDetails", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                JournalID = c.Int(false),
                Statement = c.String(),
                AccountID = c.Int(false),
                Debit = c.Double(false),
                Credit = c.Double(false),
                CurrencyID = c.Int(false),
                CurrencyRate = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Accounts", t => t.AccountID).ForeignKey("dbo.Currencies", t => t.CurrencyID)
                .ForeignKey("dbo.Journals", t => t.JournalID)
                .Index(t => t.JournalID)
                .Index(t => t.AccountID)
                .Index(t => t.CurrencyID);
            CreateTable("dbo.Currencies", delegate (ColumnBuilder c)
            {
                ColumnModel iD6 = c.Int(false, identity: true);
                ColumnModel name3 = c.String(false, 50);
                int? maxLength6 = 50;
                ColumnModel pound = c.String(null, maxLength6);
                maxLength6 = 50;
                ColumnModel pound2 = c.String(null, maxLength6);
                maxLength6 = 50;
                ColumnModel pound3 = c.String(null, maxLength6);
                maxLength6 = 50;
                ColumnModel piaster = c.String(null, maxLength6);
                maxLength6 = 50;
                ColumnModel piaster2 = c.String(null, maxLength6);
                maxLength6 = 50;
                return new
                {
                    ID = iD6,
                    Name = name3,
                    Pound1 = pound,
                    Pound2 = pound2,
                    Pound3 = pound3,
                    Piaster1 = piaster,
                    Piaster2 = piaster2,
                    Piaster3 = c.String(null, maxLength6),
                    LastRate = c.Double(false)
                };
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.Branches", delegate (ColumnBuilder c)
            {
                ColumnModel iD5 = c.Int(false, identity: true);
                ColumnModel name2 = c.String(false, 250);
                int? maxLength5 = 50;
                ColumnModel phone4 = c.String(null, maxLength5);
                maxLength5 = 150;
                ColumnModel city2 = c.String(null, maxLength5);
                maxLength5 = 250;
                return new
                {
                    ID = iD5,
                    Name = name2,
                    Phone = phone4,
                    City = city2,
                    Address = c.String(null, maxLength5),
                    AutoGenrateAccounts = c.Boolean(false),
                    PurchaseAccountID = c.Int(false),
                    PurchaseReturnAccountID = c.Int(false),
                    SellAccountID = c.Int(false),
                    SellReturnAccountID = c.Int(false),
                    OpenInventoryAccountID = c.Int(false),
                    CloseInventoryAccountID = c.Int(false),
                    PurchaseDiscountAccountID = c.Int(false),
                    SalesDiscountAccountID = c.Int(false)
                };
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Accounts", t => t.CloseInventoryAccountID).ForeignKey("dbo.Accounts", t => t.OpenInventoryAccountID)
                .ForeignKey("dbo.Accounts", t => t.PurchaseAccountID)
                .ForeignKey("dbo.Accounts", t => t.PurchaseDiscountAccountID)
                .ForeignKey("dbo.Accounts", t => t.PurchaseReturnAccountID)
                .ForeignKey("dbo.Accounts", t => t.SalesDiscountAccountID)
                .ForeignKey("dbo.Accounts", t => t.SellAccountID)
                .ForeignKey("dbo.Accounts", t => t.SellReturnAccountID)
                .Index(t => t.PurchaseAccountID)
                .Index(t => t.PurchaseReturnAccountID)
                .Index(t => t.SellAccountID)
                .Index(t => t.SellReturnAccountID)
                .Index(t => t.OpenInventoryAccountID)
                .Index(t => t.CloseInventoryAccountID)
                .Index(t => t.PurchaseDiscountAccountID)
                .Index(t => t.SalesDiscountAccountID);
            CreateTable("dbo.CashNotes", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                BranchID = c.Int(false),
                Code = c.String(),
                Date = c.DateTime(false),
                Discount = c.Double(false),
                InvoiceID = c.Int(),
                InvoicesType = c.Int(false),
                Note = c.String(),
                PersonalID = c.Int(false),
                PersonalType = c.Int(false),
                TotalPaid = c.Double(false),
                Type = c.Int(false),
                JournalID = c.Int(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Journals", t => t.JournalID).Index(t => t.JournalID);
            CreateTable("dbo.Personals", delegate (ColumnBuilder c)
            {
                ColumnModel iD4 = c.Int(false, identity: true);
                ColumnModel name = c.String(false, 400);
                ColumnModel groupID = c.Int();
                int? maxLength4 = 250;
                ColumnModel city = c.String(null, maxLength4);
                maxLength4 = 250;
                ColumnModel address2 = c.String(null, maxLength4);
                maxLength4 = 50;
                ColumnModel phone3 = c.String(null, maxLength4);
                maxLength4 = 50;
                ColumnModel mobile = c.String(null, maxLength4);
                maxLength4 = 250;
                return new
                {
                    ID = iD4,
                    Name = name,
                    GroupID = groupID,
                    City = city,
                    Address = address2,
                    Phone = phone3,
                    Mobile = mobile,
                    EMail = c.String(null, maxLength4),
                    BankAccount = c.String(),
                    LinkedToAccount = c.Boolean(false),
                    AccountID = c.Int(false),
                    TaxFileNumber = c.String(),

                };
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Accounts", t => t.AccountID).ForeignKey("dbo.PersonalGroups", t => t.GroupID)
                .Index(t => t.GroupID)
                .Index(t => t.AccountID);
            CreateTable("dbo.PersonalGroups", delegate (ColumnBuilder c)
            {
                ColumnModel iD3 = c.Int(false, identity: true);
                int? maxLength3 = 25;
                return new
                {
                    ID = iD3,
                    Code = c.String(null, maxLength3),
                    Name = c.String(false, 500),
                    ParentGroupID = c.Int(),
                    PersonalType = c.Byte(false)
                };
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.PersonalGroups", t => t.ParentGroupID).Index(t => t.ParentGroupID);
            CreateTable("dbo.GroupOfProducts", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Code = c.String(false),
                Name = c.String(false),
                ExpireType = c.Int(false),
                StartDate = c.DateTime(),
                EndDate = c.DateTime(),
                DiscountType = c.Int(false),
                Discount = c.Double(false),
                CanChangeQuantity = c.Boolean(false)
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.GroupOfProductsDetails", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                GroupID = c.Int(false),
                ProductID = c.Int(false),
                UnitID = c.Int(false),
                Quantity = c.Double(false),
                Price = c.Double(false),
                Discount = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.GroupOfProducts", t => t.GroupID).Index(t => t.GroupID);
            CreateTable("dbo.CompanyInfoes", (ColumnBuilder c) => new
            {
                ID = c.Int(false),
                Name = c.String(false, 250),
                CompanyLogo = c.Binary(null, null, null, null, null, timestamp: false, null, "image"),
                Address = c.String(false, 50),
                City = c.String(false, 50),
                Phone = c.String(false, 50),
                Mobile = c.String(false, 50),
                CommercialBook = c.String(false, 50),
                CompanyTaxCard = c.String(false, 50),
                FinancialYearStart = c.DateTime(false, null, null, null, null, "smalldatetime"),
                FinancialYearEnd = c.DateTime(false, null, null, null, null, "smalldatetime")
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.ProductTransactions", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Date = c.DateTime(false),
                Type = c.Int(false),
                BillID = c.Int(false),
                ProductID = c.Int(false),
                UnitID = c.Int(false),
                Factor = c.Double(false),
                Serial = c.String(),
                ColorID = c.Int(),
                SizeID = c.Int(),
                Expire = c.DateTime(),
                Quantity = c.Double(false),
                TransactionType = c.Int(false),
                BranchID = c.Int(false),
                Price = c.Double(false),
                CostValue = c.Double(false),
                ParentTransactionID = c.Int()
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.InvoiceShortCuts", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Action = c.Int(false),
                Key = c.Int(false),
                Modifier = c.Int(false)
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.PayDetails", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                SourceType = c.Int(false),
                SourceID = c.Int(false),
                Code = c.String(),
                MethodType = c.Int(false),
                MethodID = c.Int(false),
                Amount = c.Double(false),
                CurrancyID = c.Int(false),
                CurrancyRate = c.Double(false),
                Notes = c.String(),
                InsertDate = c.DateTime(false)
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.ProductCategories", delegate (ColumnBuilder c)
            {
                ColumnModel iD2 = c.Int(false, identity: true);
                int? maxLength2 = 25;
                return new
                {
                    ID = iD2,
                    Code = c.String(null, maxLength2),
                    Name = c.String(false, 500),
                    ParentGroupID = c.Int()
                };
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.PersonalGroups", t => t.ParentGroupID).Index(t => t.ParentGroupID);
            CreateTable("dbo.ProductColors", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Name = c.String(false, 250)
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.ProductCompanies", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Name = c.String(false, 250)
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.Products", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Code = c.String(false, 50),
                Name = c.String(false, 250),
                Type = c.Int(false),
                Descreption = c.String(),
                CategoryID = c.Int(),
                CompanyID = c.Int(),
                ProductVendorID = c.Int(),
                Suspended = c.Boolean(false),
                ImageArray = c.Binary(null, null, null, null, null, timestamp: false, null, "image"),
                HasExpier = c.Boolean(false),
                HasSerial = c.Boolean(false),
                HasWarranty = c.Boolean(false),
                ShelfLife = c.Int(false),
                WarntyDuration = c.Int(false),
                HasColor = c.Boolean(false),
                HasSize = c.Boolean(false),
                SecracyLevel = c.Int(false),
                MinStockLevel = c.Int(false),
                MaxStockLevel = c.Int(false),
                SalesTax = c.Double(false),
                PurchaseTax = c.Double(false),
                CalculateTaxAfterDiscount = c.Boolean(false),
                PriceIncludeTax = c.Boolean(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.ProductCategories", t => t.CategoryID).ForeignKey("dbo.ProductCompanies", t => t.CompanyID)
                .ForeignKey("dbo.ProductVendors", t => t.ProductVendorID)
                .Index(t => t.CategoryID)
                .Index(t => t.CompanyID)
                .Index(t => t.ProductVendorID);
            CreateTable("dbo.ProductVendors", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Name = c.String(false, 250)
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.ProductUnits", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                ProductID = c.Int(false),
                UnitNameID = c.Int(false),
                BuyPrice = c.Double(false),
                SellPrice = c.Double(false),
                SellDiscount = c.Double(false),
                Factor = c.Double(false),
                DefualtBuy = c.Boolean(false),
                DefualtSell = c.Boolean(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Products", t => t.ProductID).ForeignKey("dbo.UnitOfMeasurements", t => t.UnitNameID)
                .Index(t => t.ProductID)
                .Index(t => t.UnitNameID);
            CreateTable("dbo.ProductUnitBarcodes", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                UnitID = c.Int(false),
                Barcode = c.String(false, 50)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.ProductUnits", t => t.UnitID).Index(t => t.UnitID);
            CreateTable("dbo.UnitOfMeasurements", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Name = c.String(false, 250)
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.ProductSizes", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Name = c.String(false, 250)
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.RevExpEntries", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Code = c.String(),
                BranchID = c.Int(false),
                MethodType = c.Int(false),
                MethodID = c.Int(false),
                DateTime = c.DateTime(false),
                Total = c.Double(false),
                Notes = c.String(),
                EntryType = c.Int(false),
                JournalID = c.Int(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Journals", t => t.JournalID).Index(t => t.JournalID);
            CreateTable("dbo.RevExpEntryDetails", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                EntryID = c.Int(false),
                AccountID = c.Int(false),
                Amount = c.Double(false),
                CurrancyID = c.Int(false),
                CurrancyRate = c.Double(false),
                Notes = c.String()
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.RevExpEntries", t => t.EntryID).Index(t => t.EntryID);
            CreateTable("dbo.SystemSettings", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                MoneyToTextMode = c.Int(false),
                PrimaryReportingFolderName = c.String(),
                SecondaryReportingPath = c.String(),
                EmployeesDueAccount = c.Int(false),
                WagesAccount = c.Int(false),
                DueSalerysAccount = c.Int(false),
                FixedAssetsAccount = c.Int(false),
                DrawerAccount = c.Int(false),
                BanksAccount = c.Int(false),
                CustomersAccount = c.Int(false),
                NotesReceivableAccount = c.Int(false),
                VendorsAccount = c.Int(false),
                CapitalAccount = c.Int(false),
                NotesPayableAccount = c.Int(false),
                DepreciationAccount = c.Int(false),
                TaxAccount = c.Int(false),
                PurchaseAddTaxAccount = c.Int(false),
                SalesAddTaxAccount = c.Int(false),
                SalesDeductTaxAccount = c.Int(false),
                PurchaseDeductTaxAccount = c.Int(false),
                MerchandisingAccount = c.Int(false),
                InventoryAccount = c.Int(false),
                PurchasesAccount = c.Int(false),
                PurchasesReturnAccount = c.Int(false),
                SalesAccount = c.Int(false),
                SalesReturnAccount = c.Int(false),
                OpenInventoryAccount = c.Int(false),
                CloseInventoryAccount = c.Int(false),
                PurchaseDiscountAccount = c.Int(false),
                SalesDiscountAccount = c.Int(false),
                CostOfSoldGoodsAccount = c.Int(false),
                ExpensesAccount = c.Int(false),
                RevenueAccount = c.Int(false),
                InvoicesCodeRedundancy = c.Int(false),
                InvoicesCodeRedundancyOnSave = c.Boolean(false),
                ReadFormScaleBarcode = c.Boolean(false),
                BarcodeLength = c.Byte(false),
                ScaleBarcodePrefix = c.String(),
                ProductCodeLength = c.Byte(false),
                ValueCodeLength = c.Byte(false),
                ReadMode = c.Int(false),
                IgnoreCheckDigit = c.Boolean(false),
                DivideValueBy = c.Byte(false),
                ProductUnitsMode = c.Int(false),
                SalesTax = c.Double(false),
                PurchaseTax = c.Double(false),
                CalculateTaxAfterDiscount = c.Boolean(false),
                PriceIncludeTax = c.Boolean(false)
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.SimilarProducts", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                ProductID = c.Int(false),
                OtherProductID = c.Int(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Products", t => t.OtherProductID).ForeignKey("dbo.Products", t => t.ProductID)
                .Index(t => t.ProductID)
                .Index(t => t.OtherProductID);
            CreateTable("dbo.UserAccessProfileDetails", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                ParentID = c.Int(),
                ProfileId = c.Int(false),
                ObjectId = c.Int(false),
                CanAdd = c.Boolean(false),
                CanDelete = c.Boolean(false),
                CanEdit = c.Boolean(false),
                CanOpen = c.Boolean(false),
                CanPrint = c.Boolean(false),
                CanShow = c.Boolean(false),
                ViewActions = c.Int(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.UserAccessProfiles", t => t.ProfileId).Index(t => t.ProfileId);
            CreateTable("dbo.UserAccessProfiles", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Name = c.String(false)
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.Users", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Name = c.String(false),
                UserName = c.String(false),
                Password = c.String(false),
                Type = c.Int(false),
                AccessProfileID = c.Int(false),
                SettingsProfileID = c.Int(false),
                Notes = c.String()
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.UserAccessProfiles", t => t.AccessProfileID).ForeignKey("dbo.UserSettingsProfiles", t => t.SettingsProfileID)
                .Index(t => t.AccessProfileID)
                .Index(t => t.SettingsProfileID);
            CreateTable("dbo.UserSettingsProfiles", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Name = c.String(false),
                DefaultDrawer = c.Int(false),
                DefaultPayCard = c.Int(false),
                DefaultVendor = c.Int(false),
                DefaultCustomer = c.Int(false),
                DefaultBranch = c.Int(false),
                DefaultRawBranch = c.Int(false),
                CanChangeStore = c.Boolean(false),
                CanChangeDrawer = c.Boolean(false),
                CanChangeCustomer = c.Boolean(false),
                CanChangeVendor = c.Boolean(false),
                InvoicePrintMode = c.Int(false),
                PrintAfterSave = c.Boolean(false),
                CanChangeTax = c.Boolean(false),
                CanDeleteItemsInInvoices = c.Boolean(false),
                DefaultPayMethodInSales = c.Int(false),
                CanChangePayMethod = c.Boolean(false),
                WhenSellingToACustomerExceededMaxCredit = c.Int(false),
                CanChangeItemPriceInSales = c.Boolean(false),
                WhenSellingItemReachedReOrderLevel = c.Int(false),
                WhenSellingItemWithQtyMoreThanAvailableQty = c.Int(false),
                WhenSellingItemWithPriceLowerThanCostPrice = c.Int(false),
                MaxDiscountInInvoice = c.Double(false),
                CanChangeSalesInvoiceDate = c.Boolean(false),
                CanChangeQtyInSales = c.Boolean(false),
                PrintCreditInvoices = c.Boolean(false),
                ShowPayFormOnSavingNewInvoices = c.Boolean(false),
                CanChangeItemPriceInPurchase = c.Boolean(false),
                CanChangePurchaseInvoiceDate = c.Boolean(false),
                ProductSecrecyLevel = c.Int(false),
                AccountSecrecyLevel = c.Int(false),
                CanChangeCashNoteDate = c.Boolean(false)
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.Banks", delegate (ColumnBuilder c)
            {
                ColumnModel iD = c.Int(false);
                ColumnModel bankAccountNumber = c.String(false);
                ColumnModel swift = c.String();
                ColumnModel eBAN = c.String();
                ColumnModel address = c.String();
                int? maxLength = 20;
                ColumnModel phone = c.String(null, maxLength);
                maxLength = 20;
                ColumnModel phone2 = c.String(null, maxLength);
                maxLength = 20;
                return new
                {
                    ID = iD,
                    BankAccountNumber = bankAccountNumber,
                    Swift = swift,
                    EBAN = eBAN,
                    Address = address,
                    Phone1 = phone,
                    Phone2 = phone2,
                    Phone3 = c.String(null, maxLength)
                };
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.BillingDetails", t => t.ID).Index(t => t.ID);
            CreateTable("dbo.Customers", (ColumnBuilder c) => new
            {
                ID = c.Int(false),
                MaxCredit = c.Double(false),
                PersonalType = c.Byte(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Personals", t => t.ID).Index(t => t.ID);
            CreateTable("dbo.Drawers", (ColumnBuilder c) => new
            {
                ID = c.Int(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.BillingDetails", t => t.ID).Index(t => t.ID);
            CreateTable("dbo.InvoiceDetails", (ColumnBuilder c) => new
            {
                ID = c.Int(false),
                GroupID = c.Int(),
                Discount = c.Double(false),
                Tax = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.ProductTransactions", t => t.ID).ForeignKey("dbo.GroupOfProducts", t => t.GroupID)
                .Index(t => t.ID)
                .Index(t => t.GroupID);
            CreateTable("dbo.OpenBalanceBills", (ColumnBuilder c) => new
            {
                ID = c.Int(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Bills", t => t.ID).Index(t => t.ID);
            CreateTable("dbo.PayCards", (ColumnBuilder c) => new
            {
                ID = c.Int(false),
                BankID = c.Int(false),
                Number = c.String(false, 50),
                CommissionRate = c.Double(false),
                CommissionAccountID = c.Int(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.BillingDetails", t => t.ID).ForeignKey("dbo.Banks", t => t.BankID)
                .ForeignKey("dbo.Accounts", t => t.CommissionAccountID)
                .Index(t => t.ID)
                .Index(t => t.BankID)
                .Index(t => t.CommissionAccountID);
            CreateTable("dbo.ProductDamageBill", (ColumnBuilder c) => new
            {
                ID = c.Int(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Bills", t => t.ID).Index(t => t.ID);
            CreateTable("dbo.PurchaseInvoices", (ColumnBuilder c) => new
            {
                ID = c.Int(false),
                VendorID = c.Int(false),
                Discount = c.Double(false),
                OtherExpenses = c.Double(false),
                Tax = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Bills", t => t.ID).Index(t => t.ID);
            CreateTable("dbo.PurchaseReturnInvoices", (ColumnBuilder c) => new
            {
                ID = c.Int(false),
                InvoiceID = c.Int(false),
                VendorID = c.Int(false),
                Discount = c.Double(false),
                OtherExpenses = c.Double(false),
                Tax = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Bills", t => t.ID).Index(t => t.ID);
            CreateTable("dbo.SalesInvoices", (ColumnBuilder c) => new
            {
                ID = c.Int(false),
                CustomerID = c.Int(false),
                Discount = c.Double(false),
                OtherExpenses = c.Double(false),
                Tax = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Bills", t => t.ID).Index(t => t.ID);
            CreateTable("dbo.SalesReturnInvoices", (ColumnBuilder c) => new
            {
                ID = c.Int(false),
                InvoiceID = c.Int(false),
                CustomerID = c.Int(false),
                Discount = c.Double(false),
                OtherExpenses = c.Double(false),
                Tax = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Bills", t => t.ID).Index(t => t.ID);
            CreateTable("dbo.StockTransferBill", (ColumnBuilder c) => new
            {
                ID = c.Int(false),
                ToBranchID = c.Int(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Bills", t => t.ID).Index(t => t.ID);
            CreateTable("dbo.Vendors", (ColumnBuilder c) => new
            {
                ID = c.Int(false),
                PersonalType = c.Byte(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Personals", t => t.ID).Index(t => t.ID);
        }

        public override void Down()
        {
            DropForeignKey("dbo.Vendors", "ID", "dbo.Personals");
            DropForeignKey("dbo.StockTransferBill", "ID", "dbo.Bills");
            DropForeignKey("dbo.SalesReturnInvoices", "ID", "dbo.Bills");
            DropForeignKey("dbo.SalesInvoices", "ID", "dbo.Bills");
            DropForeignKey("dbo.PurchaseReturnInvoices", "ID", "dbo.Bills");
            DropForeignKey("dbo.PurchaseInvoices", "ID", "dbo.Bills");
            DropForeignKey("dbo.ProductDamageBill", "ID", "dbo.Bills");
            DropForeignKey("dbo.PayCards", "CommissionAccountID", "dbo.Accounts");
            DropForeignKey("dbo.PayCards", "BankID", "dbo.Banks");
            DropForeignKey("dbo.PayCards", "ID", "dbo.BillingDetails");
            DropForeignKey("dbo.OpenBalanceBills", "ID", "dbo.Bills");
            DropForeignKey("dbo.InvoiceDetails", "GroupID", "dbo.GroupOfProducts");
            DropForeignKey("dbo.InvoiceDetails", "ID", "dbo.ProductTransactions");
            DropForeignKey("dbo.Drawers", "ID", "dbo.BillingDetails");
            DropForeignKey("dbo.Customers", "ID", "dbo.Personals");
            DropForeignKey("dbo.Banks", "ID", "dbo.BillingDetails");
            DropForeignKey("dbo.Users", "SettingsProfileID", "dbo.UserSettingsProfiles");
            DropForeignKey("dbo.Users", "AccessProfileID", "dbo.UserAccessProfiles");
            DropForeignKey("dbo.UserAccessProfileDetails", "ProfileId", "dbo.UserAccessProfiles");
            DropForeignKey("dbo.SimilarProducts", "ProductID", "dbo.Products");
            DropForeignKey("dbo.SimilarProducts", "OtherProductID", "dbo.Products");
            DropForeignKey("dbo.RevExpEntries", "JournalID", "dbo.Journals");
            DropForeignKey("dbo.RevExpEntryDetails", "EntryID", "dbo.RevExpEntries");
            DropForeignKey("dbo.ProductUnits", "UnitNameID", "dbo.UnitOfMeasurements");
            DropForeignKey("dbo.ProductUnits", "ProductID", "dbo.Products");
            DropForeignKey("dbo.ProductUnitBarcodes", "UnitID", "dbo.ProductUnits");
            DropForeignKey("dbo.Products", "ProductVendorID", "dbo.ProductVendors");
            DropForeignKey("dbo.Products", "CompanyID", "dbo.ProductCompanies");
            DropForeignKey("dbo.Products", "CategoryID", "dbo.ProductCategories");
            DropForeignKey("dbo.ProductCategories", "ParentGroupID", "dbo.PersonalGroups");
            DropForeignKey("dbo.GroupOfProductsDetails", "GroupID", "dbo.GroupOfProducts");
            DropForeignKey("dbo.Personals", "GroupID", "dbo.PersonalGroups");
            DropForeignKey("dbo.Personals", "AccountID", "dbo.Accounts");
            DropForeignKey("dbo.PersonalGroups", "ParentGroupID", "dbo.PersonalGroups");
            DropForeignKey("dbo.CashNotes", "JournalID", "dbo.Journals");
            DropForeignKey("dbo.Branches", "SellReturnAccountID", "dbo.Accounts");
            DropForeignKey("dbo.Branches", "SellAccountID", "dbo.Accounts");
            DropForeignKey("dbo.Branches", "SalesDiscountAccountID", "dbo.Accounts");
            DropForeignKey("dbo.Branches", "PurchaseReturnAccountID", "dbo.Accounts");
            DropForeignKey("dbo.Branches", "PurchaseDiscountAccountID", "dbo.Accounts");
            DropForeignKey("dbo.Branches", "PurchaseAccountID", "dbo.Accounts");
            DropForeignKey("dbo.Branches", "OpenInventoryAccountID", "dbo.Accounts");
            DropForeignKey("dbo.Branches", "CloseInventoryAccountID", "dbo.Accounts");
            DropForeignKey("dbo.Bills", "JournalID", "dbo.Journals");
            DropForeignKey("dbo.JournalDetails", "JournalID", "dbo.Journals");
            DropForeignKey("dbo.JournalDetails", "CurrencyID", "dbo.Currencies");
            DropForeignKey("dbo.JournalDetails", "AccountID", "dbo.Accounts");
            DropForeignKey("dbo.BillingDetails", "AccountID", "dbo.Accounts");
            DropForeignKey("dbo.Accounts", "ParentID", "dbo.Accounts");
            DropIndex("dbo.Vendors", new string[1] { "ID" });
            DropIndex("dbo.StockTransferBill", new string[1] { "ID" });
            DropIndex("dbo.SalesReturnInvoices", new string[1] { "ID" });
            DropIndex("dbo.SalesInvoices", new string[1] { "ID" });
            DropIndex("dbo.PurchaseReturnInvoices", new string[1] { "ID" });
            DropIndex("dbo.PurchaseInvoices", new string[1] { "ID" });
            DropIndex("dbo.ProductDamageBill", new string[1] { "ID" });
            DropIndex("dbo.PayCards", new string[1] { "CommissionAccountID" });
            DropIndex("dbo.PayCards", new string[1] { "BankID" });
            DropIndex("dbo.PayCards", new string[1] { "ID" });
            DropIndex("dbo.OpenBalanceBills", new string[1] { "ID" });
            DropIndex("dbo.InvoiceDetails", new string[1] { "GroupID" });
            DropIndex("dbo.InvoiceDetails", new string[1] { "ID" });
            DropIndex("dbo.Drawers", new string[1] { "ID" });
            DropIndex("dbo.Customers", new string[1] { "ID" });
            DropIndex("dbo.Banks", new string[1] { "ID" });
            DropIndex("dbo.Users", new string[1] { "SettingsProfileID" });
            DropIndex("dbo.Users", new string[1] { "AccessProfileID" });
            DropIndex("dbo.UserAccessProfileDetails", new string[1] { "ProfileId" });
            DropIndex("dbo.SimilarProducts", new string[1] { "OtherProductID" });
            DropIndex("dbo.SimilarProducts", new string[1] { "ProductID" });
            DropIndex("dbo.RevExpEntryDetails", new string[1] { "EntryID" });
            DropIndex("dbo.RevExpEntries", new string[1] { "JournalID" });
            DropIndex("dbo.ProductUnitBarcodes", new string[1] { "UnitID" });
            DropIndex("dbo.ProductUnits", new string[1] { "UnitNameID" });
            DropIndex("dbo.ProductUnits", new string[1] { "ProductID" });
            DropIndex("dbo.Products", new string[1] { "ProductVendorID" });
            DropIndex("dbo.Products", new string[1] { "CompanyID" });
            DropIndex("dbo.Products", new string[1] { "CategoryID" });
            DropIndex("dbo.ProductCategories", new string[1] { "ParentGroupID" });
            DropIndex("dbo.GroupOfProductsDetails", new string[1] { "GroupID" });
            DropIndex("dbo.PersonalGroups", new string[1] { "ParentGroupID" });
            DropIndex("dbo.Personals", new string[1] { "AccountID" });
            DropIndex("dbo.Personals", new string[1] { "GroupID" });
            DropIndex("dbo.CashNotes", new string[1] { "JournalID" });
            DropIndex("dbo.Branches", new string[1] { "SalesDiscountAccountID" });
            DropIndex("dbo.Branches", new string[1] { "PurchaseDiscountAccountID" });
            DropIndex("dbo.Branches", new string[1] { "CloseInventoryAccountID" });
            DropIndex("dbo.Branches", new string[1] { "OpenInventoryAccountID" });
            DropIndex("dbo.Branches", new string[1] { "SellReturnAccountID" });
            DropIndex("dbo.Branches", new string[1] { "SellAccountID" });
            DropIndex("dbo.Branches", new string[1] { "PurchaseReturnAccountID" });
            DropIndex("dbo.Branches", new string[1] { "PurchaseAccountID" });
            DropIndex("dbo.JournalDetails", new string[1] { "CurrencyID" });
            DropIndex("dbo.JournalDetails", new string[1] { "AccountID" });
            DropIndex("dbo.JournalDetails", new string[1] { "JournalID" });
            DropIndex("dbo.Bills", new string[1] { "JournalID" });
            DropIndex("dbo.BillingDetails", new string[1] { "AccountID" });
            DropIndex("dbo.Accounts", new string[1] { "ParentID" });
            DropTable("dbo.Vendors");
            DropTable("dbo.StockTransferBill");
            DropTable("dbo.SalesReturnInvoices");
            DropTable("dbo.SalesInvoices");
            DropTable("dbo.PurchaseReturnInvoices");
            DropTable("dbo.PurchaseInvoices");
            DropTable("dbo.ProductDamageBill");
            DropTable("dbo.PayCards");
            DropTable("dbo.OpenBalanceBills");
            DropTable("dbo.InvoiceDetails");
            DropTable("dbo.Drawers");
            DropTable("dbo.Customers");
            DropTable("dbo.Banks");
            DropTable("dbo.UserSettingsProfiles");
            DropTable("dbo.Users");
            DropTable("dbo.UserAccessProfiles");
            DropTable("dbo.UserAccessProfileDetails");
            DropTable("dbo.SimilarProducts");
            DropTable("dbo.SystemSettings");
            DropTable("dbo.RevExpEntryDetails");
            DropTable("dbo.RevExpEntries");
            DropTable("dbo.ProductSizes");
            DropTable("dbo.UnitOfMeasurements");
            DropTable("dbo.ProductUnitBarcodes");
            DropTable("dbo.ProductUnits");
            DropTable("dbo.ProductVendors");
            DropTable("dbo.Products");
            DropTable("dbo.ProductCompanies");
            DropTable("dbo.ProductColors");
            DropTable("dbo.ProductCategories");
            DropTable("dbo.PayDetails");
            DropTable("dbo.InvoiceShortCuts");
            DropTable("dbo.ProductTransactions");
            DropTable("dbo.CompanyInfoes");
            DropTable("dbo.GroupOfProductsDetails");
            DropTable("dbo.GroupOfProducts");
            DropTable("dbo.PersonalGroups");
            DropTable("dbo.Personals");
            DropTable("dbo.CashNotes");
            DropTable("dbo.Branches");
            DropTable("dbo.Currencies");
            DropTable("dbo.JournalDetails");
            DropTable("dbo.Journals");
            DropTable("dbo.Bills");
            DropTable("dbo.BillingDetails");
            DropTable("dbo.Accounts");
        }
    }
}
