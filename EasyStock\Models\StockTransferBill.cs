﻿using EasyStock.Common;
using EasyStock.Controller;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Transferts de stock")]
    [Table("StockTransferBill")]
    public class StockTransferBill : Bill
    {
        [NotMapped]
        public Store ToStore
        {
            get
            {
                bool flag = this._toStore == null || this._toStore.ID != this.ToStoreID;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        this._toStore = db.Stores.SingleOrDefault((Store x) => x.ID == this.ToStoreID);
                    }
                }
                return this._toStore;
            }
        }

        [Display(Name = "Depuis le dépôt", GroupName = "Données du document")]
        [Range(1, 2147483647, ErrorMessage = "Veuillez sélectionner le dépôt")]
        public new int StoreID
        {
            get
            {
                return base.StoreID;
            }
            set
            {
                base.StoreID = value;
            }
        }

        [Display(Name = "Vers le dépôt", GroupName = "Données du document")]
        [Range(1, 2147483647, ErrorMessage = "Veuillez sélectionner le dépôt")]
        public int ToStoreID
        {
            get
            {
                return this._toStoreID;
            }
            set
            {
                base.SetProperty<int>(ref this._toStoreID, value, "ToStoreID");
            }
        }

        public override void GetNewCode()
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                Type sss = base.GetType();
                int maxLength = (from x in db.ProductDamageBills.AsNoTracking()
                                 select x.Code).Max((string x) => (int?)x.Length).GetValueOrDefault();
                bool flag = maxLength == 0;
                if (flag)
                {
                    base.Code = "1";
                }
                else
                {
                    string maxCode = (from x in db.ProductDamageBills.AsNoTracking()
                                      where x.Code.Length == maxLength
                                      orderby x.ID descending
                                      select x).FirstOrDefault<ProductDamageBill>().Code;
                    bool flag2 = maxCode != string.Empty;
                    if (flag2)
                    {
                        base.Code = maxCode.GetNextSequence();
                    }
                }
            }
        }

        public Store _toStore;

        private int _toStoreID;
    }
}
