﻿using DevExpress.XtraEditors;
using DevExpress.XtraEditors.DXErrorProvider;
using DevExpress.XtraLayout.Utils;
using DevExpress.XtraSplashScreen;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.Models;
using EasyStock.Views;
using EasyStock.Views.Financial;
using System;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Threading;
using System.Windows.Forms;

namespace EasyStock.MainViews
{
    public partial class FmrLogin : DevExpress.XtraEditors.XtraForm
    {
        public static FmrLogin instance;
        public static FmrLogin Instance
        {
            get
            {
                if (instance == null || instance.IsDisposed)
                {
                    instance = new FmrLogin();
                }
                return instance;
            }
        }
        public FmrLogin()
        {
            InitializeComponent();
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            IOverlaySplashScreenHandle handeler = SplashScreenManager.ShowOverlayForm(this);
            using(var db = new ERPDataContext())
            {
                User user = db.Users.Include((User x) => x.AccessProfile).Include((User x) => x.AccessProfile.Details).Include((User x) => x.SettingsProfile).FirstOrDefault((User x) => x.UserName.ToLower().Trim() == txtUserName.Text.ToLower().Trim());
                
            }

        }

        private void FmrLogin_Shown(object sender, EventArgs e)
        {
            if (ApplicationSettings.Default.RememberMe)
            {
                chkRememberMe.Checked = ApplicationSettings.Default.RememberMe;
                txtUserName.Text = ApplicationSettings.Default.UserName;
                txtPassword.Text = ApplicationSettings.Default.Password;
                cmbDbList.Text = ApplicationSettings.Default.Branch;
            
            }
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            new ConnectToServerForm().ShowDialog();
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            Close();
            Application.Exit();
        }

        public void LoadUsers()
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                txtUserName.Properties.DataSource = db.Users.Select(x=>new { x.ID, x.UserName }).ToList();
                txtUserName.Properties.PopulateColumns();
                txtUserName.Properties.ValueMember = "ID";
                txtUserName.Properties.DisplayMember = "UserName";
                txtUserName.Properties.Columns["ID"].Visible = false;
                Branch branch;
            }
        }
        private void FmrLogin_Load(object sender, EventArgs e)
        {
            LoadUsers();
            txtPassword.Focus();
        }

        private void txtPassword_Properties_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            var tagshow = txtPassword.Tag as string;
            if (tagshow == "show")
            {
                txtPassword.Properties.Buttons[0].ImageOptions.ImageIndex = 1;
                txtPassword.Tag = "hide";
                txtPassword.Properties.UseSystemPasswordChar = false;
                txtPassword.Properties.PasswordChar = '\0';
            }
            else if (tagshow == "hide")
            {
                txtPassword.Properties.Buttons[0].ImageOptions.ImageIndex = 0;
                txtPassword.Tag = "show";
                txtPassword.Properties.UseSystemPasswordChar = true;
                txtPassword.Properties.PasswordChar = '*';
            }
        }
    }
}