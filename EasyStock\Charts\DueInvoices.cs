﻿using DevExpress.Utils;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.Views;
using System;
using System.ComponentModel.DataAnnotations;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace EasyStock.Charts
{

    public class DueInvoices : GridChart
    {
        public class DueInvoicesModel
        {
            [Display(Name = "Facture N°")]
            public int InvoiceID { get; set; }

            [Display(Name = "Facture Date")]
            public DateTime? InvoiceDate { get; set; }

            [Display(Name = "Client")]
            public string CustomerName { get; set; }

            [Display(Name = "Date d'échéance")]
            public DateTime? DueDate { get; set; }

            [Display(Name = "Total")]
            public double? Total { get; set; }

            [Display(Name = "Montant payé")]
            public double? Paid { get; set; }
        }

        public bool IsSelectionMode;

        private int SelectedInvoiceID;

        public override string Caption => "Factures à payer";

        public override Color Color => ColorTranslator.FromHtml("#424242");

        public DueInvoices()
	{
		view.OptionsView.ShowFooter = true;
		if (view.Columns.Count == 0)
		{
			view.PopulateColumns(typeof(DueInvoicesModel));
		}
	}

        public override async Task<object> QueryData()
        {
            ERPDataContext db = new ERPDataContext();
            object dataSource = null;
            await Task.Run(delegate
            {
                dataSource = (from inv in db.SalesInvoices
                              join cus in db.Customers on inv.CustomerID equals cus.ID
                              where inv.DueDate <= DateTime.Now && (db.PayDetails.Where(d => d.SourceID == inv.ID && (int)d.SourceType == 4).Sum(x => (double?)(x.Amount * x.CurrancyRate)) ?? 0.0) < inv.Total + inv.Tax + inv.OtherExpenses - inv.Discount
                              select new DueInvoicesModel
                              {
                                  InvoiceID = inv.ID,
                                  InvoiceDate = inv.Date,
                                  DueDate = inv.DueDate,
                                  CustomerName = cus.Name,
                                  Total = inv.Total,
                                  Paid = (db.PayDetails.Where(d => d.SourceID == inv.ID && (int)d.SourceType == 4).Sum(x => (double?)(x.Amount * x.CurrancyRate)) ?? 0.0)
                              } into x
                              orderby x.DueDate descending
                              select x).ToList();
            });
            return dataSource;
        }

        private void DueInvoices_DoubleClick(object sender, EventArgs e)
        {
            base.DoubleClick += DueInvoices_DoubleClick;
            DXMouseEventArgs ea = e as DXMouseEventArgs;
            GridView view = sender as GridView;
            GridHitInfo info = view.CalcHitInfo(ea.Location);
            if (info.InRow || info.InRowCell)
            {
                SelectedInvoiceID = Convert.ToInt32(view.GetFocusedRowCellValue("ID"));
                if (!IsSelectionMode)
                {
                    OpenForm(SelectedInvoiceID);
                }
            }
        }

        public virtual void OpenForm(int id)
        {
            HomeForm.OpenForm(SalesInvoiceForm.Instance);
            SalesInvoiceForm.Instance.Show();
            SalesInvoiceForm.Instance.GoTo(id);
        }
    }
}
