﻿using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraLayout;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace EasyStock.Controls
{
    public class CostDistributionOption : XtraUserControl
    {
        public CostDistributionOptions SelectedOption
        {
            get
            {
                bool flag = this.group.EditValue != null;
                CostDistributionOptions result;
                if (flag)
                {
                    result = (CostDistributionOptions)this.group.EditValue;
                }
                else
                {
                    result = CostDistributionOptions.ByQty;
                }
                return result;
            }
        }

        public CostDistributionOption()
        {
            LayoutControl lc = new LayoutControl();
            lc.Dock = DockStyle.Fill;
            this.group.Properties.Items.AddRange(new RadioGroupItem[]
            {
    new RadioGroupItem(CostDistributionOptions.ByPrice, "Par prix"),
    new RadioGroupItem(CostDistributionOptions.ByQty, "Par quantité")
            });
            this.group.SelectedIndex = 1;
            lc.AddItem("Méthode de répartition des coûts sur les articles", this.group).TextLocation = Locations.Top;
            base.Controls.Add(lc);
            this.Dock = DockStyle.Top;
            base.Height = 120;
        }

        protected override void Dispose(bool disposing)
        {
            bool flag = disposing && this.components != null;
            if (flag)
            {
                this.components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            base.SuspendLayout();
            base.AutoScaleDimensions = new SizeF(6f, 13f);
            base.AutoScaleMode = AutoScaleMode.Font;
            base.Name = "CostDistributionOption";
            base.Size = new Size(468, 340);
            base.ResumeLayout(false);
        }

        public RadioGroup group = new RadioGroup();

        private IContainer components = null;
    }
}
