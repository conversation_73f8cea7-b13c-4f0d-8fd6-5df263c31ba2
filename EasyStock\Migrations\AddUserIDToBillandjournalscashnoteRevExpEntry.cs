﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x0200048C RID: 1164
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class AddUserIDToBillandjournalscashnoteRevExpEntry : DbMigration, IMigrationMetadata
	{
		// Token: 0x06002303 RID: 8963 RVA: 0x00012235 File Offset: 0x00010435
		public override void Up()
		{
			base.AddColumn("dbo.Bills", "UserID", (ColumnBuilder c) => c.Int(new bool?(false), false, new int?(1), null, null, null, null), null);
		}

		// Token: 0x06002304 RID: 8964 RVA: 0x00012269 File Offset: 0x00010469
		public override void Down()
		{
			base.DropColumn("dbo.Bills", "UserID", null);
		}

		// Token: 0x17000B7F RID: 2943
		// (get) Token: 0x06002305 RID: 8965 RVA: 0x001F1464 File Offset: 0x001EF664
		string IMigrationMetadata.Id
		{
			get
			{
				return "202106011536158_AddUserIDToBillandjournalscashnoteRevExpEntry";
			}
		}

		// Token: 0x17000B80 RID: 2944
		// (get) Token: 0x06002306 RID: 8966 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000B81 RID: 2945
		// (get) Token: 0x06002307 RID: 8967 RVA: 0x001F147C File Offset: 0x001EF67C
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B02 RID: 11010
		private readonly ResourceManager Resources = new ResourceManager(typeof(AddUserIDToBillandjournalscashnoteRevExpEntry));
	}
}
