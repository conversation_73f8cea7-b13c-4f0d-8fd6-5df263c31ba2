﻿using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [Table("PersonalGroups", Schema = "dbo")]
    public abstract class PersonalGroup
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [ReadOnly(true)]
        [Display(Name = "Code")]
        public int ID { get; set; }

        [Display(Name = "N°")]
        [MaxLength(25)]
        [ReadOnly(true)]
        public string Code { get; set; }

        [MaxLength(500)]
        [Display(Name = "Nom de la famille")]
        [Required(AllowEmptyStrings = false, ErrorMessage = "Le nom de la famille est obligatoire")]
        public string Name { get; set; }

        [Display(Name = "Catégorie principale")]
        public PersonalGroup ParentGroup { get; set; }

        [Display(Name = "Code de la famille principale")]
        public int? ParentGroupID { get; set; }

        public ICollection<Personal> Personals { get; set; }
    }
}
