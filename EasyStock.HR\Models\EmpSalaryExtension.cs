﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    public class EmpSalaryExtension : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Numéro")]
        [ReadOnly(true)]
        public int ID { get; set; }

        [Display(Name = "Code de l'employé")]
        [Range(1, 2147483647)]
        public int EmpId { get; set; }

        [Display(Name = "Code du poste")]
        [Range(1, 2147483647)]
        public int SalaryExtensionId { get; set; }

        [Display(Name = "Valeur")]
        public double Value { get; set; }

        [Display(Name = "Notes")]
        [MaxLength(250)]
        public string Notes { get; set; }

        [Display(Name = "Type")]
        public ExtensionType Type { get; set; }

        public SalaryExtension SalaryExtension { get; set; }

        public Employee Employee { get; set; }
    }
}
