﻿using DevExpress.XtraEditors;
using EasyStock.Common;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class GroupView : MasterView
    {
        public static GroupView Instance
        {
            get
            {
                bool flag = GroupView.instance == null || GroupView.instance.IsDisposed;
                if (flag)
                {
                    GroupView.instance = new GroupView();
                }
                return GroupView.instance;
            }
        }

        public Group group
        {
            get
            {
                return this.groupBindingSource.Current as Group;
            }
            set
            {
                this.groupBindingSource.DataSource = value;
            }
        }

        public GroupView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            this.context = new HRDataContext();
            this.gridView1.AddEditButton(new EventHandler(this.RepositoryItemButtonEdit1_Click));
            this.gridView1.SetAlternatingColors();
            base.Shown += this.GroupView_Shown;
        }

        private void GroupView_Shown(object sender, EventArgs e)
        {
            bool flag = this.group == null;
            if (flag)
            {
                this.New();
            }
        }

        private void RepositoryItemButtonEdit1_Click(object sender, EventArgs e)
        {
            Group row = this.gridView1.GetRow(this.gridView1.FocusedRowHandle) as Group;
            bool flag = row != null;
            if (flag)
            {
                this.GoTo(row.ID);
            }
        }

        public void GoTo(int id)
        {
            Group sourceDepr = this.context.Groups.SingleOrDefault((Group x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.group = sourceDepr;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable");
            }
        }

        public override void New()
        {
            this.group = new Group();
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.context.Groups.AddOrUpdate(new Group[]
                {
                    this.group
                });
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.gridControl1.DataSource = this.context.Groups.ToList<Group>();
            base.RefreshData();
        }

        public override void Delete()
        {
        }

        private static GroupView instance;

        private HRDataContext context;
    }
}
