﻿namespace EasyStock.HR.Views
{
	// Token: 0x02000039 RID: 57
	public partial class EmpDelayView : global::EasyStock.HR.MainViews.MasterView
	{
		// Token: 0x060001A8 RID: 424 RVA: 0x00016634 File Offset: 0x00014834
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060001A9 RID: 425 RVA: 0x0001666C File Offset: 0x0001486C
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.empOvertimeDelayBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.DayDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.DurationTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.bsencePaidImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.NotesTextEdit = new DevExpress.XtraEditors.MemoEdit();
            this.EmpIDTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.RegulationIDTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForEmpID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForRegulationID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDayDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDuration = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForbsencePaid = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForNotes = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.empOvertimeDelayBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DurationTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bsencePaidImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NotesTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmpIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.RegulationIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmpID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForRegulationID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDayDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDuration)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForbsencePaid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNotes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            this.SuspendLayout();
            // 
            // dataLayoutControl1
            // 
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.DayDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.DurationTextEdit);
            this.dataLayoutControl1.Controls.Add(this.bsencePaidImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.NotesTextEdit);
            this.dataLayoutControl1.Controls.Add(this.EmpIDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.RegulationIDTextEdit);
            this.dataLayoutControl1.DataSource = this.empOvertimeDelayBindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 26);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(800, 400);
            this.dataLayoutControl1.TabIndex = 4;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            // 
            // IDTextEdit
            // 
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empOvertimeDelayBindingSource, "ID", true));
            this.IDTextEdit.Location = new System.Drawing.Point(120, 44);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.EditMask = "N0";
            this.IDTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(317, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 4;
            // 
            // empOvertimeDelayBindingSource
            // 
            this.empOvertimeDelayBindingSource.DataSource = typeof(EasyStock.HR.Models.EmpOvertimeDelay);
            // 
            // DayDateDateEdit
            // 
            this.DayDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empOvertimeDelayBindingSource, "DayDate", true));
            this.DayDateDateEdit.EditValue = null;
            this.DayDateDateEdit.Location = new System.Drawing.Point(120, 116);
            this.DayDateDateEdit.Name = "DayDateDateEdit";
            this.DayDateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.DayDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DayDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DayDateDateEdit.Size = new System.Drawing.Size(317, 20);
            this.DayDateDateEdit.StyleController = this.dataLayoutControl1;
            this.DayDateDateEdit.TabIndex = 7;
            // 
            // DurationTextEdit
            // 
            this.DurationTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empOvertimeDelayBindingSource, "Duration", true));
            this.DurationTextEdit.Location = new System.Drawing.Point(120, 140);
            this.DurationTextEdit.Name = "DurationTextEdit";
            this.DurationTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DurationTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DurationTextEdit.Properties.Mask.EditMask = "N0";
            this.DurationTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.DurationTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.DurationTextEdit.Size = new System.Drawing.Size(317, 20);
            this.DurationTextEdit.StyleController = this.dataLayoutControl1;
            this.DurationTextEdit.TabIndex = 8;
            // 
            // bsencePaidImageComboBoxEdit
            // 
            this.bsencePaidImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empOvertimeDelayBindingSource, "bsencePaid", true));
            this.bsencePaidImageComboBoxEdit.Location = new System.Drawing.Point(120, 164);
            this.bsencePaidImageComboBoxEdit.Name = "bsencePaidImageComboBoxEdit";
            this.bsencePaidImageComboBoxEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.bsencePaidImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.bsencePaidImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.bsencePaidImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.bsencePaidImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Oui", EasyStock.HR.EAbsencePaid.Casual, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Non", EasyStock.HR.EAbsencePaid.Normal, 1)});
            this.bsencePaidImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.bsencePaidImageComboBoxEdit.Size = new System.Drawing.Size(317, 20);
            this.bsencePaidImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.bsencePaidImageComboBoxEdit.TabIndex = 9;
            // 
            // NotesTextEdit
            // 
            this.NotesTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empOvertimeDelayBindingSource, "Notes", true));
            this.NotesTextEdit.Location = new System.Drawing.Point(120, 188);
            this.NotesTextEdit.Name = "NotesTextEdit";
            this.NotesTextEdit.Size = new System.Drawing.Size(317, 55);
            this.NotesTextEdit.StyleController = this.dataLayoutControl1;
            this.NotesTextEdit.TabIndex = 10;
            // 
            // EmpIDTextEdit
            // 
            this.EmpIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empOvertimeDelayBindingSource, "EmpID", true));
            this.EmpIDTextEdit.Location = new System.Drawing.Point(120, 68);
            this.EmpIDTextEdit.Name = "EmpIDTextEdit";
            this.EmpIDTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.EmpIDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.EmpIDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.EmpIDTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.EmpIDTextEdit.Properties.NullText = "";
            this.EmpIDTextEdit.Size = new System.Drawing.Size(317, 20);
            this.EmpIDTextEdit.StyleController = this.dataLayoutControl1;
            this.EmpIDTextEdit.TabIndex = 5;
            // 
            // RegulationIDTextEdit
            // 
            this.RegulationIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empOvertimeDelayBindingSource, "RegulationID", true));
            this.RegulationIDTextEdit.Location = new System.Drawing.Point(120, 92);
            this.RegulationIDTextEdit.Name = "RegulationIDTextEdit";
            this.RegulationIDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.RegulationIDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.RegulationIDTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.RegulationIDTextEdit.Properties.NullText = "";
            this.RegulationIDTextEdit.Properties.ReadOnly = true;
            this.RegulationIDTextEdit.Size = new System.Drawing.Size(317, 20);
            this.RegulationIDTextEdit.StyleController = this.dataLayoutControl1;
            this.RegulationIDTextEdit.TabIndex = 6;
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(800, 400);
            this.Root.TextVisible = false;
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup2,
            this.emptySpaceItem2,
            this.emptySpaceItem1});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(780, 380);
            // 
            // layoutControlGroup2
            // 
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForID,
            this.ItemForEmpID,
            this.ItemForRegulationID,
            this.ItemForDayDate,
            this.ItemForDuration,
            this.ItemForbsencePaid,
            this.ItemForNotes});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(441, 247);
            this.layoutControlGroup2.Text = "Informations";
            // 
            // ItemForID
            // 
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.MaxSize = new System.Drawing.Size(417, 24);
            this.ItemForID.MinSize = new System.Drawing.Size(417, 24);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(417, 24);
            this.ItemForID.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForID.TextSize = new System.Drawing.Size(92, 13);
            // 
            // ItemForEmpID
            // 
            this.ItemForEmpID.Control = this.EmpIDTextEdit;
            this.ItemForEmpID.Location = new System.Drawing.Point(0, 24);
            this.ItemForEmpID.Name = "ItemForEmpID";
            this.ItemForEmpID.Size = new System.Drawing.Size(417, 24);
            this.ItemForEmpID.TextSize = new System.Drawing.Size(92, 13);
            // 
            // ItemForRegulationID
            // 
            this.ItemForRegulationID.Control = this.RegulationIDTextEdit;
            this.ItemForRegulationID.Location = new System.Drawing.Point(0, 48);
            this.ItemForRegulationID.Name = "ItemForRegulationID";
            this.ItemForRegulationID.Size = new System.Drawing.Size(417, 24);
            this.ItemForRegulationID.Text = "Liste des retards";
            this.ItemForRegulationID.TextSize = new System.Drawing.Size(92, 13);
            // 
            // ItemForDayDate
            // 
            this.ItemForDayDate.Control = this.DayDateDateEdit;
            this.ItemForDayDate.Location = new System.Drawing.Point(0, 72);
            this.ItemForDayDate.Name = "ItemForDayDate";
            this.ItemForDayDate.Size = new System.Drawing.Size(417, 24);
            this.ItemForDayDate.TextSize = new System.Drawing.Size(92, 13);
            // 
            // ItemForDuration
            // 
            this.ItemForDuration.Control = this.DurationTextEdit;
            this.ItemForDuration.Location = new System.Drawing.Point(0, 96);
            this.ItemForDuration.Name = "ItemForDuration";
            this.ItemForDuration.Size = new System.Drawing.Size(417, 24);
            this.ItemForDuration.TextSize = new System.Drawing.Size(92, 13);
            // 
            // ItemForbsencePaid
            // 
            this.ItemForbsencePaid.Control = this.bsencePaidImageComboBoxEdit;
            this.ItemForbsencePaid.Location = new System.Drawing.Point(0, 120);
            this.ItemForbsencePaid.Name = "ItemForbsencePaid";
            this.ItemForbsencePaid.Size = new System.Drawing.Size(417, 24);
            this.ItemForbsencePaid.TextSize = new System.Drawing.Size(92, 13);
            // 
            // ItemForNotes
            // 
            this.ItemForNotes.Control = this.NotesTextEdit;
            this.ItemForNotes.Location = new System.Drawing.Point(0, 144);
            this.ItemForNotes.MaxSize = new System.Drawing.Size(417, 59);
            this.ItemForNotes.MinSize = new System.Drawing.Size(417, 59);
            this.ItemForNotes.Name = "ItemForNotes";
            this.ItemForNotes.Size = new System.Drawing.Size(417, 59);
            this.ItemForNotes.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForNotes.TextSize = new System.Drawing.Size(92, 13);
            // 
            // emptySpaceItem2
            // 
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(0, 247);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(441, 133);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(441, 0);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(339, 380);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // EmpDelayView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.dataLayoutControl1);
            this.Name = "EmpDelayView";
            this.Text = "Retard de l\'employé";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.empOvertimeDelayBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DurationTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bsencePaidImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NotesTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmpIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.RegulationIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmpID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForRegulationID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDayDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDuration)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForbsencePaid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNotes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x04000260 RID: 608
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04000261 RID: 609
		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		// Token: 0x04000262 RID: 610
		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		// Token: 0x04000263 RID: 611
		private global::System.Windows.Forms.BindingSource empOvertimeDelayBindingSource;

		// Token: 0x04000264 RID: 612
		private global::DevExpress.XtraEditors.DateEdit DayDateDateEdit;

		// Token: 0x04000265 RID: 613
		private global::DevExpress.XtraEditors.TextEdit DurationTextEdit;

		// Token: 0x04000266 RID: 614
		private global::DevExpress.XtraEditors.ImageComboBoxEdit bsencePaidImageComboBoxEdit;

		// Token: 0x04000267 RID: 615
		private global::DevExpress.XtraEditors.MemoEdit NotesTextEdit;

		// Token: 0x04000268 RID: 616
		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		// Token: 0x04000269 RID: 617
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		// Token: 0x0400026A RID: 618
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;

		// Token: 0x0400026B RID: 619
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		// Token: 0x0400026C RID: 620
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForEmpID;

		// Token: 0x0400026D RID: 621
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForRegulationID;

		// Token: 0x0400026E RID: 622
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDayDate;

		// Token: 0x0400026F RID: 623
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDuration;

		// Token: 0x04000270 RID: 624
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForbsencePaid;

		// Token: 0x04000271 RID: 625
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForNotes;

		// Token: 0x04000272 RID: 626
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;

		// Token: 0x04000273 RID: 627
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;

		// Token: 0x04000274 RID: 628
		private global::DevExpress.XtraEditors.LookUpEdit EmpIDTextEdit;

		// Token: 0x04000275 RID: 629
		private global::DevExpress.XtraEditors.LookUpEdit RegulationIDTextEdit;
	}
}
