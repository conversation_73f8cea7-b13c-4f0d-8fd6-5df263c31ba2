﻿using DevExpress.Data;
using DevExpress.LookAndFeel;
using DevExpress.Utils;
using DevExpress.Utils.Extensions;
using DevExpress.Utils.Menu;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.DXErrorProvider;
using DevExpress.XtraEditors.Mask;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.Properties;
using EasyStock.Views;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Media;
using System.Windows.Forms;

namespace EasyStock.Classes
{
    public class ProductRowDetailManger
    {
        public ProductRowDetailManger(GridView _DetailsGridView, GridControl _DetailsGridControl, GridColumn _colProductID, GridColumn _colUnitID, GridColumn _colTaxPercentage, GridColumn _colDiscountPercentage, GridColumn _colDiscount, GridColumn _colPrice, GridColumn _colTotal, GridColumn _colNet, GridColumn _colTax, Function<IProductRowDetail> _GetNewRow)
        {
            this.DetailsGridView = _DetailsGridView;
            this.DetailsGridControl = _DetailsGridControl;
            this.colProductID = _colProductID;
            this.colUnitID = _colUnitID;
            this.colTaxPercentage = _colTaxPercentage;
            this.colDiscountPercentage = _colDiscountPercentage;
            this.colDiscount = _colDiscount;
            this.colPrice = _colPrice;
            this.colTotal = _colTotal;
            this.colNet = _colNet;
            this.colTax = _colTax;
            this.GetNewRow = _GetNewRow;
            this.ConfigGrid();
        }

        public void ConfigGrid()
        {
            GridView view = this.DetailsGridView;
            GridControl gridControl = this.DetailsGridControl;
            GridColumn colProduct = this.colProductID;
            GridColumn colUnit = this.colUnitID;
            view.Appearance.TopNewRow.BackColor = DXSkinColors.ForeColors.Warning;
            view.Appearance.TopNewRow.Options.UseBackColor = true;
            view.OptionsDetail.EnableMasterViewMode = false;
            view.NewItemRowText = "Cliquez ici pour ajouter un nouvel article";
            view.OptionsView.NewItemRowPosition = NewItemRowPosition.Top;
            view.OptionsView.ShowGroupPanel = false;
            view.Columns.Add(new GridColumn
            {
                Name = "clmCode",
                FieldName = "Code",
                Caption = "Code",
                UnboundType = UnboundColumnType.String,
                Fixed = FixedStyle.Left,
                VisibleIndex = 0,
                Visible = true
            });
            GridColumn colIndex = new GridColumn
            {
                Name = "clmIndex",
                FieldName = "Index",
                Caption = "#",
                UnboundType = UnboundColumnType.Integer,
                MaxWidth = 30,
                Fixed = FixedStyle.Left
            };
            colIndex.SummaryItem.Mode = SummaryMode.Mixed;
            colIndex.SummaryItem.SummaryType = SummaryItemType.Count;
            view.Columns.Add(colIndex);
            view.CustomUnboundColumnData += ProductRowDetailManger.View_CustomUnboundColumnData;
            RepositoryItemButtonEdit repoDelete = new RepositoryItemButtonEdit();
            EditorButton editorButton = new EditorButton(ButtonPredefines.Glyph);
            editorButton.ImageOptions.SvgImageSize = new Size(15, 15);
            editorButton.ImageOptions.SvgImage = Resources.delete;
            editorButton.Visible = true;
            editorButton.Enabled = true;
            repoDelete.Buttons.Clear();
            repoDelete.Buttons.Add(editorButton);
            repoDelete.Name = "repoDelete";
            repoDelete.TextEditStyle = TextEditStyles.HideTextEditor;
            repoDelete.Click += ProductRowDetailManger.repoDelete_Click;
            RepositoryItemLookUpEdit repoColors = new RepositoryItemLookUpEdit();
            RepositoryItemLookUpEdit repoSizes = new RepositoryItemLookUpEdit();
            repoColors.DataSource = CurrentSession.ProductColors;
            repoSizes.DataSource = CurrentSession.ProductSizes;
            repoColors.DisplayMember = "Name";
            repoSizes.DisplayMember = "Name";
            repoColors.ValueMember = "ID";
            repoSizes.ValueMember = "ID";
            repoColors.NullText = "";
            repoSizes.NullText = "";
            repoSizes.ValidateOnEnterKey = true;
            repoColors.ValidateOnEnterKey = true;
            repoColors.ImmediatePopup = true;
            repoSizes.ImmediatePopup = true;
            gridControl.RepositoryItems.AddRange(new RepositoryItem[]
            {
                repoColors,
                repoSizes
            });
            repoSizes.TextEditStyle = (repoColors.TextEditStyle = TextEditStyles.Standard);
            repoSizes.ProcessNewValue += ProductRowDetailManger.RepoSizes_ProcessNewValue;
            repoColors.ProcessNewValue += ProductRowDetailManger.RepoColors_ProcessNewValue;
            view.Columns["ColorID"].ColumnEdit = repoColors;
            view.Columns["SizeID"].ColumnEdit = repoSizes;
            view.OptionsView.ShowFooter = true;
            GridColumn ColDelete = new GridColumn();
            ColDelete.ColumnEdit = repoDelete;
            ColDelete.MaxWidth = 25;
            ColDelete.MinWidth = 25;
            ColDelete.Name = "ColDelete";
            ColDelete.Visible = true;
            ColDelete.VisibleIndex = view.Columns.Count<GridColumn>();
            ColDelete.Width = 25;
            ColDelete.Fixed = FixedStyle.Right;
            ColDelete.OptionsColumn.AllowMerge = DefaultBoolean.False;
            gridControl.RepositoryItems.Add(repoDelete);
            view.Columns.Add(ColDelete);
            ProductRowDetailManger.AddProductsRepo(gridControl, colProduct, colUnit);
            view.CellValueChanged += this.View_CellValueChanged;
            view.CustomColumnDisplayText += ProductRowDetailManger.View_CustomColumnDisplayText;
            view.SetAlternatingColors();
            view.OptionsSelection.EnableAppearanceFocusedCell = false;
            view.OptionsSelection.EnableAppearanceFocusedRow = false;
            view.FocusRectStyle = DrawFocusRectStyle.CellFocus;
            view.ShownEditor += ProductRowDetailManger.View_ShownEditor;
            view.CustomDrawCell += ProductRowDetailManger.View_CustomDrawCell;
            gridControl.ProcessGridKey += ProductRowDetailManger.GridControl_ProcessGridKey;
            view.ValidateRow += this.View_ValidateRow;
            view.InvalidRowException += this.View_InvalidRowException;
            view.CustomRowCellEditForEditing += ProductRowDetailManger.View_CustomRowCellEditForEditing;
            view.CellValueChanging += ProductRowDetailManger.View_CellValueChanging;
            view.FocusedRowChanged += ProductRowDetailManger.View_FocusedRowChanged;
            view.FocusedColumnChanged += ProductRowDetailManger.View_FocusedColumnChanged;
            view.RowCountChanged += ProductRowDetailManger.View_RowCountChanged;
            view.RowUpdated += ProductRowDetailManger.View_RowUpdated;
            RepositoryItemSpinEdit repoPers = new RepositoryItemSpinEdit();
            repoPers.Mask.MaskType = MaskType.Numeric;
            repoPers.Mask.EditMask = "p2";
            repoPers.Mask.UseMaskAsDisplayFormat = true;
            gridControl.RepositoryItems.Add(repoPers);
            this.colTaxPercentage.ColumnEdit = (this.colDiscountPercentage.ColumnEdit = repoPers);
            view.CellValueChanged += ProductRowDetailManger.View_CellValueChangedForInvoices;
            view.CustomColumnDisplayText += ProductRowDetailManger.View_CustomColumnDisplayText1;
            this.DetailsGridView.RowUpdated += this.DetailsGridView_RowUpdated;
            this.DetailsGridView.RowCountChanged += this.DetailsGridView_RowCountChanged;
            this.DetailsGridView.CustomColumnDisplayText += this.DetailsGridView_CustomColumnDisplayText;
            this.DetailsGridView.PopupMenuShowing += this.DetailsGridView_PopupMenuShowing;
            this.DetailsGridView.OptionsView.RowAutoHeight = false;
            this.DetailsGridView.RowHeight = 25;
            this.DetailsGridView.Appearance.Row.FontSizeDelta = 2;
            this.DetailsGridView.Appearance.Row.FontStyleDelta = FontStyle.Bold;
            this.DetailsGridView.Appearance.HeaderPanel.FontSizeDelta = 3;
            this.DetailsGridView.Appearance.HeaderPanel.FontStyleDelta = FontStyle.Bold;
            this.DetailsGridView.ValidateRow += this.DetailsGridView_ValidateRow;
            this.DetailsGridView.ValidatingEditor += this.DetailsGridView_ValidatingEditor;
            this.DetailsGridView.InvalidValueException += this.DetailsGridView_InvalidValueException;
            this.DetailsGridView.FocusedRowChanged += ProductRowDetailManger.View_FocusedRowChanged;
            this.DetailsGridView.FocusedColumnChanged += ProductRowDetailManger.View_FocusedColumnChanged;
            this.DetailsGridView.SetAlternatingColors(ColorTranslator.FromHtml("#fff59d"), Color.WhiteSmoke);
            this.DetailsGridView.RowStyle += this.DetailsGridView_RowStyle;
        }

        private void DetailsGridView_RowUpdated(object sender, RowObjectEventArgs e)
        {
            IEnumerable<IProductRowDetail> details = this.DetailsGridView.DataSource as IEnumerable<IProductRowDetail>;
            bool flag = details == null || this.bill == null;
            if (!flag)
            {
                this.bill.Total = details.Sum((IProductRowDetail x) => x.Total);
                this.bill.Tax = details.Sum((IProductRowDetail x) => x.Tax);
                this.bill.Discount = details.Sum((IProductRowDetail x) => x.Discount);
                this.CalculateNet();
            }
        }

        private void DetailsGridView_RowStyle(object sender, RowStyleEventArgs e)
        {
            bool flag = e.RowHandle % 2 == 0;
            if (flag)
            {
                e.Appearance.BackColor = ColorTranslator.FromHtml("#fff59d");
            }
        }

        private void DetailsGridView_InvalidValueException(object sender, InvalidValueExceptionEventArgs e)
        {
            e.ExceptionMode = ExceptionMode.Ignore;
        }

        private void DetailsGridView_ValidatingEditor(object sender, BaseContainerValidateEditorEventArgs e)
        {
            GridView view = sender as GridView;
            IProductRowDetail row = view.GetFocusedRow() as IProductRowDetail;
            bool flag = view.FocusedColumn == this.colDiscountPercentage;
            if (flag)
            {
                bool flag2 = view.FocusedColumn == this.colDiscount || view.FocusedColumn == this.colDiscountPercentage;
                if (flag2)
                {
                    double value = 0.0;
                    object value2 = e.Value;
                    double.TryParse((value2 != null) ? value2.ToString() : null, out value);
                    bool flag3 = value > CurrentSession.CurrentUser.SettingsProfile.MaxDiscountInInvoice;
                    if (flag3)
                    {
                        XtraMessageBox.Show(string.Format("Vous ne pouvez pas appliquer une remise supérieure à {0:p}", CurrentSession.CurrentUser.SettingsProfile.MaxDiscountInInvoice));
                        e.Valid = false;
                    }
                }
            }
        }

        public void DetailsGridView_ValidateRow(object sender, ValidateRowEventArgs e)
        {
            GridView view = sender as GridView;
            IProductRowDetail row = view.GetRow(e.RowHandle) as IProductRowDetail;
            bool flag = row == null || row.Product == null;
            if (!flag)
            {
                double price = row.Price;
                ProductUnit unit = row.Unit;
                double? num = (unit != null) ? new double?(unit.BuyPrice) : null;
                bool flag2 = (price <= num.GetValueOrDefault() & num != null) && CurrentSession.CurrentUser.SettingsProfile.WhenSellingItemWithPriceLowerThanCostPrice > RedundancyOptions.DoNotInterrupt;
                if (flag2)
                {
                    ErrorType error = (CurrentSession.CurrentUser.SettingsProfile.WhenSellingItemWithPriceLowerThanCostPrice == RedundancyOptions.Deny) ? ErrorType.Critical : ErrorType.Warning;
                    view.SetColumnError(view.Columns["Product"], "Le prix de vente est inférieur au prix d'achat", error);
                }
            }
        }

        private void DetailsGridView_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            if (e.Value is double val && (e.Column == colDiscount || e.Column == colTax || e.Column == colPrice || e.Column == colTotal || e.Column == colNet))
            {
                double fraction = Convert.ToInt32(val % 1.0 * 10000.0);
                fraction /= 10000.0;
                int fractionCount = fraction.ToString().Length - 2;
                if (fractionCount < 0 || fraction < 0.0001)
                {
                    fractionCount = 0;
                }
                else if (fractionCount > 2)
                {
                    fractionCount = 2;
                }
                e.DisplayText = string.Format("{0:N" + fractionCount + "}", val);
            }
        }
        private void DetailsGridView_RowCountChanged(object sender, EventArgs e)
        {
            this.DetailsGridView_RowUpdated(sender, null);
            this.DetailsGridView.FocusedRowHandle = 2;
        }

        private void CalculateNet()
        {
            this.DetailsGridView.UpdateCurrentRow();
        }

        private static void View_CustomColumnDisplayText1(object sender, CustomColumnDisplayTextEventArgs e)
        {
            bool flag = e.Column.FieldName == "Total";
            if (flag)
            {
            }
        }

        private void DetailsGridView_PopupMenuShowing(object sender, PopupMenuShowingEventArgs e)
        {
            GridView view = sender as GridView;
            bool flag = e.MenuType == GridMenuType.Row;
            if (flag)
            {
                bool flag2 = e.HitInfo.RowHandle == -2147483647;
                if (!flag2)
                {
                    DXMenuItem btn_OpenProducts = new DXMenuItem
                    {
                        Caption = "Ouvrir l'article"
                    };
                    btn_OpenProducts.Click += this.Btn_OpenProducts_Click;
                    e.Menu.Items.Add(btn_OpenProducts);
                }
            }
        }

        private void Btn_OpenProducts_Click(object sender, EventArgs e)
        {
            IProductRowDetail detail = this.DetailsGridView.GetRow(this.DetailsGridView.FocusedRowHandle) as IProductRowDetail;
            bool flag = detail == null || detail.ProductID == 0;
            if (!flag)
            {
                HomeForm.OpenForm(ProductForm.Instance, false, false, false);
                ProductForm.Instance.GoTo(detail.ProductID);
            }
        }

        private static void View_CellValueChangedForInvoices(object sender, CellValueChangedEventArgs e)
        {
            GridView view = sender as GridView;
            bool ISNew1flag = e.RowHandle == -2147483647;
            if (!(view.GetRow(e.RowHandle) is IProductRowDetail row))
            {
                return;
            }
            switch (e.Column.FieldName)
            {
                case "ProductID":
                    if (row.Product == null)
                    {
                        break;
                    }
                    row.TaxPercentage = row.Product.SalesTax;
                    goto case "UnitID";
                case "UnitID":
                    if (row.Product == null || row.Unit == null)
                    {
                        break;
                    }
                    row.DiscountPercentage = row.Unit.SellDiscount;
                    goto case "DiscountPercentage";
                case "DiscountPercentage":
                case "TaxPercentage":
                case "Price":
                case "Quantity":
                    {
                        row.Discount = row.DiscountPercentage * row.Total;
                        Product product = row.Product;
                        if (product != null && product.CalculateTaxAfterDiscount)
                        {
                            row.Tax = row.TaxPercentage * (row.Total - row.Discount);
                        }
                        else
                        {
                            row.Tax = row.TaxPercentage * row.Total;
                        }
                        goto case "Discount";
                    }
                case "Discount":
                case "Tax":
                    if (view.FocusedColumn?.FieldName == "Discount")
                    {
                        row.DiscountPercentage = row.Discount / row.Total;
                    }
                    if (view.FocusedColumn?.FieldName == "Tax")
                    {
                        Product product2 = row.Product;
                        if (product2 != null && product2.CalculateTaxAfterDiscount)
                        {
                            row.TaxPercentage = row.Tax / (row.Total - row.Discount);
                        }
                        else
                        {
                            row.TaxPercentage = row.Tax / row.Total;
                        }
                    }
                    break;
                case "Net":
                    if (!(view.FocusedColumn?.FieldName == "Net"))
                    {
                    }
                    break;
            }
        }

        private static void View_RowUpdated(object sender, RowObjectEventArgs e)
        {
            ProductRowDetailManger.View_RowCountChanged(sender, e);
        }

        private static void View_RowCountChanged(object sender, EventArgs e)
        {
            GridView view = sender as GridView;
            bool flag = !(view.DataSource is IEnumerable<IProductRowDetail>);
            if (!flag)
            {
                IEnumerable<IProductRowDetail> rows = (IEnumerable<IProductRowDetail>)view.DataSource;
                bool flag2 = rows == null;
                if (!flag2)
                {
                    var groupedRows = from r in rows
                                      group r by new
                                      {
                                          ProductID = r.ProductID,
                                          UnitID = r.UnitID,
                                          SizeID = r.SizeID,
                                          ColorID = r.ColorID,
                                          Expire = r.Expire,
                                          Price = r.Price,
                                          Discount = ((r != null) ? new double?(r.DiscountPercentage) : null),
                                          Tax = ((r != null) ? new double?(r.TaxPercentage) : null)
                                      } into g
                                      where g.Count<IProductRowDetail>() > 1
                                      select g;
                    foreach (var row in groupedRows)
                    {
                        IOrderedEnumerable<IProductRowDetail> sortedList = from x in row
                                                                           orderby x.ID descending
                                                                           select x;
                        IProductRowDetail detail = sortedList.First<IProductRowDetail>();
                        detail.Quantity = sortedList.Sum((IProductRowDetail x) => x.Quantity);
                        view.SetRowCellValue(view.GetRowHandle(rows.ToList<IProductRowDetail>().IndexOf(detail)), "Quantity", detail.Quantity);
                        for (int i = 1; i < sortedList.Count<IProductRowDetail>(); i++)
                        {
                            int rowIndex = rows.ToList<IProductRowDetail>().IndexOf(sortedList.ToList<IProductRowDetail>()[i]);
                            view.DeleteRow(view.GetRowHandle(rowIndex));
                        }
                    }
                }
            }
        }

        public static void RepoColors_ProcessNewValue(object sender, ProcessNewValueEventArgs e)
        {
            bool flag = (string)e.DisplayValue != string.Empty;
            if (flag)
            {
                ICollection<ProductColor> list = (sender as LookUpEdit).Properties.DataSource as ICollection<ProductColor>;
                ProductColor color = new ProductColor
                {
                    Name = e.DisplayValue.ToString()
                };
                using (ERPDataContext db = new ERPDataContext())
                {
                    db.ProductColors.Add(color);
                    db.SaveChanges();
                }
                list.Add(color);
                e.Handled = true;
            }
        }

        public static void RepoSizes_ProcessNewValue(object sender, ProcessNewValueEventArgs e)
        {
            bool flag = (string)e.DisplayValue != string.Empty;
            if (flag)
            {
                ICollection<ProductSize> list = (sender as LookUpEdit).Properties.DataSource as ICollection<ProductSize>;
                ProductSize size = new ProductSize
                {
                    Name = e.DisplayValue.ToString()
                };
                using (ERPDataContext db = new ERPDataContext())
                {
                    db.ProductSizes.Add(size);
                    db.SaveChanges();
                }
                list.Add(size);
                e.Handled = true;
            }
        }

        public static void View_FocusedRowChanged(object sender, FocusedRowChangedEventArgs e)
        {
            GridView view = sender as GridView;
            ProductRowDetailManger.View_FocusedColumnChanged(sender, new FocusedColumnChangedEventArgs(null, null));
        }

        private static void View_FocusedColumnChanged(object sender, FocusedColumnChangedEventArgs e)
        {
            GridView view = sender as GridView;
            IProductRowDetail row = view.GetRow(view.FocusedRowHandle) as IProductRowDetail;
            bool flag = row == null;
            if (!flag)
            {
                OptionsColumn optionsColumn = view.Columns["ColorID"].OptionsColumn;
                bool? flag2;
                if (row == null)
                {
                    flag2 = null;
                }
                else
                {
                    Product product = row.Product;
                    flag2 = ((product != null) ? new bool?(product.HasColor) : null);
                }
                bool? flag3 = flag2;
                optionsColumn.AllowFocus = flag3.GetValueOrDefault();
                OptionsColumn optionsColumn2 = view.Columns["SizeID"].OptionsColumn;
                bool? flag4;
                if (row == null)
                {
                    flag4 = null;
                }
                else
                {
                    Product product2 = row.Product;
                    flag4 = ((product2 != null) ? new bool?(product2.HasSize) : null);
                }
                flag3 = flag4;
                optionsColumn2.AllowFocus = flag3.GetValueOrDefault();
                OptionsColumn optionsColumn3 = view.Columns["Expire"].OptionsColumn;
                bool? flag5;
                if (row == null)
                {
                    flag5 = null;
                }
                else
                {
                    Product product3 = row.Product;
                    flag5 = ((product3 != null) ? new bool?(product3.HasExpier) : null);
                }
                flag3 = flag5;
                optionsColumn3.AllowFocus = flag3.GetValueOrDefault();
                OptionsColumn optionsColumn4 = view.Columns["Quantity"].OptionsColumn;
                bool? flag6;
                if (row == null)
                {
                    flag6 = null;
                }
                else
                {
                    Product product4 = row.Product;
                    flag6 = ((product4 != null) ? new bool?(product4.HasSerial) : null);
                }
                flag3 = flag6;
                optionsColumn4.ReadOnly = flag3.GetValueOrDefault();
            }
        }

        private static void View_CellValueChanging(object sender, CellValueChangedEventArgs e)
        {
            GridView view = sender as GridView;
            IProductRowDetail row = view.GetRow(e.RowHandle) as IProductRowDetail;
            bool flag = e.Column.FieldName == "ProductID";
            if (flag)
            {
                bool flag2 = row != null && e.Value != null;
                if (flag2)
                {
                    bool flag3 = !e.Value.Equals(row.ProductID);
                    if (flag3)
                    {
                        row.UnitID = 0;
                    }
                }
            }
        }

        private static void View_CustomRowCellEditForEditing(object sender, CustomRowCellEditEventArgs e)
        {
            GridView view = sender as GridView;
            IProductRowDetail row = view.GetRow(e.RowHandle) as IProductRowDetail;
            bool flag = e.Column.FieldName == "UnitID";
            if (flag)
            {
                RepositoryItemGridLookUpEdit repo = new RepositoryItemGridLookUpEdit();
                repo.NullText = "";
                e.RepositoryItem = repo;
                bool flag2 = row == null || row.Product == null;
                if (!flag2)
                {
                    GridView repoView = repo.View;
                    repoView.Columns.Clear();
                    repoView.Columns.AddField("UnitName").VisibleIndex = 0;
                    repoView.CustomColumnDisplayText += delegate (object ss, CustomColumnDisplayTextEventArgs ee)
                    {
                        bool flag4 = ee.Column.FieldName == "UnitName";
                        if (flag4)
                        {
                            UnitOfMeasurement unit = ee.Value as UnitOfMeasurement;
                            bool flag5 = unit != null;
                            if (flag5)
                            {
                                ee.DisplayText = ((unit != null) ? unit.Name : null);
                            }
                        }
                    };
                    repo.DataSource = (from x in CurrentSession.ProductUnits
                                       where x.ProductID == row.ProductID
                                       select x).ToList<ProductUnit>();
                    repo.DisplayMember = "UnitName.Name";
                    repo.ValueMember = "ID";
                    repo.NullText = "";
                    repo.BestFitMode = BestFitMode.BestFitResizePopup;
                }
            }
            else
            {
                bool flag3 = e.Column.FieldName == "Quantity" || e.Column.FieldName == "Price";
                if (flag3)
                {
                    RepositoryItemSpinEdit repoSpin = new RepositoryItemSpinEdit();
                    e.RepositoryItem = repoSpin;
                }
            }
        }

        private void View_InvalidRowException(object sender, InvalidRowExceptionEventArgs e)
        {
            bool flag;
            if (e.Row != null)
            {
                IProductRowDetail row = e.Row as IProductRowDetail;
                flag = (row != null && row.Product == null);
            }
            else
            {
                flag = true;
            }
            bool flag2 = flag;
            if (flag2)
            {
                GridView view = sender as GridView;
                view.DeleteRow(e.RowHandle);
                e.ExceptionMode = ExceptionMode.Ignore;
            }
            else
            {
                e.ExceptionMode = ExceptionMode.NoAction;
            }
        }

        public void View_ValidateRow(object sender, ValidateRowEventArgs e)
        {
            GridView view = sender as GridView;
            IProductRowDetail row = view.GetRow(e.RowHandle) as IProductRowDetail;
            bool flag = row == null || row.Product == null;
            if (flag)
            {
                e.Valid = false;
            }
            else
            {
                bool flag2 = row.Quantity < 0.0001;
                if (flag2)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Quantity"], "La quantité doit être supérieure à 0.0001");
                }
                bool flag3 = row.Product.HasColor && row.ColorID == null;
                if (flag3)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["ColorID"], LangResource.ErrorCantBeEmpry);
                    view.Columns["ColorID"].Visible = true;
                }
                bool flag4 = row.Product.HasSize && row.SizeID == null;
                if (flag4)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["SizeID"], LangResource.ErrorCantBeEmpry);
                    view.Columns["SizeID"].Visible = true;
                }
                bool flag5 = row.Product.HasExpier && row.Expire == null;
                if (flag5)
                {
                    e.Valid = false;
                    view.Columns["Expire"].Visible = true;
                    view.SetColumnError(view.Columns["Expire"], LangResource.ErrorCantBeEmpry);
                }
            }
        }

        public static void View_CustomDrawCell(object sender, RowCellCustomDrawEventArgs e)
        {
            GridView view = sender as GridView;
            GridColumn focusedColumn = view.FocusedColumn;
            bool flag = ((focusedColumn != null) ? focusedColumn.FieldName : null) == "Code" && e.Column.FieldName == "Code" && e.RowHandle < 0;
            if (flag)
            {
                e.Appearance.BackColor = DXSkinColors.ForeColors.Warning;
                e.Handled = true;
            }
            else
            {
                e.Handled = false;
            }
        }

        public static void GridControl_ProcessGridKey(object sender, KeyEventArgs e)
        {
            try
            {
                GridControl control = sender as GridControl;
                bool flag = control == null;
                if (!flag)
                {
                    GridView view = control.FocusedView as GridView;
                    bool flag2 = view == null;
                    if (!flag2)
                    {
                        bool flag3 = view.FocusedColumn == null;
                        if (!flag3)
                        {
                            bool flag4 = e.KeyCode == Keys.Return;
                            if (flag4)
                            {
                                view.PostEditor();
                                string focusedColumn = view.FocusedColumn.FieldName;
                                bool flag5 = view.FocusedColumn.FieldName == "Code" || view.FocusedColumn.FieldName == "ProductID" || view.FocusedColumn.FieldName == "Product";
                                if (flag5)
                                {
                                    ProductRowDetailManger.GridControl_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                                }
                                bool flag6 = view.FocusedRowHandle < 0;
                                if (flag6)
                                {
                                    view.AddNewRow();
                                    view.FocusedColumn = view.Columns[focusedColumn];
                                }
                                e.Handled = true;
                            }
                            else
                            {
                                bool flag7 = e.KeyCode == Keys.Tab;
                                if (flag7)
                                {
                                    view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                                    e.Handled = true;
                                }
                                else
                                {
                                    bool flag8 = e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control;
                                    if (flag8)
                                    {
                                        bool flag9 = view.FocusedRowHandle >= 0;
                                        if (flag9)
                                        {
                                            view.DeleteSelectedRows();
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
            }
        }

        private static void View_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            GridView view = sender as GridView;
            bool flag = e.Column.FieldName == "UnitID";
            if (flag)
            {
                IProductRowDetail row = view.GetRow(view.GetRowHandle(e.ListSourceRowIndex)) as IProductRowDetail;
                ProductUnit unit = (row != null) ? row.Unit : null;
                bool flag2 = row != null;
                if (flag2)
                {
                    string displayText;
                    if (unit == null)
                    {
                        displayText = null;
                    }
                    else
                    {
                        UnitOfMeasurement unitName = unit.UnitName;
                        displayText = ((unitName != null) ? unitName.Name : null);
                    }
                    e.DisplayText = displayText;
                }
            }
        }

        private static void View_CustomUnboundColumnData(object sender, CustomColumnDataEventArgs e)
        {
            GridView view = sender as GridView;
            IProductRowDetail row = e.Row as IProductRowDetail;
            bool flag = e.Column.FieldName == "Code" && row != null;
            if (flag)
            {
                bool isSetData = e.IsSetData;
                if (isSetData)
                {
                    row.ProductCode = e.Value.ToString();
                }
                else
                {
                    bool isGetData = e.IsGetData;
                    if (isGetData)
                    {
                        e.Value = row.ProductCode;
                    }
                }
            }
            else
            {
                bool flag2 = e.Column.FieldName == "Index";
                if (flag2)
                {
                    e.Value = view.GetVisibleRowHandle(e.ListSourceRowIndex) + 1;
                }
            }
        }

        public static void View_ShownEditor(object sender, EventArgs e)
        {
            GridView view = sender as GridView;
            bool flag = view.FocusedColumn.FieldName == "Code" && view.FocusedRowHandle < 0;
            if (flag)
            {
                BaseEdit editor = view.ActiveEditor;
                editor.Properties.Appearance.BackColor = DXSkinColors.ForeColors.Warning;
                editor.Properties.Appearance.Options.UseBackColor = true;
            }
        }

        private void View_CellValueChanged(object sender, CellValueChangedEventArgs e)
        {
            GridView view = sender as GridView;
            bool ISNew1flag = e.RowHandle == -2147483647;
            IProductRowDetail row = view.GetRow(e.RowHandle) as IProductRowDetail;
            if (row == null)
            {
                return;
            }
            if (e.Column.FieldName == "Code")
            {
                string ItemCode = e.Value.ToString().Trim();
                if (ItemCode.Where((char x) => x == '*').Count() >= 3)
                {
                    using ERPDataContext db = new ERPDataContext();
                    string[] data = ItemCode.Split(new char[1] { '*' }, StringSplitOptions.RemoveEmptyEntries);
                    int barcodeID = 0;
                    int productID = 0;
                    int unitID = 0;
                    int.TryParse(data[0], out barcodeID);
                    int.TryParse(data[1], out productID);
                    int.TryParse(data[2], out unitID);
                    PrintedBarcode printedBarcode = db.PrintedBarcodes.SingleOrDefault((PrintedBarcode x) => x.ID == barcodeID);
                    if (printedBarcode != null)
                    {
                        row.ProductID = printedBarcode.ProductID;
                        row.ColorID = printedBarcode.ColorID;
                        row.Expire = printedBarcode.Expire;
                        row.SizeID = printedBarcode.SizeID;
                        row.UnitID = printedBarcode.UnitID;
                    }
                }
                else if (CurrentSession.SystemSettings.ReadFormScaleBarcode && ItemCode.Length == CurrentSession.SystemSettings.BarcodeLength && ItemCode.StartsWith(CurrentSession.SystemSettings.ScaleBarcodePrefix))
                {
                    string itemCodeString = e.Value.ToString().Trim().Substring(CurrentSession.SystemSettings.ScaleBarcodePrefix.Length, CurrentSession.SystemSettings.ProductCodeLength);
                    ItemCode = Convert.ToInt32(itemCodeString).ToString();
                    ProductUnitBarcode unit = CurrentSession.ProductUnitBarcodes.FirstOrDefault((ProductUnitBarcode x) => x.Barcode == ItemCode);
                    if (unit != null)
                    {
                        row.ProductID = CurrentSession.AccessableProducts.Single((Product x) => x.ID == unit.ProductID).ID;
                        row.UnitID = unit.Unit.ID;
                        string Readvalue = e.Value.ToString().Substring(CurrentSession.SystemSettings.ScaleBarcodePrefix.Length + CurrentSession.SystemSettings.ProductCodeLength);
                        if (CurrentSession.SystemSettings.IgnoreCheckDigit)
                        {
                            Readvalue = Readvalue.Remove(Readvalue.Length - 1, 1);
                        }
                        double value = Convert.ToDouble(Readvalue);
                        value /= Math.Pow(10.0, (int)CurrentSession.SystemSettings.DivideValueBy);
                        if (CurrentSession.SystemSettings.ReadMode == ReadValueMode.Weight)
                        {
                            row.Quantity = value;
                        }
                        else if (CurrentSession.SystemSettings.ReadMode == ReadValueMode.Price && unit != null)
                        {
                            double price = 0.0;
                            price = row.Product.SalesTax;
                            row.Quantity = value / price;
                        }
                    }
                }
                if (row.Product == null)
                {
                    ItemCode = e.Value.ToString().Trim();
                    ProductUnitBarcode unit = CurrentSession.ProductUnitBarcodes.FirstOrDefault((ProductUnitBarcode x) => x.Barcode.Trim() == ItemCode);
                    if (unit != null)
                    {
                        row.ProductID = CurrentSession.AccessableProducts.Single((Product x) => x.ID == unit.ProductID).ID;
                        row.UnitID = unit.Unit.ID;
                    }
                    else
                    {
                        row.ProductID = CurrentSession.AccessableProducts.SingleOrDefault((Product x) => x.Code.Trim() == ItemCode)?.ID ?? 0;
                    }
                    if (row.ProductID == 0)
                    {
                        using ERPDataContext eRPDataContext = new ERPDataContext();
                        GroupOfProducts group = eRPDataContext.GroupsOfProducts.Include((GroupOfProducts x) => x.Details).FirstOrDefault((GroupOfProducts x) => x.Code.Trim() == ItemCode);
                        if (group != null)
                        {
                            if (group.ExpireType == GroupOfProductsExpireType.Suspended || (group.ExpireType == GroupOfProductsExpireType.Periodic && (group.EndDate.Value.Date < DateTime.Now.Date || group.StartDate.Value.Date > DateTime.Now.Date)))
                            {
                                XtraMessageBox.Show("Ce groupe d'articles a été arrêté ou a expiré", "Échec de l'ajout", MessageBoxButtons.OK, MessageBoxIcon.Hand);
                                return;
                            }
                            IList<IProductRowDetail> datasource = (IList<IProductRowDetail>)view.DataSource;
                            view.DeleteRow(e.RowHandle);
                            group.Details.ForEach(delegate (GroupOfProductsDetails x)
                            {
                                IProductRowDetail productRowDetail = GetNewRow();
                                productRowDetail.ProductID = x.ProductID;
                                productRowDetail.UnitID = x.UnitID;
                                datasource.Add(productRowDetail);
                                view.SetRowCellValue(datasource.Count - 1, "ProductID", productRowDetail.ProductID);
                                view.SetRowCellValue(datasource.Count - 1, "UnitID", productRowDetail.UnitID);
                                productRowDetail.DiscountPercentage = ((group.DiscountType == GroupOfProductDiscountType.Variable) ? x.Discount : ((group.DiscountType == GroupOfProductDiscountType.Fixed) ? group.Discount : 0.0));
                                productRowDetail.Quantity = x.Quantity;
                                view.SetRowCellValue(datasource.Count - 1, "Quantity", productRowDetail.Quantity);
                            });
                            return;
                        }
                    }
                }
                if (row.Product == null && ItemCode != null)
                {
                    if (File.Exists("beep-5.wav"))
                    {
                        SoundPlayer player = new SoundPlayer("beep-5.wav");
                        player.Play();
                    }
                    DialogResult DR = XtraMessageBox.Show("Ce produit n'existe pas. Voulez-vous l'ajouter ?", "Produit non trouvé", MessageBoxButtons.YesNo, MessageBoxIcon.Hand);
                    if (DR != DialogResult.Yes)
                    {
                        return;
                    }
                    Product product = QuickAddProduct.Add(ItemCode.Trim());
                    if (product == null)
                    {
                        return;
                    }
                    row.ProductID = product.ID;
                }
                if (row.Product != null)
                {
                    if (File.Exists("beep-6.wav"))
                    {
                        SoundPlayer player = new SoundPlayer("beep-6.wav");
                        player.Play();
                    }
                    row.ProductID = row.Product.ID;
                    View_CellValueChanged(sender, new CellValueChangedEventArgs(e.RowHandle, view.Columns["ProductID"], row.ProductID));
                    View_CellValueChanged(sender, new CellValueChangedEventArgs(e.RowHandle, view.Columns["UnitID"], row.UnitID));
                    view.SetRowCellValue(e.RowHandle, view.Columns["ProductID"], row.ProductID);
                }
            }
            string fieldName = e.Column.FieldName;
            string text = fieldName;
            if (!(text == "ProductID"))
            {
                if (text == "UnitID")
                {
                    if (row.Product.PriceIncludeTax)
                    {
                        row.Price = row.Unit.SellPrice / (1.0 + row.Product.PurchaseTax);
                    }
                    else
                    {
                        row.Price = row.Unit.SellPrice;
                    }
                    if (row.Quantity == 0.0)
                    {
                        row.Quantity = 1.0;
                    }
                    row.Factor = row.Unit.Factor;
                }
                return;
            }
            if (row.Product == null)
            {
                view.DeleteRow(e.RowHandle);
                return;
            }
            if (row.Product.HasSerial)
            {
                row.Quantity = 1.0;
            }
            if (row.UnitID == 0)
            {
                row.UnitID = (from x in CurrentSession.ProductUnits
                              where x.ProductID == row.ProductID
                              orderby x.DefualtSell descending
                              select x).First().ID;
                View_CellValueChanged(sender, new CellValueChangedEventArgs(e.RowHandle, view.Columns["UnitID"], row.UnitID));
                if (string.IsNullOrEmpty(row.ProductCode))
                {
                    row.ProductCode = row.Product.Code;
                }
            }
        }

        public static void AddProductsRepo(GridControl gridControl, GridColumn colProduct, GridColumn colUnit)
        {
            ProductRowDetailManger.AddProductsRepo(gridControl, colProduct);
        }

        public static void AddProductsRepo(GridControl gridControl, GridColumn colProduct)
        {
            RepositoryItemGridLookUpEdit repoProducts = new RepositoryItemGridLookUpEdit();
            gridControl.RepositoryItems.Add(repoProducts);
            repoProducts.DataSource = CurrentSession.Products;
            repoProducts.DisplayMember = "Name";
            repoProducts.ValueMember = "ID";
            repoProducts.NullText = "";
            repoProducts.ValidateOnEnterKey = true;
            repoProducts.AllowNullInput = DefaultBoolean.False;
            repoProducts.BestFitMode = BestFitMode.BestFitResizePopup;
            repoProducts.ImmediatePopup = true;
            GridView repoView = repoProducts.View;
            repoView.FocusRectStyle = DrawFocusRectStyle.RowFullFocus;
            repoView.OptionsSelection.UseIndicatorForSelection = true;
            repoView.OptionsView.ShowAutoFilterRow = true;
            repoView.OptionsView.ShowFilterPanelMode = ShowFilterPanelMode.ShowAlways;
            repoView.Columns.Clear();
            repoView.Columns.AddField("CustomField1").Caption = ERPDataContext.ProductCustomFields.CustomField1;
            repoView.Columns.AddField("CustomField2").Caption = ERPDataContext.ProductCustomFields.CustomField2;
            repoView.Columns.AddField("CustomField3").Caption = ERPDataContext.ProductCustomFields.CustomField3;
            repoView.Columns.AddField("CustomField4").Caption = ERPDataContext.ProductCustomFields.CustomField4;
            repoView.Columns.AddField("Type");
            repoView.Columns.AddField("Descreption");
            repoView.Columns.AddField("CategoryID");
            repoView.Columns.AddField("CompanyID");
            repoView.Columns.AddField("Name");
            repoView.Columns.AddField("Code");
            repoView.Columns.AddField("ID");
            repoView.Columns.ForEach(delegate (GridColumn x)
            {
                x.Visible = true;
                x.VisibleIndex = 0;
            });
            colProduct.ColumnEdit = repoProducts;
        }

        private static void repoDelete_Click(object sender, EventArgs e)
        {
            bool flag = !CurrentSession.CurrentUser.SettingsProfile.CanDeleteItemsInInvoices;
            if (flag)
            {
                XtraMessageBox.Show("Vous n'avez pas l'autorisation de supprimer les articles des factures et des documents", "Impossible de supprimer l'article", MessageBoxButtons.OK, MessageBoxIcon.Hand);
            }
            else
            {
                GridView view = ((GridControl)((BaseControl)sender).Parent).MainView as GridView;
                view.DeleteSelectedRows();
                view.Focus();
                view.FocusedColumn = view.Columns["Code"];
                view.FocusedRowHandle = -2147483647;
            }
        }

        private GridView DetailsGridView;

        private GridControl DetailsGridControl;

        private GridColumn colProductID;

        private GridColumn colUnitID;

        private GridColumn colTaxPercentage;

        private GridColumn colDiscountPercentage;

        private GridColumn colDiscount;

        private GridColumn colPrice;

        private GridColumn colTotal;

        private GridColumn colNet;

        private GridColumn colTax;

        public ISalesBill bill;

        private Function<IProductRowDetail> GetNewRow;
    }
}
