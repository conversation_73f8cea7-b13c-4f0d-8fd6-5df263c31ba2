﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    public class VendorGroup : PersonalGroup
    {
        [NotMapped]
        [Display(Name = "Fournisseurs")]
        public ICollection<Vendor> Vendors
        {
            get
            {
                return (ICollection<Vendor>)base.Personals;
            }
            set
            {
                base.Personals = (ICollection<Personal>)value;
            }
        }
    }
}
