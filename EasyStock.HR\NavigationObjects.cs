﻿using EasyStock.Common;
using EasyStock.HR.Views;
using EasyStock.HR.Views.PenaltyRewardView;
using EasyStock.HR.Views.Work;

namespace EasyStock.HR
{
    public static class NavigationObjects
    {
        public static NavigationObject mainData = new NavigationObject("Données principales");

        public static NavigationObject Depart = new NavigationObject("Départements", NavigationObjects.mainData, () => new DepartmentView(), true, WindowActions.All);

        public static NavigationObject job = new NavigationObject("Emplois", NavigationObjects.mainData, () => new JobView(), true, WindowActions.All);

        public static NavigationObject group = new NavigationObject("Groupes", NavigationObjects.mainData, () => new GroupView(), true, WindowActions.All);

        public static NavigationObject TimeTabel = new NavigationObject("Emploi du temps", NavigationObjects.mainData, () => new TimeTableView(), true, WindowActions.All);

        public static NavigationObject SalaryEx = new NavigationObject("Extensions de salaire", NavigationObjects.mainData, () => new SalaryExtensionView(), true, WindowActions.All);

        public static NavigationObject Vacation = new NavigationObject("Congés officiels", NavigationObjects.mainData, () => new OfficialVacationView(), true, WindowActions.All);

        public static NavigationObject AbcList = new NavigationObject("Liste des absences", NavigationObjects.mainData, () => AbcenceRegulationView.Instance, true, (WindowActions)110);

        public static NavigationObject SalaryList = new NavigationObject("Liste des salaires", NavigationObjects.mainData, () => new SalaryRegulationView(), true, WindowActions.All);

        public static NavigationObject shift = new NavigationObject("Équipes", NavigationObjects.mainData, () => new ShiftView(), true, WindowActions.All);

        public static NavigationObject OverAndDelay = new NavigationObject("Régulation des heures supplémentaires et des retards", NavigationObjects.mainData, () => new OvertimeAndDelayRegulationView(), true, WindowActions.All);

        public static NavigationObject empData = new NavigationObject("Employés");

        public static NavigationObject Emp = new NavigationObject("Données de l'employé", NavigationObjects.empData, () => new EmpView(), true, WindowActions.All);

        public static NavigationObject EmpListView = new NavigationObject("Employés", NavigationObjects.empData, () => new EmpListView(), true, WindowActions.All);

        public static NavigationObject AtendAndLeave = new NavigationObject("Présence et Absences");

        public static NavigationObject attens = new NavigationObject("Registre de présence et d'absence", NavigationObjects.AtendAndLeave, () => new EmpAttendenceView(), true, WindowActions.All);

        public static NavigationObject vacation = new NavigationObject("Enregistrement des congés", NavigationObjects.AtendAndLeave, () => new EmpVacationView(), true, WindowActions.All);

        public static NavigationObject vacationList = new NavigationObject("Congés", NavigationObjects.AtendAndLeave, () => new EmpVacationListView(), true, WindowActions.All);

        public static NavigationObject Absence = new NavigationObject("Enregistrement des absences", NavigationObjects.AtendAndLeave, () => new EmpAbsenceView(), true, WindowActions.All);

        public static NavigationObject AbsenceList = new NavigationObject("Absences", NavigationObjects.AtendAndLeave, () => new EmpAbsenceListView(), true, WindowActions.All);

        public static NavigationObject Delay = new NavigationObject("Enregistrement des retards", NavigationObjects.AtendAndLeave, () => new EmpDelayView(), true, WindowActions.All);

        public static NavigationObject DelayList = new NavigationObject("Retards", NavigationObjects.AtendAndLeave, () => new EmpDelayListView(), true, WindowActions.All);

        public static NavigationObject Overtime = new NavigationObject("Enregistrement des heures supplémentaires", NavigationObjects.AtendAndLeave, () => new EmpOvertimeView(), true, WindowActions.All);

        public static NavigationObject OvertimeList = new NavigationObject("Heures supplémentaires", NavigationObjects.AtendAndLeave, () => new EmpOvertimeListView(), true, WindowActions.All);

        public static NavigationObject Mission = new NavigationObject("Enregistrement des missions", NavigationObjects.AtendAndLeave, () => new EmpMissionView(), true, WindowActions.All);

        public static NavigationObject MissionList = new NavigationObject("Missions", NavigationObjects.AtendAndLeave, () => new EmpMissionListView(), true, WindowActions.All);

        public static NavigationObject Shift = new NavigationObject("Enregistrement des remplacements de shift", NavigationObjects.AtendAndLeave, () => new EmpShiftView(), true, WindowActions.All);

        public static NavigationObject ShiftList = new NavigationObject("Remplacements de shift", NavigationObjects.AtendAndLeave, () => new EmpShiftListView(), true, WindowActions.All);

        public static NavigationObject penaltyRewardData = new NavigationObject("Sanctions et Récompenses");

        public static NavigationObject penalty = new NavigationObject("Enregistrement des sanctions", NavigationObjects.penaltyRewardData, () => new PenaltyView(), true, WindowActions.All);

        public static NavigationObject penaltyListView = new NavigationObject("Sanctions", NavigationObjects.penaltyRewardData, () => new PenaltyListView(), true, WindowActions.All);

        public static NavigationObject Reward = new NavigationObject("Enregistrement des récompenses", NavigationObjects.penaltyRewardData, () => new RewardView(), true, WindowActions.All);

        public static NavigationObject rewardListView = new NavigationObject("Récompenses", NavigationObjects.penaltyRewardData, () => new RewardListView(), true, WindowActions.All);

        public static NavigationObject LoanData = new NavigationObject("Prêts");

        public static NavigationObject Loan = new NavigationObject("Enregistrement des prêts", NavigationObjects.LoanData, () => new EmpLoanView(), true, WindowActions.All);

        public static NavigationObject LoanList = new NavigationObject("Prêts", NavigationObjects.LoanData, () => new EmpLoanListView(), true, WindowActions.All);

        public static NavigationObject WorkData = new NavigationObject("Statut de travail");

        public static NavigationObject workLeave = new NavigationObject("Enregistrement de demande de congé", NavigationObjects.WorkData, () => new WorkLeaveView(), true, WindowActions.All);

        public static NavigationObject workLeaveListView = new NavigationObject("Demandes de congé", NavigationObjects.WorkData, () => new WorkLeaveListView(), true, WindowActions.All);

        public static NavigationObject workReturn = new NavigationObject("Enregistrement de retour au travail", NavigationObjects.WorkData, () => new WorkReturnView(), true, WindowActions.All);

        public static NavigationObject workReturnListView = new NavigationObject("Retours au travail", NavigationObjects.WorkData, () => new WorkReturnListView(), true, WindowActions.All);
    }
}
