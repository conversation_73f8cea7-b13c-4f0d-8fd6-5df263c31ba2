﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;
using EasyStock.Classes;

namespace EasyStock.Migrations
{
	// Token: 0x020004D5 RID: 1237
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class addCurrentAssetsAndOwnerEqAccountSettings : DbMigration, IMigrationMetadata
	{
		// Token: 0x060024AC RID: 9388 RVA: 0x001FD364 File Offset: 0x001FB564
		public override void Up()
		{
			base.AddColumn("dbo.SystemSettings", "CurrentAssetsAccount", (ColumnBuilder c) => c.Int(new bool?(false), false, new int?(AccountHelper.Accounts.CurrentAssetsAccount.ID), null, null, null, null), null);
			base.AddColumn("dbo.SystemSettings", "OwnersEquityAccount", (ColumnBuilder c) => c.Int(new bool?(false), false, new int?(AccountHelper.Accounts.Owner_EquityAccount.ID), null, null, null, null), null);
		}

		// Token: 0x060024AD RID: 9389 RVA: 0x00012D73 File Offset: 0x00010F73
		public override void Down()
		{
			base.DropColumn("dbo.SystemSettings", "OwnersEquityAccount", null);
			base.DropColumn("dbo.SystemSettings", "CurrentAssetsAccount", null);
		}

		// Token: 0x17000BF4 RID: 3060
		// (get) Token: 0x060024AE RID: 9390 RVA: 0x001FD3D4 File Offset: 0x001FB5D4
		string IMigrationMetadata.Id
		{
			get
			{
				return "202102251813031_addCurrentAssetsAndOwnerEqAccountSettings";
			}
		}

		// Token: 0x17000BF5 RID: 3061
		// (get) Token: 0x060024AF RID: 9391 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000BF6 RID: 3062
		// (get) Token: 0x060024B0 RID: 9392 RVA: 0x001FD3EC File Offset: 0x001FB5EC
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002BC6 RID: 11206
		private readonly ResourceManager Resources = new ResourceManager(typeof(addCurrentAssetsAndOwnerEqAccountSettings));
	}
}
