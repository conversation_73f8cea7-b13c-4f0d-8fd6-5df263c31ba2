﻿using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Mask;
using EasyStock.HR.BLL;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Windows.Forms;

namespace EasyStock.HR.Views.Work
{
    public partial class WorkReturnView : MasterView
    {
        public static WorkReturnView Instance
        {
            get
            {
                bool flag = WorkReturnView.instance == null || WorkReturnView.instance.IsDisposed;
                if (flag)
                {
                    WorkReturnView.instance = new WorkReturnView();
                }
                return WorkReturnView.instance;
            }
        }

        public WorkLeaveReturn WorkReturn
        {
            get
            {
                return this.WorkbindingSource.Current as WorkLeaveReturn;
            }
            set
            {
                this.WorkbindingSource.DataSource = value;
            }
        }

        public WorkReturnView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            base.Shown += this.WorkReturnView_Shown;
            this.Text = "Enregistrement du formulaire de retour au travail";
            this.BindingLookupControls();
            this.DateDateEdit.Properties.Mask.MaskType = MaskType.DateTime;
            this.DateDateEdit.Properties.Mask.EditMask = "dd/MM/yyyy";
            this.DateDateEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.btn_Refresh.Visibility = BarItemVisibility.Always;
        }

        private void WorkReturnView_Shown(object sender, EventArgs e)
        {
            bool flag = this.WorkReturn == null;
            if (flag)
            {
                this.New();
            }
        }

        public void GoTo(int id)
        {
            WorkLeaveReturn _obj = WorkLeaveReturnBLL.Get(id, WorkLeaveReturnType.WorkReturn);
            bool flag = _obj != null;
            if (flag)
            {
                this.WorkReturn = _obj;
                this.EmployeeIdLookUpEdit.ReadOnly = true;
                this.BindEmpSource(false);
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("L'enregistrement est introuvable. ");
            }
        }

        public override void New()
        {
            this.WorkReturn = new WorkLeaveReturn
            {
                Type = WorkLeaveReturnType.WorkReturn,
                Date = DateTime.Now
            };
            this.EmployeeIdLookUpEdit.ReadOnly = false;
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                int res = WorkLeaveReturnBLL.AddOrUpdate(this.WorkReturn);
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.New();
            base.RefreshData();
            this.BindEmpSource(true);
        }

        public override void Delete()
        {
            bool flag = this.WorkReturn == null || this.WorkReturn.ID == 0;
            if (!flag)
            {
                int res = WorkLeaveReturnBLL.Delete(this.WorkReturn.ID);
                bool flag2 = res > 0;
                if (flag2)
                {
                    base.Delete();
                    bool flag3 = XtraMessageBox.Show("Voulez-vous que l'employé quitte le travail ?", this.Text, MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation) == DialogResult.Yes;
                    if (flag3)
                    {
                        res = EmployeeBLL.UpdateEmpState(this.WorkReturn.EmployeeId, EmpState.leftWork);
                    }
                }
            }
        }

        public void BindingLookupControls()
        {
            this.BindEmpSource(true);
            this.EmployeeIdLookUpEdit.Properties.DisplayMember = "Name";
            this.EmployeeIdLookUpEdit.Properties.ValueMember = "ID";
            this.EmployeeIdLookUpEdit.Properties.NullText = "";
            this.EmployeeIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("ID", "Code"));
            this.EmployeeIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "L'employé"));
        }

        public void BindEmpSource(bool flag)
        {
            if (flag)
            {
                this.EmployeeIdLookUpEdit.Properties.DataSource = EmployeeBLL.GetEmpByStatus(EmpState.leftWork);
            }
            else
            {
                this.EmployeeIdLookUpEdit.Properties.DataSource = EmployeeBLL.GetEmpByStatus(EmpState.stillWorking);
            }
        }

        private static WorkReturnView instance;
    }
}
