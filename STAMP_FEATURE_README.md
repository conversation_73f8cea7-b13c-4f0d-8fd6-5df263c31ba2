# إضافة ميزة الطابع الجبائي (Timbre Fiscal)

## نظرة عامة
تم إضافة ميزة الطابع الجبائي إلى نظام EasyStock لدعم القانون الجزائري للطابع الجبائي على المدفوعات النقدية.

## التغييرات المضافة

### 1. أنواع الدفع الجديدة
- **Espèces sans timbre** (الدفع النقدي بدون طابع)
- **Espèces** (الدفع النقدي مع طابع جبائي)

### 2. حساب الطابع الجبائي حسب القانون الجزائري
- **≤ 300 دج**: لا يوجد طابع جبائي
- **300.01 - 30,000 دج**: 1 دج
- **30,000.01 - 100,000 دج**: 1.5 دج لكل 100 دج أو جزء منها
- **> 100,000 دج**: حسا<PERSON> متدرج

### 3. الملفات المضافة/المعدلة

#### الملفات الجديدة:
- `Models/StampCalculationMode.cs` - أنماط حساب الطابع
- `Classes/StampCalculator.cs` - منطق حساب الطابع الجبائي
- `Classes/StampCalculatorTest.cs` - اختبارات الحساب
- `Migrations/AddStampAmountToPayDetails.cs` - إضافة عمود الطابع
- `Migrations/AddStampSettingsToSystemSettings.cs` - إضافة إعدادات الطابع

#### الملفات المعدلة:
- `Models/PayMethodType.cs` - إضافة نوع الدفع مع الطابع
- `Models/PayDetail.cs` - إضافة خصائص الطابع الجبائي
- `Models/SystemSettings.cs` - إضافة إعدادات الطابع
- `Classes/AccountHelper.cs` - معالجة القيود المحاسبية للطابع
- `MethodOfPayment.cs` - تحديث أنواع الدفع

### 4. الإعدادات الجديدة في النظام
- **نمط حساب الطابع**: قانون جزائري أو مبلغ ثابت
- **مبلغ الطابع الثابت**: للاستخدام مع النمط الثابت
- **حساب الطابع الجبائي**: الحساب المحاسبي للطابع

### 5. المعالجة المحاسبية
عند الدفع نقداً مع طابع جبائي، يتم إنشاء القيود التالية:
- **مدين**: حساب الطابع الجبائي (بقيمة الطابع)
- **دائن**: حساب الخزينة (بقيمة الطابع)

## كيفية الاستخدام

### 1. إعداد النظام
1. اذهب إلى **إعدادات النظام** → **الضرائب**
2. اختر **نمط حساب الطابع الجبائي**:
   - **القانون الجزائري**: حساب تلقائي حسب المبلغ
   - **مبلغ ثابت**: مبلغ ثابت لكل عملية
3. حدد **حساب الطابع الجبائي** في قسم الحسابات

### 2. استخدام الطابع في الفواتير
1. عند إدخال طريقة الدفع، اختر:
   - **Espèces sans timbre**: للدفع النقدي بدون طابع
   - **Espèces**: للدفع النقدي مع طابع جبائي
2. سيتم حساب قيمة الطابع تلقائياً حسب المبلغ والإعدادات

### 3. التقارير والمحاسبة
- يظهر الطابع الجبائي كبند منفصل في التقارير
- يتم إنشاء قيود محاسبية تلقائية للطابع
- يمكن تتبع إجمالي الطوابع المدفوعة

## أمثلة على الحساب

### حسب القانون الجزائري:
- **200 دج**: 0 دج طابع
- **500 دج**: 1 دج طابع
- **30,000 دج**: 1 دج طابع
- **50,000 دج**: 301 دج طابع (1 + 200×1.5)
- **100,000 دج**: 1051 دج طابع (1 + 700×1.5)

## ملاحظات مهمة
1. الطابع الجبائي يطبق فقط على الدفع النقدي
2. يتم حساب الطابع على المبلغ المدفوع وليس على إجمالي الفاتورة
3. يمكن تخصيص حساب محاسبي منفصل للطابع الجبائي
4. النظام يدعم كلاً من الحساب التلقائي والمبلغ الثابت

## الاختبار
يمكن استخدام `StampCalculatorTest.TestStampCalculation()` لاختبار حساب الطابع الجبائي لمبالغ مختلفة.