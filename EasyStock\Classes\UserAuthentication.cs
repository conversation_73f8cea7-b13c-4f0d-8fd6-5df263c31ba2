﻿using DevExpress.XtraEditors;
using EasyStock.Common;
using EasyStock.Models;
using System;
using System.Linq;
using System.Windows.Forms;

namespace EasyStock.Classes
{

    public static class UserAuthentication
    {
        public static bool CheckAction(this UserAccessProfileDetail profile, WindowActions actions)
        {
            bool flag = true;
            if (profile == null)
            {
                return true;
            }
            flag = actions switch
            {
                WindowActions.Add => profile.CanAdd,
                WindowActions.Edit => profile.CanEdit,
                WindowActions.Delete => profile.CanDelete,
                WindowActions.Print => profile.CanPrint,
                WindowActions.Show => profile.CanEdit,
                WindowActions.Open => profile.CanOpen,
                _ => throw new NotImplementedException(),
            };
            if (!flag)
            {
                XtraMessageBox.Show("Vous n'avez pas les autorisations nécessaires", "Gestion des autorisations", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
            }
            return flag;
        }

        public static UserAccessProfileDetail GetWindowProfile(this UserAccessProfile profile, Form form)
        {
            object tag = form.Tag;
            NavigationObject NavigationObject;
            if (tag is int)
            {
                int id = (int)tag;
                if (true)
                {
                    NavigationObject = NavigationObjects.AllObjects.FirstOrDefault((NavigationObject x) => x.ID == id);
                    goto IL_0094;
                }
            }
            NavigationObject = NavigationObjects.AllObjects.Where((NavigationObject x) => x.Form != null).FirstOrDefault((NavigationObject x) => x.Form.Name == form.Name);
            goto IL_0094;
        IL_0094:
            UserAccessProfileDetail ProfileDetail = null;
            if (NavigationObject != null)
            {
                ProfileDetail = profile.Details.Where((UserAccessProfileDetail x) => x.ObjectId == NavigationObject.ID).FirstOrDefault();
            }
            return ProfileDetail;
        }

        public static UserAccessProfileDetail GetWindowProfile(this UserAccessProfile profile, string formName)
        {
            NavigationObject NavigationObject = NavigationObjects.AllObjects.Where((NavigationObject x) => x.Form != null).FirstOrDefault((NavigationObject x) => x.Form.Name == formName);
            UserAccessProfileDetail ProfileDetail = null;
            if (NavigationObject != null)
            {
                ProfileDetail = profile.Details.Where((UserAccessProfileDetail x) => x.ObjectId == NavigationObject.ID).FirstOrDefault();
            }
            return ProfileDetail;
        }

        public static bool CanOpenWindow(this UserAccessProfile profile, Form form)
        {
            object tag = form.Tag;
            NavigationObject NavigationObject;
            if (tag is int)
            {
                int id = (int)tag;
                if (true)
                {
                    NavigationObject = NavigationObjects.AllObjects.FirstOrDefault((NavigationObject x) => x.ID == id);
                    goto IL_0094;
                }
            }
            NavigationObject = NavigationObjects.AllObjects.Where((NavigationObject x) => x.Form != null).FirstOrDefault((NavigationObject x) => x.Form.Name == form.Name);
            goto IL_0094;
        IL_0094:
            UserAccessProfileDetail ProfileDetail = null;
            if (NavigationObject != null)
            {
                ProfileDetail = profile.Details.Where((UserAccessProfileDetail x) => x.ObjectId == NavigationObject.ID).FirstOrDefault();
            }
            return ProfileDetail?.CanOpen ?? true;
        }
    }
}
