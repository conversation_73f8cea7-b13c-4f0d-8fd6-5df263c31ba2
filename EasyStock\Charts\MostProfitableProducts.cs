﻿using DevExpress.XtraCharts;
using EasyStock.Controller;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;

namespace EasyStock.Charts
{

    internal class MostProfitableProducts : BaseChart
    {
        public override string Caption => "Les articles les plus rentables ce mois-ci";

        public override Color Color => ColorTranslator.FromHtml("#424242");

        public MostProfitableProducts()
        {
            Series MostSellingProductSeries = new Series("", ViewType.Doughnut);
            MostSellingProductSeries.ArgumentDataMember = "ProductName";
            MostSellingProductSeries.ValueScaleType = ScaleType.Numerical;
            MostSellingProductSeries.ValueDataMembersSerializable = "Value";
            MostSellingProductSeries.Label.TextPattern = "{V}";
            MostSellingProductSeries.LegendTextPattern = "{A}: {VP:P0}";
            MostSellingProductSeries.TopNOptions.ShowOthers = true;
            MostSellingProductSeries.TopNOptions.Enabled = true;
            MostSellingProductSeries.TopNOptions.Count = 10;
            ((DoughnutSeriesLabel)MostSellingProductSeries.Label).Position = PieSeriesLabelPosition.TwoColumns;
            ((DoughnutSeriesLabel)MostSellingProductSeries.Label).ResolveOverlappingMode = ResolveOverlappingMode.Default;
            ((DoughnutSeriesLabel)MostSellingProductSeries.Label).ResolveOverlappingMinIndent = 5;
            MostSellingProductSeries.ShowInLegend = true;
            base.AnimationStartMode = ChartAnimationMode.OnDataChanged;
            base.Series.Clear();
            base.Series.Add(MostSellingProductSeries);
            base.Height = 400;
        }

        public override async Task<object> QueryData()
        {
            ERPDataContext db = new ERPDataContext();
            object dataSource = null;
            await Task.Run(delegate
            {
                dataSource = (from x in db.ProductTransactions
                              where (int)x.TransactionState == 2
                              select x into log
                              from item in from x in db.Products
                                           where x.ID == log.ProductID
                                           select x
                              where (int)log.Type == 4
                              select new
                              {
                                  Name = item.Name,
                                  Profit = log.Price * log.Quantity - log.CostValue
                              } into x
                              group x by x.Name into x
                              select new
                              {
                                  ProductName = x.Key,
                                  Value = x.Sum(c => c.Profit)
                              } into x
                              orderby x.Value descending
                              select x).Take(10).ToList();
            });
            return dataSource;
        }
    }
}
