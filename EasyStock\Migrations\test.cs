﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x020004C7 RID: 1223
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class test : DbMigration, IMigrationMetadata
	{
		// Token: 0x06002464 RID: 9316 RVA: 0x00006A8E File Offset: 0x00004C8E
		public override void Up()
		{
		}

		// Token: 0x06002465 RID: 9317 RVA: 0x00006A8E File Offset: 0x00004C8E
		public override void Down()
		{
		}

		// Token: 0x17000BDC RID: 3036
		// (get) Token: 0x06002466 RID: 9318 RVA: 0x001FC7AC File Offset: 0x001FA9AC
		string IMigrationMetadata.Id
		{
			get
			{
				return "202101061658171_test";
			}
		}

		// Token: 0x17000BDD RID: 3037
		// (get) Token: 0x06002467 RID: 9319 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000BDE RID: 3038
		// (get) Token: 0x06002468 RID: 9320 RVA: 0x001FC7C4 File Offset: 0x001FA9C4
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002BAC RID: 11180
		private readonly ResourceManager Resources = new ResourceManager(typeof(test));
	}
}
