﻿using DevExpress.Utils;
using DevExpress.XtraCharts;
using EasyStock.Classes;
using EasyStock.Controller;
using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;

namespace EasyStock.Controls
{
    public class DashWidgets
    {
        public DashWidgets()
        {
            this.RefreshData();
            this.IntSeries();
        }
        public ChartControl MostSellingProduct { get; set; }
        public ChartControl MostPruchesedProduct { get; set; }
        public ChartControl MostValueCustomer { get; set; }

        public ChartControl SalesByProductCategory { get; set; }

        public ChartControl TopRevenue { get; set; }
        public ChartControl TopExpences { get; set; }
        public ChartControl RevExpOverTime { get; set; }

        public async Task LoadMostSellingProduct()
        {
            ERPDataContext db = new ERPDataContext();
            object MostSellingProductSeriesDataSource = null;
            await Task.Run(delegate
            {
                MostSellingProductSeriesDataSource = (from x in (from x in db.ProductTransactions
                                                                 where (int)x.TransactionState == 2
                                                                 select x into log
                                                                 from item in from x in db.Products
                                                                              where x.ID == log.ProductID
                                                                              select x
                                                                 where (int)log.Type == 4
                                                                 select new { log.Quantity, item.Name, log.Price } into x
                                                                 group x by x.Name into x
                                                                 select new
                                                                 {
                                                                     Product = x.Key,
                                                                     Sum = x.Sum(c => c.Quantity)
                                                                 }).ToList()
                                                      orderby x.Sum descending
                                                      select x).Take(25).ToList();
            });
            MostSellingProductSeries.DataSource = MostSellingProductSeriesDataSource;
        }

        public async Task LoadMostPruchesedProduct()
        {
            ERPDataContext db = new ERPDataContext();
            object dataSource = null;
            await Task.Run(delegate
            {
                dataSource = (from x in db.ProductTransactions
                              where (int)x.TransactionState == 2
                              select x into log
                              from item in from x in db.Products
                                           where x.ID == log.ProductID
                                           select x
                              where (int)log.Type == 2
                              select new { log.Quantity, item.Name } into x
                              group x by x.Name into x
                              select new
                              {
                                  Product = x.Key,
                                  Sum = x.Sum(c => c.Quantity)
                              }).ToList();
            });
            MostPruchesedProductSeries.DataSource = dataSource;
        }

        public async Task LoadSalesSeries()
        {
            ERPDataContext db = new ERPDataContext();
            object dataSource = null;
            await Task.Run(delegate
            {
                dataSource = (from i in db.SalesInvoices
                              join c in db.Customers on i.CustomerID equals c.ID
                              select new
                              {
                                  ID = i.ID,
                                  Customer = c.Name,
                                  Date = DbFunctions.TruncateTime(i.Date),
                                  Net = i.Total + i.Tax + i.OtherExpenses - i.Discount,
                                  Paid = (db.PayDetails.Where((PayDetail d) => d.SourceID == i.ID && (int)d.SourceType == 4).Sum((Expression<Func<PayDetail, double?>>)((PayDetail x) => x.Amount * x.CurrancyRate)) ?? 0.0),
                                  Remaining = i.Total + i.Tax + i.OtherExpenses - i.Discount - (db.PayDetails.Where((PayDetail d) => d.SourceID == i.ID && (int)d.SourceType == 4).Sum((Expression<Func<PayDetail, double?>>)((PayDetail x) => x.Amount * x.CurrancyRate)) ?? 0.0)
                              } into x
                              group x by x.Date into x
                              select new
                              {
                                  Date = x.Key,
                                  Sum = x.Sum(c => c.Net)
                              }).ToList();
            });
            SalesSeries.ArgumentScaleType = ScaleType.DateTime;
            SalesSeries.ArgumentDataMember = "Date";
            SalesSeries.ValueScaleType = ScaleType.Numerical;
            SalesSeries.ValueDataMembers.AddRange("Sum");
            SalesPreformance.AnimationStartMode = ChartAnimationMode.OnDataChanged;
            XYDiagram diagram = (XYDiagram)SalesPreformance.Diagram;
            diagram.AxisX.VisualRange.Auto = true;
            diagram.AxisY.VisualRange.Auto = true;
            diagram.AxisX.VisualRange.SetMinMaxValues(DateTime.Now.AddDays(-365.0), DateTime.Now);
            SalesSeries.BindToData(dataSource, "Date", "Sum");
        }

        public async Task LoadMostValueCustomer()
        {
            ERPDataContext db = new ERPDataContext();
            object dataSource = null;
            await Task.Run(delegate
            {
                dataSource = (from i in db.SalesInvoices
                              from c in from x in db.Customers
                                        where x.ID == i.CustomerID
                                        select x
                              select new
                              {
                                  Customer = c.Name,
                                  Net = i.Total + i.Tax + i.OtherExpenses - i.Discount
                              } into x
                              group x by x.Customer into x
                              select new
                              {
                                  Customer = x.Key,
                                  Sum = x.Sum(s => s.Net)
                              }).ToList();
            });
            MostValueCustomerSeries.DataSource = dataSource;
        }

        public async Task LoadSalesByProductCategory()
        {
            ERPDataContext db = new ERPDataContext();
            object dataSource = null;
            await Task.Run(delegate
            {
                dataSource = (from x in db.ProductTransactions
                              where (int)x.TransactionState == 2
                              select x into log
                              from item in from x in db.Products
                                           where x.ID == log.ProductID
                                           select x
                              from g in from x in db.ProductCategories
                                        where (int?)x.ID == item.CategoryID
                                        select x
                              where (int)log.Type == 4
                              select new { log.Quantity, g.Name, log.Price } into x
                              group x by x.Name into x
                              select new
                              {
                                  Categroy = x.Key,
                                  Sum = x.Sum(c => c.Quantity * c.Price)
                              }).ToList();
            });
            SalesByProductCategorySeries.DataSource = dataSource;
        }

        public async Task LoadTopRevenue()
        {
            ERPDataContext db = new ERPDataContext();
            object dataSource = null;
            await Task.Run(delegate
            {
                dataSource = (from x in db.JournalDetails.Include((JournalDetail x) => x.Account)
                              where x.Account.Number.IndexOf(db.Accounts.FirstOrDefault((Account a) => a.ID == CurrentSession.SystemSettings.RevenueAccount).Number) == 0
                              select x into ad
                              from acc in from x in db.Accounts
                                          where x.ID == ad.AccountID
                                          select x
                              select new { acc.Name, ad.Debit } into x
                              group x by x.Name into x
                              select new
                              {
                                  Revenue = x.Key,
                                  Sum = x.Sum(c => c.Debit)
                              }).ToList();
            });
            TopRevenueSeries.DataSource = dataSource;
        }

        public async Task LoadTopExpences()
        {
            ERPDataContext db = new ERPDataContext();
            object dataSource = null;
            await Task.Run(delegate
            {
                dataSource = (from x in db.JournalDetails.Include((JournalDetail x) => x.Account)
                              where x.Account.Number.IndexOf(db.Accounts.FirstOrDefault((Account a) => a.ID == CurrentSession.SystemSettings.ExpensesAccount).Number) == 0
                              select x into ad
                              from acc in from x in db.Accounts
                                          where x.ID == ad.AccountID
                                          select x
                              select new { acc.Name, ad.Credit } into x
                              group x by x.Name into x
                              select new
                              {
                                  Expence = x.Key,
                                  Sum = x.Sum(c => c.Credit)
                              }).ToList();
            });
            TopExpencesSeries.DataSource = dataSource;
        }

        public async Task LoadRevExpOverTime()
        {
            ERPDataContext db = new ERPDataContext();
            List<JournalOverTime> dataSource = null;
            await Task.Run(delegate
            {
                List<JournalOverTime> list = (from l in db.Journals
                                              join d in db.JournalDetails.Include((JournalDetail x) => x.Account) on l.ID equals d.JournalID
                                              where d.Account.Number.IndexOf(db.Accounts.FirstOrDefault((Account a) => a.ID == CurrentSession.SystemSettings.ExpensesAccount).Number) == 0 || d.Account.Number.IndexOf(db.Accounts.FirstOrDefault((Account a) => a.ID == CurrentSession.SystemSettings.ExpensesAccount).Number) == 0
                                              select new JournalOverTime
                                              {
                                                  Date = DbFunctions.TruncateTime(l.Date),
                                                  Debit = d.Debit,
                                                  Credit = d.Credit
                                              }).ToList();
                Thread.Sleep(10000);
            });
            RevOverTimeSeries.DataSource = (from x in dataSource
                                            group x by x.Date into x
                                            select new
                                            {
                                                Date = x.Key,
                                                Debit = x.Sum((JournalOverTime d) => d.Debit)
                                            }).ToList();
            ExpOverTimeSeries.DataSource = (from x in dataSource
                                            group x by x.Date into x
                                            select new
                                            {
                                                Date = x.Key,
                                                Credit = x.Sum((JournalOverTime d) => d.Credit)
                                            }).ToList();
        }
        public async void RefreshData()
        {
            await LoadMostSellingProduct();
            await LoadMostPruchesedProduct();
            await LoadSalesSeries();
            await LoadMostValueCustomer();
            await LoadSalesByProductCategory();
            await LoadTopRevenue();
            await LoadTopExpences();
            await LoadRevExpOverTime();
        }
        public void IntSeries()
        {
            this.MostSellingProductSeries.ArgumentDataMember = "Product";
            this.MostSellingProductSeries.ValueScaleType = ScaleType.Numerical;
            this.MostSellingProductSeries.ValueDataMembers.AddRange(new string[]
            {
                "Sum"
            });
            this.MostSellingProductSeries.Label.TextPattern = "{VP:P0}";
            this.MostSellingProductSeries.LegendTextPattern = "{A}-{VP:P0}";
            this.MostSellingProductSeries.TopNOptions.ShowOthers = true;
            this.MostSellingProductSeries.TopNOptions.Enabled = true;
            this.MostSellingProductSeries.TopNOptions.Count = 25;
            ((DoughnutSeriesLabel)this.MostSellingProductSeries.Label).Position = PieSeriesLabelPosition.TwoColumns;
            ((DoughnutSeriesLabel)this.MostSellingProductSeries.Label).ResolveOverlappingMode = ResolveOverlappingMode.Default;
            ((DoughnutSeriesLabel)this.MostSellingProductSeries.Label).ResolveOverlappingMinIndent = 5;
            this.MostSellingProductSeries.ShowInLegend = true;
            this.MostSellingProduct = new ChartControl();
            this.MostSellingProduct.AnimationStartMode = ChartAnimationMode.OnDataChanged;
            this.MostSellingProduct.Series.Clear();
            this.MostSellingProduct.Series.Add(this.MostSellingProductSeries);
            this.SalesSeries.ArgumentScaleType = ScaleType.DateTime;
            this.SalesSeries.ArgumentDataMember = "Date";
            this.SalesSeries.ValueScaleType = ScaleType.Numerical;
            this.SalesSeries.ValueDataMembers.AddRange(new string[]
            {
                "Sum"
            });
            this.SalesPreformance = new ChartControl();
            this.SalesPreformance.Series.Clear();
            this.SalesPreformance.Series.Add(this.SalesSeries);
            XYDiagram diagram = (XYDiagram)this.SalesPreformance.Diagram;
            diagram.AxisX.VisualRange.Auto = true;
            diagram.AxisY.VisualRange.Auto = true;
            diagram.AxisX.VisualRange.SetMinMaxValues(DateTime.Now.AddDays(-365.0), DateTime.Now);
            this.TopRevenueSeries.ArgumentDataMember = "Revenue";
            this.TopRevenueSeries.ValueScaleType = ScaleType.Numerical;
            this.TopRevenueSeries.ValueDataMembers.AddRange(new string[]
            {
                "Sum"
            });
            this.TopRevenueSeries.Label.TextPattern = "{VP:P0}";
            this.TopRevenueSeries.LegendTextPattern = "{A}";
            ((DoughnutSeriesLabel)this.TopRevenueSeries.Label).Position = PieSeriesLabelPosition.TwoColumns;
            ((DoughnutSeriesLabel)this.TopRevenueSeries.Label).ResolveOverlappingMode = ResolveOverlappingMode.Default;
            ((DoughnutSeriesLabel)this.TopRevenueSeries.Label).ResolveOverlappingMinIndent = 5;
            this.TopRevenueSeries.ShowInLegend = true;
            this.TopRevenue = new ChartControl();
            this.TopRevenue.AnimationStartMode = ChartAnimationMode.OnDataChanged;
            this.TopRevenue.Series.Clear();
            this.TopRevenue.Series.Add(this.TopRevenueSeries);
            this.TopExpencesSeries.ArgumentDataMember = "Expence";
            this.TopExpencesSeries.ValueScaleType = ScaleType.Numerical;
            this.TopExpencesSeries.ValueDataMembers.AddRange(new string[]
            {
                "Sum"
            });
            this.TopExpencesSeries.Label.TextPattern = "{VP:P0}";
            this.TopExpencesSeries.LegendTextPattern = "{A}";
            ((DoughnutSeriesLabel)this.TopExpencesSeries.Label).Position = PieSeriesLabelPosition.TwoColumns;
            ((DoughnutSeriesLabel)this.TopExpencesSeries.Label).ResolveOverlappingMode = ResolveOverlappingMode.Default;
            ((DoughnutSeriesLabel)this.TopExpencesSeries.Label).ResolveOverlappingMinIndent = 5;
            this.TopExpencesSeries.ShowInLegend = true;
            this.TopExpences = new ChartControl();
            this.TopExpences.AnimationStartMode = ChartAnimationMode.OnDataChanged;
            this.TopExpences.Series.Clear();
            this.TopExpences.Series.Add(this.TopExpencesSeries);
            this.MostPruchesedProductSeries.ArgumentDataMember = "Product";
            this.MostPruchesedProductSeries.ValueScaleType = ScaleType.Numerical;
            this.MostPruchesedProductSeries.ValueDataMembers.AddRange(new string[]
            {
                "Sum"
            });
            // Configure MostPruchesedProductSeries
            this.MostPruchesedProductSeries.Label.TextPattern = "{VP:P0}";
            this.MostPruchesedProductSeries.LegendTextPattern = "{A}";
            ((DoughnutSeriesLabel)this.MostPruchesedProductSeries.Label).Position = PieSeriesLabelPosition.TwoColumns;
            ((DoughnutSeriesLabel)this.MostPruchesedProductSeries.Label).ResolveOverlappingMode = ResolveOverlappingMode.Default;
            ((DoughnutSeriesLabel)this.MostPruchesedProductSeries.Label).ResolveOverlappingMinIndent = 5;
            this.MostPruchesedProductSeries.ShowInLegend = true;
            this.MostPruchesedProduct = new ChartControl();
            this.MostPruchesedProduct.AnimationStartMode = ChartAnimationMode.OnDataChanged;
            this.MostPruchesedProduct.Series.Clear();
            this.MostPruchesedProduct.Series.Add(this.MostPruchesedProductSeries);

            // Configure MostValueCustomerSeries
            this.MostValueCustomerSeries.ArgumentDataMember = "Customer";
            this.MostValueCustomerSeries.Label.TextPattern = "{A}:{V}";
            this.MostValueCustomerSeries.ValueScaleType = ScaleType.Numerical;
            this.MostValueCustomerSeries.ValueDataMembers.AddRange(new string[] { "Sum" });
            this.MostValueCustomerSeries.ShowInLegend = true;
            ((PieSeriesView)this.MostValueCustomerSeries.View).Colorizer = new RangeColorizer(); // Updated line

            this.MostValueCustomer = new ChartControl();
            this.MostValueCustomer.AnimationStartMode = ChartAnimationMode.OnDataChanged;
            this.MostValueCustomer.Series.Clear();
            this.MostValueCustomer.Series.Add(this.MostValueCustomerSeries);

            // Configure RevOverTimeSeries and ExpOverTimeSeries
            this.RevOverTimeSeries.ArgumentDataMember = (this.ExpOverTimeSeries.ArgumentDataMember = "Date");
            this.RevOverTimeSeries.Name = LangResource.Revenues;
            this.ExpOverTimeSeries.Name = LangResource.Expenses;
            this.RevOverTimeSeries.ValueDataMembers.AddRange(new string[]
            {
                "Debit"
            });
            this.ExpOverTimeSeries.ValueDataMembers.AddRange(new string[]
            {
                "Credit"
            });
            this.RevExpOverTime = new ChartControl();
            this.RevExpOverTime.AnimationStartMode = ChartAnimationMode.OnDataChanged;
            this.RevExpOverTime.Series.Clear();
            this.RevExpOverTime.Series.Add(this.ExpOverTimeSeries);
            this.RevExpOverTime.Series.Add(this.RevOverTimeSeries);
            ((XYDiagram)this.RevExpOverTime.Diagram).AxisY.Visibility = DefaultBoolean.True;
            ((XYDiagram)this.RevExpOverTime.Diagram).AxisX.Visibility = DefaultBoolean.True;
            this.RevExpOverTime.Legend.Visibility = DefaultBoolean.True;
            this.SalesByProductCategorySeries.ArgumentDataMember = "Categroy";
            this.SalesByProductCategorySeries.ValueScaleType = ScaleType.Numerical;
            this.SalesByProductCategorySeries.ValueDataMembers.AddRange(new string[]
            {
                "Sum"
            });
            this.SalesByProductCategory = new ChartControl();
            this.SalesByProductCategory.Series.Clear();
            this.SalesByProductCategory.Series.Add(this.SalesByProductCategorySeries);
            ((XYDiagram)this.SalesByProductCategory.Diagram).AxisY.Visibility = DefaultBoolean.True;
            ((XYDiagram)this.SalesByProductCategory.Diagram).AxisX.Visibility = DefaultBoolean.True;
            this.SalesByProductCategory.Legend.Visibility = DefaultBoolean.True;
            this.SalesByProductCategory.AnimationStartMode = ChartAnimationMode.OnDataChanged;
        }

        // Token: 0x04002CBF RID: 11455
        public ChartControl SalesPreformance = new ChartControl();

        // Token: 0x04002CC5 RID: 11461
        private Series MostSellingProductSeries = new Series("", ViewType.Doughnut);

        // Token: 0x04002CC6 RID: 11462
        private Series MostPruchesedProductSeries = new Series("", ViewType.Doughnut);

        private Series SalesSeries = new Series("", ViewType.SplineArea);

        private Series MostValueCustomerSeries = new Series("", ViewType.Bar);

        private Series SalesByProductCategorySeries = new Series("", ViewType.Bar);

        private Series TopExpencesSeries = new Series("", ViewType.Doughnut);

        private Series TopRevenueSeries = new Series("", ViewType.Doughnut);

        private Series RevOverTimeSeries = new Series("", ViewType.Bar);

        private Series ExpOverTimeSeries = new Series("", ViewType.Bar);

        private class JournalOverTime
        {
            public DateTime? Date { get; set; }

            public double Debit { get; set; }
            public double Credit { get; set; }
        }
    }
}
