﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    [DisplayColumn("Prêts")]
    public class EmpLoan : BaseNotifyPropertyChangedModel
    {
        private int id;

        private int branch;

        private DateTime date;

        private int accountNo;


        private int empid;

        private double balance;

        private double amount;

        private int instalmentNo;

        private string notes;

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return id;
            }
            set
            {
                SetProperty(ref id, value, "ID");
            }
        }

        [Display(Name = "Succursale")]
        public int BranchID
        {
            get
            {
                return branch;
            }
            set
            {
                SetProperty(ref branch, value, "BranchID");
            }
        }

        [Display(Name = "Date")]
        public DateTime Date
        {
            get
            {
                return date;
            }
            set
            {
                SetProperty(ref date, value, "Date");
            }
        }

        [Display(Name = "Numéro de compte")]
        public int AccountNo
        {
            get
            {
                return accountNo;
            }
            set
            {
                SetProperty(ref accountNo, value, "AccountNo");
            }
        }

        [Display(Name = "Employé")]
        [Range(1, int.MaxValue, ErrorMessage = "*")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int EmpID
        {
            get
            {
                return empid;
            }
            set
            {
                SetProperty(ref empid, value, "EmpID");
            }
        }

        [Display(Name = "Solde actuel")]
        public double Balance
        {
            get
            {
                return balance;
            }
            set
            {
                SetProperty(ref balance, value, "Balance");
            }
        }

        [Display(Name = "Montant total")]
        public double TotalAmount
        {
            get
            {
                return amount;
            }
            set
            {
                SetProperty(ref amount, value, "TotalAmount");
            }
        }

        [Display(Name = "Nombre de versements")]
        public int InstalmentNo
        {
            get
            {
                return instalmentNo;
            }
            set
            {
                SetProperty(ref instalmentNo, value, "InstalmentNo");
            }
        }

        [Display(Name = "Notes")]
        public string Notes
        {
            get
            {
                return notes;
            }
            set
            {
                SetProperty(ref notes, value, "Notes");
            }
        }

        public BindingList<EmpLoanDetails> EmpLoanDetails { get; set; }
    }
}
