﻿using DevExpress.Utils.Extensions;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraGrid.Views.Grid;
using EasyStock.Common;
using EasyStock.HR.BLL;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.Entity;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class EmpLoanView : MasterView
    {
        public static EmpLoanView Instance
        {
            get
            {
                bool flag = EmpLoanView.instance == null || EmpLoanView.instance.IsDisposed;
                if (flag)
                {
                    EmpLoanView.instance = new EmpLoanView();
                }
                return EmpLoanView.instance;
            }
        }

        public EmpLoan loan
        {
            get
            {
                return this.empLoanBindingSource.Current as EmpLoan;
            }
            set
            {
                this.empLoanBindingSource.DataSource = value;
            }
        }

        private BindingList<EmpLoanDetails> LoanDetails
        {
            get
            {
                return this.gridControl1.DataSource as BindingList<EmpLoanDetails>;
            }
            set
            {
                this.gridControl1.DataSource = value;
            }
        }

        public EmpLoanView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            this.context = new HRDataContext();
            base.Shown += this.EmpLoanView_Shown;
            this.inital();
        }

        private void EmpIDTextEdit_EditValueChanged(object sender, EventArgs e)
        {
            this.EpmReg();
        }

        private void EmpLoanView_Shown(object sender, EventArgs e)
        {
            bool flag = this.loan == null;
            if (flag)
            {
                this.New();
            }
        }

        private void EpmReg()
        {
            Employee emp = EmployeeBLL.Get(this.loan.EmpID);
            bool flag = emp != null;
            if (flag)
            {
            }
        }

        public override void New()
        {
            this.loan = new EmpLoan
            {
                Date = this.context.GetServerTime()
            };
            this.Details = new HRDataContext();
            (from x in this.Details.EmpLoanDetails
             where x.LoanID == this.loan.ID
             select x).Load();
            this.gridControl1.DataSource = this.Details.EmpLoanDetails.Local.ToBindingList<EmpLoanDetails>();
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.EpmReg();
                this.CreateInstallment();
                (this.gridControl1.DataSource as BindingList<EmpLoanDetails>).ForEach(delegate (EmpLoanDetails d)
                {
                    d.LoanID = this.loan.ID;
                });
                this.context.EmpLoans.AddOrUpdate(new EmpLoan[]
                {
                    this.loan
                });
                this.context.EmpLoanDetails.AddOrUpdate(this.LoanDetails.ToArray<EmpLoanDetails>());
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        private void inital()
        {
            this.EmpIDTextEdit.Properties.DataSource = EmployeeBLL.GetActive();
            this.EmpIDTextEdit.Properties.DisplayMember = "Name";
            this.EmpIDTextEdit.Properties.ValueMember = "ID";
            this.EmpIDTextEdit.Properties.NullText = "";
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("ID", "Code"));
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Nom"));
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("PayPeriod", "Période de paiement"));
            this.AccountNoTextEdit.Properties.DataSource = ExternalBLL.Accounts;
            this.AccountNoTextEdit.Properties.DisplayMember = "AccName";
            this.AccountNoTextEdit.Properties.ValueMember = "ID";
            this.AccountNoTextEdit.Properties.NullText = "";
            this.AccountNoTextEdit.Properties.Columns.Add(new LookUpColumnInfo("AccNo", "Numéro de compte"));
            this.AccountNoTextEdit.Properties.Columns.Add(new LookUpColumnInfo("AccName", "Nom du compte"));
            this.BranchIDTextEdit.Properties.DataSource = ExternalBLL.Branches;
            this.BranchIDTextEdit.Properties.DisplayMember = "AccName";
            this.BranchIDTextEdit.Properties.ValueMember = "ID";
            this.BranchIDTextEdit.Properties.NullText = "";
            this.BranchIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("ID", "Numéro de succursale"));
            this.BranchIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("AccName", "Nom de succursale"));

        }

        public override void RefreshData()
        {
            this.EmpIDTextEdit.Properties.DataSource = EmployeeBLL.GetActive();
            this.AccountNoTextEdit.Properties.DataSource = ExternalBLL.Accounts;
            this.BranchIDTextEdit.Properties.DataSource = ExternalBLL.Branches;
            base.RefreshData();
        }

        public void GoTo(int id)
        {
            EmpLoan sourceDepr = this.context.EmpLoans.SingleOrDefault((EmpLoan x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.loan = sourceDepr;
                this.Details = new HRDataContext();
                (from x in this.Details.EmpLoanDetails
                 where x.LoanID == this.loan.ID
                 select x).Load();
                this.gridControl1.DataSource = this.Details.EmpLoanDetails.Local.ToBindingList<EmpLoanDetails>();
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable");
            }
        }

        public override void Delete()
        {
        }

        private void CreateInstallment()
        {
            using (HRDataContext db = new HRDataContext())
            {
                List<EmpLoanDetails> emps = (from x in db.EmpLoanDetails
                                             where x.LoanID == this.loan.ID
                                             select x).ToList<EmpLoanDetails>();
                db.EmpLoanDetails.RemoveRange(emps);
            }
            GridView view = this.gridView1;
            while (this.gridView1.RowCount > 0)
            {
                view.DeleteRow(this.gridView1.RowCount - 1);
            }
            for (int i = 0; i < this.loan.InstalmentNo; i++)
            {
                (this.gridControl1.DataSource as BindingList<EmpLoanDetails>).AddNew();
            }
            (this.gridControl1.DataSource as BindingList<EmpLoanDetails>).ForEach(delegate (EmpLoanDetails d)
            {
                d.LoanID = this.loan.ID;
                d.Amount = this.loan.TotalAmount / (double)this.loan.InstalmentNo;
            });
        }

        private void simpleButton1_Click_1(object sender, EventArgs e)
        {
        }

        private static EmpLoanView instance;

        private HRDataContext context;

        private HRDataContext Details = new HRDataContext();
    }
}
