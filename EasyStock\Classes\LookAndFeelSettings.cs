﻿using DevExpress.LookAndFeel;
using System;
using System.IO;
using System.Runtime.Serialization.Formatters;
using System.Runtime.Serialization.Formatters.Binary;

namespace EasyStock.Classes
{
    [Serializable]
    public class LookAndFeelSettings
    {
        private static string lookandFellPath
        {
            get
            {
                return Path.Combine(CurrentSession.DefualtSettingPath, "LookAndFeelSettings.dat");
            }
        }

        public static void Save(string fileName = null)
        {
            bool flag = fileName == null;
            if (flag)
            {
                fileName = LookAndFeelSettings.lookandFellPath;
            }
            LookAndFeelSettings settings = new LookAndFeelSettings();
            settings.SkinName = UserLookAndFeel.Default.SkinName;
            settings.Style = UserLookAndFeel.Default.Style;
            settings.UseWindowsXPTheme = UserLookAndFeel.Default.UseWindowsXPTheme;
            FileStream fileStream;
            FileStream stream = fileStream = new FileStream(fileName, FileMode.Create);
            try
            {
                new BinaryFormatter
                {
                    AssemblyFormat = FormatterAssemblyStyle.Simple
                }.Serialize(stream, settings);
            }
            finally
            {
                if (fileStream != null)
                {
                    ((IDisposable)fileStream).Dispose();
                }
            }
        }

        public static void Load(string fileName = null)
        {
            bool flag = fileName == null;
            if (flag)
            {
                fileName = LookAndFeelSettings.lookandFellPath;
            }
            bool flag2 = File.Exists(fileName);
            if (flag2)
            {
                using (FileStream stream = new FileStream(fileName, FileMode.Open))
                {
                    BinaryFormatter formatter = new BinaryFormatter();
                    formatter.AssemblyFormat = FormatterAssemblyStyle.Simple;
                    try
                    {
                        LookAndFeelSettings settings = formatter.Deserialize(stream) as LookAndFeelSettings;
                        bool flag3 = settings != null;
                        if (flag3)
                        {
                            UserLookAndFeel.Default.UseWindowsXPTheme = settings.UseWindowsXPTheme;
                            UserLookAndFeel.Default.Style = settings.Style;
                            UserLookAndFeel.Default.SkinName = settings.SkinName;
                        }
                    }
                    catch (Exception)
                    {
                    }
                }
            }
        }

        public string SkinName;

        public LookAndFeelStyle Style;

        public bool UseWindowsXPTheme;
    }
}
