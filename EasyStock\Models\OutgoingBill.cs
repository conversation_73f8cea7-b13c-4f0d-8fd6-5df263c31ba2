﻿using EasyStock.Common;
using EasyStock.Controller;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Solde d'ouverture des articles")]
    [Table("OutgoingBill")]
    public class OutgoingBill : Bill
    {
        public override void GetNewCode()
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                Type sss = base.GetType();
                int maxLength = (from x in db.OpenBalanceBills.AsNoTracking()
                                 select x.Code).Max((string x) => (int?)x.Length).GetValueOrDefault();
                bool flag = maxLength == 0;
                if (flag)
                {
                    base.Code = "1";
                }
                else
                {
                    string maxCode = (from x in db.OutgoingBills.AsNoTracking()
                                      where x.Code.Length == maxLength
                                      orderby x.ID descending
                                      select x).FirstOrDefault<OutgoingBill>().Code;
                    bool flag2 = maxCode != string.Empty;
                    if (flag2)
                    {
                        base.Code = maxCode.GetNextSequence();
                    }
                }
            }
        }

        [Display(Name = "Code source")]
        public int? SourceID
        {
            get
            {
                return this.sourceID;
            }
            set
            {
                base.SetProperty<int?>(ref this.sourceID, value, "SourceID");
            }
        }

        [Display(Name = "Type de source")]
        public BillSourceType SourceType
        {
            get
            {
                return this.sourceType;
            }
            set
            {
                base.SetProperty<BillSourceType>(ref this.sourceType, value, "SourceType");
            }
        }

        [Display(Name = "Partie impliquée")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int PersonalID
        {
            get
            {
                return this._PersonalID;
            }
            set
            {
                base.SetProperty<int>(ref this._PersonalID, value, "PersonalID");
            }
        }

        [Display(Name = "Type de partie impliquée")]
        public PersonalType PersonalType
        {
            get
            {
                return this._personalType;
            }
            set
            {
                base.SetProperty<PersonalType>(ref this._personalType, value, "PersonalType");
            }
        }

        [Display(Name = "État de la transaction")]
        public TransactionState TransactionState
        {
            get
            {
                return this.transactionState;
            }
            set
            {
                base.SetProperty<TransactionState>(ref this.transactionState, value, "TransactionState");
            }
        }


        private int? sourceID;

        private BillSourceType sourceType;

        private int _PersonalID;

        private PersonalType _personalType;

        private TransactionState transactionState = TransactionState.Posted;
    }
}
