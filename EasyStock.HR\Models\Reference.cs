﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    public class Reference : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Numéro")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Nom")]
        [StringLength(150)]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public string Name
        {
            get
            {
                return this.name;
            }
            set
            {
                base.SetProperty<string>(ref this.name, value, "Name");
            }
        }

        [Display(Name = "Type")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public ReferenceType RefType
        {
            get
            {
                return this.refType;
            }
            set
            {
                base.SetProperty<ReferenceType>(ref this.refType, value, "RefType");
            }
        }

        private int id;

        private string name;

        private ReferenceType refType;
    }
}
