<?xml version="1.0"?>
<doc>
    <assembly>
        <name>protobuf-net.Core</name>
    </assembly>
    <members>
        <member name="T:ProtoBuf.BclHelpers">
            <summary>
            Provides support for common .NET types that do not have a direct representation
            in protobuf, using the definitions from bcl.proto
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.GetUninitializedObject(System.Type)">
            <summary>
            Creates a new instance of the specified type, bypassing the constructor.
            </summary>
            <param name="type">The type to create</param>
            <returns>The new instance</returns>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteTimeSpan(System.TimeSpan,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a TimeSpan to a protobuf stream using protobuf-net's own representation, bcl.TimeSpan
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteTimeSpan(ProtoBuf.ProtoWriter.State@,System.TimeSpan)">
            <summary>
            Writes a TimeSpan to a protobuf stream using protobuf-net's own representation, bcl.TimeSpan
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadTimeSpan(ProtoBuf.ProtoReader)">
            <summary>
            Parses a TimeSpan from a protobuf stream using protobuf-net's own representation, bcl.TimeSpan
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadTimeSpan(ProtoBuf.ProtoReader.State@)">
            <summary>
            Parses a TimeSpan from a protobuf stream using protobuf-net's own representation, bcl.TimeSpan
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadDuration(ProtoBuf.ProtoReader)">
            <summary>
            Parses a TimeSpan from a protobuf stream using the standardized format, google.protobuf.Duration
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadDuration(ProtoBuf.ProtoReader.State@)">
            <summary>
            Parses a TimeSpan from a protobuf stream using the standardized format, google.protobuf.Duration
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteDuration(System.TimeSpan,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a TimeSpan to a protobuf stream using the standardized format, google.protobuf.Duration
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteDuration(ProtoBuf.ProtoWriter.State@,System.TimeSpan)">
            <summary>
            Writes a TimeSpan to a protobuf stream using the standardized format, google.protobuf.Duration
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadTimestamp(ProtoBuf.ProtoReader)">
            <summary>
            Parses a DateTime from a protobuf stream using the standardized format, google.protobuf.Timestamp
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadTimestamp(ProtoBuf.ProtoReader.State@)">
            <summary>
            Parses a DateTime from a protobuf stream using the standardized format, google.protobuf.Timestamp
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteTimestamp(System.DateTime,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a DateTime to a protobuf stream using the standardized format, google.protobuf.Timestamp
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteTimestamp(ProtoBuf.ProtoWriter.State@,System.DateTime)">
            <summary>
            Writes a DateTime to a protobuf stream using the standardized format, google.protobuf.Timestamp
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadDateTime(ProtoBuf.ProtoReader)">
            <summary>
            Parses a DateTime from a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadDateTime(ProtoBuf.ProtoReader.State@)">
            <summary>
            Parses a DateTime from a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteDateTime(System.DateTime,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a DateTime to a protobuf stream, excluding the <c>Kind</c>
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteDateTime(ProtoBuf.ProtoWriter.State@,System.DateTime)">
            <summary>
            Writes a DateTime to a protobuf stream, excluding the <c>Kind</c>
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteDateTimeWithKind(System.DateTime,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a DateTime to a protobuf stream, including the <c>Kind</c>
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteDateTimeWithKind(ProtoBuf.ProtoWriter.State@,System.DateTime)">
            <summary>
            Writes a DateTime to a protobuf stream, including the <c>Kind</c>
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadDecimal(ProtoBuf.ProtoReader)">
            <summary>
            Parses a decimal from a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadDecimal(ProtoBuf.ProtoReader.State@)">
            <summary>
            Parses a decimal from a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadDecimalString(ProtoBuf.ProtoReader.State@)">
            <summary>
            Parses a decimal from a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteDecimal(System.Decimal,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a decimal to a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteDecimal(ProtoBuf.ProtoWriter.State@,System.Decimal)">
            <summary>
            Writes a decimal to a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteDecimalString(ProtoBuf.ProtoWriter.State@,System.Decimal)">
            <summary>
            Writes a decimal to a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteGuid(System.Guid,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a Guid to a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteGuid(ProtoBuf.ProtoWriter.State@,System.Guid)">
            <summary>
            Writes a Guid to a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteGuidBytes(ProtoBuf.ProtoWriter.State@,System.Guid)">
            <summary>
            Writes a Guid to a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteGuidString(ProtoBuf.ProtoWriter.State@,System.Guid)">
            <summary>
            Writes a Guid to a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadGuid(ProtoBuf.ProtoReader)">
            <summary>
            Parses a Guid from a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadGuid(ProtoBuf.ProtoReader.State@)">
            <summary>
            Parses a Guid from a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadGuidBytes(ProtoBuf.ProtoReader.State@)">
            <summary>
            Parses a Guid from a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadGuidString(ProtoBuf.ProtoReader.State@)">
            <summary>
            Parses a Guid from a protobuf stream
            </summary>
        </member>
        <member name="T:ProtoBuf.BufferExtension">
            <summary>
            Provides a simple buffer-based implementation of an <see cref="T:ProtoBuf.IExtension">extension</see> object.
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoBeforeSerializationAttribute">
            <summary>Specifies a method on the root-contract in an hierarchy to be invoked before serialization.</summary>
        </member>
        <member name="T:ProtoBuf.ProtoAfterSerializationAttribute">
            <summary>Specifies a method on the root-contract in an hierarchy to be invoked after serialization.</summary>
        </member>
        <member name="T:ProtoBuf.ProtoBeforeDeserializationAttribute">
            <summary>Specifies a method on the root-contract in an hierarchy to be invoked before deserialization.</summary>
        </member>
        <member name="T:ProtoBuf.ProtoAfterDeserializationAttribute">
            <summary>Specifies a method on the root-contract in an hierarchy to be invoked after deserialization.</summary>
        </member>
        <member name="T:ProtoBuf.CompatibilityLevel">
            <summary>
            Defines the compatibility level / conventions to use when encoding common
            types into protobuf. When starting a new green-field project the highest
            available level can be safely applied, but note that changing the
            compatibility level changes the encoding. For this reason, it should not
            be casually changed on brown-field projects, unless you are also knowingly
            breaking the encoding requirements of pre-existing data. If not specified,
            the oldest (lowest number) is assumed, for safety.
            </summary>
        </member>
        <member name="F:ProtoBuf.CompatibilityLevel.NotSpecified">
            <summary>
            Functionally identical to <see cref="F:ProtoBuf.CompatibilityLevel.Level200"/>
            </summary>
        </member>
        <member name="F:ProtoBuf.CompatibilityLevel.Level200">
            <summary>
            Uses bcl.proto for <see cref="T:System.DateTime"/>, <see cref="T:System.TimeSpan"/>, <see cref="T:System.Guid"/> and <see cref="T:System.Decimal"/>, for compatibility
            with all versions of protobuf-net, at the expense of being inconvenient for use with other protobuf implementations.
            </summary>
        </member>
        <member name="F:ProtoBuf.CompatibilityLevel.Level240">
            <summary>
            Like <see cref="F:ProtoBuf.CompatibilityLevel.Level200"/>, but uses '.google.protobuf.Timestamp' for <see cref="T:System.DateTime"/> and '.google.protobuf.Duration' for <see cref="T:System.TimeSpan"/>.
            This is functionally identical to a <see cref="F:ProtoBuf.CompatibilityLevel.Level200"/> configuration that specifies <see cref="F:ProtoBuf.DataFormat.WellKnown"/>.
            </summary>
        </member>
        <member name="F:ProtoBuf.CompatibilityLevel.Level300">
            <summary>
            Like <see cref="F:ProtoBuf.CompatibilityLevel.Level240"/>, but uses 'string' for <see cref="T:System.Guid"/> (big-endian hyphenated UUID format; a shorter 'bytes' variant is also available via <see cref="F:ProtoBuf.DataFormat.FixedSize"/>)
            and <see cref="T:System.Decimal"/> (invariant "general" format).
            </summary>
        </member>
        <member name="T:ProtoBuf.CompatibilityLevelAttribute">
            <summary>
            Defines the compatibiltiy level to use for an element
            </summary>
        </member>
        <member name="P:ProtoBuf.CompatibilityLevelAttribute.Level">
            <summary>
            The compatibiltiy level to use for this element
            </summary>
        </member>
        <member name="M:ProtoBuf.CompatibilityLevelAttribute.#ctor(ProtoBuf.CompatibilityLevel)">
            <summary>
            Create a new CompatibilityLevelAttribute instance
            </summary>
        </member>
        <member name="T:ProtoBuf.DataFormat">
            <summary>
            Sub-format to use when serializing/deserializing data
            </summary>
        </member>
        <member name="F:ProtoBuf.DataFormat.Default">
            <summary>
            Uses the default encoding for the data-type.
            </summary>
        </member>
        <member name="F:ProtoBuf.DataFormat.ZigZag">
            <summary>
            When applied to signed integer-based data (including Decimal), this
            indicates that zigzag variant encoding will be used. This means that values
            with small magnitude (regardless of sign) take a small amount
            of space to encode.
            </summary>
        </member>
        <member name="F:ProtoBuf.DataFormat.TwosComplement">
            <summary>
            When applied to signed integer-based data (including Decimal), this
            indicates that two's-complement variant encoding will be used.
            This means that any -ve number will take 10 bytes (even for 32-bit),
            so should only be used for compatibility.
            </summary>
        </member>
        <member name="F:ProtoBuf.DataFormat.FixedSize">
            <summary>
            When applied to signed integer-based data (including Decimal), this
            indicates that a fixed amount of space will be used.
            </summary>
        </member>
        <member name="F:ProtoBuf.DataFormat.Group">
            <summary>
            When applied to a sub-message, indicates that the value should be treated
            as group-delimited.
            </summary>
        </member>
        <member name="F:ProtoBuf.DataFormat.WellKnown">
            <summary>
            When applied to members of types such as DateTime or TimeSpan, specifies
            that the "well known" standardized representation should be use; DateTime uses Timestamp,
            TimeSpan uses Duration.
            </summary>
        </member>
        <member name="T:ProtoBuf.DiscriminatedUnionObject">
            <summary>Represent multiple types as a union; this is used as part of OneOf -
            note that it is the caller's responsbility to only read/write the value as the same type</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnionObject.Object">
            <summary>The value typed as Object</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnionObject.Is(System.Int32)">
            <summary>Indicates whether the specified discriminator is assigned</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnionObject.#ctor(System.Int32,System.Object)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnionObject.Reset(ProtoBuf.DiscriminatedUnionObject@,System.Int32)">
            <summary>Reset a value if the specified discriminator is assigned</summary>
        </member>
        <member name="P:ProtoBuf.DiscriminatedUnionObject.Discriminator">
            <summary>The discriminator value</summary>
        </member>
        <member name="T:ProtoBuf.DiscriminatedUnion64">
            <summary>Represent multiple types as a union; this is used as part of OneOf -
            note that it is the caller's responsbility to only read/write the value as the same type</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.Int64">
            <summary>The value typed as Int64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.UInt64">
            <summary>The value typed as UInt64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.Int32">
            <summary>The value typed as Int32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.UInt32">
            <summary>The value typed as UInt32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.Boolean">
            <summary>The value typed as Boolean</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.Single">
            <summary>The value typed as Single</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.Double">
            <summary>The value typed as Double</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.DateTime">
            <summary>The value typed as DateTime</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.TimeSpan">
            <summary>The value typed as TimeSpan</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.Is(System.Int32)">
            <summary>Indicates whether the specified discriminator is assigned</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.Int64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.Int32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.UInt64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.UInt32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.Single)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.Double)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.Boolean)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.Nullable{System.DateTime})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.Nullable{System.TimeSpan})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.Reset(ProtoBuf.DiscriminatedUnion64@,System.Int32)">
            <summary>Reset a value if the specified discriminator is assigned</summary>
        </member>
        <member name="P:ProtoBuf.DiscriminatedUnion64.Discriminator">
            <summary>The discriminator value</summary>
        </member>
        <member name="T:ProtoBuf.DiscriminatedUnion128Object">
            <summary>Represent multiple types as a union; this is used as part of OneOf -
            note that it is the caller's responsbility to only read/write the value as the same type</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.Int64">
            <summary>The value typed as Int64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.UInt64">
            <summary>The value typed as UInt64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.Int32">
            <summary>The value typed as Int32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.UInt32">
            <summary>The value typed as UInt32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.Boolean">
            <summary>The value typed as Boolean</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.Single">
            <summary>The value typed as Single</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.Double">
            <summary>The value typed as Double</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.DateTime">
            <summary>The value typed as DateTime</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.TimeSpan">
            <summary>The value typed as TimeSpan</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.Guid">
            <summary>The value typed as Guid</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.Object">
            <summary>The value typed as Object</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.Is(System.Int32)">
            <summary>Indicates whether the specified discriminator is assigned</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Int64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Int32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.UInt64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.UInt32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Single)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Double)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Boolean)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Object)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Nullable{System.DateTime})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Nullable{System.TimeSpan})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Nullable{System.Guid})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.Reset(ProtoBuf.DiscriminatedUnion128Object@,System.Int32)">
            <summary>Reset a value if the specified discriminator is assigned</summary>
        </member>
        <member name="P:ProtoBuf.DiscriminatedUnion128Object.Discriminator">
            <summary>The discriminator value</summary>
        </member>
        <member name="T:ProtoBuf.DiscriminatedUnion128">
            <summary>Represent multiple types as a union; this is used as part of OneOf -
            note that it is the caller's responsbility to only read/write the value as the same type</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.Int64">
            <summary>The value typed as Int64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.UInt64">
            <summary>The value typed as UInt64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.Int32">
            <summary>The value typed as Int32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.UInt32">
            <summary>The value typed as UInt32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.Boolean">
            <summary>The value typed as Boolean</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.Single">
            <summary>The value typed as Single</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.Double">
            <summary>The value typed as Double</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.DateTime">
            <summary>The value typed as DateTime</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.TimeSpan">
            <summary>The value typed as TimeSpan</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.Guid">
            <summary>The value typed as Guid</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.Is(System.Int32)">
            <summary>Indicates whether the specified discriminator is assigned</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Int64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Int32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.UInt64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.UInt32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Single)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Double)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Boolean)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Nullable{System.DateTime})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Nullable{System.TimeSpan})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Nullable{System.Guid})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.Reset(ProtoBuf.DiscriminatedUnion128@,System.Int32)">
            <summary>Reset a value if the specified discriminator is assigned</summary>
        </member>
        <member name="P:ProtoBuf.DiscriminatedUnion128.Discriminator">
            <summary>The discriminator value</summary>
        </member>
        <member name="T:ProtoBuf.DiscriminatedUnion64Object">
            <summary>Represent multiple types as a union; this is used as part of OneOf -
            note that it is the caller's responsbility to only read/write the value as the same type</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.Int64">
            <summary>The value typed as Int64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.UInt64">
            <summary>The value typed as UInt64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.Int32">
            <summary>The value typed as Int32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.UInt32">
            <summary>The value typed as UInt32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.Boolean">
            <summary>The value typed as Boolean</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.Single">
            <summary>The value typed as Single</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.Double">
            <summary>The value typed as Double</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.DateTime">
            <summary>The value typed as DateTime</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.TimeSpan">
            <summary>The value typed as TimeSpan</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.Object">
            <summary>The value typed as Object</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.Is(System.Int32)">
            <summary>Indicates whether the specified discriminator is assigned</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Int64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Int32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.UInt64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.UInt32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Single)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Double)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Boolean)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Object)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Nullable{System.DateTime})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Nullable{System.TimeSpan})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.Reset(ProtoBuf.DiscriminatedUnion64Object@,System.Int32)">
            <summary>Reset a value if the specified discriminator is assigned</summary>
        </member>
        <member name="P:ProtoBuf.DiscriminatedUnion64Object.Discriminator">
            <summary>The discriminator value</summary>
        </member>
        <member name="T:ProtoBuf.DiscriminatedUnion32">
            <summary>Represent multiple types as a union; this is used as part of OneOf -
            note that it is the caller's responsbility to only read/write the value as the same type</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32.Int32">
            <summary>The value typed as Int32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32.UInt32">
            <summary>The value typed as UInt32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32.Boolean">
            <summary>The value typed as Boolean</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32.Single">
            <summary>The value typed as Single</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32.Is(System.Int32)">
            <summary>Indicates whether the specified discriminator is assigned</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32.#ctor(System.Int32,System.Int32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32.#ctor(System.Int32,System.UInt32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32.#ctor(System.Int32,System.Single)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32.#ctor(System.Int32,System.Boolean)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32.Reset(ProtoBuf.DiscriminatedUnion32@,System.Int32)">
            <summary>Reset a value if the specified discriminator is assigned</summary>
        </member>
        <member name="P:ProtoBuf.DiscriminatedUnion32.Discriminator">
            <summary>The discriminator value</summary>
        </member>
        <member name="T:ProtoBuf.DiscriminatedUnion32Object">
            <summary>Represent multiple types as a union; this is used as part of OneOf -
            note that it is the caller's responsbility to only read/write the value as the same type</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32Object.Int32">
            <summary>The value typed as Int32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32Object.UInt32">
            <summary>The value typed as UInt32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32Object.Boolean">
            <summary>The value typed as Boolean</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32Object.Single">
            <summary>The value typed as Single</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32Object.Object">
            <summary>The value typed as Object</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32Object.Is(System.Int32)">
            <summary>Indicates whether the specified discriminator is assigned</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32Object.#ctor(System.Int32,System.Int32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32Object.#ctor(System.Int32,System.UInt32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32Object.#ctor(System.Int32,System.Single)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32Object.#ctor(System.Int32,System.Boolean)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32Object.#ctor(System.Int32,System.Object)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32Object.Reset(ProtoBuf.DiscriminatedUnion32Object@,System.Int32)">
            <summary>Reset a value if the specified discriminator is assigned</summary>
        </member>
        <member name="P:ProtoBuf.DiscriminatedUnion32Object.Discriminator">
            <summary>The discriminator value</summary>
        </member>
        <member name="T:ProtoBuf.Extensible">
            <summary>
            Simple base class for supporting unexpected fields allowing
            for loss-less round-tips/merge, even if the data is not understod.
            The additional fields are (by default) stored in-memory in a buffer.
            </summary>
            <remarks>As an example of an alternative implementation, you might
            choose to use the file system (temporary files) as the back-end, tracking
            only the paths [such an object would ideally be IDisposable and use
            a finalizer to ensure that the files are removed].</remarks>
            <seealso cref="T:ProtoBuf.IExtensible"/>
        </member>
        <member name="M:ProtoBuf.Extensible.GetExtensionObject(System.Boolean)">
            <summary>
            Retrieves the <see cref="T:ProtoBuf.IExtension">extension</see> object for the current
            instance, optionally creating it if it does not already exist.
            </summary>
            <param name="createIfMissing">Should a new extension object be
            created if it does not already exist?</param>
            <returns>The extension object if it exists (or was created), or null
            if the extension object does not exist or is not available.</returns>
            <remarks>The <c>createIfMissing</c> argument is false during serialization,
            and true during deserialization upon encountering unexpected fields.</remarks>
        </member>
        <member name="M:ProtoBuf.Extensible.GetExtensionObject(ProtoBuf.IExtension@,System.Type,System.Boolean)">
            <summary>
            Provides a simple, default implementation for <see cref="T:ProtoBuf.IExtension">extension</see> support,
            optionally creating it if it does not already exist. Designed to be called by
            classes implementing <see cref="T:ProtoBuf.IExtensible"/>.
            </summary>
            <param name="createIfMissing">Should a new extension object be
            created if it does not already exist?</param>
            <param name="type">The <see cref="T:System.Type"/> that holds the fields, in terms of the inheritance model; the same <c>tag</c> key can appear against different <c>type</c> levels for the same <c>instance</c>, with different values.</param>
            <param name="extensionObject">The extension field to check (and possibly update).</param>
            <returns>The extension object if it exists (or was created), or null
            if the extension object does not exist or is not available.</returns>
            <remarks>The <c>createIfMissing</c> argument is false during serialization,
            and true during deserialization upon encountering unexpected fields.</remarks>
        </member>
        <member name="M:ProtoBuf.Extensible.GetExtensionObject(ProtoBuf.IExtension@,System.Boolean)">
            <summary>
            Provides a simple, default implementation for <see cref="T:ProtoBuf.IExtension">extension</see> support,
            optionally creating it if it does not already exist. Designed to be called by
            classes implementing <see cref="T:ProtoBuf.IExtensible"/>.
            </summary>
            <param name="createIfMissing">Should a new extension object be
            created if it does not already exist?</param>
            <param name="extensionObject">The extension field to check (and possibly update).</param>
            <returns>The extension object if it exists (or was created), or null
            if the extension object does not exist or is not available.</returns>
            <remarks>The <c>createIfMissing</c> argument is false during serialization,
            and true during deserialization upon encountering unexpected fields.</remarks>
        </member>
        <member name="M:ProtoBuf.Extensible.AppendValue``1(ProtoBuf.IExtensible,System.Int32,``0)">
            <summary>
            Appends the value as an additional (unexpected) data-field for the instance.
            Note that for non-repeated sub-objects, this equates to a merge operation;
            for repeated sub-objects this adds a new instance to the set; for simple
            values the new value supercedes the old value.
            </summary>
            <remarks>Note that appending a value does not remove the old value from
            the stream; avoid repeatedly appending values for the same field.</remarks>
            <typeparam name="TValue">The type of the value to append.</typeparam>
            <param name="instance">The extensible object to append the value to.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="value">The value to append.</param>
        </member>
        <member name="M:ProtoBuf.Extensible.AppendValue``1(ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat,``0)">
            <summary>
            Appends the value as an additional (unexpected) data-field for the instance.
            Note that for non-repeated sub-objects, this equates to a merge operation;
            for repeated sub-objects this adds a new instance to the set; for simple
            values the new value supercedes the old value.
            </summary>
            <remarks>Note that appending a value does not remove the old value from
            the stream; avoid repeatedly appending values for the same field.</remarks>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="format">The data-format to use when encoding the value.</param>
            <param name="instance">The extensible object to append the value to.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="value">The value to append.</param>
        </member>
        <member name="M:ProtoBuf.Extensible.AppendValue``1(ProtoBuf.Meta.TypeModel,ProtoBuf.IExtensible,System.Int32,``0,ProtoBuf.DataFormat)">
            <summary>
            Appends the value as an additional (unexpected) data-field for the instance.
            Note that for non-repeated sub-objects, this equates to a merge operation;
            for repeated sub-objects this adds a new instance to the set; for simple
            values the new value supercedes the old value.
            </summary>
            <remarks>Note that appending a value does not remove the old value from
            the stream; avoid repeatedly appending values for the same field.</remarks>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="format">The data-format to use when encoding the value.</param>
            <param name="model">The model to use for serialization.</param>
            <param name="instance">The extensible object to append the value to.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="value">The value to append.</param>
        </member>
        <member name="M:ProtoBuf.Extensible.GetValue``1(ProtoBuf.IExtensible,System.Int32)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned is the composed value after merging any duplicated content; if the
            value is "repeated" (a list), then use GetValues instead.
            </summary>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <returns>The effective value of the field, or the default value if not found.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.GetValue``1(ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned is the composed value after merging any duplicated content; if the
            value is "repeated" (a list), then use GetValues instead.
            </summary>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <returns>The effective value of the field, or the default value if not found.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.GetValue``1(ProtoBuf.Meta.TypeModel,ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned is the composed value after merging any duplicated content; if the
            value is "repeated" (a list), then use GetValues instead.
            </summary>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="model">The type model to use for deserialization.</param>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <returns>The effective value of the field, or the default value if not found.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.TryGetValue``1(ProtoBuf.IExtensible,System.Int32,``0@)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned (in "value") is the composed value after merging any duplicated content;
            if the value is "repeated" (a list), then use GetValues instead.
            </summary>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="value">The effective value of the field, or the default value if not found.</param>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <returns>True if data for the field was present, false otherwise.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.TryGetValue``1(ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat,``0@)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned (in "value") is the composed value after merging any duplicated content;
            if the value is "repeated" (a list), then use GetValues instead.
            </summary>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="value">The effective value of the field, or the default value if not found.</param>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <returns>True if data for the field was present, false otherwise.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.TryGetValue``1(ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat,System.Boolean,``0@)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned (in "value") is the composed value after merging any duplicated content;
            if the value is "repeated" (a list), then use GetValues instead.
            </summary>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="value">The effective value of the field, or the default value if not found.</param>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <param name="allowDefinedTag">Allow tags that are present as part of the definition; for example, to query unknown enum values.</param>
            <returns>True if data for the field was present, false otherwise.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.TryGetValue``1(ProtoBuf.Meta.TypeModel,ProtoBuf.IExtensible,System.Int32,``0@,ProtoBuf.DataFormat,System.Boolean)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned (in "value") is the composed value after merging any duplicated content;
            if the value is "repeated" (a list), then use GetValues instead.
            </summary>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="value">The effective value of the field, or the default value if not found.</param>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="model">The type model to use for deserialization.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <param name="allowDefinedTag">Allow tags that are present as part of the definition; for example, to query unknown enum values.</param>
            <returns>True if data for the field was present, false otherwise.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.GetValues``1(ProtoBuf.IExtensible,System.Int32)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            Each occurrence of the field is yielded separately, making this usage suitable for "repeated"
            (list) fields.
            </summary>
            <remarks>The extended data is processed lazily as the enumerator is iterated.</remarks>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <returns>An enumerator that yields each occurrence of the field.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.GetValues``1(ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            Each occurrence of the field is yielded separately, making this usage suitable for "repeated"
            (list) fields.
            </summary>
            <remarks>The extended data is processed lazily as the enumerator is iterated.</remarks>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <returns>An enumerator that yields each occurrence of the field.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.GetValues``1(ProtoBuf.Meta.TypeModel,ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            Each occurrence of the field is yielded separately, making this usage suitable for "repeated"
            (list) fields.
            </summary>
            <remarks>The extended data is processed lazily as the enumerator is iterated.</remarks>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="model">The type model to use for deserialization.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <returns>An enumerator that yields each occurrence of the field.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.TryGetValue(ProtoBuf.Meta.TypeModel,System.Type,ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat,System.Boolean,System.Object@)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned (in "value") is the composed value after merging any duplicated content;
            if the value is "repeated" (a list), then use GetValues instead.
            </summary>
            <param name="type">The data-type of the field.</param>
            <param name="model">The model to use for configuration.</param>
            <param name="value">The effective value of the field, or the default value if not found.</param>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <param name="allowDefinedTag">Allow tags that are present as part of the definition; for example, to query unknown enum values.</param>
            <returns>True if data for the field was present, false otherwise.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.GetValues(ProtoBuf.Meta.TypeModel,System.Type,ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            Each occurrence of the field is yielded separately, making this usage suitable for "repeated"
            (list) fields.
            </summary>
            <remarks>The extended data is processed lazily as the enumerator is iterated.</remarks>
            <param name="model">The model to use for configuration.</param>
            <param name="type">The data-type of the field.</param>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <returns>An enumerator that yields each occurrence of the field.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.AppendValue(ProtoBuf.Meta.TypeModel,ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat,System.Object)">
            <summary>
            Appends the value as an additional (unexpected) data-field for the instance.
            Note that for non-repeated sub-objects, this equates to a merge operation;
            for repeated sub-objects this adds a new instance to the set; for simple
            values the new value supercedes the old value.
            </summary>
            <remarks>Note that appending a value does not remove the old value from
            the stream; avoid repeatedly appending values for the same field.</remarks>
            <param name="model">The model to use for configuration.</param>
            <param name="format">The data-format to use when encoding the value.</param>
            <param name="instance">The extensible object to append the value to.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="value">The value to append.</param>
        </member>
        <member name="T:ProtoBuf.ExtensibleUtil">
            <summary>
            This class acts as an internal wrapper allowing us to do a dynamic
            methodinfo invoke; an't put into Serializer as don't want on public
            API; can't put into Serializer&lt;T&gt; since we need to invoke
            across classes
            </summary>
        </member>
        <member name="M:ProtoBuf.ExtensibleUtil.GetExtendedValues``1(ProtoBuf.Meta.TypeModel,ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat,System.Boolean,System.Boolean)">
            <summary>
            All this does is call GetExtendedValuesTyped with the correct type for "instance";
            this ensures that we don't get issues with subclasses declaring conflicting types -
            the caller must respect the fields defined for the type they pass in.
            </summary>
        </member>
        <member name="M:ProtoBuf.ExtensibleUtil.GetExtendedValues(ProtoBuf.Meta.TypeModel,System.Type,ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat,System.Boolean,System.Boolean)">
            <summary>
            All this does is call GetExtendedValuesTyped with the correct type for "instance";
            this ensures that we don't get issues with subclasses declaring conflicting types -
            the caller must respect the fields defined for the type they pass in.
            </summary>
        </member>
        <member name="T:ProtoBuf.Helpers">
            <summary>
            Not all frameworks are created equal (fx1.1 vs fx2.0,
            micro-framework, compact-framework,
            silverlight, etc). This class simply wraps up a few things that would
            otherwise make the real code unnecessarily messy, providing fallback
            implementations if necessary.
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoTypeCode">
            <summary>
            Intended to be a direct map to regular TypeCode, but:
            - with missing types
            - existing on WinRT
            </summary>
        </member>
        <member name="T:ProtoBuf.IExtensible">
            <summary>
            Indicates that the implementing type has support for protocol-buffer
            <see cref="T:ProtoBuf.IExtension">extensions</see>.
            </summary>
            <remarks>Can be implemented by deriving from Extensible.</remarks>
        </member>
        <member name="M:ProtoBuf.IExtensible.GetExtensionObject(System.Boolean)">
            <summary>
            Retrieves the <see cref="T:ProtoBuf.IExtension">extension</see> object for the current
            instance, optionally creating it if it does not already exist.
            </summary>
            <param name="createIfMissing">Should a new extension object be
            created if it does not already exist?</param>
            <returns>The extension object if it exists (or was created), or null
            if the extension object does not exist or is not available.</returns>
            <remarks>The <c>createIfMissing</c> argument is false during serialization,
            and true during deserialization upon encountering unexpected fields.</remarks>
        </member>
        <member name="T:ProtoBuf.ITypedExtensible">
            <summary>
            Indicates that the implementing type has support for protocol-buffer
            <see cref="T:ProtoBuf.IExtension">extensions</see> at multiple inheritance levels.
            </summary>
            <remarks>Can be implemented by deriving from Extensible.</remarks>
        </member>
        <member name="M:ProtoBuf.ITypedExtensible.GetExtensionObject(System.Type,System.Boolean)">
            <summary>
            Retrieves the <see cref="T:ProtoBuf.IExtension">extension</see> object for the current
            instance, optionally creating it if it does not already exist.
            </summary>
            <param name="createIfMissing">Should a new extension object be
            created if it does not already exist?</param>
            <param name="type">The <see cref="T:System.Type"/> that holds the fields, in terms of the inheritance model; the same <c>tag</c> key can appear against different <c>type</c> levels for the same <c>instance</c>, with different values.</param>
            <returns>The extension object if it exists (or was created), or null
            if the extension object does not exist or is not available.</returns>
            <remarks>The <c>createIfMissing</c> argument is false during serialization,
            and true during deserialization upon encountering unexpected fields.</remarks>
        </member>
        <member name="T:ProtoBuf.IExtension">
            <summary>
            Provides addition capability for supporting unexpected fields during
            protocol-buffer serialization/deserialization. This allows for loss-less
            round-trip/merge, even when the data is not fully understood.
            </summary>
        </member>
        <member name="M:ProtoBuf.IExtension.BeginAppend">
            <summary>
            Requests a stream into which any unexpected fields can be persisted.
            </summary>
            <returns>A new stream suitable for storing data.</returns>
        </member>
        <member name="M:ProtoBuf.IExtension.EndAppend(System.IO.Stream,System.Boolean)">
            <summary>
            Indicates that all unexpected fields have now been stored. The
            implementing class is responsible for closing the stream. If
            "commit" is not true the data may be discarded.
            </summary>
            <param name="stream">The stream originally obtained by BeginAppend.</param>
            <param name="commit">True if the append operation completed successfully.</param>
        </member>
        <member name="M:ProtoBuf.IExtension.BeginQuery">
            <summary>
            Requests a stream of the unexpected fields previously stored.
            </summary>
            <returns>A prepared stream of the unexpected fields.</returns>
        </member>
        <member name="M:ProtoBuf.IExtension.EndQuery(System.IO.Stream)">
            <summary>
            Indicates that all unexpected fields have now been read. The
            implementing class is responsible for closing the stream.
            </summary>
            <param name="stream">The stream originally obtained by BeginQuery.</param>
        </member>
        <member name="M:ProtoBuf.IExtension.GetLength">
            <summary>
            Requests the length of the raw binary stream; this is used
            when serializing sub-entities to indicate the expected size.
            </summary>
            <returns>The length of the binary stream representing unexpected data.</returns>
        </member>
        <member name="T:ProtoBuf.IExtensionResettable">
            <summary>
            Provides the ability to remove all existing extension data
            </summary>
        </member>
        <member name="M:ProtoBuf.IExtensionResettable.Reset">
            <summary>
            Remove all existing extension data
            </summary>
        </member>
        <member name="T:ProtoBuf.ImplicitFields">
            <summary>
            Specifies the method used to infer field tags for members of the type
            under consideration. Tags are deduced using the invariant alphabetic
            sequence of the members' names; this makes implicit field tags very brittle,
            and susceptible to changes such as field names (normally an isolated
            change).
            </summary>
        </member>
        <member name="F:ProtoBuf.ImplicitFields.None">
            <summary>
            No members are serialized implicitly; all members require a suitable
            attribute such as [ProtoMember]. This is the recmomended mode for
            most scenarios.
            </summary>
        </member>
        <member name="F:ProtoBuf.ImplicitFields.AllPublic">
            <summary>
            Public properties and fields are eligible for implicit serialization;
            this treats the public API as a contract. Ordering beings from ImplicitFirstTag.
            </summary>
        </member>
        <member name="F:ProtoBuf.ImplicitFields.AllFields">
            <summary>
            Public and non-public fields are eligible for implicit serialization;
            this acts as a state/implementation serializer. Ordering beings from ImplicitFirstTag.
            </summary>
        </member>
        <member name="T:ProtoBuf.Internal.PrimaryTypeProvider.DecimalAccessor">
            <summary>
            Provides access to the inner fields of a decimal.
            Similar to decimal.GetBits(), but faster and avoids the int[] allocation
            </summary>
        </member>
        <member name="T:ProtoBuf.Internal.PrimaryTypeProvider.GuidAccessor">
            <summary>
            Provides access to the inner fields of a Guid.
            Similar to Guid.ToByteArray(), but faster and avoids the byte[] allocation
            </summary>
        </member>
        <member name="M:ProtoBuf.Internal.ReferenceValueChecker.ProtoBuf#Internal#IValueChecker{System#Object}#HasNonTrivialValue(System.Object)">
            <summary>
            Indicates whether a value is non-null and needs serialization (non-zero, not an empty string, etc)
            </summary>
        </member>
        <member name="M:ProtoBuf.Internal.ReferenceValueChecker.ProtoBuf#Internal#IValueChecker{System#Object}#IsNull(System.Object)">
            <summary>
            Indicates whether a value is null
            </summary>
        </member>
        <member name="T:ProtoBuf.IProtoInput`1">
            <summary>
            Represents the ability to deserialize values from an input of type <typeparamref name="TInput"/>
            </summary>
        </member>
        <member name="M:ProtoBuf.IProtoInput`1.Deserialize``1(`0,``0,System.Object)">
            <summary>
            Deserialize a value from the input
            </summary>
        </member>
        <member name="T:ProtoBuf.IProtoOutput`1">
            <summary>
            Represents the ability to serialize values to an output of type <typeparamref name="TOutput"/>
            </summary>
        </member>
        <member name="M:ProtoBuf.IProtoOutput`1.Serialize``1(`0,``0,System.Object)">
            <summary>
            Serialize the provided value
            </summary>
        </member>
        <member name="T:ProtoBuf.IMeasuredProtoOutput`1">
            <summary>
            Represents the ability to serialize values to an output of type <typeparamref name="TOutput"/>
            with pre-computation of the length
            </summary>
        </member>
        <member name="M:ProtoBuf.IMeasuredProtoOutput`1.Measure``1(``0,System.Object)">
            <summary>
            Measure the length of a value in advance of serialization
            </summary>
        </member>
        <member name="M:ProtoBuf.IMeasuredProtoOutput`1.Serialize``1(ProtoBuf.MeasureState{``0},`0)">
            <summary>
            Serialize the previously measured value
            </summary>
        </member>
        <member name="T:ProtoBuf.ISerializationContext">
            <summary>
            Represents common state during a serialization operation; this instance should not be stored - it may be reused later with different meaning
            </summary>
        </member>
        <member name="P:ProtoBuf.ISerializationContext.Model">
            <summary>
            The type-model that represents the operation
            </summary>
        </member>
        <member name="P:ProtoBuf.ISerializationContext.UserState">
            <summary>
            Addition information about this serialization operation.
            </summary>
        </member>
        <member name="T:ProtoBuf.MeasureState`1">
            <summary>
            Represents the outcome of computing the length of an object; since this may have required computing lengths
            for multiple objects, some metadata is retained so that a subsequent serialize operation using
            this instance can re-use the previously calculated lengths. If the object state changes between the
            measure and serialize operations, the behavior is undefined.
            </summary>
        </member>
        <member name="M:ProtoBuf.MeasureState`1.Dispose">
            <summary>
            Releases all resources associated with this value
            </summary>
        </member>
        <member name="P:ProtoBuf.MeasureState`1.Length">
            <summary>
            Gets the calculated length of this serialize operation, in bytes
            </summary>
        </member>
        <member name="M:ProtoBuf.MeasureState`1.LengthOnly">
            <summary>
            Returns the calculated length, disposing the value as a side-effect
            </summary>
        </member>
        <member name="M:ProtoBuf.MeasureState`1.Serialize(System.IO.Stream)">
            <summary>
            Perform the calculated serialization operation against the provided target stream. If the object state changes between the
            measure and serialize operations, the behavior is undefined.
            </summary>
        </member>
        <member name="M:ProtoBuf.MeasureState`1.Serialize(System.Buffers.IBufferWriter{System.Byte})">
            <summary>
            Perform the calculated serialization operation against the provided target writer. If the object state changes between the
            measure and serialize operations, the behavior is undefined.
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.ProtoSyntax">
            <summary>
            Indiate the variant of the protobuf .proto DSL syntax to use
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.ProtoSyntax.Default">
            <summary>
            Use the global default
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.ProtoSyntax.Proto2">
            <summary>
            https://developers.google.com/protocol-buffers/docs/proto
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.ProtoSyntax.Proto3">
            <summary>
            https://developers.google.com/protocol-buffers/docs/proto3
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.SchemaGenerationOptions">
            <summary>
            Options for controlling schema generation
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.SchemaGenerationOptions.Syntax">
            <summary>
            Indiate the variant of the protobuf .proto DSL syntax to use
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.SchemaGenerationOptions.Flags">
            <summary>
            Additional flags to control schema generation
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.SchemaGenerationOptions.Package">
            <summary>
            The package to use for generation (<c>null</c> to try to infer)
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.SchemaGenerationOptions.Services">
            <summary>
            The services to consider as part of this operation.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.SchemaGenerationOptions.Types">
            <summary>
            The types to consider as part of this operation.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.SchemaGenerationOptions.Origin">
            <summary>
            The file that defines this type (as used with <c>import</c> in .proto); when non-empty, only
            types in the same <c>Origin</c> are included; this option is inferred if <c>null</c>.
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.SchemaGenerationFlags">
            <summary>
            Additional flags to control schema generation
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.SchemaGenerationFlags.None">
            <summary>
            No additional flags
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.SchemaGenerationFlags.MultipleNamespaceSupport">
            <summary>
            Provide support for extended/multiple namespace details in schemas
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.SchemaGenerationFlags.PreserveSubType">
            <summary>
            Record the sub-type relationship formally in schemas
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.Service">
            <summary>
            Describes a service.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.Service.Name">
            <summary>
            The name of the service.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.Service.Methods">
            <summary>
            The methods available on the service.
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.ServiceMethod">
            <summary>
            Describes a method of a service.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ServiceMethod.Name">
            <summary>
            The name of the method.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ServiceMethod.InputType">
            <summary>
            The type sent by the client.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ServiceMethod.OutputType">
            <summary>
            The type returned from the server.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ServiceMethod.ServerStreaming">
            <summary>
            Identifies if server streams multiple server messages.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ServiceMethod.ClientStreaming">
            <summary>
            Identifies if client streams multiple client messages.
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.TypeFormatEventArgs">
            <summary>
            Event arguments needed to perform type-formatting functions; this could be resolving a Type to a string suitable for serialization, or could
            be requesting a Type from a string. If no changes are made, a default implementation will be used (from the assembly-qualified names).
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.TypeFormatEventArgs.Type">
            <summary>
            The type involved in this map; if this is initially null, a Type is expected to be provided for the string in FormattedName.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.TypeFormatEventArgs.FormattedName">
            <summary>
            The formatted-name involved in this map; if this is initially null, a formatted-name is expected from the type in Type.
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.TypeFormatEventHandler">
            <summary>
            Delegate type used to perform type-formatting functions; the sender originates as the type-model.
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.TypeModel">
            <summary>
            Provides protobuf serialization support for a number of types
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.TypeModel.BufferSize">
            <summary>
            Gets or sets the buffer-size to use when writing messages via <see cref="T:System.Buffers.IBufferWriter`1"/>
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.TypeModel.MaxDepth">
            <summary>
            Gets or sets the max serialization/deserialization depth
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.GetSerializer``2">
            <summary>
            Gets a cached serializer for a type, as offered by a given provider
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.TypeModel.TypeModelOptions">
            <summary>
            Specifies optional behaviors associated with a type model
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.TypeModel.TypeModelOptions.None">
            <summary>
            No additional options
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.TypeModel.TypeModelOptions.InternStrings">
            <summary>
            Should the deserializer attempt to avoid duplicate copies of the same string?
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.TypeModel.TypeModelOptions.IncludeDateTimeKind">
            <summary>
            Should the <c>Kind</c> be included on date/time values?
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.TypeModel.TypeModelOptions.SkipZeroLengthPackedArrays">
            <summary>
            Should zero-length packed arrays be serialized? (this is the v2 behavior, but skipping them is more efficient)
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.TypeModel.TypeModelOptions.AllowPackedEncodingAtRoot">
            <summary>
            Should root-values allow "packed" encoding? (v2 does not support this)
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.TypeModel.Options">
            <summary>
            Specifies optional behaviors associated with this model
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.MapType(System.Type)">
            <summary>
            Resolve a System.Type to the compiler-specific type
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.MapType(System.Type,System.Boolean)">
            <summary>
            Resolve a System.Type to the compiler-specific type
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.IsKnownType``1(ProtoBuf.CompatibilityLevel)">
            <summary>
            Indicates whether a type is known to the model
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.TrySerializeAuxiliaryType(ProtoBuf.ProtoWriter.State@,System.Type,ProtoBuf.DataFormat,System.Int32,System.Object,System.Boolean,System.Object,System.Boolean)">
            <summary>
            This is the more "complete" version of Serialize, which handles single instances of mapped types.
            The value is written as a complete field, including field-header and (for sub-objects) a
            length-prefix
            In addition to that, this provides support for:
             - basic values; individual int / string / Guid / etc
             - IEnumerable sequences of any type handled by TrySerializeAuxiliaryType
             
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Serialize(System.IO.Stream,System.Object)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream.
            </summary>
            <param name="value">The existing instance to be serialized (cannot be null).</param>
            <param name="dest">The destination stream to write to.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Serialize(System.IO.Stream,System.Object,ProtoBuf.SerializationContext)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream.
            </summary>
            <param name="value">The existing instance to be serialized (cannot be null).</param>
            <param name="dest">The destination stream to write to.</param>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Serialize(System.Buffers.IBufferWriter{System.Byte},System.Object,System.Object)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied writer.
            </summary>
            <param name="value">The existing instance to be serialized (cannot be null).</param>
            <param name="dest">The destination stream to write to.</param>
            <param name="userState">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Serialize``1(System.IO.Stream,``0,System.Object)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream.
            </summary>
            <param name="value">The existing instance to be serialized (cannot be null).</param>
            <param name="dest">The destination stream to write to.</param>
            <param name="userState">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Serialize``1(System.Buffers.IBufferWriter{System.Byte},``0,System.Object)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied writer.
            </summary>
            <param name="value">The existing instance to be serialized (cannot be null).</param>
            <param name="dest">The destination stream to write to.</param>
            <param name="userState">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Measure``1(``0,System.Object,System.Int64)">
            <summary>
            Calculates the length of a protocol-buffer payload for an item
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Serialize(ProtoBuf.ProtoWriter,System.Object)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied writer.
            </summary>
            <param name="value">The existing instance to be serialized (cannot be null).</param>
            <param name="dest">The destination writer to write to.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeWithLengthPrefix(System.IO.Stream,System.Object,System.Type,ProtoBuf.PrefixStyle,System.Int32)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (or null), using length-prefixed
            data - useful with network IO.
            </summary>
            <param name="type">The type being merged.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="fieldNumber">The tag used as a prefix to each record (only used with base-128 style prefixes).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeWithLengthPrefix(System.IO.Stream,System.Object,System.Type,ProtoBuf.PrefixStyle,System.Int32,ProtoBuf.TypeResolver)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (or null), using length-prefixed
            data - useful with network IO.
            </summary>
            <param name="type">The type being merged.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="expectedField">The tag used as a prefix to each record (only used with base-128 style prefixes).</param>
            <param name="resolver">Used to resolve types on a per-field basis.</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeWithLengthPrefix(System.IO.Stream,System.Object,System.Type,ProtoBuf.PrefixStyle,System.Int32,ProtoBuf.TypeResolver,System.Int32@)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (or null), using length-prefixed
            data - useful with network IO.
            </summary>
            <param name="type">The type being merged.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="expectedField">The tag used as a prefix to each record (only used with base-128 style prefixes).</param>
            <param name="resolver">Used to resolve types on a per-field basis.</param>
            <param name="bytesRead">Returns the number of bytes consumed by this operation (includes length-prefix overheads and any skipped data).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeWithLengthPrefix(System.IO.Stream,System.Object,System.Type,ProtoBuf.PrefixStyle,System.Int32,ProtoBuf.TypeResolver,System.Int64@)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (or null), using length-prefixed
            data - useful with network IO.
            </summary>
            <param name="type">The type being merged.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="expectedField">The tag used as a prefix to each record (only used with base-128 style prefixes).</param>
            <param name="resolver">Used to resolve types on a per-field basis.</param>
            <param name="bytesRead">Returns the number of bytes consumed by this operation (includes length-prefix overheads and any skipped data).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeItems(System.IO.Stream,System.Type,ProtoBuf.PrefixStyle,System.Int32,ProtoBuf.TypeResolver)">
            <summary>
            Reads a sequence of consecutive length-prefixed items from a stream, using
            either base-128 or fixed-length prefixes. Base-128 prefixes with a tag
            are directly comparable to serializing multiple items in succession
            (use the <see cref="F:ProtoBuf.Meta.TypeModel.ListItemTag"/> tag to emulate the implicit behavior
            when serializing a list/array). When a tag is
            specified, any records with different tags are silently omitted. The
            tag is ignored. The tag is ignores for fixed-length prefixes.
            </summary>
            <param name="source">The binary stream containing the serialized records.</param>
            <param name="style">The prefix style used in the data.</param>
            <param name="expectedField">The tag of records to return (if non-positive, then no tag is
            expected and all records are returned).</param>
            <param name="resolver">On a field-by-field basis, the type of object to deserialize (can be null if "type" is specified). </param>
            <param name="type">The type of object to deserialize (can be null if "resolver" is specified).</param>
            <returns>The sequence of deserialized objects.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeItems(System.IO.Stream,System.Type,ProtoBuf.PrefixStyle,System.Int32,ProtoBuf.TypeResolver,ProtoBuf.SerializationContext)">
            <summary>
            Reads a sequence of consecutive length-prefixed items from a stream, using
            either base-128 or fixed-length prefixes. Base-128 prefixes with a tag
            are directly comparable to serializing multiple items in succession
            (use the <see cref="F:ProtoBuf.Meta.TypeModel.ListItemTag"/> tag to emulate the implicit behavior
            when serializing a list/array). When a tag is
            specified, any records with different tags are silently omitted. The
            tag is ignored. The tag is ignores for fixed-length prefixes.
            </summary>
            <param name="source">The binary stream containing the serialized records.</param>
            <param name="style">The prefix style used in the data.</param>
            <param name="expectedField">The tag of records to return (if non-positive, then no tag is
            expected and all records are returned).</param>
            <param name="resolver">On a field-by-field basis, the type of object to deserialize (can be null if "type" is specified). </param>
            <param name="type">The type of object to deserialize (can be null if "resolver" is specified).</param>
            <returns>The sequence of deserialized objects.</returns>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeItems``1(System.IO.Stream,ProtoBuf.PrefixStyle,System.Int32)">
            <summary>
            Reads a sequence of consecutive length-prefixed items from a stream, using
            either base-128 or fixed-length prefixes. Base-128 prefixes with a tag
            are directly comparable to serializing multiple items in succession
            (use the <see cref="F:ProtoBuf.Meta.TypeModel.ListItemTag"/> tag to emulate the implicit behavior
            when serializing a list/array). When a tag is
            specified, any records with different tags are silently omitted. The
            tag is ignored. The tag is ignores for fixed-length prefixes.
            </summary>
            <typeparam name="T">The type of object to deserialize.</typeparam>
            <param name="source">The binary stream containing the serialized records.</param>
            <param name="style">The prefix style used in the data.</param>
            <param name="expectedField">The tag of records to return (if non-positive, then no tag is
            expected and all records are returned).</param>
            <returns>The sequence of deserialized objects.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeItems``1(System.IO.Stream,ProtoBuf.PrefixStyle,System.Int32,ProtoBuf.SerializationContext)">
            <summary>
            Reads a sequence of consecutive length-prefixed items from a stream, using
            either base-128 or fixed-length prefixes. Base-128 prefixes with a tag
            are directly comparable to serializing multiple items in succession
            (use the <see cref="F:ProtoBuf.Meta.TypeModel.ListItemTag"/> tag to emulate the implicit behavior
            when serializing a list/array). When a tag is
            specified, any records with different tags are silently omitted. The
            tag is ignored. The tag is ignores for fixed-length prefixes.
            </summary>
            <typeparam name="T">The type of object to deserialize.</typeparam>
            <param name="source">The binary stream containing the serialized records.</param>
            <param name="style">The prefix style used in the data.</param>
            <param name="expectedField">The tag of records to return (if non-positive, then no tag is
            expected and all records are returned).</param>
            <returns>The sequence of deserialized objects.</returns>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.SerializeWithLengthPrefix(System.IO.Stream,System.Object,System.Type,ProtoBuf.PrefixStyle,System.Int32)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream,
            with a length-prefix. This is useful for socket programming,
            as DeserializeWithLengthPrefix can be used to read the single object back
            from an ongoing stream.
            </summary>
            <param name="type">The type being serialized.</param>
            <param name="value">The existing instance to be serialized (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="dest">The destination stream to write to.</param>
            <param name="fieldNumber">The tag used as a prefix to each record (only used with base-128 style prefixes).</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.SerializeWithLengthPrefix(System.IO.Stream,System.Object,System.Type,ProtoBuf.PrefixStyle,System.Int32,ProtoBuf.SerializationContext)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream,
            with a length-prefix. This is useful for socket programming,
            as DeserializeWithLengthPrefix can be used to read the single object back
            from an ongoing stream.
            </summary>
            <param name="type">The type being serialized.</param>
            <param name="value">The existing instance to be serialized (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="dest">The destination stream to write to.</param>
            <param name="fieldNumber">The tag used as a prefix to each record (only used with base-128 style prefixes).</param>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.IO.Stream,System.Object,System.Type)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.IO.Stream,System.Object,System.Type,ProtoBuf.SerializationContext)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize``1(System.IO.Stream,``0,System.Object)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <typeparam name="T">The type (including inheritance) to consider.</typeparam>
            <param name="userState">Additional information about this serialization operation.</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize``1(System.ReadOnlyMemory{System.Byte},``0,System.Object)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <typeparam name="T">The type (including inheritance) to consider.</typeparam>
            <param name="userState">Additional information about this serialization operation.</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize``1(System.ReadOnlySpan{System.Byte},``0,System.Object)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <typeparam name="T">The type (including inheritance) to consider.</typeparam>
            <param name="userState">Additional information about this serialization operation.</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize``1(System.Buffers.ReadOnlySequence{System.Byte},``0,System.Object)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <typeparam name="T">The type (including inheritance) to consider.</typeparam>
            <param name="userState">Additional information about this serialization operation.</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.Type,System.IO.Stream,System.Object,System.Object,System.Int64)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="userState">Additional information about this serialization operation.</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="length">The number of bytes to consider (no limit if omitted).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.Type,System.ReadOnlyMemory{System.Byte},System.Object,System.Object)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="userState">Additional information about this serialization operation.</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.Type,System.ReadOnlySpan{System.Byte},System.Object,System.Object)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="userState">Additional information about this serialization operation.</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.Type,System.Buffers.ReadOnlySequence{System.Byte},System.Object,System.Object)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="userState">Additional information about this serialization operation.</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.IO.Stream,System.Object,System.Type,System.Int32)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="length">The number of bytes to consume.</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.IO.Stream,System.Object,System.Type,System.Int64)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="length">The number of bytes to consume.</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.IO.Stream,System.Object,System.Type,System.Int32,ProtoBuf.SerializationContext)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="length">The number of bytes to consume (or -1 to read to the end of the stream).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.IO.Stream,System.Object,System.Type,System.Int64,ProtoBuf.SerializationContext)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="length">The number of bytes to consume (or -1 to read to the end of the stream).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.ReadOnlyMemory{System.Byte},System.Type,System.Object,System.Object)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary payload  to apply to the instance.</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
            <param name="userState">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.Buffers.ReadOnlySequence{System.Byte},System.Type,System.Object,System.Object)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary payload  to apply to the instance.</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
            <param name="userState">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(ProtoBuf.ProtoReader,System.Object,System.Type)">
            <summary>
            Applies a protocol-buffer reader to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The reader to apply to the instance (cannot be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.TryDeserializeAuxiliaryType(ProtoBuf.ProtoReader.State@,ProtoBuf.DataFormat,System.Int32,System.Type,System.Object@,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Object,System.Boolean)">
            <summary>
            <para>
            This is the more "complete" version of Deserialize, which handles single instances of mapped types.
            The value is read as a complete field, including field-header and (for sub-objects) a
            length-prefix..kmc  
            </para>
            <para>
            In addition to that, this provides support for:
             - basic values; individual int / string / Guid / etc
             - IList sets of any type handled by TryDeserializeAuxiliaryType
            </para>
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Create">
            <summary>
            Creates a new runtime model, to which the caller
            can add support for a range of types. A model
            can be used "as is", or can be compiled for
            optimal performance.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.CreateForAssembly``1">
            <summary>
            Create a model that serializes all types from an
            assembly specified by type
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.CreateForAssembly(System.Type)">
            <summary>
            Create a model that serializes all types from an
            assembly specified by type
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.CreateForAssembly(System.Reflection.Assembly)">
            <summary>
            Create a model that serializes all types from an assembly
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.IsDefined(System.Type)">
            <summary>
            Indicates whether the supplied type is explicitly modelled by the model
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.IsDefined(System.Type,ProtoBuf.CompatibilityLevel)">
            <summary>
            Indicates whether the supplied type is explicitly modelled by the model
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.GetSerializer``1">
            <summary>
            Get a typed serializer for <typeparamref name="T"/>
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.GetInbuiltSerializer``1(ProtoBuf.CompatibilityLevel,ProtoBuf.DataFormat)">
            <summary>
            Gets the inbuilt serializer relevant to a specific <see cref="T:ProtoBuf.CompatibilityLevel"/> (and <see cref="T:ProtoBuf.DataFormat"/>).
            Returns null if there is no defined inbuilt serializer.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(ProtoBuf.Internal.ObjectScope,ProtoBuf.ProtoReader.State@,System.Type,System.Object)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">Represents the type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="state">Reader state</param>
            <param name="scope">The style of serialization to adopt</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="T:ProtoBuf.Meta.TypeModel.CallbackType">
            <summary>
            Indicates the type of callback to be used
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.TypeModel.CallbackType.BeforeSerialize">
            <summary>
            Invoked before an object is serialized
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.TypeModel.CallbackType.AfterSerialize">
            <summary>
            Invoked after an object is serialized
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.TypeModel.CallbackType.BeforeDeserialize">
            <summary>
            Invoked before an object is deserialized (or when a new instance is created)
            </summary>            
        </member>
        <member name="F:ProtoBuf.Meta.TypeModel.CallbackType.AfterDeserialize">
            <summary>
            Invoked after an object is deserialized
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeepClone``1(``0,System.Object)">
            <summary>
            Create a deep clone of the supplied instance; any sub-items are also cloned.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeepClone(System.Object)">
            <summary>
            Create a deep clone of the supplied instance; any sub-items are also cloned.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.ThrowUnexpectedSubtype(System.Type,System.Type)">
            <summary>
            Indicates that while an inheritance tree exists, the exact type encountered was not
            specified in that hierarchy and cannot be processed.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.ThrowUnexpectedSubtype``1(``0)">
            <summary>
            Indicates that while an inheritance tree exists, the exact type encountered was not
            specified in that hierarchy and cannot be processed.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.ThrowUnexpectedSubtype``2(``0)">
            <summary>
            Indicates that while an inheritance tree exists, the exact type encountered was not
            specified in that hierarchy and cannot be processed.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.IsSubType``1(``0)">
            <summary>
            Returns whether the object provided is a subtype of the expected type
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.ThrowUnexpectedType(System.Type,ProtoBuf.Meta.TypeModel)">
            <summary>
            Indicates that the given type was not expected, and cannot be processed.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.ThrowCannotCreateInstance(System.Type,System.Exception)">
            <summary>
            Indicates that the given type cannot be constructed; it may still be possible to 
            deserialize into existing instances.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.CanSerializeContractType(System.Type)">
            <summary>
            Returns true if the type supplied is either a recognised contract type,
            or a *list* of a recognised contract type. 
            </summary>
            <remarks>Note that primitives always return false, even though the engine
            will, if forced, try to serialize such</remarks>
            <returns>True if this type is recognised as a serializable entity, else false</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.CanSerialize(System.Type)">
            <summary>
            Returns true if the type supplied is a basic type with inbuilt handling,
            a recognised contract type, or a *list* of a basic / contract type. 
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.CanSerializeBasicType(System.Type)">
            <summary>
            Returns true if the type supplied is a basic type with inbuilt handling,
            or a *list* of a basic type with inbuilt handling
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.GetSchema(System.Type)">
            <summary>
            Suggest a .proto definition for the given type
            </summary>
            <param name="type">The type to generate a .proto definition for, or <c>null</c> to generate a .proto that represents the entire model</param>
            <returns>The .proto definition as a string</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.GetSchema(System.Type,ProtoBuf.Meta.ProtoSyntax)">
            <summary>
            Suggest a .proto definition for the given type
            </summary>
            <param name="type">The type to generate a .proto definition for, or <c>null</c> to generate a .proto that represents the entire model</param>
            <returns>The .proto definition as a string</returns>
            <param name="syntax">The .proto syntax to use for the operation</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.GetSchema(ProtoBuf.Meta.SchemaGenerationOptions)">
            <summary>
            Suggest a .proto definition for the given configuration
            </summary>
            <returns>The .proto definition as a string</returns>
            <param name="options">Options for schema generation</param>
        </member>
        <member name="E:ProtoBuf.Meta.TypeModel.DynamicTypeFormatting">
            <summary>
            Used to provide custom services for writing and parsing type names when using dynamic types. Both parsing and formatting
            are provided on a single API as it is essential that both are mapped identically at all times.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.CreateFormatter(System.Type)">
            <summary>
            Creates a new IFormatter that uses protocol-buffer [de]serialization.
            </summary>
            <returns>A new IFormatter to be used during [de]serialization.</returns>
            <param name="type">The type of object to be [de]deserialized by the formatter.</param>
        </member>
        <member name="F:ProtoBuf.Meta.TypeModel.ListItemTag">
            <summary>
            The field number that is used as a default when serializing/deserializing a list of objects.
            The data is treated as repeated message with field number 1.
            </summary>
        </member>
        <member name="T:ProtoBuf.NullWrappedValueAttribute">
            <summary>when used on a nullable scalar value, indicates that an extra message layer
            is added, for compatibility with <c>wrappers.proto</c>, rather than
            "field presence";
            when used on a collection/dictionary, indicates  that the values can track nulls;
            see https://protobuf-net.github.io/protobuf-net/nullwrappers for more information.
            </summary>
        </member>
        <member name="P:ProtoBuf.NullWrappedValueAttribute.AsGroup">
            <summary>Indicates that the collection message wrapper should use group encoding; this is more
            efficient to write, but may be hard to consume in cross-platform scenarios; this feature is
            usually used for compatibility with protobuf-net v2 <c>SupportNull</c> usage</summary>
        </member>
        <member name="T:ProtoBuf.NullWrappedCollectionAttribute">
            <summary>Indicates that a collection can track the difference between a null and empty collection;
            see https://protobuf-net.github.io/protobuf-net/nullwrappers for more information.
            </summary>
        </member>
        <member name="P:ProtoBuf.NullWrappedCollectionAttribute.AsGroup">
            <summary>Indicates that the collection message wrapper should use group encoding; this is more efficient to write, but may be hard to consume in cross-platform scenarios.</summary>
        </member>
        <member name="T:ProtoBuf.PrefixStyle">
            <summary>
            Specifies the type of prefix that should be applied to messages.
            </summary>
        </member>
        <member name="F:ProtoBuf.PrefixStyle.None">
            <summary>
            No length prefix is applied to the data; the data is terminated only be the end of the stream.
            </summary>
        </member>
        <member name="F:ProtoBuf.PrefixStyle.Base128">
            <summary>
            A base-128 ("varint", the default prefix format in protobuf) length prefix is applied to the data (efficient for short messages).
            </summary>
        </member>
        <member name="F:ProtoBuf.PrefixStyle.Fixed32">
            <summary>
            A fixed-length (little-endian) length prefix is applied to the data (useful for compatibility).
            </summary>
        </member>
        <member name="F:ProtoBuf.PrefixStyle.Fixed32BigEndian">
            <summary>
            A fixed-length (big-endian) length prefix is applied to the data (useful for compatibility).
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoContractAttribute">
            <summary>
            Indicates that a type is defined for protocol-buffer serialization.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.Name">
            <summary>
            Gets or sets the defined name of the type. This can be fully qualified , for example <c>.foo.bar.someType</c> if required.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.Origin">
            <summary>
            Gets or sets the file that defines this type (as used with <c>import</c> in .proto)
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.ImplicitFirstTag">
            <summary>
            Gets or sets the fist offset to use with implicit field tags;
            only uesd if ImplicitFields is set.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.UseProtoMembersOnly">
            <summary>
            If specified, alternative contract markers (such as markers for XmlSerailizer or DataContractSerializer) are ignored.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.IgnoreListHandling">
            <summary>
            If specified, do NOT treat this type as a list, even if it looks like one.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.ImplicitFields">
            <summary>
            Gets or sets the mechanism used to automatically infer field tags
            for members. This option should be used in advanced scenarios only.
            Please review the important notes against the ImplicitFields enumeration.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.InferTagFromName">
            <summary>
            Enables/disables automatic tag generation based on the existing name / order
            of the defined members. This option is not used for members marked
            with ProtoMemberAttribute, as intended to provide compatibility with
            WCF serialization. WARNING: when adding new fields you must take
            care to increase the Order for new elements, otherwise data corruption
            may occur.
            </summary>
            <remarks>If not explicitly specified, the default is assumed from Serializer.GlobalOptions.InferTagFromName.</remarks>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.InferTagFromNameHasValue">
            <summary>
            Has a InferTagFromName value been explicitly set? if not, the default from the type-model is assumed.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.DataMemberOffset">
            <summary>
            Specifies an offset to apply to [DataMember(Order=...)] markers;
            this is useful when working with mex-generated classes that have
            a different origin (usually 1 vs 0) than the original data-contract.
            
            This value is added to the Order of each member.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.SkipConstructor">
            <summary>
            If true, the constructor for the type is bypassed during deserialization, meaning any field initializers
            or other initialization code is skipped.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.AsReferenceDefault">
            <summary>
            Should this type be treated as a reference by default? Please also see the implications of this,
            as recorded on ProtoMemberAttribute.AsReference
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.IsGroup">
            <summary>
            Indicates whether this type should always be treated as a "group" (rather than a string-prefixed sub-message)
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.IgnoreUnknownSubTypes">
            <summary>
            Gets or sets a value indicating whether unknown sub-types should cause serialization failure
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.EnumPassthru">
            <summary>
            Applies only to enums (not to DTO classes themselves); gets or sets a value indicating that an enum should be treated directly as an int/short/etc, rather
            than enforcing .proto enum rules. This is useful *in particul* for [Flags] enums.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.Surrogate">
            <summary>
            Defines a surrogate type used for serialization/deserialization purpose.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.Serializer">
            <summary>
            Defines a serializer to use for this type; the serializer must implement ISerializer-T for this type
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoConverterAttribute">
            <summary>
            Indicates that a static member should be considered the same as though
            were an implicit / explicit conversion operator; in particular, this
            is useful for conversions that operator syntax does not allow, such as
            to/from interface types.
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoEnumAttribute">
            <summary>
            Used to define protocol-buffer specific behavior for
            enumerated values.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoEnumAttribute.Value">
            <summary>
            Gets or sets the specific value to use for this enum during serialization.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoEnumAttribute.HasValue">
            <summary>
            Indicates whether this instance has a customised value mapping
            </summary>
            <returns>true if a specific value is set</returns>
        </member>
        <member name="P:ProtoBuf.ProtoEnumAttribute.Name">
            <summary>
            Gets or sets the defined name of the enum, as used in .proto
            (this name is not used during serialization).
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoException">
            <summary>
            Indicates an error during serialization/deserialization of a proto stream.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoException.#ctor">
            <summary>Creates a new ProtoException instance.</summary>
        </member>
        <member name="M:ProtoBuf.ProtoException.#ctor(System.String)">
            <summary>Creates a new ProtoException instance.</summary>
        </member>
        <member name="M:ProtoBuf.ProtoException.#ctor(System.String,System.Exception)">
            <summary>Creates a new ProtoException instance.</summary>
        </member>
        <member name="M:ProtoBuf.ProtoException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>Creates a new ProtoException instance.</summary>
        </member>
        <member name="T:ProtoBuf.ProtoIgnoreAttribute">
            <summary>
            Indicates that a member should be excluded from serialization; this
            is only normally used when using implict fields.
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoPartialIgnoreAttribute">
            <summary>
            Indicates that a member should be excluded from serialization; this
            is only normally used when using implict fields. This allows
            ProtoIgnoreAttribute usage
            even for partial classes where the individual members are not
            under direct control.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoPartialIgnoreAttribute.#ctor(System.String)">
            <summary>
            Creates a new ProtoPartialIgnoreAttribute instance.
            </summary>
            <param name="memberName">Specifies the member to be ignored.</param>
        </member>
        <member name="P:ProtoBuf.ProtoPartialIgnoreAttribute.MemberName">
            <summary>
            The name of the member to be ignored.
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoIncludeAttribute">
            <summary>
            Indicates the known-types to support for an individual
            message. This serializes each level in the hierarchy as
            a nested message to retain wire-compatibility with
            other protocol-buffer implementations.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoIncludeAttribute.#ctor(System.Int32,System.Type)">
            <summary>
             Creates a new instance of the ProtoIncludeAttribute.
             </summary>
             <param name="tag">The unique index (within the type) that will identify this data.</param>
             <param name="knownType">The additional type to serialize/deserialize.</param>
        </member>
        <member name="M:ProtoBuf.ProtoIncludeAttribute.#ctor(System.Int32,System.String)">
            <summary>
            Creates a new instance of the ProtoIncludeAttribute.
            </summary>
            <param name="tag">The unique index (within the type) that will identify this data.</param>
            <param name="knownTypeName">The additional type to serialize/deserialize.</param>
        </member>
        <member name="P:ProtoBuf.ProtoIncludeAttribute.Tag">
            <summary>
            Gets the unique index (within the type) that will identify this data.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoIncludeAttribute.KnownTypeName">
            <summary>
            Gets the additional type to serialize/deserialize.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoIncludeAttribute.KnownType">
            <summary>
            Gets the additional type to serialize/deserialize.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoIncludeAttribute.DataFormat">
            <summary>
            Specifies whether the inherited type's sub-message should be
            written with a length-prefix (default), or with group markers.
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoMapAttribute">
            <summary>
            Controls the formatting of elements in a dictionary, and indicates that
            "map" rules should be used: duplicates *replace* earlier values, rather
            than throwing an exception
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMapAttribute.KeyFormat">
            <summary>
            Describes the data-format used to store the key
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMapAttribute.ValueFormat">
            <summary>
            Describes the data-format used to store the value
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMapAttribute.DisableMap">
            <summary>
            Disables "map" handling; dictionaries will use ".Add(key,value)" instead of  "[key] = value",
            which means duplicate keys will cause an exception (instead of retaining the final value); if
            a proto schema is emitted, it will be produced using "repeated" instead of "map"
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoMemberAttribute">
            <summary>
            Declares a member to be used in protocol-buffer serialization, using
            the given Tag. A DataFormat may be used to optimise the serialization
            format (for instance, using zigzag encoding for negative numbers, or 
            fixed-length encoding for large values.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoMemberAttribute.CompareTo(System.Object)">
            <summary>
            Compare with another ProtoMemberAttribute for sorting purposes
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoMemberAttribute.CompareTo(ProtoBuf.ProtoMemberAttribute)">
            <summary>
            Compare with another ProtoMemberAttribute for sorting purposes
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoMemberAttribute.#ctor(System.Int32)">
            <summary>
            Creates a new ProtoMemberAttribute instance.
            </summary>
            <param name="tag">Specifies the unique tag used to identify this member within the type.</param>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.Name">
            <summary>
            Gets or sets the original name defined in the .proto; not used
            during serialization.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.DataFormat">
            <summary>
            Gets or sets the data-format to be used when encoding this value.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.Tag">
            <summary>
            Gets the unique tag used to identify this member within the type.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.IsRequired">
            <summary>
            Gets or sets a value indicating whether this member is mandatory.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.IsPacked">
            <summary>
            Gets a value indicating whether this member is packed.
            This option only applies to list/array data of primitive types (int, double, etc).
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.OverwriteList">
            <summary>
            Indicates whether this field should *replace* existing values (the default is false, meaning *append*).
            This option only applies to list/array data.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.AsReference">
            <summary>
            Enables full object-tracking/full-graph support.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.DynamicType">
            <summary>
            Embeds the type information into the stream, allowing usage with types not known in advance.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.Options">
            <summary>
            Gets or sets a value indicating whether this member is packed (lists/arrays).
            </summary>
        </member>
        <member name="T:ProtoBuf.MemberSerializationOptions">
            <summary>
            Additional (optional) settings that control serialization of members
            </summary>
        </member>
        <member name="F:ProtoBuf.MemberSerializationOptions.None">
            <summary>
            Default; no additional options
            </summary>
        </member>
        <member name="F:ProtoBuf.MemberSerializationOptions.Packed">
            <summary>
            Indicates that repeated elements should use packed (length-prefixed) encoding
            </summary>
        </member>
        <member name="F:ProtoBuf.MemberSerializationOptions.Required">
            <summary>
            Indicates that the given item is required
            </summary>
        </member>
        <member name="F:ProtoBuf.MemberSerializationOptions.AsReference">
            <summary>
            Enables full object-tracking/full-graph support
            </summary>
        </member>
        <member name="F:ProtoBuf.MemberSerializationOptions.DynamicType">
            <summary>
            Embeds the type information into the stream, allowing usage with types not known in advance
            </summary>
        </member>
        <member name="F:ProtoBuf.MemberSerializationOptions.OverwriteList">
            <summary>
            Indicates whether this field should *replace* existing values (the default is false, meaning *append*).
            This option only applies to list/array data.
            </summary>
        </member>
        <member name="F:ProtoBuf.MemberSerializationOptions.AsReferenceHasValue">
            <summary>
            Determines whether the types AsReferenceDefault value is used, or whether this member's AsReference should be used
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoPartialMemberAttribute">
            <summary>
            Declares a member to be used in protocol-buffer serialization, using
            the given Tag and MemberName. This allows ProtoMemberAttribute usage
            even for partial classes where the individual members are not
            under direct control.
            A DataFormat may be used to optimise the serialization
            format (for instance, using zigzag encoding for negative numbers, or 
            fixed-length encoding for large values.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoPartialMemberAttribute.#ctor(System.Int32,System.String)">
            <summary>
            Creates a new ProtoMemberAttribute instance.
            </summary>
            <param name="tag">Specifies the unique tag used to identify this member within the type.</param>
            <param name="memberName">Specifies the member to be serialized.</param>
        </member>
        <member name="P:ProtoBuf.ProtoPartialMemberAttribute.MemberName">
            <summary>
            The name of the member to be serialized.
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoReader">
            <summary>
            A stateful reader, used to read a protobuf stream. Typical usage would be (sequentially) to call
            ReadFieldHeader and (after matching the field) an appropriate Read* method.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.FieldNumber">
            <summary>
            Gets the number of the field being processed.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.WireType">
            <summary>
            Indicates the underlying proto serialization format on the wire.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.InternStrings">
            <summary>
            Gets / sets a flag indicating whether strings should be checked for repetition; if
            true, any repeated UTF-8 byte sequence will result in the same String instance, rather
            than a second instance of the same string. Disabled by default. Note that this uses
            a <i>custom</i> interner - the system-wide string interner is not used.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.Init(ProtoBuf.Meta.TypeModel,System.Object)">
            <summary>
            Initialize the reader
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.UserState">
            <summary>
            Addition information about this deserialization operation.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.Context">
            <summary>
            Addition information about this deserialization operation.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.Dispose">
            <summary>
            Releases resources used by the reader, but importantly <b>does not</b> Dispose the 
            underlying stream; in many typical use-cases the stream is used for different
            processes, so it is assumed that the consumer will Dispose their stream separately.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.Position">
            <summary>
            Returns the position of the current reader (note that this is not necessarily the same as the position
            in the underlying stream, if multiple readers are used on the same stream)
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.LongPosition">
            <summary>
            Returns the position of the current reader (note that this is not necessarily the same as the position
            in the underlying stream, if multiple readers are used on the same stream)
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadInt16">
            <summary>
            Reads a signed 16-bit integer from the stream: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadUInt16">
            <summary>
            Reads an unsigned 16-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadByte">
            <summary>
            Reads an unsigned 8-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadSByte">
            <summary>
            Reads a signed 8-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadUInt32">
            <summary>
            Reads an unsigned 32-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadInt32">
            <summary>
            Reads a signed 32-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadInt64">
            <summary>
            Reads a signed 64-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadString">
            <summary>
            Reads a string from the stream (using UTF8); supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ThrowEnumException(System.Type,System.Int32)">
            <summary>
            Throws an exception indication that the given value cannot be mapped to an enum.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadDouble">
            <summary>
            Reads a double-precision number from the stream; supported wire-types: Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadObject(System.Object,System.Type,ProtoBuf.ProtoReader)">
            <summary>
            Reads (merges) a sub-message from the stream, internally calling StartSubItem and EndSubItem, and (in between)
            parsing the message in accordance with the model associated with the reader
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.EndSubItem(ProtoBuf.SubItemToken,ProtoBuf.ProtoReader)">
            <summary>
            Makes the end of consuming a nested message in the stream; the stream must be either at the correct EndGroup
            marker, or all fields of the sub-message must have been consumed (in either case, this means ReadFieldHeader
            should return zero)
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.StartSubItem(ProtoBuf.ProtoReader)">
            <summary>
            Begins consuming a nested message in the stream; supported wire-types: StartGroup, String
            </summary>
            <remarks>The token returned must be help and used when callining EndSubItem</remarks>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadFieldHeader">
            <summary>
            Reads a field header from the stream, setting the wire-type and retuning the field number. If no
            more fields are available, then 0 is returned. This methods respects sub-messages.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.TryReadFieldHeader(System.Int32)">
            <summary>
            Looks ahead to see whether the next field in the stream is what we expect
            (typically; what we've just finished reading - for example ot read successive list items)
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.Model">
            <summary>
            Get the TypeModel associated with this reader
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.Hint(ProtoBuf.WireType)">
            <summary>
            Compares the streams current wire-type to the hinted wire-type, updating the reader if necessary; for example,
            a Variant may be updated to SignedVariant. If the hinted wire-type is unrelated then no change is made.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.Assert(ProtoBuf.WireType)">
            <summary>
            Verifies that the stream's current wire-type is as expected, or a specialized sub-type (for example,
            SignedVariant) - in which case the current wire-type is updated. Otherwise an exception is thrown.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.SkipField">
            <summary>
            Discards the data for the current field.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadUInt64">
            <summary>
            Reads an unsigned 64-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadSingle">
            <summary>
            Reads a single-precision number from the stream; supported wire-types: Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadBoolean">
            <summary>
            Reads a boolean value from the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.AppendBytes(System.Byte[],ProtoBuf.ProtoReader)">
            <summary>
            Reads a byte-sequence from the stream, appending them to an existing byte-sequence (which can be null); supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadLengthPrefix(System.IO.Stream,System.Boolean,ProtoBuf.PrefixStyle,System.Int32@)">
            <summary>
            Reads the length-prefix of a message from a stream without buffering additional data, allowing a fixed-length
            reader to be created.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.DirectReadLittleEndianInt32(System.IO.Stream)">
            <summary>
            Reads a little-endian encoded integer. An exception is thrown if the data is not all available.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.DirectReadBigEndianInt32(System.IO.Stream)">
            <summary>
            Reads a big-endian encoded integer. An exception is thrown if the data is not all available.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.DirectReadVarintInt32(System.IO.Stream)">
            <summary>
            Reads a varint encoded integer. An exception is thrown if the data is not all available.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.DirectReadBytes(System.IO.Stream,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a string (of a given lenth, in bytes) directly from the source into a pre-existing buffer. An exception is thrown if the data is not all available.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.DirectReadBytes(System.IO.Stream,System.Int32)">
            <summary>
            Reads a given number of bytes directly from the source. An exception is thrown if the data is not all available.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.DirectReadString(System.IO.Stream,System.Int32)">
            <summary>
            Reads a string (of a given lenth, in bytes) directly from the source. An exception is thrown if the data is not all available.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadLengthPrefix(System.IO.Stream,System.Boolean,ProtoBuf.PrefixStyle,System.Int32@,System.Int32@)">
            <summary>
            Reads the length-prefix of a message from a stream without buffering additional data, allowing a fixed-length
            reader to be created.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadLongLengthPrefix(System.IO.Stream,System.Boolean,ProtoBuf.PrefixStyle,System.Int32@,System.Int32@)">
            <summary>
            Reads the length-prefix of a message from a stream without buffering additional data, allowing a fixed-length
            reader to be created.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.TryReadUInt64Varint(System.IO.Stream,System.UInt64@)">
            <summary>Read a varint if possible</summary>
            <returns>The number of bytes consumed; 0 if no data available</returns>
        </member>
        <member name="M:ProtoBuf.ProtoReader.AppendExtensionData(ProtoBuf.IExtensible)">
            <summary>
            Copies the current field into the instance as extension data
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.HasSubValue(ProtoBuf.WireType,ProtoBuf.ProtoReader)">
            <summary>
            Indicates whether the reader still has data remaining in the current sub-item,
            additionally setting the wire-type for the next field if there is more data.
            This is used when decoding packed data.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadType">
            <summary>
            Reads a Type from the stream, using the model's DynamicTypeFormatting if appropriate; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.Merge(ProtoBuf.ProtoReader,System.Object,System.Object)">
            <summary>
            Merge two objects using the details from the current reader; this is used to change the type
            of objects when an inheritance relationship is discovered later than usual during deserilazation.
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoReader.State">
            <summary>
            Holds state used by the deserializer
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.Create(System.Buffers.ReadOnlySequence{System.Byte},ProtoBuf.Meta.TypeModel,System.Object)">
            <summary>
            Creates a new reader against a multi-segment buffer
            </summary>
            <param name="source">The source buffer</param>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to deserialize sub-objects</param>
            <param name="userState">Additional context about this serialization operation</param>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.Create(System.ReadOnlyMemory{System.Byte},ProtoBuf.Meta.TypeModel,System.Object)">
            <summary>
            Creates a new reader against a multi-segment buffer
            </summary>
            <param name="source">The source buffer</param>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to deserialize sub-objects</param>
            <param name="userState">Additional context about this serialization operation</param>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.Dispose">
            <summary>
            Release any resources associated with this instance
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadUInt16">
            <summary>
            Reads an unsigned 16-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadInt16">
            <summary>
            Reads a signed 16-bit integer from the stream: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.GetPosition">
            <summary>
            Returns the position of the current reader (note that this is not necessarily the same as the position
            in the underlying stream, if multiple readers are used on the same stream)
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadByte">
            <summary>
            Reads an unsigned 8-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadSByte">
            <summary>
            Reads a signed 8-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadIntPtr">
            <summary>
            Reads a native integer from the stream; if the value exceeds the native width, an error will occur; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadUIntPtr">
            <summary>
            Reads a native integer from the stream; if the value exceeds the native width, an error will occur; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadUInt32">
            <summary>
            Reads an unsigned 32-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadInt32">
            <summary>
            Reads a signed 32-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadInt64">
            <summary>
            Reads a signed 64-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadDouble">
            <summary>
            Reads a double-precision number from the stream; supported wire-types: Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadSingle">
            <summary>
            Reads a single-precision number from the stream; supported wire-types: Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadBoolean">
            <summary>
            Reads a boolean value from the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadUInt64">
            <summary>
            Reads an unsigned 64-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.AppendBytes(System.Byte[])">
            <summary>
            Reads a byte-sequence from the stream, appending them to an existing byte-sequence (which can be null); supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.AppendBytes(System.ReadOnlyMemory{System.Byte})">
            <summary>
            Reads a byte-sequence from the stream, appending them to an existing byte-sequence; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.AppendBytes(System.Memory{System.Byte})">
            <summary>
            Reads a byte-sequence from the stream, appending them to an existing byte-sequence; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.AppendBytes(System.ArraySegment{System.Byte})">
            <summary>
            Reads a byte-sequence from the stream, appending them to an existing byte-sequence; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.AppendBytes``1(``0,ProtoBuf.Serializers.IMemoryConverter{``0,System.Byte})">
            <summary>
            Reads a byte-sequence from the stream, appending them to an existing byte-sequence (which can be null); supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadBytes(System.Span{System.Byte})">
            <summary>
            Tries to read a string-like type directly into a span; if successful, the span
            returned indicates the available amount of data; if unsuccessful, an exception
            is thrown; this should only be used when there is confidence that the length
            is bounded.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadBytes(System.Span{System.Byte},System.Int32@)">
            <summary>
            Tries to read a string-like type directly into a span; if successful, the span
            returned indicates the available amount of data; if unsuccessful, an exception
            is thrown; this should only be used when there is confidence that the length
            is bounded.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.StartSubItem">
            <summary>
            Begins consuming a nested message in the stream; supported wire-types: StartGroup, String
            </summary>
            <remarks>The token returned must be help and used when callining EndSubItem</remarks>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.EndSubItem(ProtoBuf.SubItemToken)">
            <summary>
            Makes the end of consuming a nested message in the stream; the stream must be either at the correct EndGroup
            marker, or all fields of the sub-message must have been consumed (in either case, this means ReadFieldHeader
            should return zero)
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadObject(System.Object,System.Type)">
            <summary>
            Reads (merges) a sub-message from the stream, internally calling StartSubItem and EndSubItem, and (in between)
            parsing the message in accordance with the model associated with the reader
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadString(ProtoBuf.StringMap)">
            <summary>
            Reads a string from the stream (using UTF8); supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.Assert(ProtoBuf.WireType)">
            <summary>
            Verifies that the stream's current wire-type is as expected, or a specialized sub-type (for example,
            SignedVariant) - in which case the current wire-type is updated. Otherwise an exception is thrown.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.SkipField">
            <summary>
            Discards the data for the current field.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadFieldHeader">
            <summary>
            Reads a field header from the stream, setting the wire-type and retuning the field number. If no
            more fields are available, then 0 is returned. This methods respects sub-messages.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.TryReadFieldHeader(System.Int32)">
            <summary>
            Looks ahead to see whether the next field in the stream is what we expect
            (typically; what we've just finished reading - for example ot read successive list items)
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.Hint(ProtoBuf.WireType)">
            <summary>
            Compares the streams current wire-type to the hinted wire-type, updating the reader if necessary; for example,
            a Variant may be updated to SignedVariant. If the hinted wire-type is unrelated then no change is made.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ThrowEnumException(System.Type,System.Int32)">
            <summary>
            Throws an exception indication that the given value cannot be mapped to an enum.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.AppendExtensionData(ProtoBuf.IExtensible)">
            <summary>
            Copies the current field into the instance as extension data
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.AppendExtensionData(ProtoBuf.ITypedExtensible,System.Type)">
            <summary>
            Copies the current field into the instance as extension data
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.State.WireType">
            <summary>
            Indicates the underlying proto serialization format on the wire.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.State.InternStrings">
            <summary>
            Gets / sets a flag indicating whether strings should be checked for repetition; if
            true, any repeated UTF-8 byte sequence will result in the same String instance, rather
            than a second instance of the same string. Disabled by default. Note that this uses
            a <i>custom</i> interner - the system-wide string interner is not used.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.State.FieldNumber">
            <summary>
            Gets the number of the field being processed.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadType">
            <summary>
            Reads a Type from the stream, using the model's DynamicTypeFormatting if appropriate; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadMessage``1(``0)">
            <summary>
            Reads a sub-item from the input reader
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadMessage``1(ProtoBuf.Serializers.SerializerFeatures,``0,ProtoBuf.Serializers.ISerializer{``0})">
            <summary>
            Reads a sub-item from the input reader
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadMessage``2(ProtoBuf.Serializers.SerializerFeatures,``1,``0@)">
            <summary>
            Reads a sub-item from the input reader
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadAny``1(``0)">
            <summary>
            Reads a value or sub-item from the input reader
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadAny``1(ProtoBuf.Serializers.SerializerFeatures,``0,ProtoBuf.Serializers.ISerializer{``0})">
            <summary>
            Reads a value or sub-item from the input reader
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadWrapped``1(ProtoBuf.Serializers.SerializerFeatures,``0,ProtoBuf.Serializers.ISerializer{``0})">
            <summary>
            Read a value or sub-item with an additional level of message wrapping, that can be used to express <c>null</c> values of arbitrary types (as field 1)
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.GetSerializer``1">
            <summary>
            Gets the serializer associated with a specific type
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.ReadBaseType``2(``1,ProtoBuf.Serializers.ISubTypeSerializer{``0})">
            <summary>
            Reads a sub-item from the input reader
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.DeserializeRoot``1(``0,ProtoBuf.Serializers.ISerializer{``0})">
            <summary>
            Deserialize an instance of the provided type
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.State.Context">
            <summary>
            Gets the serialization context associated with this instance;
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.HasSubValue(ProtoBuf.WireType)">
            <summary>
            Indicates whether the reader still has data remaining in the current sub-item,
            additionally setting the wire-type for the next field if there is more data.
            This is used when decoding packed data.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.CreateInstance``1(ProtoBuf.Serializers.ISerializer{``0})">
            <summary>
            Create an instance of the provided type, respecting any custom factory rules
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.State.Create(System.IO.Stream,ProtoBuf.Meta.TypeModel,System.Object,System.Int64)">
            <summary>
            Creates a new reader against a stream
            </summary>
            <param name="source">The source stream</param>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to deserialize sub-objects</param>
            <param name="userState">Additional context about this serialization operation</param>
            <param name="length">The number of bytes to read, or -1 to read until the end of the stream</param>
        </member>
        <member name="M:ProtoBuf.ProtoReader.DefaultState">
            <summary>
            Get the default state associated with this reader
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.Create(System.IO.Stream,ProtoBuf.Meta.TypeModel,ProtoBuf.SerializationContext,System.Int64)">
            <summary>
            Creates a new reader against a stream
            </summary>
            <param name="source">The source stream</param>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to deserialize sub-objects</param>
            <param name="context">Additional context about this serialization operation</param>
            <param name="length">The number of bytes to read, or -1 to read until the end of the stream</param>
        </member>
        <member name="M:ProtoBuf.ProtoReader.StreamProtoReader.#ctor(System.IO.Stream,ProtoBuf.Meta.TypeModel,ProtoBuf.SerializationContext,System.Int32)">
            <summary>
            Creates a new reader against a stream
            </summary>
            <param name="source">The source stream</param>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to deserialize sub-objects</param>
            <param name="context">Additional context about this serialization operation</param>
            <param name="length">The number of bytes to read, or -1 to read until the end of the stream</param>
        </member>
        <member name="M:ProtoBuf.ProtoReader.StreamProtoReader.#ctor(System.IO.Stream,ProtoBuf.Meta.TypeModel,ProtoBuf.SerializationContext,System.Int64)">
            <summary>
            Creates a new reader against a stream
            </summary>
            <param name="source">The source stream</param>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to deserialize sub-objects</param>
            <param name="context">Additional context about this serialization operation</param>
            <param name="length">The number of bytes to read, or -1 to read until the end of the stream</param>
        </member>
        <member name="M:ProtoBuf.ProtoReader.StreamProtoReader.#ctor(System.IO.Stream,ProtoBuf.Meta.TypeModel,ProtoBuf.SerializationContext)">
            <summary>
            Creates a new reader against a stream
            </summary>
            <param name="source">The source stream</param>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to deserialize sub-objects</param>
            <param name="context">Additional context about this serialization operation</param>
        </member>
        <member name="T:ProtoBuf.ProtoReservedAttribute">
            <summary>
            Indicates a reserved field or range
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReservedAttribute.From">
            <summary>
            The start of a numeric field range
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReservedAttribute.To">
            <summary>
            The end of a numeric field range
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReservedAttribute.Name">
            <summary>
            A named field reservation
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReservedAttribute.#ctor(System.Int32,System.String)">
            <summary>
            Creates a new instance of a single number field reservation
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReservedAttribute.#ctor(System.Int32,System.Int32,System.String)">
            <summary>
            Creates a new instance of a range number field reservation
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReservedAttribute.Comment">
            <summary>
            Records a comment explaining this reservation
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReservedAttribute.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of a named field reservation
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoWriter">
            <summary>
            <para>Represents an output stream for writing protobuf data.</para>
            <para>
            Why is the API backwards (static methods with writer arguments)?
            See: http://marcgravell.blogspot.com/2010/03/last-will-be-first-and-first-will-be.html
            </para>
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoWriter.State">
            <summary>
            Writer state
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.Create(System.Buffers.IBufferWriter{System.Byte},ProtoBuf.Meta.TypeModel,System.Object)">
            <summary>
            Create a new ProtoWriter that targets a buffer writer
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.Flush">
            <summary>
            Writes any uncommitted data to the output
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteString(System.Int32,System.String,ProtoBuf.StringMap)">
            <summary>
            Writes a string to the stream
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteString(System.String,ProtoBuf.StringMap)">
            <summary>
            Writes a string to the stream; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteType(System.Type)">
            <summary>
            Writes a Type to the stream, using the model's DynamicTypeFormatting if appropriate; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteFieldHeader(System.Int32,ProtoBuf.WireType)">
            <summary>
            Writes a field-header, indicating the format of the next data we plan to write.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteInt32Varint(System.Int32,System.Int32)">
            <summary>
            Writes a signed 32-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteInt32(System.Int32)">
            <summary>
            Writes a signed 32-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteSByte(System.SByte)">
            <summary>
            Writes a signed 8-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteInt16(System.Int16)">
            <summary>
            Writes a signed 16-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteUInt16(System.UInt16)">
            <summary>
            Writes an unsigned 16-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteByte(System.Byte)">
            <summary>
            Writes an unsigned 8-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteBoolean(System.Boolean)">
            <summary>
            Writes a boolean to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteIntPtr(System.IntPtr)">
            <summary>
            Writes a native integer from the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteUIntPtr(System.UIntPtr)">
            <summary>
            Writes a native integer from the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteUInt32(System.UInt32)">
            <summary>
            Writes an unsigned 16-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteDouble(System.Double)">
            <summary>
            Writes a double-precision number to the stream; supported wire-types: Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteSingle(System.Single)">
            <summary>
            Writes a single-precision number to the stream; supported wire-types: Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteInt64(System.Int64)">
            <summary>
            Writes a signed 64-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteUInt64(System.UInt64)">
            <summary>
            Writes an unsigned 64-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteMessage``1(ProtoBuf.Serializers.SerializerFeatures,``0,ProtoBuf.Serializers.ISerializer{``0})">
            <summary>
            Writes a sub-item to the writer
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteMessage``1(System.Int32,ProtoBuf.Serializers.SerializerFeatures,``0,ProtoBuf.Serializers.ISerializer{``0})">
            <summary>
            Writes a sub-item to the writer
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteGroup``1(System.Int32,ProtoBuf.Serializers.SerializerFeatures,``0,ProtoBuf.Serializers.ISerializer{``0})">
            <summary>
            Writes a sub-item to the writer
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteAny``1(System.Int32,``0,ProtoBuf.Serializers.ISerializer{``0})">
            <summary>
            Writes a value or sub-item to the writer
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteWrapped``1(System.Int32,ProtoBuf.Serializers.SerializerFeatures,``0,ProtoBuf.Serializers.ISerializer{``0})">
            <summary>
            Write a value or sub-item with an additional level of message wrapping, that can be used to express <c>null</c> values of arbitrary types (as field 1)
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteAny``1(System.Int32,ProtoBuf.Serializers.SerializerFeatures,``0,ProtoBuf.Serializers.ISerializer{``0})">
            <summary>
            Writes a value or sub-item to the writer
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteSubType``1(``0,ProtoBuf.Serializers.ISubTypeSerializer{``0})">
            <summary>
            Writes a sub-type to the input writer
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteSubType``1(System.Int32,``0,ProtoBuf.Serializers.ISubTypeSerializer{``0})">
            <summary>
            Writes a sub-type to the input writer
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteBaseType``1(``0,ProtoBuf.Serializers.ISubTypeSerializer{``0})">
            <summary>
            Writes a base-type to the input writer
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.GetSerializer``1">
            <summary>
            Gets the serializer associated with a specific type
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoWriter.State.Context">
            <summary>
            The serialization context associated with this instance
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteBytes(System.Buffers.ReadOnlySequence{System.Byte})">
            <summary>
            Writes a byte-array to the stream; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteBytes(System.ArraySegment{System.Byte})">
            <summary>
            Writes a byte-array to the stream; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteBytes(System.Byte[])">
            <summary>
            Writes a byte-array to the stream; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteBytes``1(``0,ProtoBuf.Serializers.IMemoryConverter{``0,System.Byte})">
            <summary>
            Writes a binary chunk to the stream; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteBytes(System.Memory{System.Byte})">
            <summary>
            Writes a binary chunk to the stream; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteBytes(System.ReadOnlyMemory{System.Byte})">
            <summary>
            Writes a binary chunk to the stream; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WriteBytes(System.ReadOnlySpan{System.Byte})">
            <summary>
            Writes a binary chunk to the stream; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.SerializeRoot``1(``0,ProtoBuf.Serializers.ISerializer{``0})">
            <summary>
            Writes an object to the input writer as a root value; if the
            object is determined to be a scalar, it is written as though it were
            part of a message with field-number 1
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.Abandon">
            <summary>
            Abandon any pending unflushed data
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.WritePackedPrefix(System.Int32,ProtoBuf.WireType)">
            <summary>
            Used for packed encoding; writes the length prefix using fixed sizes rather than using
            buffering. Only valid for fixed-32 and fixed-64 encoding.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.StartSubItem(System.Object)">
            <summary>
            Indicates the start of a nested record.
            </summary>
            <param name="instance">The instance to write.</param>
            <returns>A token representing the state of the stream; this token is given to EndSubItem.</returns>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.Dispose">
            <summary>
            Releases any resources associated with this instance
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.Close">
            <summary>
            Flushes data to the underlying stream, and releases any resources. The underlying stream is *not* disposed
            by this operation.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.EndSubItem(ProtoBuf.SubItemToken)">
            <summary>
            Indicates the end of a nested record.
            </summary>
            <param name="token">The token obtained from StartubItem.</param>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.AppendExtensionData(ProtoBuf.IExtensible)">
            <summary>
            Copies any extension data stored for the instance to the underlying stream
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.AppendExtensionData(ProtoBuf.ITypedExtensible,System.Type)">
            <summary>
            Copies any extension data stored for the instance to the underlying stream
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.SetPackedField(System.Int32)">
            <summary>
            Used for packed encoding; indicates that the next field should be skipped rather than
            a field header written. Note that the field number must match, else an exception is thrown
            when the attempt is made to write the (incorrect) field. The wire-type is taken from the
            subsequent call to WriteFieldHeader. Only primitive types can be packed.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.ClearPackedField(System.Int32)">
            <summary>
            Used for packed encoding; explicitly reset the packed field marker; this is not required
            if using StartSubItem/EndSubItem
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.ThrowEnumException(System.Object)">
            <summary>
            Throws an exception indicating that the given enum cannot be mapped to a serialized value.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.State.Create(System.IO.Stream,ProtoBuf.Meta.TypeModel,System.Object)">
            <summary>
            Creates a new writer against a stream
            </summary>
            <param name="dest">The destination stream</param>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to serialize sub-objects</param>
            <param name="userState">Additional context about this serialization operation</param>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteFieldHeader(System.Int32,ProtoBuf.WireType,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a field-header, indicating the format of the next data we plan to write.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteBytes(System.Byte[],ProtoBuf.ProtoWriter)">
            <summary>
            Writes a byte-array to the stream; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteBytes(System.Byte[],System.Int32,System.Int32,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a byte-array to the stream; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.StartSubItem(System.Object,ProtoBuf.ProtoWriter)">
            <summary>
            Indicates the start of a nested record.
            </summary>
            <param name="instance">The instance to write.</param>
            <param name="writer">The destination.</param>
            <returns>A token representing the state of the stream; this token is given to EndSubItem.</returns>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.EndSubItem(ProtoBuf.SubItemToken,ProtoBuf.ProtoWriter)">
            <summary>
            Indicates the end of a nested record.
            </summary>
            <param name="token">The token obtained from StartSubItem.</param>
            <param name="writer">The destination.</param>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.Init(ProtoBuf.Meta.TypeModel,System.Object,System.Boolean)">
            <summary>
            Creates a new writer against a stream
            </summary>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to serialize sub-objects</param>
            <param name="userState">Additional context about this serialization operation</param>
            <param name="impactCount">Whether this initialization should impact usage counters (to check for double-usage)</param>
        </member>
        <member name="P:ProtoBuf.ProtoWriter.Context">
            <summary>
            Addition information about this serialization operation.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoWriter.UserState">
            <summary>
            Addition information about this serialization operation.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteMessage``1(ProtoBuf.ProtoWriter.State@,``0,ProtoBuf.Serializers.ISerializer{``0},ProtoBuf.PrefixStyle,System.Boolean)">
            <summary>
            Writes a sub-item to the input writer
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteSubType``1(ProtoBuf.ProtoWriter.State@,``0,ProtoBuf.Serializers.ISubTypeSerializer{``0})">
            <summary>
            Writes a sub-item to the input writer
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.Abandon">
            <summary>
            Abandon any pending unflushed data
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.Close">
            <summary>
            Flushes data to the underlying stream, and releases any resources. The underlying stream is *not* disposed
            by this operation.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoWriter.Model">
            <summary>
            Get the TypeModel associated with this writer
            </summary>
        </member>
        <member name="F:ProtoBuf.ProtoWriter.UTF8">
            <summary>
            The encoding used by the writer
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteString(System.String,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a string to the stream; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.TryFlush(ProtoBuf.ProtoWriter.State@)">
            <summary>
            Writes any buffered data (if possible) to the underlying stream.
            </summary>
            <param name="state">writer state</param>
            <remarks>It is not always possible to fully flush, since some sequences
            may require values to be back-filled into the byte-stream.</remarks>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteUInt64(System.UInt64,ProtoBuf.ProtoWriter)">
            <summary>
            Writes an unsigned 64-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteInt64(System.Int64,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a signed 64-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteUInt32(System.UInt32,ProtoBuf.ProtoWriter)">
            <summary>
            Writes an unsigned 16-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteInt16(System.Int16,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a signed 16-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteUInt16(System.UInt16,ProtoBuf.ProtoWriter)">
            <summary>
            Writes an unsigned 16-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteByte(System.Byte,ProtoBuf.ProtoWriter)">
            <summary>
            Writes an unsigned 8-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteSByte(System.SByte,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a signed 8-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteInt32(System.Int32,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a signed 32-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteDouble(System.Double,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a double-precision number to the stream; supported wire-types: Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteSingle(System.Single,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a single-precision number to the stream; supported wire-types: Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.ThrowEnumException(ProtoBuf.ProtoWriter,System.Object)">
            <summary>
            Throws an exception indicating that the given enum cannot be mapped to a serialized value.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteBoolean(System.Boolean,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a boolean to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.AppendExtensionData(ProtoBuf.IExtensible,ProtoBuf.ProtoWriter)">
            <summary>
            Copies any extension data stored for the instance to the underlying stream
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.SetPackedField(System.Int32,ProtoBuf.ProtoWriter)">
            <summary>
            Used for packed encoding; indicates that the next field should be skipped rather than
            a field header written. Note that the field number must match, else an exception is thrown
            when the attempt is made to write the (incorrect) field. The wire-type is taken from the
            subsequent call to WriteFieldHeader. Only primitive types can be packed.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.ClearPackedField(System.Int32,ProtoBuf.ProtoWriter)">
            <summary>
            Used for packed encoding; explicitly reset the packed field marker; this is not required
            if using StartSubItem/EndSubItem
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WritePackedPrefix(System.Int32,ProtoBuf.WireType,ProtoBuf.ProtoWriter)">
            <summary>
            Used for packed encoding; writes the length prefix using fixed sizes rather than using
            buffering. Only valid for fixed-32 and fixed-64 encoding.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoWriter.BufferSize">
            <summary>
            Buffer size to use when writing; if non-positive, an internal default is used.
            </summary>
            <remarks>Not all writer implementations make use of this API</remarks>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteType(System.Type,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a Type to the stream, using the model's DynamicTypeFormatting if appropriate; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.DefaultState">
            <summary>
            Gets the default state associated with this writer
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.Create(System.IO.Stream,ProtoBuf.Meta.TypeModel,ProtoBuf.SerializationContext)">
            <summary>
            Creates a new writer against a stream
            </summary>
            <param name="dest">The destination stream</param>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to serialize sub-objects</param>
            <param name="context">Additional context about this serialization operation</param>
        </member>
        <member name="T:ProtoBuf.SerializationContext">
            <summary>
            Additional information about a serialization operation
            </summary>
        </member>
        <member name="P:ProtoBuf.SerializationContext.Context">
            <summary>
            Gets or sets a user-defined object containing additional information about this serialization/deserialization operation.
            </summary>
        </member>
        <member name="P:ProtoBuf.SerializationContext.Default">
            <summary>
            A default SerializationContext, with minimal information.
            </summary>
        </member>
        <member name="P:ProtoBuf.SerializationContext.State">
            <summary>
            Gets or sets the source or destination of the transmitted data.
            </summary>
        </member>
        <member name="M:ProtoBuf.SerializationContext.op_Implicit(ProtoBuf.SerializationContext)~System.Runtime.Serialization.StreamingContext">
            <summary>
            Convert a SerializationContext to a StreamingContext
            </summary>
        </member>
        <member name="M:ProtoBuf.SerializationContext.op_Implicit(System.Runtime.Serialization.StreamingContext)~ProtoBuf.SerializationContext">
            <summary>
            Convert a StreamingContext to a SerializationContext
            </summary>
        </member>
        <member name="M:ProtoBuf.SerializationContext.AsStreamingContext(ProtoBuf.ISerializationContext)">
            <summary>
            Create a StreamingContext from a serialization context
            </summary>
        </member>
        <member name="M:ProtoBuf.SerializationContext.AsSerializationContext(ProtoBuf.ISerializationContext)">
            <summary>
            Creates a frozen SerializationContext from a serialization context
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializers.EnumSerializer">
            <summary>
            Provides utility methods for creating enum serializers
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.EnumSerializer.CreateSByte``1">
            <summary>
            Create an enum serializer for the provided type, which much be a matching enum
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.EnumSerializer.CreateInt16``1">
            <summary>
            Create an enum serializer for the provided type, which much be a matching enum
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.EnumSerializer.CreateInt32``1">
            <summary>
            Create an enum serializer for the provided type, which much be a matching enum
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.EnumSerializer.CreateInt64``1">
            <summary>
            Create an enum serializer for the provided type, which much be a matching enum
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.EnumSerializer.CreateByte``1">
            <summary>
            Create an enum serializer for the provided type, which much be a matching enum
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.EnumSerializer.CreateUInt16``1">
            <summary>
            Create an enum serializer for the provided type, which much be a matching enum
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.EnumSerializer.CreateUInt32``1">
            <summary>
            Create an enum serializer for the provided type, which much be a matching enum
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.EnumSerializer.CreateUInt64``1">
            <summary>
            Create an enum serializer for the provided type, which much be a matching enum
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializers.EnumSerializer`1">
            <summary>
            Base type for enum serializers
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.EnumSerializer`1.Read(ProtoBuf.ProtoReader.State@,`0)">
            <summary>
            Deserialize an enum
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.EnumSerializer`1.Write(ProtoBuf.ProtoWriter.State@,`0)">
            <summary>
            Serialize an enum
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializers.IMemoryConverter`2">
            <summary>
            Provides an abstract way of referring to simple range-based
            data types as Memory<typeparamref name="TElement"/>.
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.IMemoryConverter`2.NonNull(`0@)">
            <summary>
            Provides a non-null value from the provided storage.
            For many value-types, this will simply return the input value. For
            reference-types, the input should be null-coalesced against an
            empty value such as Array.Empty<typeparamref name="TStorage"/>().
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.IMemoryConverter`2.GetLength(`0@)">
            <summary>
            Get the length (in terms of element count) of the provided storage.
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.IMemoryConverter`2.GetMemory(`0@)">
            <summary>
            Access a Memory<typeparamref name="TElement"/> that is the underlying
            data held by this storage.
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.IMemoryConverter`2.Expand(ProtoBuf.ISerializationContext,`0@,System.Int32)">
            <summary>
            Resizes (typically: allocates and copies) the provided storage by
            the requested additional capacity, returning a memory to *just
            the additional portion*). The implementor is responsible for
            ensuring that the old values are copied if necessary.
            The implementor may choose to recycle the old storage, if
            appropriate.
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializers.DefaultMemoryConverter`1">
            <summary>
            Provides a memory converter implementation for many common storage kinds.
            </summary>
        </member>
        <member name="P:ProtoBuf.Serializers.DefaultMemoryConverter`1.Instance">
            <summary>
            Provides the singleton instance for element type <typeparamref name="T"/>.
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializers.SerializerFeatures">
            <summary>
            Indicates capabilities and behaviors of a serializer
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.WireTypeVarint">
            <summary>
            Base-128 variable-length encoding
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.WireTypeFixed64">
            <summary>
            Fixed-length 8-byte encoding
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.WireTypeString">
            <summary>
            Length-variant-prefixed encoding
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.WireTypeStartGroup">
            <summary>
            Indicates the start of a group
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.WireTypeFixed32">
            <summary>
            Fixed-length 4-byte encoding
            </summary>10
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.WireTypeSignedVarint">
            <summary>
            Denotes a varint that should be interpreted using
            zig-zag semantics (so -ve numbers aren't a significant overhead)
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.WireTypeSpecified">
            <summary>
            Indicates that the wire-type has been explicitly specified
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.CategoryRepeated">
            <summary>
            Indicates that this data should be treated like a list/array
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.CategoryScalar">
            <summary>
            Scalars are simple types such as integers, not messages; when written as
            a root message, a field-one wrapper is added
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.CategoryMessage">
            <summary>
            Indicates a type that is a message
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.CategoryMessageWrappedAtRoot">
            <summary>
            Indicates a type that is both "message" and "scalar"; *at the root only* it will be a message wrapped like a scalar; otherwise, it is
            treated as a message; see: DateTime/TimeSpan
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.OptionPackedDisabled">
            <summary>
            Explicitly disables packed encoding; normally, packed encoding is
            used by default when appropriate
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.OptionClearCollection">
            <summary>
            List-like values should clear any existing contents before adding new
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.OptionFailOnDuplicateKey">
            <summary>
            Maps should use dictionary Add rather than overwrite; this means that duplicate keys will cause failure
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.OptionSkipRecursionCheck">
            <summary>
            Disable recursion checking
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.OptionWrappedValue">
            <summary>
            Adds an additional message when writing scalar values or collection elements; the inner field
            number is always <c>1</c>, as per <c>wrappers.proto</c>
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.OptionWrappedValueGroup">
            <summary>
            When using <see cref="F:ProtoBuf.Serializers.SerializerFeatures.OptionWrappedValue"/>, specifies that the wrapper message should be written using group semantics (rather than length-prefix semantics)
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.OptionWrappedValueFieldPresence">
            <summary>
            When using <see cref="F:ProtoBuf.Serializers.SerializerFeatures.OptionWrappedValue"/>, specifies that the field should be written using field-presence
            rules (rather than implicit-zero rules, as per <c>wrappers.proto</c>); when specified, the wrapper message
            is always written, and the inner field is only written if the value is non-null; when omitted, the wrapper
            message is only written if the value is not null, and the inner field is only written if the value
            is non-zero/empty; this flag is added automatically when serializing collection elements
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.OptionWrappedCollection">
            <summary>
            Adds a layer of writing, which only writes values if they are not null; the inner field number is always <c>1</c>, as per <c>wrappers.proto</c>
            </summary>
        </member>
        <member name="F:ProtoBuf.Serializers.SerializerFeatures.OptionWrappedCollectionGroup">
            <summary>
            When using <see cref="F:ProtoBuf.Serializers.SerializerFeatures.OptionWrappedCollectionGroup"/>, specifies that the wrapper message should be written using group semantics (rather than length-prefix semantics)
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializers.ISerializer`1">
            <summary>
            Abstract API capable of serializing/deserializing messages or values
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.ISerializer`1.Read(ProtoBuf.ProtoReader.State@,`0)">
            <summary>
            Deserialize an instance from the supplied writer
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.ISerializer`1.Write(ProtoBuf.ProtoWriter.State@,`0)">
            <summary>
            Serialize an instance to the supplied writer
            </summary>
        </member>
        <member name="P:ProtoBuf.Serializers.ISerializer`1.Features">
            <summary>
            Indicates the features (including the default wire-type) for this type/serializer
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializers.ISerializerProxy`1">
            <summary>
            Provides indirect access to a serializer for a given type
            </summary>
        </member>
        <member name="P:ProtoBuf.Serializers.ISerializerProxy`1.Serializer">
            <summary>
            Gets the actual serializer for the type
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializers.IMeasuringSerializer`1">
            <summary>
            Abstract API capable of measuring values without writing them
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.IMeasuringSerializer`1.Measure(ProtoBuf.ISerializationContext,ProtoBuf.WireType,`0)">
            <summary>
            Measure the given value, reporting the required length for the payload (not including the field-header)
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializers.IRepeatedSerializer`1">
            <summary>
            Abstract API capable of serializing/deserializing a sequence of messages or values
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.IRepeatedSerializer`1.WriteRepeated(ProtoBuf.ProtoWriter.State@,System.Int32,ProtoBuf.Serializers.SerializerFeatures,`0)">
            <summary>
            Serialize a sequence of values to the supplied writer
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.IRepeatedSerializer`1.ReadRepeated(ProtoBuf.ProtoReader.State@,ProtoBuf.Serializers.SerializerFeatures,`0)">
            <summary>
            Deserializes a sequence of values from the supplied reader
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializers.IObjectSerializer`1">
            <summary>
            A serializer capable of representing complex objects that may warrant length caching
            </summary>
        </member>
        <member name="P:ProtoBuf.Serializers.IObjectSerializer`1.BaseType">
            <summary>
            The effective <see cref="P:ProtoBuf.Serializers.IObjectSerializer`1.BaseType"/> that this serializer represents; in the case of
            an object hierarchy, this is the base-type.
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializers.ISubTypeSerializer`1">
            <summary>
            Abstract API capable of serializing/deserializing objects as part of a type hierarchy
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.ISubTypeSerializer`1.WriteSubType(ProtoBuf.ProtoWriter.State@,`0)">
            <summary>
            Serialize an instance to the supplied writer
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.ISubTypeSerializer`1.ReadSubType(ProtoBuf.ProtoReader.State@,ProtoBuf.Serializers.SubTypeState{`0})">
            <summary>
            Deserialize an instance from the supplied writer
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializers.SubTypeState`1">
            <summary>
            Represents the state of an inheritance deserialization operation
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.SubTypeState`1.Create``1(ProtoBuf.ISerializationContext,``0)">
            <summary>
            Create a new value, using the provided concrete type if a new instance is required
            </summary>
        </member>
        <member name="P:ProtoBuf.Serializers.SubTypeState`1.Value">
            <summary>
            Gets or sets the current instance represented
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.SubTypeState`1.CreateIfNeeded">
            <summary>
            Ensures that the instance has a value
            </summary>
        </member>
        <member name="P:ProtoBuf.Serializers.SubTypeState`1.HasValue">
            <summary>
            Indicates whether an instance currently exists
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.SubTypeState`1.ReadSubType``1(ProtoBuf.ProtoReader.State@,ProtoBuf.Serializers.ISubTypeSerializer{``0})">
            <summary>
            Parse the input as a sub-type of the instance
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.SubTypeState`1.OnBeforeDeserialize(System.Action{`0,ProtoBuf.ISerializationContext})">
            <summary>
            Specifies a serialization callback to be used when the item is constructed; if the item already exists, the callback is executed immediately
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializers.IFactory`1">
            <summary>
            Abstract API capable of serializing/deserializing complex objects with inheritance
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.IFactory`1.Create(ProtoBuf.ISerializationContext)">
            <summary>
            Create a new instance of the type
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializers.MapSerializer">
            <summary>
            Provides utility methods for creating serializers for repeated data
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.MapSerializer.CreateConcurrentDictionary``3">
            <summary>Create a map serializer that operates on concurrent dictionaries</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.MapSerializer.CreateDictionary``2">
            <summary>Create a map serializer that operates on dictionaries</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.MapSerializer.CreateDictionary``3">
            <summary>Create a map serializer that operates on dictionaries</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.MapSerializer.CreateIReadOnlyDictionary``2">
            <summary>Create a map serializer that operates on dictionaries</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.MapSerializer.CreateImmutableDictionary``2">
            <summary>Create a map serializer that operates on immutable dictionaries</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.MapSerializer.CreateImmutableSortedDictionary``2">
            <summary>Create a map serializer that operates on immutable dictionaries</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.MapSerializer.CreateIImmutableDictionary``2">
            <summary>Create a map serializer that operates on immutable dictionaries</summary>
        </member>
        <member name="T:ProtoBuf.Serializers.MapSerializer`3">
            <summary>
            Base class for dictionary-like collection serializers
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.MapSerializer`3.WriteMap(ProtoBuf.ProtoWriter.State@,System.Int32,ProtoBuf.Serializers.SerializerFeatures,`0,ProtoBuf.Serializers.SerializerFeatures,ProtoBuf.Serializers.SerializerFeatures,ProtoBuf.Serializers.ISerializer{`1},ProtoBuf.Serializers.ISerializer{`2})">
            <summary>
            Deserializes a sequence of values from the supplied reader
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.MapSerializer`3.Initialize(`0,ProtoBuf.ISerializationContext)">
            <summary>Ensure that the collection is not nil, if required</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.MapSerializer`3.Clear(`0,ProtoBuf.ISerializationContext)">
            <summary>Remove any existing contents from the collection</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.MapSerializer`3.AddRange(`0,System.ArraySegment{System.Collections.Generic.KeyValuePair{`1,`2}}@,ProtoBuf.ISerializationContext)">
            <summary>Add new contents to the collection</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.MapSerializer`3.SetValues(`0,System.ArraySegment{System.Collections.Generic.KeyValuePair{`1,`2}}@,ProtoBuf.ISerializationContext)">
            <summary>Update the new contents intoto the collection, overwriting existing values</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.MapSerializer`3.ReadMap(ProtoBuf.ProtoReader.State@,ProtoBuf.Serializers.SerializerFeatures,`0,ProtoBuf.Serializers.SerializerFeatures,ProtoBuf.Serializers.SerializerFeatures,ProtoBuf.Serializers.ISerializer{`1},ProtoBuf.Serializers.ISerializer{`2})">
            <summary>
            Deserializes a sequence of values from the supplied reader
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializers.ExternalMapSerializer`3">
            <summary>
            ExternalMapSerializer provides a base class for concrete types to inherit from, but only provide the methods for collection management
            It does not require changes to internal protobuf-net state handling
            </summary>
            <typeparam name="TCollection">the collection type being provided (e.g. Map for F#) </typeparam>
            <typeparam name="TKey">key to the collection</typeparam>
            <typeparam name="TValue">type of the value held within the collection</typeparam>
        </member>
        <member name="T:ProtoBuf.Serializers.RepeatedSerializer">
            <summary>
            Provides utility methods for creating serializers for repeated data
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateConcurrentBag``2">
            <summary>Create a serializer that operates on immutable sets</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateConcurrentStack``2">
            <summary>Create a serializer that operates on immutable sets</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateConcurrentQueue``2">
            <summary>Create a serializer that operates on immutable sets</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateIProducerConsumerCollection``2">
            <summary>Create a serializer that operates on immutable sets</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateNestedDataNotSupported``2">
            <summary>Create a serializer that indicates that a scenario is not supported</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateNotSupported``2">
            <summary>Create a serializer that indicates that a scenario is not supported</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateList``1">
            <summary>Create a serializer that operates on lists</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateList``2">
            <summary>Create a serializer that operates on lists</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateEnumerable``2">
            <summary>Create a serializer that operates on most common collections</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateEnumerable``3">
            <summary>Create a serializer that operates on most common collections</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateVector``1">
            <summary>Create a serializer that operates on lists</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateQueue``2">
            <summary>Create a serializer that operates on lists</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateStack``2">
            <summary>Create a serializer that operates on lists</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.ReverseInPlace``1(System.ArraySegment{``0}@)">
            <summary>Reverses a range of values</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateImmutableArray``1">
            <summary>Create a serializer that operates on immutable arrays</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateImmutableList``1">
            <summary>Create a serializer that operates on immutable lists</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateImmutableIList``1">
            <summary>Create a serializer that operates on immutable lists</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateImmutableQueue``1">
            <summary>Create a serializer that operates on immutable queues</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateImmutableIQueue``1">
            <summary>Create a serializer that operates on immutable queues</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateImmutableStack``1">
            <summary>Create a serializer that operates on immutable stacks</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateImmutableIStack``1">
            <summary>Create a serializer that operates on immutable stacks</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateImmutableHashSet``1">
            <summary>Create a serializer that operates on immutable sets</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateImmutableSortedSet``1">
            <summary>Create a serializer that operates on immutable sets</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer.CreateImmutableISet``1">
            <summary>Create a serializer that operates on immutable sets</summary>
        </member>
        <member name="T:ProtoBuf.Serializers.RepeatedSerializer`2">
            <summary>
            Base class for simple collection serializers
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer`2.WriteRepeated(ProtoBuf.ProtoWriter.State@,System.Int32,ProtoBuf.Serializers.SerializerFeatures,`0,ProtoBuf.Serializers.ISerializer{`1})">
            <summary>
            Serialize a sequence of values to the supplied writer
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer`2.TryGetCount(`0)">
            <summary>If possible to do so *cheaply*, return the count of the items in the collection</summary>
            <remarks>TryGetCountDefault can be used as a reasonable fallback</remarks>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer`2.TryGetCountDefault(`0)">
            <summary>Applies a range of common strategies for cheaply counting collections</summary>
            <remarks>This involves multiple tests and exception handling; if your collection is known to be reliable, you should prefer an exposed .Count or similar</remarks>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer`2.ReadRepeated(ProtoBuf.ProtoReader.State@,ProtoBuf.Serializers.SerializerFeatures,`0,ProtoBuf.Serializers.ISerializer{`1})">
            <summary>
            Deserializes a sequence of values from the supplied reader
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer`2.Initialize(`0,ProtoBuf.ISerializationContext)">
            <summary>Ensure that the collection is not nil, if required</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer`2.Clear(`0,ProtoBuf.ISerializationContext)">
            <summary>Remove any existing contents from the collection</summary>
        </member>
        <member name="M:ProtoBuf.Serializers.RepeatedSerializer`2.AddRange(`0,System.ArraySegment{`1}@,ProtoBuf.ISerializationContext)">
            <summary>Add new contents to the collection</summary>
        </member>
        <member name="T:ProtoBuf.Serializers.ExternalSerializer`2">
            <summary>
            ExternalSerializer provides a base class for concrete types to inherit from, but only provide the methods for collection management
            It does not require changes to internal protobuf-net state handling
            </summary>
            <typeparam name="TCollection">the collection type being provided (e.g. Map for F#) </typeparam>
            <typeparam name="T">type of the value held within the collection</typeparam>
        </member>
        <member name="T:ProtoBuf.Serializers.SerializerCache">
            <summary>
            Provides access to cached serializers
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.SerializerCache.Get``2">
            <summary>
            Gets a cached serializer instance for a type, in the context of a given provider
            </summary>
        </member>
        <member name="T:ProtoBuf.StringMap">
            <summary>
            Not yet implemented
            </summary>
        </member>
        <member name="T:ProtoBuf.SubItemToken">
            <summary>
            Used to hold particulars relating to nested objects. This is opaque to the caller - simply
            give back the token you are given at the end of an object.
            </summary>
        </member>
        <member name="M:ProtoBuf.SubItemToken.ToString">
            <summary>
            See object.ToString()
            </summary>
        </member>
        <member name="M:ProtoBuf.SubItemToken.GetHashCode">
            <summary>
            See object.GetHashCode()
            </summary>
        </member>
        <member name="M:ProtoBuf.SubItemToken.Equals(System.Object)">
            <summary>
            See object.Equals()
            </summary>
        </member>
        <member name="T:ProtoBuf.TypedExtensible">
            <summary>
            Provides etension methods to access extended (unknown) fields against an instance
            </summary>
        </member>
        <member name="M:ProtoBuf.TypedExtensible.TryGetValue``1(ProtoBuf.ITypedExtensible,System.Int32,``0@,System.Type,ProtoBuf.DataFormat,ProtoBuf.Meta.TypeModel)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned (in "value") is the composed value after merging any duplicated content;
            if the value is "repeated" (a list), then use GetValues instead.
            </summary>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="value">The effective value of the field, or the default value if not found.</param>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="model">The type model to use for deserialization.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <param name="type">The <see cref="T:System.Type"/> that holds the fields, in terms of the inheritance model; the same <c>tag</c> key can appear against different <c>type</c> levels for the same <c>instance</c>, with different values.</param>
            <returns>True if data for the field was present, false otherwise.</returns>
        </member>
        <member name="M:ProtoBuf.TypedExtensible.GetValue``1(ProtoBuf.ITypedExtensible,System.Int32,System.Type,ProtoBuf.DataFormat,ProtoBuf.Meta.TypeModel)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned is the composed value after merging any duplicated content; if the
            value is "repeated" (a list), then use GetValues instead.
            </summary>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="model">The type model to use for deserialization.</param>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <param name="type">The <see cref="T:System.Type"/> that holds the fields, in terms of the inheritance model; the same <c>tag</c> key can appear against different <c>type</c> levels for the same <c>instance</c>, with different values.</param>
            <returns>The effective value of the field, or the default value if not found.</returns>
        </member>
        <member name="M:ProtoBuf.TypedExtensible.GetValues``1(ProtoBuf.ITypedExtensible,System.Int32,System.Type,ProtoBuf.DataFormat,ProtoBuf.Meta.TypeModel)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            Each occurrence of the field is yielded separately, making this usage suitable for "repeated"
            (list) fields.
            </summary>
            <remarks>The extended data is processed lazily as the enumerator is iterated.</remarks>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="model">The type model to use for deserialization.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <param name="type">The <see cref="T:System.Type"/> that holds the fields, in terms of the inheritance model; the same <c>tag</c> key can appear against different <c>type</c> levels for the same <c>instance</c>, with different values.</param>
            <returns>An enumerator that yields each occurrence of the field.</returns>
        </member>
        <member name="M:ProtoBuf.TypedExtensible.AppendValue``1(ProtoBuf.ITypedExtensible,System.Int32,``0,System.Type,ProtoBuf.DataFormat,ProtoBuf.Meta.TypeModel)">
            <summary>
            Appends the value as an additional (unexpected) data-field for the instance.
            Note that for non-repeated sub-objects, this equates to a merge operation;
            for repeated sub-objects this adds a new instance to the set; for simple
            values the new value supercedes the old value.
            </summary>
            <remarks>Note that appending a value does not remove the old value from
            the stream; avoid repeatedly appending values for the same field.</remarks>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="format">The data-format to use when encoding the value.</param>
            <param name="model">The model to use for serialization.</param>
            <param name="instance">The extensible object to append the value to.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="value">The value to append.</param>
            <param name="type">The <see cref="T:System.Type"/> that holds the fields, in terms of the inheritance model; the same <c>tag</c> key can appear against different <c>type</c> levels for the same <c>instance</c>, with different values.</param>
        </member>
        <member name="T:ProtoBuf.TypeResolver">
            <summary>
            Maps a field-number to a type
            </summary>
        </member>
        <member name="T:ProtoBuf.WellKnownTypes.Duration">
            <summary>
            A Duration represents a signed, fixed-length span of time represented
            as a count of seconds and fractions of seconds at nanosecond
            resolution. It is independent of any calendar and concepts like "day"
            or "month". It is related to Timestamp in that the difference between
            two Timestamp values is a Duration and it can be added or subtracted
            from a Timestamp. 
            </summary>
        </member>
        <member name="P:ProtoBuf.WellKnownTypes.Duration.Seconds">
            <summary>
            Signed seconds of the span of time.
            </summary>
        </member>
        <member name="P:ProtoBuf.WellKnownTypes.Duration.Nanoseconds">
            <summary>
            Signed fractions of a second at nanosecond resolution of the span of time.
            </summary>
        </member>
        <member name="M:ProtoBuf.WellKnownTypes.Duration.#ctor(System.Int64,System.Int32)">
            <summary>Creates a new Duration with the supplied values</summary>
        </member>
        <member name="M:ProtoBuf.WellKnownTypes.Duration.#ctor(System.TimeSpan)">
            <summary>Converts a TimeSpan to a Duration</summary>
        </member>
        <member name="M:ProtoBuf.WellKnownTypes.Duration.AsTimeSpan">
            <summary>Converts a Duration to a TimeSpan</summary>
        </member>
        <member name="M:ProtoBuf.WellKnownTypes.Duration.op_Implicit(ProtoBuf.WellKnownTypes.Duration)~System.TimeSpan">
            <summary>Converts a Duration to a TimeSpan</summary>
        </member>
        <member name="M:ProtoBuf.WellKnownTypes.Duration.op_Implicit(System.TimeSpan)~ProtoBuf.WellKnownTypes.Duration">
            <summary>Converts a TimeSpan to a Duration</summary>
        </member>
        <member name="M:ProtoBuf.WellKnownTypes.Duration.Normalize">
            <summary>
            Applies .proto rules to ensure that this value is in the expected ranges
            </summary>
        </member>
        <member name="T:ProtoBuf.WellKnownTypes.Empty">
            <summary>
            A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs
            </summary>
        </member>
        <member name="T:ProtoBuf.WellKnownTypes.Timestamp">
            <summary>
             A Timestamp represents a point in time independent of any time zone or local
            calendar, encoded as a count of seconds and fractions of seconds at
            nanosecond resolution. The count is relative to an epoch at UTC midnight on
            January 1, 1970, in the proleptic Gregorian calendar which extends the
            Gregorian calendar backwards to year one.
            </summary>
        </member>
        <member name="P:ProtoBuf.WellKnownTypes.Timestamp.Seconds">
            <summary>
            Represents seconds of UTC time since Unix epoch
            </summary>
        </member>
        <member name="P:ProtoBuf.WellKnownTypes.Timestamp.Nanoseconds">
            <summary>
            Non-negative fractions of a second at nanosecond resolution.
            </summary>
        </member>
        <member name="M:ProtoBuf.WellKnownTypes.Timestamp.#ctor(System.Int64,System.Int32)">
            <summary>Creates a new Duration with the supplied values</summary>
        </member>
        <member name="M:ProtoBuf.WellKnownTypes.Timestamp.#ctor(System.DateTime)">
            <summary>Converts a DateTime to a Timestamp</summary>
        </member>
        <member name="M:ProtoBuf.WellKnownTypes.Timestamp.Normalize">
            <summary>
            Applies .proto rules to ensure that this value is in the expected ranges
            </summary>
        </member>
        <member name="M:ProtoBuf.WellKnownTypes.Timestamp.AsDateTime">
            <summary>Converts a Timestamp to a DateTime</summary>
        </member>
        <member name="M:ProtoBuf.WellKnownTypes.Timestamp.op_Implicit(ProtoBuf.WellKnownTypes.Timestamp)~System.DateTime">
            <summary>Converts a Timestamp to a DateTime</summary>
        </member>
        <member name="M:ProtoBuf.WellKnownTypes.Timestamp.op_Implicit(System.DateTime)~ProtoBuf.WellKnownTypes.Timestamp">
            <summary>Converts a DateTime to a Timestamp</summary>
        </member>
        <member name="F:ProtoBuf.WellKnownTypes.Timestamp.TimestampEpoch">
            <summary>
            The default value for dates that are following google.protobuf.Timestamp semantics
            </summary>
        </member>
        <member name="T:ProtoBuf.WireType">
            <summary>
            Indicates the encoding used to represent an individual value in a protobuf stream
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.None">
            <summary>
            Represents an error condition
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.Variant">
            <summary>
            Base-128 variable-length encoding
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.Varint">
            <summary>
            Base-128 variable-length encoding
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.Fixed64">
            <summary>
            Fixed-length 8-byte encoding
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.String">
            <summary>
            Length-variant-prefixed encoding
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.StartGroup">
            <summary>
            Indicates the start of a group
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.EndGroup">
            <summary>
            Indicates the end of a group
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.Fixed32">
            <summary>
            Fixed-length 4-byte encoding
            </summary>10
        </member>
        <member name="F:ProtoBuf.WireType.SignedVariant">
            <summary>
            This is not a formal wire-type in the "protocol buffers" spec, but
            denotes a varint that should be interpreted using
            zig-zag semantics (so -ve numbers aren't a significant overhead)
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.SignedVarint">
            <summary>
            This is not a formal wire-type in the "protocol buffers" spec, but
            denotes a varint that should be interpreted using
            zig-zag semantics (so -ve numbers aren't a significant overhead)
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute">
             <summary>
             Indicates that certain members on a specified <see cref="T:System.Type"/> are accessed dynamically,
             for example through <see cref="N:System.Reflection"/>.
             </summary>
             <remarks>
             This allows tools to understand which members are being accessed during the execution
             of a program.
            
             This attribute is valid on members whose type is <see cref="T:System.Type"/> or <see cref="T:System.String"/>.
            
             When this attribute is applied to a location of type <see cref="T:System.String"/>, the assumption is
             that the string represents a fully qualified type name.
            
             If the attribute is applied to a method it's treated as a special case and it implies
             the attribute should be applied to the "this" parameter of the method. As such the attribute
             should only be used on instance methods of types assignable to System.Type (or string, but no methods
             will use it there).
             </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute.#ctor(System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute"/> class
            with the specified member types.
            </summary>
            <param name="memberTypes">The types of members dynamically accessed.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute.MemberTypes">
            <summary>
            Gets the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes"/> which specifies the type
            of members dynamically accessed.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes">
             <summary>
             Specifies the types of members that are dynamically accessed.
            
             This enumeration has a <see cref="T:System.FlagsAttribute"/> attribute that allows a
             bitwise combination of its member values.
             </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.None">
            <summary>
            Specifies no members.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicParameterlessConstructor">
            <summary>
            Specifies the default, parameterless public constructor.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicConstructors">
            <summary>
            Specifies all public constructors.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicConstructors">
            <summary>
            Specifies all non-public constructors.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicMethods">
            <summary>
            Specifies all public methods.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicMethods">
            <summary>
            Specifies all non-public methods.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicFields">
            <summary>
            Specifies all public fields.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicFields">
            <summary>
            Specifies all non-public fields.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicNestedTypes">
            <summary>
            Specifies all public nested types.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicNestedTypes">
            <summary>
            Specifies all non-public nested types.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicProperties">
            <summary>
            Specifies all public properties.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicProperties">
            <summary>
            Specifies all non-public properties.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicEvents">
            <summary>
            Specifies all public events.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicEvents">
            <summary>
            Specifies all non-public events.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.All">
            <summary>
            Specifies all members.
            </summary>
        </member>
    </members>
</doc>
