﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x020004A5 RID: 1189
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class InvoicesSourceMigration : DbMigration, IMigrationMetadata
	{
		// Token: 0x06002389 RID: 9097 RVA: 0x001F3A0C File Offset: 0x001F1C0C
		public override void Up()
		{
			base.AddColumn("dbo.OutgoingBill", "TransactionState", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
			base.AddColumn("dbo.PurchaseInvoices", "SourceID", (ColumnBuilder c) => c.Int(null, false, null, null, null, null, null), null);
			base.AddColumn("dbo.PurchaseInvoices", "SourceType", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
			base.AddColumn("dbo.SalesInvoices", "SourceID", (ColumnBuilder c) => c.Int(null, false, null, null, null, null, null), null);
			base.AddColumn("dbo.SalesInvoices", "SourceType", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
		}

		// Token: 0x0600238A RID: 9098 RVA: 0x001F3B10 File Offset: 0x001F1D10
		public override void Down()
		{
			base.DropColumn("dbo.SalesInvoices", "SourceType", null);
			base.DropColumn("dbo.SalesInvoices", "SourceID", null);
			base.DropColumn("dbo.PurchaseInvoices", "SourceType", null);
			base.DropColumn("dbo.PurchaseInvoices", "SourceID", null);
			base.DropColumn("dbo.OutgoingBill", "TransactionState", null);
		}

		// Token: 0x17000BA6 RID: 2982
		// (get) Token: 0x0600238B RID: 9099 RVA: 0x001F3B78 File Offset: 0x001F1D78
		string IMigrationMetadata.Id
		{
			get
			{
				return "202107311029584_InvoicesSourceMigration";
			}
		}

		// Token: 0x17000BA7 RID: 2983
		// (get) Token: 0x0600238C RID: 9100 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000BA8 RID: 2984
		// (get) Token: 0x0600238D RID: 9101 RVA: 0x001F3B90 File Offset: 0x001F1D90
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B3B RID: 11067
		private readonly ResourceManager Resources = new ResourceManager(typeof(InvoicesSourceMigration));
	}
}
