﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    public class TransactionLinkModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Key]
        public int ID { get; set; }

        public ProductTransaction DistnationTransaction { get; set; }

        public int DistnationTransactionID { get; set; }

        public ProductTransaction SourceTransaction { get; set; }

        public int SourceTransactionID { get; set; }

        public double Quantity { get; set; }
    }
}
