namespace EasyStock.HR.Models
{
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;

    public partial class SalaryExtensions
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public SalaryExtensions()
        {
            EmpSalaryExtensions = new HashSet<EmpSalaryExtensions>();
            SalaryRegulationExtensions = new HashSet<SalaryRegulationExtensions>();
        }

        public int ID { get; set; }

        [Required]
        [StringLength(150)]
        public string Name { get; set; }

        public int Type { get; set; }

        public int CalculationType { get; set; }

        public int Status { get; set; }

        public int MainAccount { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<EmpSalaryExtensions> EmpSalaryExtensions { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<SalaryRegulationExtensions> SalaryRegulationExtensions { get; set; }
    }
}
