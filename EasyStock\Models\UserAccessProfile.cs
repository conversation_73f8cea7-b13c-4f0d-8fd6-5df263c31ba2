﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
	[DisplayName("Modèle des Droits d'Accès")]
	public class UserAccessProfile : BaseNotifyPropertyChangedModel
	{
		[Display(Name = "N°")]
		[ReadOnly(true)]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID
		{
			get
			{
				return this.id;
			}
			set
			{
				base.SetProperty<int>(ref this.id, value, "ID");
			}
		}

		[Display(Name = "Nom")]
		[Required(ErrorMessage = "Ce champ est obligatoire")]
		public string Name
		{
			get
			{
				return this.name;
			}
			set
			{
				base.SetProperty<string>(ref this.name, value, "Name");
			}
		}

		[Display(Name = "Autorisations")]
		public BindingList<UserAccessProfileDetail> Details
		{
			get
			{
				return this.details;
			}
			set
			{
				base.SetProperty<BindingList<UserAccessProfileDetail>>(ref this.details, value, "Details");
			}
		}

		[Display(Name = "Utilisateurs")]
		public BindingList<User> Users
		{
			get
			{
				return this.users;
			}
			set
			{
				base.SetProperty<BindingList<User>>(ref this.users, value, "Users");
			}
		}

		private int id;

		private string name;

		private BindingList<UserAccessProfileDetail> details;

		private BindingList<User> users;
	}
}
