namespace EasyStock.HR.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Data.Entity.Spatial;

    public partial class EmpSalaryExtensions
    {
        public int ID { get; set; }

        public int EmpId { get; set; }

        public int SalaryExtensionId { get; set; }

        public double Value { get; set; }

        [StringLength(250)]
        public string Notes { get; set; }

        public int? Employee_ID { get; set; }

        public int? Employee_ID1 { get; set; }

        public int? Employee_ID2 { get; set; }

        public int Type { get; set; }

        public virtual Employees Employees { get; set; }

        public virtual Employees Employees1 { get; set; }

        public virtual Employees Employees2 { get; set; }

        public virtual SalaryExtensions SalaryExtensions { get; set; }
    }
}
