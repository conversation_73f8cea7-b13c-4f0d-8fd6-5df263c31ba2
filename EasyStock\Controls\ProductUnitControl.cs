﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.LookAndFeel;
using DevExpress.Utils;
using DevExpress.Utils.Extensions;
using DevExpress.XtraBars.Docking2010;
using DevExpress.XtraDataLayout;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.ButtonPanel;
using DevExpress.XtraEditors.ButtonsPanelControl;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Mask;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraLayout;
using DevExpress.XtraLayout.Utils;
using EasyStock.Controller;
using EasyStock.Models;
using EasyStock.Properties;

namespace EasyStock.Controls
{
    public class ProductUnitControl : UserControl
    {
        private new ProductUnitsListControl Parent;

        private double OldFactorValue;

        private IContainer components = null;

        private DataLayoutControl dataLayoutControl1;

        private LayoutControlGroup Root;

        private BindingSource productUnitBindingSource;

        private TextEdit BuyPriceTextEdit;

        private TextEdit SellPriceTextEdit;

        private TextEdit SellDiscountTextEdit;

        private TextEdit FactorTextEdit;

        private CheckEdit DefualtBuyCheckEdit;

        private CheckEdit DefualtSellCheckEdit;

        private GridControl UnitsBarcodeGridControl;

        private GridView UnitsBarcodeGridView;

        private LayoutControlGroup layoutControlGroup1;

        private LayoutControlGroup layoutControlGroup2;

        private LayoutControlItem ItemForBuyPrice;

        private LayoutControlItem ItemForUnitNameID;

        private LayoutControlItem ItemForSellPrice;

        private LayoutControlItem ItemForSellDiscount;

        private LayoutControlItem ItemForFactor;

        private LayoutControlItem ItemForDefualtBuy;

        private LayoutControlItem ItemForDefualtSell;

        private LayoutControlItem ItemForBarcodes;

        public LookUpEdit UnitNameIDTextEdit;

        private GridColumn gridColumn1;

        public ProductUnit Unit
        {
            get
            {
                return productUnitBindingSource.Current as ProductUnit;
            }
            set
            {
                productUnitBindingSource.DataSource = value;
            }
        }

        public ProductUnitControl(ProductUnitsListControl parent, ProductUnit unit)
        {
            InitializeComponent();
            Parent = parent;
            Unit = unit;
            UnitNameIDTextEdit.Properties.DataSource = Parent.UOMs;
            FactorTextEdit.Properties.ReadOnly = unit.Factor == 1.0;
            UnitNameIDTextEdit_EditValueChanged(null, null);
            UnitNameIDTextEdit.QueryPopUp += UnitNameIDTextEdit_QueryPopUp;
            RepositoryItemButtonEdit repoDeleteBarcode = new RepositoryItemButtonEdit();
            EditorButtonImageOptions editorButtonImageOptions1 = new EditorButtonImageOptions();
            repoDeleteBarcode.AutoHeight = false;
            editorButtonImageOptions1.SvgImage = Resources.actions_add;
            editorButtonImageOptions1.SvgImageSize = new Size(16, 16);
            repoDeleteBarcode.Buttons.AddRange(new EditorButton[1]
            {
            new EditorButton(ButtonPredefines.Glyph)
            });
            repoDeleteBarcode.Name = "repoDeleteBarcode";
            repoDeleteBarcode.TextEditStyle = TextEditStyles.HideTextEditor;
            repoDeleteBarcode.Click += repoDeleteBarcode_Click;
            GridColumn colDeleteBarcode = new GridColumn
            {
                ColumnEdit = repoDeleteBarcode,
                MaxWidth = 25,
                MinWidth = 25,
                Name = "colDeleteBarcode",
                Visible = true,
                VisibleIndex = 1,
                Width = 25
            };
            UnitsBarcodeGridView.Appearance.TopNewRow.BackColor = DXSkinColors.ForeColors.Warning;
            UnitsBarcodeGridView.Appearance.TopNewRow.Options.UseBackColor = true;
            UnitsBarcodeGridView.Appearance.ViewCaption.Font = new Font("Tahoma", 8.25f, FontStyle.Regular, GraphicsUnit.Point, 0);
            UnitsBarcodeGridView.Appearance.ViewCaption.Options.UseFont = true;
            UnitsBarcodeGridView.Columns.AddRange(new GridColumn[1] { colDeleteBarcode });
            UnitsBarcodeGridView.GridControl = UnitsBarcodeGridControl;
            UnitsBarcodeGridView.Name = "UnitsBarcodeGridView";
            UnitsBarcodeGridView.NewItemRowText = "Cliquez ici pour ajouter un nouvel élément.";
            UnitsBarcodeGridView.OptionsView.NewItemRowPosition = NewItemRowPosition.Top;
            UnitsBarcodeGridView.OptionsView.ShowColumnHeaders = false;
            UnitsBarcodeGridView.OptionsView.ShowGroupPanel = false;
            UnitsBarcodeGridView.OptionsView.ShowIndicator = false;
            UnitsBarcodeGridView.OptionsView.ShowViewCaption = true;
            UnitsBarcodeGridView.ViewCaption = "Les numéros de code-barres";
            UnitsBarcodeGridView.InitNewRow += UnitsBarcodeGridView_InitNewRow;
        }

        private void UnitsBarcodeGridView_InitNewRow(object sender, InitNewRowEventArgs e)
        {
            if (UnitsBarcodeGridView.GetRow(e.RowHandle) is ProductUnitBarcode barcode)
            {
                barcode.Unit = Unit;
            }
        }

        private void repoDeleteBarcode_Click(object sender, EventArgs e)
        {
            ProductUnitBarcode barcode = UnitsBarcodeGridView.GetRow(UnitsBarcodeGridView.FocusedRowHandle) as ProductUnitBarcode;
            if (UnitsBarcodeGridView.FocusedRowHandle >= 0 && barcode != null && XtraMessageBox.Show(this, "Voulez-vous supprimer le code-barres?", "Confirmer la suppression", MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation) == DialogResult.Yes)
            {
                UnitsBarcodeGridView.DeleteSelectedRows();
                Parent.db.ProductUnitBarCodes.RemoveRange(Parent.db.ProductUnitBarCodes.Where((ProductUnitBarcode x) => x.ID == barcode.ID));
                barcode.Unit?.Barcodes?.Remove(barcode);
            }
        }

        private void UnitNameIDTextEdit_QueryPopUp(object sender, CancelEventArgs e)
        {
            List<int> selectedUOMs = (from x in Parent.DataSource
                                      where x != Unit
                                      select x.UnitNameID).ToList();
            UnitNameIDTextEdit.Properties.DataSource = Parent.UOMs.Where((UnitOfMeasurement x) => !selectedUOMs.Contains(x.ID)).ToList();
        }

        private void UnitNameIDTextEdit_EditValueChanged(object sender, EventArgs e)
        {
            layoutControlGroup2.Text = UnitNameIDTextEdit.Text;
            if (UnitNameIDTextEdit.Text.Length == 0)
            {
                layoutControlGroup2.Text = " ";
            }
        }

        private void layoutControlGroup2_CustomButtonClick(object sender, BaseButtonEventArgs e)
        {
            using ERPDataContext context = new ERPDataContext();
            if (context.ProductTransactions.Where((ProductTransaction x) => (int)x.TransactionState == 2).Any((ProductTransaction x) => x.UnitID == Unit.ID))
            {
                XtraMessageBox.Show("Cette unité ne peut pas être supprimée car elle est utilisée dans le système", "");
                return;
            }
            if (Unit.Factor == 1.0)
            {
                XtraMessageBox.Show("Impossible de supprimer l'unité principale", "");
                return;
            }
            Parent.db.Entry(Unit).State = EntityState.Detached;
            context.ProductUnitBarCodes.RemoveRange(context.ProductUnitBarCodes.Where((ProductUnitBarcode x) => x.UnitID == Unit.ID));
            context.ProductUnits.RemoveRange(context.ProductUnits.Where((ProductUnit u) => u.ID == Unit.ID));
            context.SaveChanges();
            Dispose();
        }

        private void UnitNameIDTextEdit_ProcessNewValue(object sender, ProcessNewValueEventArgs e)
        {
            if ((string)e.DisplayValue != string.Empty)
            {
                ICollection<UnitOfMeasurement> list = (sender as LookUpEdit).Properties.DataSource as ICollection<UnitOfMeasurement>;
                UnitOfMeasurement unit = new UnitOfMeasurement
                {
                    Name = e.DisplayValue.ToString()
                };
                using ERPDataContext context = new ERPDataContext();
                context.UnitOfMeasurements.Add(unit);
                context.SaveChanges();
                list.Add(unit);
                e.Handled = true;
            }
        }

        private void FactorTextEdit_EditValueChanged(object sender, EventArgs e)
        {
            if (!FactorTextEdit.ContainsFocus)
            {
                return;
            }
            int index = Parent.DataSource.IndexOf(Unit);
            if (index > 0)
            {
                ProductUnit mainUnit = Parent.DataSource[0];
                BuyPriceTextEdit.EditValue = mainUnit.BuyPrice * Unit.Factor;
                SellPriceTextEdit.EditValue = mainUnit.SellPrice * Unit.Factor;
            }
            double value = 0.0;
            if (double.TryParse(FactorTextEdit.EditValue.ToString(), out value) && value <= 1.0)
            {
                if (OldFactorValue <= 1.0)
                {
                    OldFactorValue = 10.0;
                }
                FactorTextEdit.EditValue = OldFactorValue;
                FactorTextEdit.ErrorText = "Le coefficient doit être supérieur à un entier";
            }
            if (FactorTextEdit.EditValue is double factorValue)
            {
                OldFactorValue = factorValue;
            }
        }

        private void DefualtBuyCheckEdit_CheckedChanged(object sender, EventArgs e)
        {
            if (DefualtBuyCheckEdit.Checked)
            {
                Parent.DataSource.Where((ProductUnit x) => x != Unit).ForEach(delegate (ProductUnit x)
                {
                    x.DefualtBuy = false;
                });
            }
        }

        private void DefualtSellCheckEdit_CheckedChanged(object sender, EventArgs e)
        {
            if (DefualtSellCheckEdit.Checked)
            {
                Parent.DataSource.Where((ProductUnit x) => x != Unit).ForEach(delegate (ProductUnit x)
                {
                    x.DefualtSell = false;
                });
            }
        }

        private void FactorTextEdit_EditValueChanging(object sender, ChangingEventArgs e)
        {
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraEditors.ButtonsPanelControl.ButtonImageOptions buttonImageOptions1 = new DevExpress.XtraEditors.ButtonsPanelControl.ButtonImageOptions();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.BuyPriceTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.productUnitBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.SellPriceTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.SellDiscountTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.FactorTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.DefualtBuyCheckEdit = new DevExpress.XtraEditors.CheckEdit();
            this.DefualtSellCheckEdit = new DevExpress.XtraEditors.CheckEdit();
            this.UnitsBarcodeGridControl = new DevExpress.XtraGrid.GridControl();
            this.UnitsBarcodeGridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.UnitNameIDTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForUnitNameID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForSellPrice = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForBarcodes = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForBuyPrice = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDefualtBuy = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDefualtSell = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForFactor = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForSellDiscount = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.BuyPriceTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.productUnitBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.SellPriceTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.SellDiscountTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.FactorTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DefualtBuyCheckEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DefualtSellCheckEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.UnitsBarcodeGridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.UnitsBarcodeGridView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.UnitNameIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForUnitNameID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSellPrice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBarcodes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBuyPrice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDefualtBuy)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDefualtSell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFactor)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSellDiscount)).BeginInit();
            this.SuspendLayout();
            // 
            // dataLayoutControl1
            // 
            this.dataLayoutControl1.Controls.Add(this.BuyPriceTextEdit);
            this.dataLayoutControl1.Controls.Add(this.SellPriceTextEdit);
            this.dataLayoutControl1.Controls.Add(this.SellDiscountTextEdit);
            this.dataLayoutControl1.Controls.Add(this.FactorTextEdit);
            this.dataLayoutControl1.Controls.Add(this.DefualtBuyCheckEdit);
            this.dataLayoutControl1.Controls.Add(this.DefualtSellCheckEdit);
            this.dataLayoutControl1.Controls.Add(this.UnitsBarcodeGridControl);
            this.dataLayoutControl1.Controls.Add(this.UnitNameIDTextEdit);
            this.dataLayoutControl1.DataSource = this.productUnitBindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 0);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(597, 30, 650, 400);
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(550, 180);
            this.dataLayoutControl1.TabIndex = 0;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            // 
            // BuyPriceTextEdit
            // 
            this.BuyPriceTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.productUnitBindingSource, "BuyPrice", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.BuyPriceTextEdit.Location = new System.Drawing.Point(108, 104);
            this.BuyPriceTextEdit.Name = "BuyPriceTextEdit";
            this.BuyPriceTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.BuyPriceTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.BuyPriceTextEdit.Properties.Mask.EditMask = "F";
            this.BuyPriceTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.BuyPriceTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.BuyPriceTextEdit.Size = new System.Drawing.Size(128, 22);
            this.BuyPriceTextEdit.StyleController = this.dataLayoutControl1;
            this.BuyPriceTextEdit.TabIndex = 5;
            // 
            // productUnitBindingSource
            // 
            this.productUnitBindingSource.DataSource = typeof(EasyStock.Models.ProductUnit);
            // 
            // SellPriceTextEdit
            // 
            this.SellPriceTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.productUnitBindingSource, "SellPrice", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.SellPriceTextEdit.Location = new System.Drawing.Point(108, 130);
            this.SellPriceTextEdit.Name = "SellPriceTextEdit";
            this.SellPriceTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.SellPriceTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.SellPriceTextEdit.Properties.Mask.EditMask = "F";
            this.SellPriceTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.SellPriceTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.SellPriceTextEdit.Size = new System.Drawing.Size(128, 22);
            this.SellPriceTextEdit.StyleController = this.dataLayoutControl1;
            this.SellPriceTextEdit.TabIndex = 6;
            // 
            // SellDiscountTextEdit
            // 
            this.SellDiscountTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.productUnitBindingSource, "SellDiscount", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.SellDiscountTextEdit.Location = new System.Drawing.Point(108, 78);
            this.SellDiscountTextEdit.Name = "SellDiscountTextEdit";
            this.SellDiscountTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.SellDiscountTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.SellDiscountTextEdit.Properties.Mask.EditMask = "F";
            this.SellDiscountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.SellDiscountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.SellDiscountTextEdit.Size = new System.Drawing.Size(224, 22);
            this.SellDiscountTextEdit.StyleController = this.dataLayoutControl1;
            this.SellDiscountTextEdit.TabIndex = 7;
            // 
            // FactorTextEdit
            // 
            this.FactorTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.productUnitBindingSource, "Factor", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.FactorTextEdit.Location = new System.Drawing.Point(108, 52);
            this.FactorTextEdit.Name = "FactorTextEdit";
            this.FactorTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.FactorTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.FactorTextEdit.Properties.EditValueChangedDelay = 1000;
            this.FactorTextEdit.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Buffered;
            this.FactorTextEdit.Properties.Mask.EditMask = "F";
            this.FactorTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.FactorTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.FactorTextEdit.Size = new System.Drawing.Size(224, 22);
            this.FactorTextEdit.StyleController = this.dataLayoutControl1;
            this.FactorTextEdit.TabIndex = 8;
            this.FactorTextEdit.EditValueChanged += new System.EventHandler(this.FactorTextEdit_EditValueChanged);
            this.FactorTextEdit.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.FactorTextEdit_EditValueChanging);
            // 
            // DefualtBuyCheckEdit
            // 
            this.DefualtBuyCheckEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.productUnitBindingSource, "DefualtBuy", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DefualtBuyCheckEdit.Location = new System.Drawing.Point(240, 104);
            this.DefualtBuyCheckEdit.Name = "DefualtBuyCheckEdit";
            this.DefualtBuyCheckEdit.Properties.Caption = "Achat par défaut";
            this.DefualtBuyCheckEdit.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Default;
            this.DefualtBuyCheckEdit.Size = new System.Drawing.Size(92, 20);
            this.DefualtBuyCheckEdit.StyleController = this.dataLayoutControl1;
            this.DefualtBuyCheckEdit.TabIndex = 9;
            this.DefualtBuyCheckEdit.CheckedChanged += new System.EventHandler(this.DefualtBuyCheckEdit_CheckedChanged);
            // 
            // DefualtSellCheckEdit
            // 
            this.DefualtSellCheckEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.productUnitBindingSource, "DefualtSell", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DefualtSellCheckEdit.Location = new System.Drawing.Point(240, 130);
            this.DefualtSellCheckEdit.Name = "DefualtSellCheckEdit";
            this.DefualtSellCheckEdit.Properties.Caption = "Vente par défaut";
            this.DefualtSellCheckEdit.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Default;
            this.DefualtSellCheckEdit.Size = new System.Drawing.Size(92, 20);
            this.DefualtSellCheckEdit.StyleController = this.dataLayoutControl1;
            this.DefualtSellCheckEdit.TabIndex = 10;
            this.DefualtSellCheckEdit.CheckedChanged += new System.EventHandler(this.DefualtSellCheckEdit_CheckedChanged);
            // 
            // UnitsBarcodeGridControl
            // 
            this.UnitsBarcodeGridControl.DataBindings.Add(new System.Windows.Forms.Binding("DataSource", this.productUnitBindingSource, "Barcodes", true));
            this.UnitsBarcodeGridControl.Location = new System.Drawing.Point(336, 26);
            this.UnitsBarcodeGridControl.MainView = this.UnitsBarcodeGridView;
            this.UnitsBarcodeGridControl.Name = "UnitsBarcodeGridControl";
            this.UnitsBarcodeGridControl.Size = new System.Drawing.Size(209, 149);
            this.UnitsBarcodeGridControl.TabIndex = 11;
            this.UnitsBarcodeGridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.UnitsBarcodeGridView});
            // 
            // UnitsBarcodeGridView
            // 
            this.UnitsBarcodeGridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1});
            this.UnitsBarcodeGridView.GridControl = this.UnitsBarcodeGridControl;
            this.UnitsBarcodeGridView.Name = "UnitsBarcodeGridView";
            this.UnitsBarcodeGridView.OptionsView.ShowColumnHeaders = false;
            this.UnitsBarcodeGridView.OptionsView.ShowGroupPanel = false;
            this.UnitsBarcodeGridView.OptionsView.ShowIndicator = false;
            this.UnitsBarcodeGridView.OptionsView.ShowViewCaption = true;
            this.UnitsBarcodeGridView.ViewCaption = "Code-Barres";
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "gridColumn1";
            this.gridColumn1.FieldName = "Barcode";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // UnitNameIDTextEdit
            // 
            this.UnitNameIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.productUnitBindingSource, "UnitNameID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.UnitNameIDTextEdit.Location = new System.Drawing.Point(108, 26);
            this.UnitNameIDTextEdit.Name = "UnitNameIDTextEdit";
            this.UnitNameIDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.UnitNameIDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.UnitNameIDTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.UnitNameIDTextEdit.Properties.DisplayMember = "Name";
            this.UnitNameIDTextEdit.Properties.NullText = "";
            this.UnitNameIDTextEdit.Properties.ShowHeader = false;
            this.UnitNameIDTextEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.UnitNameIDTextEdit.Properties.ValueMember = "ID";
            this.UnitNameIDTextEdit.Size = new System.Drawing.Size(224, 22);
            this.UnitNameIDTextEdit.StyleController = this.dataLayoutControl1;
            this.UnitNameIDTextEdit.TabIndex = 4;
            this.UnitNameIDTextEdit.ProcessNewValue += new DevExpress.XtraEditors.Controls.ProcessNewValueEventHandler(this.UnitNameIDTextEdit_ProcessNewValue);
            this.UnitNameIDTextEdit.EditValueChanged += new System.EventHandler(this.UnitNameIDTextEdit_EditValueChanged);
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.Root.Size = new System.Drawing.Size(550, 180);
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup2});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(550, 180);
            // 
            // layoutControlGroup2
            // 
            buttonImageOptions1.SvgImage = global::EasyStock.Properties.Resources.actions_remove;
            buttonImageOptions1.SvgImageSize = new System.Drawing.Size(18, 18);
            this.layoutControlGroup2.CustomHeaderButtons.AddRange(new DevExpress.XtraEditors.ButtonPanel.IBaseButton[] {
            new DevExpress.XtraEditors.ButtonsPanelControl.GroupBoxButton("Supprimer", true, buttonImageOptions1, DevExpress.XtraBars.Docking2010.ButtonStyle.PushButton, "", -1, true, null, true, false, true, null, -1)});
            this.layoutControlGroup2.ExpandButtonVisible = true;
            this.layoutControlGroup2.HeaderButtonsLocation = DevExpress.Utils.GroupElementLocation.AfterText;
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForUnitNameID,
            this.ItemForSellPrice,
            this.ItemForBarcodes,
            this.ItemForBuyPrice,
            this.ItemForDefualtBuy,
            this.ItemForDefualtSell,
            this.ItemForFactor,
            this.ItemForSellDiscount});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.layoutControlGroup2.Size = new System.Drawing.Size(550, 180);
            this.layoutControlGroup2.Text = " ";
            this.layoutControlGroup2.CustomButtonClick += new DevExpress.XtraBars.Docking2010.BaseButtonEventHandler(this.layoutControlGroup2_CustomButtonClick);
            // 
            // ItemForUnitNameID
            // 
            this.ItemForUnitNameID.Control = this.UnitNameIDTextEdit;
            this.ItemForUnitNameID.Location = new System.Drawing.Point(0, 0);
            this.ItemForUnitNameID.Name = "ItemForUnitNameID";
            this.ItemForUnitNameID.Size = new System.Drawing.Size(331, 26);
            this.ItemForUnitNameID.TextSize = new System.Drawing.Size(91, 13);
            // 
            // ItemForSellPrice
            // 
            this.ItemForSellPrice.Control = this.SellPriceTextEdit;
            this.ItemForSellPrice.Location = new System.Drawing.Point(0, 104);
            this.ItemForSellPrice.Name = "ItemForSellPrice";
            this.ItemForSellPrice.Size = new System.Drawing.Size(235, 49);
            this.ItemForSellPrice.TextSize = new System.Drawing.Size(91, 13);
            // 
            // ItemForBarcodes
            // 
            this.ItemForBarcodes.Control = this.UnitsBarcodeGridControl;
            this.ItemForBarcodes.Location = new System.Drawing.Point(331, 0);
            this.ItemForBarcodes.Name = "ItemForBarcodes";
            this.ItemForBarcodes.Size = new System.Drawing.Size(213, 153);
            this.ItemForBarcodes.StartNewLine = true;
            this.ItemForBarcodes.TextSize = new System.Drawing.Size(0, 0);
            this.ItemForBarcodes.TextVisible = false;
            // 
            // ItemForBuyPrice
            // 
            this.ItemForBuyPrice.Control = this.BuyPriceTextEdit;
            this.ItemForBuyPrice.Location = new System.Drawing.Point(0, 78);
            this.ItemForBuyPrice.Name = "ItemForBuyPrice";
            this.ItemForBuyPrice.Size = new System.Drawing.Size(235, 26);
            this.ItemForBuyPrice.TextSize = new System.Drawing.Size(91, 13);
            // 
            // ItemForDefualtBuy
            // 
            this.ItemForDefualtBuy.Control = this.DefualtBuyCheckEdit;
            this.ItemForDefualtBuy.Location = new System.Drawing.Point(235, 78);
            this.ItemForDefualtBuy.Name = "ItemForDefualtBuy";
            this.ItemForDefualtBuy.Size = new System.Drawing.Size(96, 26);
            this.ItemForDefualtBuy.TextSize = new System.Drawing.Size(0, 0);
            this.ItemForDefualtBuy.TextVisible = false;
            // 
            // ItemForDefualtSell
            // 
            this.ItemForDefualtSell.Control = this.DefualtSellCheckEdit;
            this.ItemForDefualtSell.Location = new System.Drawing.Point(235, 104);
            this.ItemForDefualtSell.Name = "ItemForDefualtSell";
            this.ItemForDefualtSell.Size = new System.Drawing.Size(96, 49);
            this.ItemForDefualtSell.TextSize = new System.Drawing.Size(0, 0);
            this.ItemForDefualtSell.TextVisible = false;
            // 
            // ItemForFactor
            // 
            this.ItemForFactor.Control = this.FactorTextEdit;
            this.ItemForFactor.Location = new System.Drawing.Point(0, 26);
            this.ItemForFactor.Name = "ItemForFactor";
            this.ItemForFactor.Size = new System.Drawing.Size(331, 26);
            this.ItemForFactor.TextSize = new System.Drawing.Size(91, 13);
            // 
            // ItemForSellDiscount
            // 
            this.ItemForSellDiscount.Control = this.SellDiscountTextEdit;
            this.ItemForSellDiscount.Location = new System.Drawing.Point(0, 52);
            this.ItemForSellDiscount.Name = "ItemForSellDiscount";
            this.ItemForSellDiscount.Size = new System.Drawing.Size(331, 26);
            this.ItemForSellDiscount.TextSize = new System.Drawing.Size(91, 13);
            // 
            // ProductUnitControl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.dataLayoutControl1);
            this.MaximumSize = new System.Drawing.Size(750, 200);
            this.MinimumSize = new System.Drawing.Size(550, 180);
            this.Name = "ProductUnitControl";
            this.Size = new System.Drawing.Size(550, 180);
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.BuyPriceTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.productUnitBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.SellPriceTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.SellDiscountTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.FactorTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DefualtBuyCheckEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DefualtSellCheckEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.UnitsBarcodeGridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.UnitsBarcodeGridView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.UnitNameIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForUnitNameID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSellPrice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBarcodes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBuyPrice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDefualtBuy)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDefualtSell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFactor)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSellDiscount)).EndInit();
            this.ResumeLayout(false);

        }
    }

}
