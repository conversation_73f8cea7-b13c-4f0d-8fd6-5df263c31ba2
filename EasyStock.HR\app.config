﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <entityFramework>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <connectionStrings>
    <add name="DefaultMaterialconsumptions" connectionString="data source=.;initial catalog=EasyStock.HR.Models.DefaultMaterialconsumptions;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="EmpVacation" connectionString="data source=.;initial catalog=EasyStock.HR.Models.EmpVacation;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="EmpAbsence" connectionString="data source=.;initial catalog=EasyStock.HR.Models.EmpAbsence;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="EmpDelay" connectionString="data source=.;initial catalog=EasyStock.HR.Models.EmpDelay;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="EmpExtra" connectionString="data source=.;initial catalog=EasyStock.HR.Models.EmpExtra;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="EmpShift" connectionString="data source=.;initial catalog=EasyStock.HR.Models.EmpShift;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="EmpAttendance" connectionString="data source=.;initial catalog=EasyStock.HR.Models.EmpAttendance;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="EmpLoan" connectionString="data source=.;initial catalog=EasyStock.HR.Models.EmpLoan;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="EmpLoanDetails" connectionString="data source=.;initial catalog=EasyStock.HR.Models.EmpLoanDetails;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="Model1" connectionString="data source=.;initial catalog=EasyStock.Controller.DataContext;integrated security=True;encrypt=True;trustservercertificate=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>
</configuration>