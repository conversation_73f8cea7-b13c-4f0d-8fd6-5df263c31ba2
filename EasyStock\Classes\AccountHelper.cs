using DevExpress.LookAndFeel;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.DXErrorProvider;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using EasyStock.Controller;
using EasyStock.Models;
using EasyStock.Properties;
using EasyStock.ReportModels;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;

namespace EasyStock.Classes
{

    public static class AccountHelper
    {
        public enum CashTransactionType
        {
            In,
            Out
        }

        public static class Accounts
        {
            public static Account AssetsAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(null, ""),
                Name = LangResource.Assets,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                AccountType = AccountType.Debit,
                ParentID = null
            };

            public static Account FixedAssetsAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(AssetsAccount.ID, AssetsAccount.Number),
                Name = LangResource.FixedAssets,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = AssetsAccount.ID
            };

            public static Account CurrentAssetsAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(AssetsAccount.ID, AssetsAccount.Number),
                Name = LangResource.CurrentAssets,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = AssetsAccount.ID
            };

            public static Account DrawerAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(CurrentAssetsAccount.ID, CurrentAssetsAccount.Number),
                Name = LangResource.Drawer,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = CurrentAssetsAccount.ID
            };

            public static Account BanksAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(CurrentAssetsAccount.ID, CurrentAssetsAccount.Number),
                Name = LangResource.Banks,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = CurrentAssetsAccount.ID
            };

            public static Account CustomersAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(CurrentAssetsAccount.ID, CurrentAssetsAccount.Number),
                Name = LangResource.Customers,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = CurrentAssetsAccount.ID
            };

            public static Account NotesReceivableAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(CurrentAssetsAccount.ID, CurrentAssetsAccount.Number),
                Name = LangResource.NotesReceivable,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = CurrentAssetsAccount.ID
            };

            public static Account InventoryAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(CurrentAssetsAccount.ID, CurrentAssetsAccount.Number),
                Name = LangResource.TheInventory,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = CurrentAssetsAccount.ID
            };

            public static Account EmployeesDueAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(CurrentAssetsAccount.ID, CurrentAssetsAccount.Number),
                Name = LangResource.EmployeesDue,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = CurrentAssetsAccount.ID
            };

            public static Account FinancialCustodiesAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(CurrentAssetsAccount.ID, CurrentAssetsAccount.Number),
                Name = LangResource.FinancialCustodies,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = CurrentAssetsAccount.ID
            };

            public static Account PermanentCustodiesAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(FinancialCustodiesAccount.ID, FinancialCustodiesAccount.Number),
                Name = LangResource.PermanentCustodies,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = FinancialCustodiesAccount.ID
            };

            public static Account TemporaryCustdodiesAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(FinancialCustodiesAccount.ID, FinancialCustodiesAccount.Number),
                Name = LangResource.TemporaryCustdodies,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = FinancialCustodiesAccount.ID
            };

            public static Account OtherAssetsAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(AssetsAccount.ID, AssetsAccount.Number),
                Name = LangResource.OtherAssets,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = AssetsAccount.ID
            };

            public static Account Liabilites_Owners_EquityAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(null, ""),
                Name = LangResource.Liabilites_Owners_Equity,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                AccountType = AccountType.Credit,
                ParentID = null
            };

            public static Account CreditorAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(Liabilites_Owners_EquityAccount.ID, Liabilites_Owners_EquityAccount.Number),
                Name = LangResource.Creditor,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = Liabilites_Owners_EquityAccount.ID
            };

            public static Account VendorsAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(CreditorAccount.ID, CreditorAccount.Number),
                Name = LangResource.Vendors,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = CreditorAccount.ID
            };

            public static Account LoansAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(CreditorAccount.ID, CreditorAccount.Number),
                Name = LangResource.Loans,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = CreditorAccount.ID
            };

            public static Account Owner_EquityAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(Liabilites_Owners_EquityAccount.ID, Liabilites_Owners_EquityAccount.Number),
                Name = LangResource.Owner_Equity,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = Liabilites_Owners_EquityAccount.ID
            };

            public static Account CapitalAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(Owner_EquityAccount.ID, Owner_EquityAccount.Number),
                Name = LangResource.Capital,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = Owner_EquityAccount.ID
            };

            public static Account CurrentAccountAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(Owner_EquityAccount.ID, Owner_EquityAccount.Number),
                Name = LangResource.CurrentAccount,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = Owner_EquityAccount.ID
            };

            public static Account DueSalerysAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(Liabilites_Owners_EquityAccount.ID, Liabilites_Owners_EquityAccount.Number),
                Name = LangResource.DueSalerys,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = Liabilites_Owners_EquityAccount.ID
            };

            public static Account DepreciationComplexAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(Liabilites_Owners_EquityAccount.ID, Liabilites_Owners_EquityAccount.Number),
                Name = LangResource.DepreciationComplex,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = Liabilites_Owners_EquityAccount.ID
            };

            public static Account ExpensesAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(null, ""),
                Name = LangResource.Expenses,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                AccountType = AccountType.Debit,
                ParentID = null
            };

            public static Account WagesAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(ExpensesAccount.ID, ExpensesAccount.Number),
                Name = LangResource.EmployeeSalaries,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = ExpensesAccount.ID
            };

            public static Account TaxAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(ExpensesAccount.ID, ExpensesAccount.Number),
                Name = LangResource.SalesTax,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = ExpensesAccount.ID
            };

            public static Account GeneralExpensesAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(ExpensesAccount.ID, ExpensesAccount.Number),
                Name = LangResource.GeneralExpenses,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = ExpensesAccount.ID
            };

            public static Account GeneralTaxesAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(ExpensesAccount.ID, ExpensesAccount.Number),
                Name = LangResource.GeneralTaxes,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = ExpensesAccount.ID
            };

            public static Account RevenueAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(null, ""),
                Name = LangResource.Revenues,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                AccountType = AccountType.Credit,
                ParentID = null
            };

            public static Account GeneralRevenuesAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(RevenueAccount.ID, RevenueAccount.Number),
                Name = LangResource.GeneralRevenues,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = RevenueAccount.ID
            };

            public static Account MerchandisingAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(null, ""),
                Name = LangResource.Merchandising,
                CanEdit = true,
                Secrecy = SecracyLevel.Normal,
                AccountType = AccountType.Without,
                ParentID = null
            };

            public static Account PurchasesAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(MerchandisingAccount.ID, MerchandisingAccount.Number),
                Name = LangResource.Purchases,
                CanEdit = true,
                Secrecy = SecracyLevel.Normal,
                AccountType = AccountType.Debit,
                ParentID = MerchandisingAccount.ID
            };

            public static Account PurchasesReturnAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(MerchandisingAccount.ID, MerchandisingAccount.Number),
                Name = LangResource.PurchasesReturn,
                CanEdit = true,
                Secrecy = SecracyLevel.Normal,
                AccountType = AccountType.Credit,
                ParentID = MerchandisingAccount.ID
            };

            public static Account SalesAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(MerchandisingAccount.ID, MerchandisingAccount.Number),
                Name = LangResource.Sales,
                CanEdit = true,
                Secrecy = SecracyLevel.Normal,
                AccountType = AccountType.Credit,
                ParentID = MerchandisingAccount.ID
            };

            public static Account SalesReturnAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(MerchandisingAccount.ID, MerchandisingAccount.Number),
                Name = LangResource.SalesReturn,
                CanEdit = true,
                Secrecy = SecracyLevel.Normal,
                AccountType = AccountType.Debit,
                ParentID = MerchandisingAccount.ID
            };

            public static Account SalesDiscountAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(MerchandisingAccount.ID, MerchandisingAccount.Number),
                Name = LangResource.SalesDiscount,
                CanEdit = true,
                Secrecy = SecracyLevel.Normal,
                AccountType = AccountType.Debit,
                ParentID = MerchandisingAccount.ID
            };

            public static Account PurchaseDiscountAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(MerchandisingAccount.ID, MerchandisingAccount.Number),
                Name = LangResource.PurchaseDiscount,
                CanEdit = true,
                Secrecy = SecracyLevel.Normal,
                AccountType = AccountType.Credit,
                ParentID = MerchandisingAccount.ID
            };

            public static Account OpenInventoryAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(MerchandisingAccount.ID, MerchandisingAccount.Number),
                Name = LangResource.OpenInventory,
                CanEdit = true,
                Secrecy = SecracyLevel.Normal,
                AccountType = AccountType.Debit,
                ParentID = MerchandisingAccount.ID
            };

            public static Account CloseInventoryAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(MerchandisingAccount.ID, MerchandisingAccount.Number),
                Name = LangResource.CloseInventory,
                CanEdit = true,
                Secrecy = SecracyLevel.Normal,
                AccountType = AccountType.Credit,
                ParentID = MerchandisingAccount.ID
            };

            public static Account NotesPayableAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(Liabilites_Owners_EquityAccount.ID, Liabilites_Owners_EquityAccount.Number),
                Name = LangResource.NotesPayableAccount,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = Liabilites_Owners_EquityAccount.ID
            };

            public static Account ManufacturingExpAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(ExpensesAccount.ID, ExpensesAccount.Number),
                Name = LangResource.ManufacturingExpAccount,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = ExpensesAccount.ID
            };

            public static Account SalesDeductTaxAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(ExpensesAccount.ID, ExpensesAccount.Number),
                Name = LangResource.SalesDeductTaxAccount,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = ExpensesAccount.ID
            };

            public static Account PurchaseAddTaxAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(ExpensesAccount.ID, ExpensesAccount.Number),
                Name = LangResource.PurchaseAddTaxAccount,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = ExpensesAccount.ID
            };

            public static Account SalesAddTaxAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(ExpensesAccount.ID, ExpensesAccount.Number),
                Name = LangResource.SalesAddTaxAccount,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = ExpensesAccount.ID
            };

            public static Account PurchaseDeductTaxAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(ExpensesAccount.ID, ExpensesAccount.Number),
                Name = LangResource.PurchaseDeductTaxAccount,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = ExpensesAccount.ID
            };

            public static Account CostOfSoldGoodsAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(MerchandisingAccount.ID, MerchandisingAccount.Number),
                Name = LangResource.CostOfSoldGoodsAccount,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = MerchandisingAccount.ID
            };

            public static Account DepreciationAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(Liabilites_Owners_EquityAccount.ID, Liabilites_Owners_EquityAccount.Number),
                Name = LangResource.DepreciationAccount,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = Liabilites_Owners_EquityAccount.ID
            };

            public static Account RecieveNotesUnderCollectAccount = new Account
            {
                ID = MaxID,
                Number = GetNewNumber(CurrentAssetsAccount.ID, CurrentAssetsAccount.Number),
                Name = LangResource.RecieveNotesUnderCollectAccount,
                CanEdit = false,
                Secrecy = SecracyLevel.Normal,
                ParentID = CurrentAssetsAccount.ID
            };
        }

        public static byte AccountsLevel = 3;

        private static int _maxID = 0;

        private static int _maxNumber = 0;

        private static List<Account> ListOfAccounts = new List<Account>();

        private static int MaxID
        {
            get
            {
                _maxID++;
                return _maxID;
            }
        }

        private static int MaxNumber
        {
            get
            {
                _maxNumber++;
                return _maxNumber;
            }
        }

        public static double GetBalance(int id, bool asCredit)
        {
            using ERPDataContext db = new ERPDataContext();
            IQueryable<JournalDetail> accountJournals = db.JournalDetails.Where((JournalDetail j) => j.AccountID == id);
            double totalDebit = accountJournals.Sum(x => (double?)(x.Debit * x.CurrencyRate)).GetValueOrDefault();
            double totalCredit = accountJournals.Sum(x => (double?)(x.Credit * x.CurrencyRate)).GetValueOrDefault();
            string type = ((totalCredit > totalDebit) ? "Créditeur" : "Débiteur");
            if (asCredit)
            {
                return totalCredit - totalDebit;
            }
            return totalDebit - totalCredit;
        }

        public static string GetBalance(int id) 
        {
            using ERPDataContext db = new ERPDataContext();
            return db.Accounts.SingleOrDefault((Account x) => x.ID == id).GetBalance();
        }

        public static string GetBalance(this Account account, TextEdit txtbalance = null, TextEdit txtbalanceType = null)
        {
            if (account == null)
            {
                throw new NullReferenceException();
            }
            using ERPDataContext db = new ERPDataContext();
            IQueryable<JournalDetail> accountJournals = from j in db.JournalDetails.Include(x => x.Account)
                                                        where j.Account.Number.IndexOf(account.Number) == 0
                                                        select j;
            double totalDebit = accountJournals.Sum(x => (double?)(x.Debit * x.CurrencyRate)).GetValueOrDefault();
            double totalCredit = accountJournals.Sum(x => (double?)(x.Credit * x.CurrencyRate)).GetValueOrDefault();
            double balance = Math.Abs(totalCredit - totalDebit);
            string type = ((totalCredit > totalDebit) ? "Créditeur" : "Débiteur");
            if (txtbalance != null)
            {
                txtbalance.Text = $"{balance:N}";
            }
            if (txtbalanceType != null)
            {
                txtbalanceType.Text = type;
            }
            return $"{balance:N} {type}";
        }

        public static string GetBalanceAsString(this Account account, DateTime date, ERPDataContext db = null)
        {
            double balance = account.GetBalanceAsValue(date, db);
            double absoluteBalance = Math.Abs(balance);
            string type = ((balance > 0.0) ? "Créditeur" : "Débiteur");
            return $"{absoluteBalance:N} {type}";
        }

        public static double GetBalanceAsValue(this Account account, DateTime date, ERPDataContext db = null)
        {
            if (account == null)
            {
                throw new NullReferenceException();
            }
            if (db == null)
            {
                db = new ERPDataContext();
            }
            IQueryable<JournalDetail> accountJournals = from j in db.JournalDetails.Include((JournalDetail x) => x.Account)
                                                        where j.Account.Number.IndexOf(account.Number) == 0 && DbFunctions.TruncateTime(j.Journal.Date) < date
                                                        select j;
            double totalDebit = accountJournals.Sum(x => (double?)(x.Debit * x.CurrencyRate)).GetValueOrDefault();
            double totalCredit = accountJournals.Sum(x => (double?)(x.Credit * x.CurrencyRate)).GetValueOrDefault();
            double balance = Math.Abs(totalCredit - totalDebit);
            return totalCredit - totalDebit;
        }

        public static string GetForginCurrancyBalance(this Account account, DateTime date)
        {
            if (account == null)
            {
                throw new NullReferenceException();
            }
            using ERPDataContext db = new ERPDataContext();
            IQueryable<JournalDetail> accountJournals = from j in db.JournalDetails.Include((JournalDetail x) => x.Account)
                                                        where j.Account.Number.IndexOf(account.Number) == 0 && DbFunctions.TruncateTime(j.Journal.Date) <= date && j.CurrencyID != 1
                                                        select j;
            double totalDebit = accountJournals.Sum(x => (double?)(x.Debit * x.CurrencyRate)).GetValueOrDefault();
            double totalCredit = accountJournals.Sum(x => (double?)(x.Credit * x.CurrencyRate)).GetValueOrDefault();
            double balance = Math.Abs(totalCredit - totalDebit);
            string type = ((totalCredit > totalDebit) ? "Créditeur" : "Débiteur");
            return $"{balance:N} {type}";
        }

        public static string GetLocalCurrancyBalance(this Account account, DateTime date)
        {
            if (account == null)
            {
                throw new NullReferenceException();
            }
            using ERPDataContext db = new ERPDataContext();
            IQueryable<JournalDetail> accountJournals = from j in db.JournalDetails.Include((JournalDetail x) => x.Account)
                                                        where j.Account.Number.IndexOf(account.Number) == 0 && DbFunctions.TruncateTime(j.Journal.Date) <= date && j.CurrencyID == 1
                                                        select j;
            double totalDebit = accountJournals.Sum(x => (double?)(x.Debit * x.CurrencyRate)).GetValueOrDefault();
            double totalCredit = accountJournals.Sum(x => (double?)(x.Credit * x.CurrencyRate)).GetValueOrDefault();
            double balance = Math.Abs(totalCredit - totalDebit);
            string type = ((totalCredit > totalDebit) ? "Créditeur" : "Débiteur");
            return $"{balance:N} {type}";
        }

        public static void ConfigerPayDetailsGrid(GridControl gridControl, GridView view)
        {
            Color color = Color.FromArgb(100, DXSkinColors.ForeColors.Question);
            view.Appearance.TopNewRow.BackColor = color;
            view.Appearance.TopNewRow.Options.UseBackColor = true;
            view.NewItemRowText = "Cliquez ici pour ajouter un nouvel élément";
            view.OptionsView.NewItemRowPosition = NewItemRowPosition.Top;
            view.OptionsView.ShowGroupPanel = false;
            RepositoryItemButtonEdit repoDelete = new RepositoryItemButtonEdit();
            EditorButton editorButton = new EditorButton(ButtonPredefines.Glyph);
            editorButton.ImageOptions.SvgImageSize = new Size(15, 15);
            editorButton.ImageOptions.SvgImage = Resources.delete;
            editorButton.Visible = true;
            editorButton.Enabled = true;
            repoDelete.Buttons.Clear();
            repoDelete.Buttons.Add(editorButton);
            repoDelete.Name = "repoDelete";
            repoDelete.TextEditStyle = TextEditStyles.HideTextEditor;
            repoDelete.Click += repoDelete_Click;
            GridColumn ColDelete = new GridColumn();
            ColDelete.ColumnEdit = repoDelete;
            ColDelete.MaxWidth = 25;
            ColDelete.MinWidth = 25;
            ColDelete.Name = "ColDelete";
            ColDelete.Visible = true;
            ColDelete.VisibleIndex = view.Columns.Count();
            ColDelete.Width = 25;
            ColDelete.Fixed = FixedStyle.Right;
            gridControl.RepositoryItems.Add(repoDelete);
            view.Columns.Add(ColDelete);
            view.CustomRowCellEditForEditing += View_CustomRowCellEditForEditing;
            view.CustomColumnDisplayText += View_CustomColumnDisplayText;
            view.RowCountChanged += View_RowCountChanged;
            view.CellValueChanged += View_CellValueChanged;
            view.FocusedColumnChanged += View_FocusedColumnChanged;
            view.FocusedRowChanged += View_FocusedRowChanged;
            OptionsColumn optionsColumn = view.Columns["LocalAmount"].OptionsColumn;
            bool allowFocus = (view.Columns["InsertDate"].OptionsColumn.AllowFocus = false);
            optionsColumn.AllowFocus = allowFocus;
            RepositoryItemLookUpEdit repoCurrancy = new RepositoryItemLookUpEdit();
            using (ERPDataContext db = new ERPDataContext())
            {
                repoCurrancy.DataSource = db.Currencies.AsNoTracking().ToList();
                if (db.Currencies.Count() == 1)
                {
                    view.Columns["CurrancyID"].VisibleIndex = -1;
                    view.Columns["CurrancyRate"].VisibleIndex = -1;
                    view.Columns["LocalAmount"].VisibleIndex = -1;
                }
            }
            repoCurrancy.Columns.Clear();
            repoCurrancy.Columns.Add(new LookUpColumnInfo("Name"));
            repoCurrancy.Columns.Add(new LookUpColumnInfo("LastRate"));
            repoCurrancy.DisplayMember = "Name";
            repoCurrancy.ValueMember = "ID";
            gridControl.RepositoryItems.Add(repoCurrancy);
            view.Columns["CurrancyID"].ColumnEdit = repoCurrancy;
            view.ShownEditor += PayDetailsGridView_ShownEditor;
            view.ValidateRow += View_ValidateRow;
            view.InvalidRowException += View_InvalidRowException;
        }

        private static void View_InvalidRowException(object sender, InvalidRowExceptionEventArgs e)
        {
        }

        public static void View_ValidateRow(object sender, ValidateRowEventArgs e)
        {
            GridView view = sender as GridView;
            if (view.GetRow(e.RowHandle) is PayDetail row)
            {
                if (row.MethodID <= 0)
                {
                    ErrorType error = ErrorType.Critical;
                    view.SetColumnError(view.Columns["MethodID"], "Ce champ est obligatoire", error);
                }
                if (row.CurrancyID <= 0)
                {
                    ErrorType error = ErrorType.Critical;
                    view.SetColumnError(view.Columns["CurrancyID"], "Ce champ est obligatoire", error);
                }
                if (row.CurrancyRate <= 0.0)
                {
                    ErrorType error = ErrorType.Critical;
                    view.SetColumnError(view.Columns["CurrancyRate"], "La valeur doit être supérieure à zéro", error);
                }
                if (row.Amount <= 0.0)
                {
                    ErrorType error = ErrorType.Critical;
                    view.SetColumnError(view.Columns["Amount"], "La valeur doit être supérieure à zéro", error);
                }
            }
        }

        public static void PayDetailsGridView_ShownEditor(object sender, EventArgs e)
        {
            GridView view = sender as GridView;
            if (view.FocusedColumn.FieldName == "MethodType")
            {
                ImageComboBoxEdit editor = (ImageComboBoxEdit)view.ActiveEditor;
                editor.Properties.Items.RemoveAt(4);
            }
        }

        private static void View_FocusedRowChanged(object sender, FocusedRowChangedEventArgs e)
        {
            GridView view = sender as GridView;
            View_FocusedColumnChanged(sender, new FocusedColumnChangedEventArgs(view.FocusedColumn, view.FocusedColumn));
        }

        private static void View_FocusedColumnChanged(object sender, FocusedColumnChangedEventArgs e)
        {
            GridView view = sender as GridView;
            if (!(view.GetFocusedRow() is PayDetail row) || e.FocusedColumn == null || view == null)
            {
                return;
            }
            if (row.MethodType == PayMethodType.CashNote)
            {
                e.FocusedColumn.OptionsColumn.AllowEdit = false;
                return;
            }
            e.FocusedColumn.OptionsColumn.AllowEdit = true;
            switch (e.FocusedColumn.FieldName)
            {
                case "Code":
                    e.FocusedColumn.OptionsColumn.AllowEdit = row.MethodType == PayMethodType.Bank || row.MethodType == PayMethodType.PayCard;
                    break;
                case "CurrancyRate":
                    e.FocusedColumn.OptionsColumn.AllowEdit = row.CurrancyID > 1;
                    break;
                case "MethodID":
                    e.FocusedColumn.OptionsColumn.AllowEdit = CurrentSession.CurrentUser.SettingsProfile.CanChangeDrawer;
                    break;
            }
        }

        private static void View_CellValueChanged(object sender, CellValueChangedEventArgs e)
        {
            GridView view = sender as GridView;
            bool ISNew1flag = e.RowHandle == -**********;
            PayDetail row = view.GetRow(e.RowHandle) as PayDetail;
            if (row == null)
            {
                return;
            }
            using ERPDataContext db = new ERPDataContext();
            string fieldName = e.Column.FieldName;
            string text = fieldName;
            if (!(text == "MethodType"))
            {
                if (text == "CurrancyID")
                {
                    Currency currency = db.Currencies.SingleOrDefault((Currency x) => x.ID == row.CurrancyID);
                    row.CurrancyRate = currency.LastRate;
                }
                return;
            }
            switch (row.MethodType)
            {
                case PayMethodType.Bank:
                    row.MethodID = CurrentSession.DefualtBank?.ID ?? 0;
                    break;
                case PayMethodType.Drawer:
                    row.MethodID = CurrentSession.DefualtDrawer?.ID ?? 0;
                    break;
                case PayMethodType.DrawerWithStamp:
                    row.MethodID = CurrentSession.DefualtDrawer?.ID ?? 0;
                    // سيتم حساب StampAmount تلقائياً في PayDetail
                    break;
                case PayMethodType.PayCard:
                    row.MethodID = CurrentSession.DefualtPayCard?.ID ?? 0;
                    break;
                case PayMethodType.Account:
                    break;
            }
        }

        private static void View_RowCountChanged(object sender, EventArgs e)
        {
            GridView view = sender as GridView;
            if (!(view.DataSource is IEnumerable<PayDetail> rows))
            {
                return;
            }
            var groupedRows = from r in rows
                              group r by new { r.MethodID, r.MethodType, r.CurrancyID, r.CurrancyRate } into g
                              where g.Count() > 1
                              select g;
            foreach (var row in groupedRows)
            {
                IOrderedEnumerable<PayDetail> sortedList = row.OrderByDescending((PayDetail x) => x.ID);
                PayDetail detail = sortedList.First();
                detail.Amount = sortedList.Sum((PayDetail x) => x.Amount);
                view.SetRowCellValue(view.GetRowHandle(rows.ToList().IndexOf(detail)), "Amount", detail.Amount);
                for (int i = 1; i < sortedList.Count(); i++)
                {
                    int rowIndex = rows.ToList().IndexOf(sortedList.ToList()[i]);
                    view.DeleteRow(view.GetRowHandle(rowIndex));
                }
            }
        }

        private static void View_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            if (!(sender is GridView view))
            {
                return;
            }
            PayDetail detail = view.GetRow(view.GetRowHandle(e.ListSourceRowIndex)) as PayDetail;
            if (detail == null)
            {
                return;
            }
            using ERPDataContext db = new ERPDataContext();
            if (!(e.Column.FieldName == "MethodID"))
            {
                return;
            }
            switch (detail.MethodType)
            {
                case PayMethodType.Account:
                    e.DisplayText = db.Accounts.AsNoTracking().SingleOrDefault((Account x) => x.ID == detail.MethodID)?.Name;
                    break;
                case PayMethodType.Drawer:
                case PayMethodType.DrawerWithStamp:
                case PayMethodType.Bank:
                case PayMethodType.PayCard:
                    e.DisplayText = db.BillingDetails.AsNoTracking().SingleOrDefault((BillingDetail x) => x.ID == detail.MethodID)?.DisplayName;
                    break;
            }
        }

        private static void View_CustomRowCellEditForEditing(object sender, CustomRowCellEditEventArgs e)
        {
            if (!(sender is GridView view) || !(view.GetRow(e.RowHandle) is PayDetail detail))
            {
                return;
            }
            using (new ERPDataContext())
            {
                if (e.Column.FieldName == "MethodID")
                {
                    RepositoryItemGridLookUpEdit edit = (RepositoryItemGridLookUpEdit)(e.RepositoryItem = new RepositoryItemGridLookUpEdit());
                    AddPayMethods(edit, (CashNotePaySourceType)detail.MethodType);
                }
            }
        }

        public static void AddPayMethods(RepositoryItemGridLookUpEdit edit, CashNotePaySourceType methodType)
        {
            edit.DataSource = null;
            edit.CustomDisplayText -= Edit_CustomDisplayText;
            edit.DisplayMember = "DisplayName";
            edit.ValueMember = "ID";
            edit.NullText = "";
            edit.BestFitMode = BestFitMode.BestFitResizePopup;
            edit.View.OptionsBehavior.AutoPopulateColumns = false;
            edit.View.OptionsView.ShowColumnHeaders = false;
            edit.View.OptionsView.ShowAutoFilterRow = true;
            edit.View.Columns.Clear();
            edit.PopupFormMinSize = new Size(250, 250);
            ERPDataContext db = new ERPDataContext();
            try
            {
                switch (methodType)
                {
                    case CashNotePaySourceType.Account:
                        edit.DataSource = CurrentSession.EndNodeAccounts;
                        edit.DisplayMember = "Name";
                        edit.View.Columns.AddField("ID").VisibleIndex = 1;
                        edit.View.Columns.AddField("Name").VisibleIndex = 1;
                        edit.View.Columns.AddField("Number").VisibleIndex = 2;
                        break;
                    case CashNotePaySourceType.Bank:
                        edit.DataSource = CurrentSession.AccessableBanks;
                        edit.View.Columns.AddField("ID").VisibleIndex = 1;
                        edit.View.Columns.AddField("DisplayName").VisibleIndex = 1;
                        if (edit.OwnerEdit != null)
                        {
                            edit.OwnerEdit.EditValue = CurrentSession.DefualtBank?.ID;
                        }
                        break;
                    case CashNotePaySourceType.Drawer:
                        edit.DataSource = CurrentSession.AccessableDrawers;
                        edit.View.Columns.AddField("ID").VisibleIndex = 1;
                        edit.View.Columns.AddField("DisplayName").VisibleIndex = 1;
                        if (edit.OwnerEdit != null)
                        {
                            edit.OwnerEdit.EditValue = CurrentSession.DefualtDrawer?.ID;
                        }
                        break;
                    case CashNotePaySourceType.PayCard:
                        edit.DataSource = CurrentSession.AccessablePayCards;
                        edit.View.Columns.AddField("ID").VisibleIndex = 1;
                        edit.View.Columns.AddField("DisplayName").VisibleIndex = 1;
                        if (edit.OwnerEdit != null)
                        {
                            edit.OwnerEdit.EditValue = CurrentSession.DefualtPayCard?.ID;
                        }
                        break;
                    case CashNotePaySourceType.PettyCash:
                        {
                            edit.DataSource = (from x in (from i in db.PettyCashes
                                                          where i.IsClosed == false
                                                          where db.CashNotes.Any((CashNote x) => (int)x.LinkType == 20 && x.LinkID == (int?)i.ID)
                                                          select i).Include((PettyCash x) => x.Branch).Include((PettyCash x) => x.Holder).AsNoTracking()
                                               select new PettyCashReportModel
                                               {
                                                   ID = x.ID,
                                                   HolderName = x.Holder.Name,
                                                   Amount = x.Amount,
                                                   Date = x.Date
                                               }).ToList();
                            PettyCashReportModel PettyCashIns = new PettyCashReportModel();
                            edit.View.Columns.AddField("ID").VisibleIndex = 1;
                            edit.View.Columns.AddField("Amount").VisibleIndex = 1;
                            edit.View.Columns.AddField("Date").VisibleIndex = 1;
                            edit.View.Columns.AddField("HolderName").VisibleIndex = 1;
                            edit.CustomDisplayText += Edit_CustomDisplayText;
                            edit.View.OptionsView.ShowColumnHeaders = true;
                            edit.BestFitMode = BestFitMode.BestFitResizePopup;
                            break;
                        }
                }
            }
            finally
            {
                if (db != null)
                {
                    ((IDisposable)db).Dispose();
                }
            }
        }

        private static void Edit_CustomDisplayText(object sender, CustomDisplayTextEventArgs e)
        {
            if (sender is GridLookUpEdit edit && edit.GetSelectedDataRow() is PettyCashReportModel model && model != null)
            {
                e.DisplayText = $"{model.HolderName} - {model.ID}";
            }
        }

        private static void repoDelete_Click(object sender, EventArgs e)
        {
            GridView view = ((GridControl)((BaseControl)sender).Parent).MainView as GridView;
            if (view.GetFocusedRow() is PayDetail detail)
            {
                if (detail.MethodType == PayMethodType.CashNote)
                {
                    XtraMessageBox.Show("Le détachement n'est pas possible depuis cet écran\nVeuillez effectuer l'opération d'annulation depuis l'écran du document.", "", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
                    return;
                }
                view.DeleteSelectedRows();
                view.Focus();
                view.FocusedRowHandle = -**********;
            }
        }

        private static string GetNewNumber(int? parentID, string ParentNumber)
        {
            return "x";
        }

        private static string GetNewAccountNumber(int? parentID)
        {
            return "x";
        }

        public static string GetNewNumber(int parentID)
        {
            using ERPDataContext db = new ERPDataContext();
            Account parent = db.Accounts.Where((Account a) => a.ID == parentID).First();
            IQueryable<string> ParentChilds = from a in db.Accounts
                                              where a.Parent.ID == parent.ID
                                              select a.Number;
            if (ParentChilds.Count() > 0)
            {
                return (Convert.ToInt64(ParentChilds.Max()) + 1).ToString();
            }
            string num = parent.Number.ToString();
            for (int i = 0; i < AccountsLevel; i++)
            {
                num += "0";
            }
            return num + "1";
        }

        public static void RebuildMasterAccounts()
        {
            using ERPDataContext db = new ERPDataContext();
            List<Account> firstAccounts = db.Accounts.Where((Account x) => x.ParentID == null).ToList();
            foreach (Account acc in firstAccounts)
            {
                acc.Number = (acc.ID + 100).ToString();
                RebuildChildsAccount(acc);
            }
            db.SaveChanges();
        }

        public static void RebuildChildsAccount(Account MasterAccount)
        {
            using ERPDataContext db = new ERPDataContext();
            List<Account> childs = db.Accounts.Where((Account x) => x.ParentID == (int?)MasterAccount.ID).ToList();
            long number = Convert.ToInt64(MasterAccount.Number + "0001");
            foreach (Account acc in childs)
            {
                acc.Number = number.ToString();
                if (MasterAccount.AccountType != 0)
                {
                    acc.AccountType = MasterAccount.AccountType;
                }
                acc.Secrecy = MasterAccount.Secrecy;
                RebuildChildsAccount(acc);
                number++;
            }
            db.SaveChanges();
        }

        public static List<Account> GetTemplate()
        {
            Type t = typeof(Accounts);
            FieldInfo[] fields = t.GetFields(BindingFlags.Static | BindingFlags.Public);
            List<Account> ls = new List<Account>();
            FieldInfo[] array = fields;
            foreach (FieldInfo item in array)
            {
                Account account = (Account)item.GetValue(null);
                ls.Add(account);
                Debug.Print("Name = {0} , ID = {1} , Number = {2}", account.Name, account.ID, account.Number);
            }
            return ls;
        }

        public static void ProcessPay(
            CashTransactionType type,
            int accountID,
            IEnumerable<PayDetail> payDetails,
            Journal journal,
            string statement,
            ERPDataContext db,
            int? costCenterID = null)
        {
            var groupedPayDetails = payDetails
                .GroupBy(d => new { d.CurrancyID, d.CurrancyRate })
                .ToList();        

            foreach (var group in groupedPayDetails)
            {
                var currencyID = group.Key.CurrancyID;
                var currencyRate = group.Key.CurrancyRate;
                var totalAmount = group.Sum(x => x.Amount);

                journal.Details.Add(new JournalDetail
                {
                    Account = db.Accounts.SingleOrDefault(x => x.ID == accountID),
                    Credit = type == CashTransactionType.In ? totalAmount : 0.0,
                    Debit = type == CashTransactionType.Out ? totalAmount : 0.0,
                    Currency = db.Currencies.SingleOrDefault(x => x.ID == currencyID),
                    Statement = statement,
                    CurrencyRate = currencyRate,
                    Journal = journal,
                    CostCenterID = costCenterID
                });

                foreach (var pay in group)
                {
                    int payAccountID = 0;

                    switch (pay.MethodType)
                    {
                        case PayMethodType.Account:
                            payAccountID = pay.MethodID;
                            break;

                        case PayMethodType.Drawer:
                        case PayMethodType.DrawerWithStamp:
                        case PayMethodType.Bank:
                            var billingDetail = db.BillingDetails
                                .Include(x => x.Account)
                                .SingleOrDefault(x => x.ID == pay.MethodID);
                            payAccountID = billingDetail?.AccountID ?? 0;
                            
                            // إضافة قيد الطابع الجبائي إذا كان الدفع بالطابع
                            if (pay.MethodType == PayMethodType.DrawerWithStamp && pay.StampAmount > 0)
                            {
                                var stampAccountID = ERPDataContext.SystemSettings.StampAccount;
                                if (stampAccountID > 0)
                                {
                                    // قيد الطابع الجبائي
                                    journal.Details.Add(new JournalDetail
                                    {
                                        AccountID = stampAccountID,
                                        Debit = pay.StampAmount,
                                        Credit = 0,
                                        Statement = $"طابع جبائي - {pay.Code}",
                                        CostCenterID = costCenterID,
                                        CurrencyID = pay.CurrancyID,
                                        CurrencyRate = pay.CurrancyRate,
                                        Journal = journal
                                    });
                                    
                                    // تقليل المبلغ المدفوع من الخزينة بقيمة الطابع
                                    journal.Details.Add(new JournalDetail
                                    {
                                        AccountID = payAccountID,
                                        Debit = 0,
                                        Credit = pay.StampAmount,
                                        Statement = $"طابع جبائي - {pay.Code}",
                                        CostCenterID = costCenterID,
                                        CurrencyID = pay.CurrancyID,
                                        CurrencyRate = pay.CurrancyRate,
                                        Journal = journal
                                    });
                                }
                            }
                            break;

                        case PayMethodType.PayCard:
                            var card = db.PayCards
                                .Include(x => x.Account)
                                .Include(x => x.Bank)
                                .SingleOrDefault(x => x.ID == pay.MethodID);

                            if (card != null && card.CommissionRate > 0.0)
                            {
                                var commissionAmount = pay.Amount * card.CommissionRate;

                                journal.Details.Add(new JournalDetail
                                {
                                    Account = db.Accounts.SingleOrDefault(x => x.ID == card.CommissionAccountID),
                                    Debit = commissionAmount,
                                    Currency = db.Currencies.SingleOrDefault(x => x.ID == currencyID),
                                    Statement = statement + " - Commission bancaire",
                                    CurrencyRate = pay.CurrancyRate,
                                    Journal = journal,
                                    CostCenterID = costCenterID
                                });

                                journal.Details.Add(new JournalDetail
                                {
                                    Account = db.Accounts.SingleOrDefault(x => x.ID == card.AccountID),
                                    Credit = commissionAmount,
                                    Currency = db.Currencies.SingleOrDefault(x => x.ID == currencyID),
                                    Statement = statement + " - Commission bancaire",
                                    CurrencyRate = pay.CurrancyRate,
                                    Journal = journal,
                                    CostCenterID = costCenterID
                                });
                            }

                            payAccountID = card?.AccountID ?? 0;
                            break;
                    }

                    journal.Details.Add(new JournalDetail
                    {
                        Account = db.Accounts.SingleOrDefault(x => x.ID == payAccountID),
                        Debit = type == CashTransactionType.In ? pay.Amount : 0.0,
                        Credit = type == CashTransactionType.Out ? pay.Amount : 0.0,
                        Currency = db.Currencies.SingleOrDefault(x => x.ID == currencyID),
                        Statement = statement,
                        CurrencyRate = pay.CurrancyRate,
                        Journal = journal,
                        CostCenterID = costCenterID
                    });
                }
            }
        }
    }
}
