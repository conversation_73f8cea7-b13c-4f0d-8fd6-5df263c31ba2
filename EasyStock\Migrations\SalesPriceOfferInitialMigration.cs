﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x020004A9 RID: 1193
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class SalesPriceOfferInitialMigration : DbMigration, IMigrationMetadata
	{
		// Token: 0x060023A0 RID: 9120 RVA: 0x001F4200 File Offset: 0x001F2400
		public override void Up()
		{
			base.AddColumn("dbo.SalesPriceOffers", "SourceID", (ColumnBuilder c) => c.Int(null, false, null, null, null, null, null), null);
			base.AddColumn("dbo.SalesPriceOffers", "SourceType", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
			base.DropColumn("dbo.SalesPriceOfferDetails", "PriceOfferType", null);
		}

		// Token: 0x060023A1 RID: 9121 RVA: 0x001F4284 File Offset: 0x001F2484
		public override void Down()
		{
			base.AddColumn("dbo.SalesPriceOfferDetails", "PriceOfferType", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
			base.DropColumn("dbo.SalesPriceOffers", "SourceType", null);
			base.DropColumn("dbo.SalesPriceOffers", "SourceID", null);
		}

		// Token: 0x17000BAC RID: 2988
		// (get) Token: 0x060023A2 RID: 9122 RVA: 0x001F42E8 File Offset: 0x001F24E8
		string IMigrationMetadata.Id
		{
			get
			{
				return "202108021546224_SalesPriceOfferInitialMigration";
			}
		}

		// Token: 0x17000BAD RID: 2989
		// (get) Token: 0x060023A3 RID: 9123 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000BAE RID: 2990
		// (get) Token: 0x060023A4 RID: 9124 RVA: 0x001F4300 File Offset: 0x001F2500
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B46 RID: 11078
		private readonly ResourceManager Resources = new ResourceManager(typeof(SalesPriceOfferInitialMigration));
	}
}
