﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;
using EasyStock.Classes;

namespace EasyStock.Migrations
{
	// Token: 0x020004D3 RID: 1235
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class addTwoAccountSettings : DbMigration, IMigrationMetadata
	{
		// Token: 0x060024A2 RID: 9378 RVA: 0x001FD2B8 File Offset: 0x001FB4B8
		public override void Up()
		{
			base.AddColumn("dbo.SystemSettings", "AssetsAccount", (ColumnBuilder c) => c.Int(new bool?(false), false, new int?(AccountHelper.Accounts.AssetsAccount.ID), null, null, null, null), null);
			base.AddColumn("dbo.SystemSettings", "LiabilitiesAndOwnersEquity", (ColumnBuilder c) => c.Int(new bool?(false), false, new int?(AccountHelper.Accounts.Liabilites_Owners_EquityAccount.ID), null, null, null, null), null);
		}

		// Token: 0x060024A3 RID: 9379 RVA: 0x00012CDE File Offset: 0x00010EDE
		public override void Down()
		{
			base.DropColumn("dbo.SystemSettings", "LiabilitiesAndOwnersEquity", null);
			base.DropColumn("dbo.SystemSettings", "AssetsAccount", null);
		}

		// Token: 0x17000BF1 RID: 3057
		// (get) Token: 0x060024A4 RID: 9380 RVA: 0x001FD328 File Offset: 0x001FB528
		string IMigrationMetadata.Id
		{
			get
			{
				return "202102241811034_addTwoAccountSettings";
			}
		}

		// Token: 0x17000BF2 RID: 3058
		// (get) Token: 0x060024A5 RID: 9381 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000BF3 RID: 3059
		// (get) Token: 0x060024A6 RID: 9382 RVA: 0x001FD340 File Offset: 0x001FB540
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002BC2 RID: 11202
		private readonly ResourceManager Resources = new ResourceManager(typeof(addTwoAccountSettings));
	}
}
