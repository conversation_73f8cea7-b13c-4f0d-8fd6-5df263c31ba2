﻿<XtraSerializer version="1.0" application="View">
  <property name="#LayoutVersion" />
  <property name="#LayoutScaleFactor">@1,Width=1@1,Height=1</property>
  <property name="OptionsView" isnull="true" iskey="true">
    <property name="ShowAutoFilterRow">true</property>
    <property name="ShowDetailButtons">false</property>
    <property name="ShowGroupPanel">false</property>
    <property name="EnableAppearanceOddRow">true</property>
    <property name="EnableAppearanceEvenRow">true</property>
  </property>
  <property name="ActiveFilterEnabled">true</property>
  <property name="Columns" iskey="true" value="1">
    <property name="Item1" isnull="true" iskey="true">
      <property name="Name">colName1</property>
      <property name="VisibleIndex">0</property>
      <property name="Visible">true</property>
      <property name="Width">232</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
  </property>
  <property name="GroupSummary" iskey="true" value="0" />
  <property name="ActiveFilterString" />
  <property name="GroupSummarySortInfoState" />
  <property name="FindFilterText" />
  <property name="FindPanelVisible">false</property>
</XtraSerializer>