﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
	[Table("WorkOrders")]
	[DisplayColumn("Ordres de travail")]
	public class WorkOrder : BaseNotifyPropertyChangedModel
	{
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		[Display(Name = "N°")]
		[ReadOnly(true)]
		public int ID
		{
			get
			{
				return this.id;
			}
			set
			{
				base.SetProperty<int>(ref this.id, value, "ID");
			}
		}

		[Display(Name = "Code")]
		[Required(ErrorMessage = "Ce champ est obligatoire")]
		public int Code
		{
			get
			{
				return this.code;
			}
			set
			{
				base.SetProperty<int>(ref this.code, value, "Code");
			}
		}

		[Display(Name = "Succursale")]
		[Required(ErrorMessage = "Ce champ est obligatoire")]
		public int BranchID
		{
			get
			{
				return this.branchID;
			}
			set
			{
				base.SetProperty<int>(ref this.branchID, value, "BranchID");
			}
		}

		[Display(Name = "Dépôt")]
		[Required(ErrorMessage = "Ce champ est obligatoire")]
		public int StoreID
		{
			get
			{
				return this.storeID;
			}
			set
			{
				base.SetProperty<int>(ref this.storeID, value, "StoreID");
			}
		}

		[Display(Name = "Date de début")]
		[Required(ErrorMessage = "Ce champ est obligatoire")]
		public DateTime StartDate
		{
			get
			{
				return this.startdate;
			}
			set
			{
				base.SetProperty<DateTime>(ref this.startdate, value, "StartDate");
			}
		}

		[Display(Name = "Date de fin")]
		public DateTime? EndDate
		{
			get
			{
				return this.enddate;
			}
			set
			{
				base.SetProperty<DateTime?>(ref this.enddate, value, "EndDate");
			}
		}

		[Display(Name = "Centre de coût")]
		[Required(ErrorMessage = "Ce champ est obligatoire")]
		public int CostCenter
		{
			get
			{
				return this.costCenter;
			}
			set
			{
				base.SetProperty<int>(ref this.costCenter, value, "CostCenter");
			}
		}

		[Display(Name = "Notes")]
		public string Notes
		{
			get
			{
				return this.notes;
			}
			set
			{
				base.SetProperty<string>(ref this.notes, value, "Notes");
			}
		}

		[Display(Name = "Quantité demandée")]
		[Required(ErrorMessage = "Ce champ est obligatoire")]
		public double TotalOrderQuantity
		{
			get
			{
				return this.Orderquantity;
			}
			set
			{
				base.SetProperty<double>(ref this.Orderquantity, value, "TotalOrderQuantity");
			}
		}

		[Display(Name = "Quantité réelle")]
		[Required(ErrorMessage = "Ce champ est obligatoire")]
		public double TotalAcualQuantity
		{
			get
			{
				return this.Acualquantity;
			}
			set
			{
				base.SetProperty<double>(ref this.Acualquantity, value, "TotalAcualQuantity");
			}
		}

		[Display(Name = "Coût total par défaut")]
		[Required(ErrorMessage = "Ce champ est obligatoire")]
		public double TotalDefaultCost
		{
			get
			{
				return this.defaultCost;
			}
			set
			{
				base.SetProperty<double>(ref this.defaultCost, value, "TotalDefaultCost");
			}
		}

		[Display(Name = "Coût total réel")]
		[Required(ErrorMessage = "Ce champ est obligatoire")]
		public double TotalAcualCost
		{
			get
			{
				return this.AcualCost;
			}
			set
			{
				base.SetProperty<double>(ref this.AcualCost, value, "TotalAcualCost");
			}
		}

		[Display(Name = "Coût unitaire")]
		[Required(ErrorMessage = "Ce champ est obligatoire")]
		public double UnitCost
		{
			get
			{
				return this.unitcost;
			}
			set
			{
				base.SetProperty<double>(ref this.unitcost, value, "UnitCost");
			}
		}

		public BindingList<DefaultExpenses> DefaultExpenses { get; set; }

		public BindingList<ActualCost> ActualCosts { get; set; }

		public BindingList<DefaultMaterialConsumption> DefaultMaterialConsumptions { get; set; }

		public BindingList<ActualMaterialConsumption> AcualMaterialConsumptions { get; set; }

		public BindingList<WorkOrderDetails> WorkOrderDetails { get; set; }

		private int id;

		private int code;

		private int branchID;

		private int storeID;

		private DateTime startdate;

		private DateTime? enddate;

		private int costCenter;

		private string notes;

		private double Orderquantity;

		private double Acualquantity;

		private double defaultCost;

		private double AcualCost;

		private double unitcost;
	}
}
