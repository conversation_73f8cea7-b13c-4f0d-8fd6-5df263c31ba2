﻿namespace EasyStock.HR.Views
{
	// Token: 0x02000047 RID: 71
	public partial class ShiftView : global::EasyStock.HR.MainViews.MasterView
	{
		// Token: 0x0600027E RID: 638 RVA: 0x0002F6BC File Offset: 0x0002D8BC
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600027F RID: 639 RVA: 0x0002F6F4 File Offset: 0x0002D8F4
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.shiftBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControlShiftDay = new DevExpress.XtraGrid.GridControl();
            this.shiftDayBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gridViewShiftDay = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colDay = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTimeTableID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemLookUpEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.timeTableBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.NameTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.StartDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.RepeatEveryTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup3 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForName = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForStartDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForRepeatEvery = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup4 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.shiftBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlShiftDay)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.shiftDayBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewShiftDay)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeTableBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NameTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.StartDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.StartDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.RepeatEveryTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForStartDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForRepeatEvery)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).BeginInit();
            this.SuspendLayout();
            // 
            // dataLayoutControl1
            // 
            this.dataLayoutControl1.Controls.Add(this.gridControl1);
            this.dataLayoutControl1.Controls.Add(this.gridControlShiftDay);
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NameTextEdit);
            this.dataLayoutControl1.Controls.Add(this.StartDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.RepeatEveryTextEdit);
            this.dataLayoutControl1.DataSource = this.shiftBindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 26);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(800, 400);
            this.dataLayoutControl1.TabIndex = 0;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            // 
            // gridControl1
            // 
            this.gridControl1.DataSource = this.shiftBindingSource;
            this.gridControl1.Location = new System.Drawing.Point(358, 44);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(334, 325);
            this.gridControl1.TabIndex = 9;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // shiftBindingSource
            // 
            this.shiftBindingSource.DataSource = typeof(EasyStock.HR.Models.Shift);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colName});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // colName
            // 
            this.colName.FieldName = "Name";
            this.colName.Name = "colName";
            this.colName.Visible = true;
            this.colName.VisibleIndex = 0;
            // 
            // gridControlShiftDay
            // 
            this.gridControlShiftDay.DataSource = this.shiftDayBindingSource;
            this.gridControlShiftDay.Location = new System.Drawing.Point(24, 184);
            this.gridControlShiftDay.MainView = this.gridViewShiftDay;
            this.gridControlShiftDay.Name = "gridControlShiftDay";
            this.gridControlShiftDay.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemLookUpEdit1});
            this.gridControlShiftDay.Size = new System.Drawing.Size(306, 182);
            this.gridControlShiftDay.TabIndex = 8;
            this.gridControlShiftDay.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewShiftDay});
            // 
            // shiftDayBindingSource
            // 
            this.shiftDayBindingSource.DataSource = typeof(EasyStock.HR.Models.ShiftDay);
            // 
            // gridViewShiftDay
            // 
            this.gridViewShiftDay.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colDay,
            this.colTimeTableID});
            this.gridViewShiftDay.GridControl = this.gridControlShiftDay;
            this.gridViewShiftDay.Name = "gridViewShiftDay";
            this.gridViewShiftDay.OptionsView.ShowGroupPanel = false;
            this.gridViewShiftDay.CustomColumnDisplayText += new DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventHandler(this.gridViewShiftDay_CustomColumnDisplayText);
            // 
            // colDay
            // 
            this.colDay.FieldName = "Day";
            this.colDay.Name = "colDay";
            this.colDay.Visible = true;
            this.colDay.VisibleIndex = 0;
            this.colDay.Width = 86;
            // 
            // colTimeTableID
            // 
            this.colTimeTableID.ColumnEdit = this.repositoryItemLookUpEdit1;
            this.colTimeTableID.FieldName = "TimeTableID";
            this.colTimeTableID.Name = "colTimeTableID";
            this.colTimeTableID.Visible = true;
            this.colTimeTableID.VisibleIndex = 1;
            this.colTimeTableID.Width = 195;
            // 
            // repositoryItemLookUpEdit1
            // 
            this.repositoryItemLookUpEdit1.AutoHeight = false;
            this.repositoryItemLookUpEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemLookUpEdit1.CascadingMember = "AttendanceTime";
            this.repositoryItemLookUpEdit1.DataSource = this.timeTableBindingSource;
            this.repositoryItemLookUpEdit1.DisplayMember = "AttendanceTime";
            this.repositoryItemLookUpEdit1.Name = "repositoryItemLookUpEdit1";
            this.repositoryItemLookUpEdit1.ValueMember = "AttendanceTime";
            // 
            // timeTableBindingSource
            // 
            this.timeTableBindingSource.DataSource = typeof(EasyStock.HR.Models.TimeTable);
            // 
            // IDTextEdit
            // 
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.shiftBindingSource, "ID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.IDTextEdit.Location = new System.Drawing.Point(108, 44);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.EditMask = "N0";
            this.IDTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(222, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 4;
            // 
            // NameTextEdit
            // 
            this.NameTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.shiftBindingSource, "Name", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NameTextEdit.Location = new System.Drawing.Point(108, 68);
            this.NameTextEdit.Name = "NameTextEdit";
            this.NameTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.NameTextEdit.Size = new System.Drawing.Size(222, 20);
            this.NameTextEdit.StyleController = this.dataLayoutControl1;
            this.NameTextEdit.TabIndex = 5;
            // 
            // StartDateDateEdit
            // 
            this.StartDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.shiftBindingSource, "StartDate", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.StartDateDateEdit.EditValue = null;
            this.StartDateDateEdit.Location = new System.Drawing.Point(108, 92);
            this.StartDateDateEdit.Name = "StartDateDateEdit";
            this.StartDateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.StartDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.StartDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.StartDateDateEdit.Size = new System.Drawing.Size(222, 20);
            this.StartDateDateEdit.StyleController = this.dataLayoutControl1;
            this.StartDateDateEdit.TabIndex = 6;
            this.StartDateDateEdit.EditValueChanged += new System.EventHandler(this.StartDateDateEdit_EditValueChanged);
            // 
            // RepeatEveryTextEdit
            // 
            this.RepeatEveryTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.shiftBindingSource, "RepeatEvery", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.RepeatEveryTextEdit.Location = new System.Drawing.Point(108, 116);
            this.RepeatEveryTextEdit.Name = "RepeatEveryTextEdit";
            this.RepeatEveryTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.RepeatEveryTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.RepeatEveryTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.RepeatEveryTextEdit.Properties.Mask.EditMask = "N0";
            this.RepeatEveryTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.RepeatEveryTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.RepeatEveryTextEdit.Size = new System.Drawing.Size(222, 20);
            this.RepeatEveryTextEdit.StyleController = this.dataLayoutControl1;
            this.RepeatEveryTextEdit.TabIndex = 7;
            this.RepeatEveryTextEdit.EditValueChanged += new System.EventHandler(this.RepeatEveryTextEdit_EditValueChanged);
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(800, 400);
            this.Root.TextVisible = false;
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup2,
            this.layoutControlGroup3,
            this.emptySpaceItem2,
            this.layoutControlGroup4});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(780, 380);
            // 
            // layoutControlGroup2
            // 
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem1,
            this.emptySpaceItem1});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 140);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(334, 240);
            this.layoutControlGroup2.Text = "Les équipes du jour";
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.gridControlShiftDay;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(310, 186);
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 186);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(310, 10);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlGroup3
            // 
            this.layoutControlGroup3.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForID,
            this.ItemForName,
            this.ItemForStartDate,
            this.ItemForRepeatEvery});
            this.layoutControlGroup3.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup3.Name = "layoutControlGroup3";
            this.layoutControlGroup3.Size = new System.Drawing.Size(334, 140);
            this.layoutControlGroup3.Text = "Informations";
            // 
            // ItemForID
            // 
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.MaxSize = new System.Drawing.Size(310, 24);
            this.ItemForID.MinSize = new System.Drawing.Size(310, 24);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(310, 24);
            this.ItemForID.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForID.TextSize = new System.Drawing.Size(80, 13);
            // 
            // ItemForName
            // 
            this.ItemForName.Control = this.NameTextEdit;
            this.ItemForName.Location = new System.Drawing.Point(0, 24);
            this.ItemForName.Name = "ItemForName";
            this.ItemForName.Size = new System.Drawing.Size(310, 24);
            this.ItemForName.TextSize = new System.Drawing.Size(80, 13);
            // 
            // ItemForStartDate
            // 
            this.ItemForStartDate.Control = this.StartDateDateEdit;
            this.ItemForStartDate.Location = new System.Drawing.Point(0, 48);
            this.ItemForStartDate.Name = "ItemForStartDate";
            this.ItemForStartDate.Size = new System.Drawing.Size(310, 24);
            this.ItemForStartDate.TextSize = new System.Drawing.Size(80, 13);
            // 
            // ItemForRepeatEvery
            // 
            this.ItemForRepeatEvery.Control = this.RepeatEveryTextEdit;
            this.ItemForRepeatEvery.Location = new System.Drawing.Point(0, 72);
            this.ItemForRepeatEvery.Name = "ItemForRepeatEvery";
            this.ItemForRepeatEvery.Size = new System.Drawing.Size(310, 24);
            this.ItemForRepeatEvery.TextSize = new System.Drawing.Size(80, 13);
            // 
            // emptySpaceItem2
            // 
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(696, 0);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(84, 380);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlGroup4
            // 
            this.layoutControlGroup4.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem2});
            this.layoutControlGroup4.Location = new System.Drawing.Point(334, 0);
            this.layoutControlGroup4.Name = "layoutControlGroup4";
            this.layoutControlGroup4.Size = new System.Drawing.Size(362, 380);
            this.layoutControlGroup4.Text = "Les équipes";
            // 
            // layoutControlItem2
            // 
            this.layoutControlItem2.Control = this.gridControl1;
            this.layoutControlItem2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem2.MaxSize = new System.Drawing.Size(338, 329);
            this.layoutControlItem2.MinSize = new System.Drawing.Size(338, 329);
            this.layoutControlItem2.Name = "layoutControlItem2";
            this.layoutControlItem2.Size = new System.Drawing.Size(338, 336);
            this.layoutControlItem2.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem2.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem2.TextVisible = false;
            // 
            // ShiftView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.dataLayoutControl1);
            this.Name = "ShiftView";
            this.Text = "Les équipes";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.shiftBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlShiftDay)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.shiftDayBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewShiftDay)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeTableBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NameTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.StartDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.StartDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.RepeatEveryTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForStartDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForRepeatEvery)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x04000438 RID: 1080
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04000439 RID: 1081
		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		// Token: 0x0400043A RID: 1082
		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		// Token: 0x0400043B RID: 1083
		private global::System.Windows.Forms.BindingSource shiftBindingSource;

		// Token: 0x0400043C RID: 1084
		private global::DevExpress.XtraEditors.TextEdit NameTextEdit;

		// Token: 0x0400043D RID: 1085
		private global::DevExpress.XtraEditors.DateEdit StartDateDateEdit;

		// Token: 0x0400043E RID: 1086
		private global::DevExpress.XtraEditors.TextEdit RepeatEveryTextEdit;

		// Token: 0x0400043F RID: 1087
		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		// Token: 0x04000440 RID: 1088
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		// Token: 0x04000441 RID: 1089
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		// Token: 0x04000442 RID: 1090
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForName;

		// Token: 0x04000443 RID: 1091
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForStartDate;

		// Token: 0x04000444 RID: 1092
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForRepeatEvery;

		// Token: 0x04000445 RID: 1093
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;

		// Token: 0x04000446 RID: 1094
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;

		// Token: 0x04000447 RID: 1095
		private global::DevExpress.XtraGrid.GridControl gridControl1;

		// Token: 0x04000448 RID: 1096
		private global::DevExpress.XtraGrid.Views.Grid.GridView gridView1;

		// Token: 0x04000449 RID: 1097
		private global::DevExpress.XtraGrid.Columns.GridColumn colName;

		// Token: 0x0400044A RID: 1098
		private global::DevExpress.XtraGrid.GridControl gridControlShiftDay;

		// Token: 0x0400044B RID: 1099
		private global::System.Windows.Forms.BindingSource shiftDayBindingSource;

		// Token: 0x0400044C RID: 1100
		private global::DevExpress.XtraGrid.Views.Grid.GridView gridViewShiftDay;

		// Token: 0x0400044D RID: 1101
		private global::DevExpress.XtraGrid.Columns.GridColumn colDay;

		// Token: 0x0400044E RID: 1102
		private global::DevExpress.XtraGrid.Columns.GridColumn colTimeTableID;

		// Token: 0x0400044F RID: 1103
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;

		// Token: 0x04000450 RID: 1104
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;

		// Token: 0x04000451 RID: 1105
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup3;

		// Token: 0x04000452 RID: 1106
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup4;

		// Token: 0x04000453 RID: 1107
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem2;

		// Token: 0x04000454 RID: 1108
		private global::DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repositoryItemLookUpEdit1;

		// Token: 0x04000455 RID: 1109
		private global::System.Windows.Forms.BindingSource timeTableBindingSource;
	}
}
