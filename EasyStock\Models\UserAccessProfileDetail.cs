﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EasyStock.Common;

namespace EasyStock.Models
{
    public class UserAccessProfileDetail : BaseNotifyPropertyChangedModel
    {
        [Display(Name = "N°")]
        [ReadOnly(true)]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        public int? ParentID { get; set; }

        [Display(Name = "")]
        public UserAccessProfile Profile
        {
            get
            {
                return this.profile;
            }
            set
            {
                base.SetProperty<UserAccessProfile>(ref this.profile, value, "Profile");
            }
        }

        [Display(Name = "")]
        public int ProfileId
        {
            get
            {
                return this.profileId;
            }
            set
            {
                base.SetProperty<int>(ref this.profileId, value, "ProfileId");
            }
        }

        [Display(Name = "")]
        public int ObjectId
        {
            get
            {
                return this.objectId;
            }
            set
            {
                base.SetProperty<int>(ref this.objectId, value, "ObjectId");
            }
        }

        [Display(Name = "Écran")]
        [NotMapped]
        public string ObjectName
        {
            get
            {
                return this.objectName;
            }
            set
            {
                base.SetProperty<string>(ref this.objectName, value, "ObjectName");
            }
        }

        [Display(Name = "Ajouter")]
        public bool CanAdd
        {
            get
            {
                return this.canAdd;
            }
            set
            {
                base.SetProperty<bool>(ref this.canAdd, value, "CanAdd");
            }
        }

        [Display(Name = "Supprimer")]
        public bool CanDelete
        {
            get
            {
                return this.canDelete;
            }
            set
            {
                base.SetProperty<bool>(ref this.canDelete, value, "CanDelete");
            }
        }

        [Display(Name = "Modifier")]
        public bool CanEdit
        {
            get
            {
                return this.canEdit;
            }
            set
            {
                base.SetProperty<bool>(ref this.canEdit, value, "CanEdit");
            }
        }

        [Display(Name = "Ouvrir")]
        public bool CanOpen
        {
            get
            {
                return this.canOpen;
            }
            set
            {
                base.SetProperty<bool>(ref this.canOpen, value, "CanOpen");
            }
        }

        [Display(Name = "Imprimer")]
        public bool CanPrint
        {
            get
            {
                return this.canPrint;
            }
            set
            {
                base.SetProperty<bool>(ref this.canPrint, value, "CanPrint");
            }
        }

        [Display(Name = "Afficher")]
        public bool CanShow
        {
            get
            {
                return this.canShow;
            }
            set
            {
                base.SetProperty<bool>(ref this.canShow, value, "CanShow");
            }
        }

        public WindowActions ViewActions { get; set; }

        private int id;

        private UserAccessProfile profile;

        private int profileId;

        private int objectId;

        private string objectName;

        private bool canAdd;

        private bool canDelete;

        private bool canEdit;

        private bool canOpen;

        private bool canPrint;

        private bool canShow;
    }
}
