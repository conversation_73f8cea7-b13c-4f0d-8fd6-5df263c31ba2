﻿using EasyStock.Common;
using EasyStock.Controller;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Procès-verbal de stock")]
    [Table("StockBalanceCorrection")]
    public class StockBalanceCorrection : Bill
    {
        [Display(Name = "Employé du contrôle de stock")]
        [StringLength(150)]
        public string EmployeeName
        {
            get
            {
                return this.employeeName;
            }
            set
            {
                base.SetProperty<string>(ref this.employeeName, value, "EmployeeName");
            }
        }

        [Display(Name = "Gérant du stock")]
        [StringLength(150)]
        public string StoreKeeperName
        {
            get
            {
                return this.storeKeeperName;
            }
            set
            {
                base.SetProperty<string>(ref this.storeKeeperName, value, "StoreKeeperName");
            }
        }

        [Display(Name = "Quantité totale réelle")]
        [ReadOnly(true)]
        public double TotalRealQuantity
        {
            get
            {
                return this.totalRealQuantity;
            }
            set
            {
                base.SetProperty<double>(ref this.totalRealQuantity, value, "TotalRealQuantity");
            }
        }

        [Display(Name = "Coût total réel")]
        [ReadOnly(true)]
        public double TotalRealCost
        {
            get
            {
                return this.totalRealCost;
            }
            set
            {
                base.SetProperty<double>(ref this.totalRealCost, value, "TotalRealCost");
            }
        }

        [Display(Name = "Quantité totale de manque")]
        [ReadOnly(true)]
        public double TotalShortageQuantity
        {
            get
            {
                return this.totalShortageQuantity;
            }
            set
            {
                base.SetProperty<double>(ref this.totalShortageQuantity, value, "TotalShortageQuantity");
            }
        }

        [Display(Name = "Coût total de manque")]
        [ReadOnly(true)]
        public double TotalShortageCost
        {
            get
            {
                return this.totalShortageCost;
            }
            set
            {
                base.SetProperty<double>(ref this.totalShortageCost, value, "TotalShortageCost");
            }
        }

        [Display(Name = "Quantité totale d'excédent")]
        [ReadOnly(true)]
        public double TotalSurplusQuantity
        {
            get
            {
                return this.totalSurplusQuantity;
            }
            set
            {
                base.SetProperty<double>(ref this.totalSurplusQuantity, value, "TotalSurplusQuantity");
            }
        }

        [Display(Name = "Coût total d'excédent")]
        [ReadOnly(true)]
        public double TotalSurplusCost
        {
            get
            {
                return this.totalSurplusCost;
            }
            set
            {
                base.SetProperty<double>(ref this.totalSurplusCost, value, "TotalSurplusCost");
            }
        }

        [Display(Name = "Quantité totale de correction")]
        [ReadOnly(true)]
        public double TotalCorrectionQuantity
        {
            get
            {
                return this.totalCorrectionQuantity;
            }
            set
            {
                base.SetProperty<double>(ref this.totalCorrectionQuantity, value, "TotalCorrectionQuantity");
            }
        }

        [Display(Name = "Coût total de correction")]
        [ReadOnly(true)]
        public double TotalCorrectionCost
        {
            get
            {
                return this.totalCorrectionCost;
            }
            set
            {
                base.SetProperty<double>(ref this.totalCorrectionCost, value, "TotalCorrectionCost");
            }
        }

        public BindingList<StockBalanceBeforeCorrectionDetail> DetailsBefore { get; set; }

        public BindingList<StockBalanceAfterCorrectionDetail> DetailsAfter { get; set; }

        public override void GetNewCode()
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                Type sss = base.GetType();
                int maxLength = (from x in db.StockBalanceCorrections.AsNoTracking()
                                 select x.Code).Max((string x) => (int?)x.Length).GetValueOrDefault();
                bool flag = maxLength == 0;
                if (flag)
                {
                    base.Code = "1";
                }
                else
                {
                    string maxCode = (from x in db.StockBalanceCorrections.AsNoTracking()
                                      where x.Code.Length == maxLength
                                      orderby x.ID descending
                                      select x).FirstOrDefault<StockBalanceCorrection>().Code;
                    bool flag2 = maxCode != string.Empty;
                    if (flag2)
                    {
                        base.Code = maxCode.GetNextSequence();
                    }
                }
            }
        }

        private string employeeName;

        private string storeKeeperName;

        private double totalRealQuantity;

        private double totalRealCost;

        private double totalShortageQuantity;

        private double totalShortageCost;

        private double totalSurplusQuantity;

        private double totalSurplusCost;

        private double totalCorrectionQuantity;

        private double totalCorrectionCost;
    }
}
