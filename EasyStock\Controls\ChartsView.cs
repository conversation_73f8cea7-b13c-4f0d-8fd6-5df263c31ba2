﻿using DevExpress.Utils;
using DevExpress.XtraBars.Docking2010;
using DevExpress.XtraBars.Docking2010.Views;
using DevExpress.XtraBars.Docking2010.Views.Widget;
using DevExpress.XtraEditors;
using EasyStock.Charts;
using EasyStock.Charts.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyStock.Controls
{
    public class ChartsView : XtraUserControl
    {
        private WidgetView view;

        private List<IWidget> DocumntsNamesList = new List<IWidget>
    {
        new SalesPerformanceMonthlyChart(),
        new TopSoldProductChart(),
        new SalesPerformanceDailyChart(),
        new MostProfitableProducts(),
        new LeastSoldProductChart(),
        new StagentProductsCharts(),
        new LeastProfitableProducts(),
        new TotalBanksBalances(),
        new BanksBalances(),
        new DueInvoices()
    };

        private IContainer components = null;

        private System.Windows.Forms.Timer timer1;

        public ChartsView()
        {
            InitializeComponent();
            base.Load += ChartsView_Load;
        }

        private void ChartsView_Load(object sender, EventArgs e)
        {
            IntWidgets();
        }

        private void IntWidgets()
        {
            DocumentManager dM = new DocumentManager();
            view = new WidgetView();
            dM.View = view;
            view.DocumentProperties.AllowClose = false;
            view.AllowDocumentStateChangeAnimation = DefaultBoolean.True;
            view.AllowStartupAnimation = DefaultBoolean.True;
            view.AllowDragDropWobbleAnimation = DefaultBoolean.True;
            StackGroup group1 = new StackGroup();
            StackGroup group2 = new StackGroup();
            group1.Length.UnitType = LengthUnitType.Star;
            group1.Length.UnitValue = 3.0;
            group2.Length.UnitValue = 2.0;
            view.StackGroups.AddRange(new StackGroup[2] { group1, group2 });
            view.QueryControl += widgetView1_QueryControl;
            dM.ContainerControl = this;
            bool flag = false;
            foreach (IWidget chart in DocumntsNamesList)
            {
                Document document = view.AddDocument(chart.Caption, chart.Caption) as Document;
                document.Tag = chart;
                document.Height = chart.Height;
                if (flag)
                {
                    group2.Items.Add(document);
                }
                else
                {
                    group1.Items.Add(document);
                }
                flag = !flag;
            }
            Dock = DockStyle.Fill;
        }

        public void widgetView1_QueryControl(object sender, QueryControlEventArgs e)
        {
            if (e.Document.Tag is Control control)
            {
                e.Control = control;
            }
            else
            {
                e.Control = new Control();
            }
        }

        private async void timer1_Tick(object sender, EventArgs e)
        {
            foreach (IWidget chart in DocumntsNamesList)
            {
                await Task.Run(delegate
                {
                    chart.ReloadDataAsync();
                    Thread.Sleep(500);
                });
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            base.SuspendLayout();
            this.timer1.Enabled = true;
            this.timer1.Interval = 15000;
            this.timer1.Tick += new System.EventHandler(timer1_Tick);
            base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
            base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            base.Name = "ChartsView";
            base.Size = new System.Drawing.Size(708, 415);
            base.ResumeLayout(false);
        }
    }
}
