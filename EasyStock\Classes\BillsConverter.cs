﻿using EasyStock.Common;
using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace EasyStock.Classes
{

    public static class BillsConverter
    {
        public static SalesOrder ConvertToSalesOrder(this SalesPriceOffer sourceBill, SalesOrder distnationBill)
        {
            if (distnationBill == null)
            {
                distnationBill = new SalesOrder();
            }
            sourceBill.CopyPropertiesTo(distnationBill, "ID", "Date", "Code");
            distnationBill.SourceType = BillSourceType.SalesPriceOffer;
            distnationBill.SourceID = sourceBill.ID;
            sourceBill.State = BillState.Completed;
            IEnumerable<SalesOrderDetail> details = sourceBill.Details.OrderByDescending((SalesPriceOfferDetail x) => x.ID).Select(delegate (SalesPriceOfferDetail x)
            {
                SalesOrderDetail salesOrderDetail = new SalesOrderDetail();
                x.CopyPropertiesTo(salesOrderDetail);
                salesOrderDetail.ID = 0;
                salesOrderDetail.SalesOrderID = 0;
                salesOrderDetail.SalesOrder = distnationBill;
                return salesOrderDetail;
            }).AsEnumerable();
            if (distnationBill.Details == null)
            {
                distnationBill.Details = new BindingList<SalesOrderDetail>();
            }
            distnationBill.Details.AddRange(details);
            return distnationBill;
        }

        public static SalesOrder ConvertToSalesOrder(this SalesPriceOffer sourceBill)
        {
            SalesOrder distnationBill = new SalesOrder();
            return sourceBill.ConvertToSalesOrder(distnationBill);
        }

        public static (SalesInvoice invoice, List<InvoiceDetail> details) ConvertToSalesInvoice(this ISalesBill sourceBill, SalesInvoice distnationBill)
        {
            if (distnationBill == null)
            {
                distnationBill = new SalesInvoice();
            }
            sourceBill.CopyPropertiesTo(distnationBill, "ID", "Date", "Code");
            if (sourceBill is SalesOrder)
            {
                distnationBill.SourceType = BillSourceType.SalesOrder;
            }
            else
            {
                if (!(sourceBill is SalesPriceOffer))
                {
                    throw new NotImplementedException();
                }
                distnationBill.SourceType = BillSourceType.SalesPriceOffer;
            }
            distnationBill.SourceID = sourceBill.ID;
            sourceBill.State = BillState.Completed;
            List<InvoiceDetail> details = sourceBill.BaseDetails.Select(delegate (IProductRowDetail x)
            {
                InvoiceDetail invoiceDetail = new InvoiceDetail();
                x.CopyPropertiesTo(invoiceDetail, "ID");
                invoiceDetail.ID = 0;
                invoiceDetail.BillID = 0;
                return invoiceDetail;
            }).ToList();
            return (invoice: distnationBill, details: details);
        }

        public static (SalesInvoice invoice, List<InvoiceDetail> details) ConvertToSalesInvoice(this ISalesBill sourceBill)
        {
            SalesInvoice distnationBill = new SalesInvoice();
            return sourceBill.ConvertToSalesInvoice(distnationBill);
        }
    }
}
