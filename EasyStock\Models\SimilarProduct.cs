﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Les articles similaires")]
    public class SimilarProduct : BaseNotifyPropertyChangedModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "L'article")]
        public Product Product
        {
            get
            {
                return this.product;
            }
            set
            {
                base.SetProperty<Product>(ref this.product, value, "Product");
            }
        }

        [Display(Name = "L'article")]
        public int ProductID
        {
            get
            {
                return this.productID;
            }
            set
            {
                base.SetProperty<int>(ref this.productID, value, "ProductID");
            }
        }

        [Display(Name = "L'article similaire")]
        public Product OtherProduct
        {
            get
            {
                return this.otherProduct;
            }
            set
            {
                base.SetProperty<Product>(ref this.otherProduct, value, "OtherProduct");
            }
        }

        [Display(Name = "L'article similaire")]
        [Range(1, 2147483647, ErrorMessage = "Vous devez sélectionner L'article")]
        public int OtherProductID
        {
            get
            {
                return this.otherProductID;
            }
            set
            {
                base.SetProperty<int>(ref this.otherProductID, value, "OtherProductID");
            }
        }

        private int id;

        private Product product;

        private int productID;

        private Product otherProduct;

        private int otherProductID;
    }
}
