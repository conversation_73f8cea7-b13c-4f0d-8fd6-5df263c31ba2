﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x020004B0 RID: 1200
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class RemoveNameColumnInPettyCash : DbMigration, IMigrationMetadata
	{
		// Token: 0x060023C6 RID: 9158 RVA: 0x00012754 File Offset: 0x00010954
		public override void Up()
		{
			base.DropColumn("dbo.PettyCashes", "Name", null);
		}

		// Token: 0x060023C7 RID: 9159 RVA: 0x00012769 File Offset: 0x00010969
		public override void Down()
		{
			base.AddColumn("dbo.PettyCashes", "Name", (ColumnBuilder c) => c.String(new bool?(false), new int?(150), null, null, null, null, null, null, null), null);
		}

		// Token: 0x17000BB8 RID: 3000
		// (get) Token: 0x060023C8 RID: 9160 RVA: 0x001F4B94 File Offset: 0x001F2D94
		string IMigrationMetadata.Id
		{
			get
			{
				return "202108151803316_RemoveNameColumnInPettyCash";
			}
		}

		// Token: 0x17000BB9 RID: 3001
		// (get) Token: 0x060023C9 RID: 9161 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000BBA RID: 3002
		// (get) Token: 0x060023CA RID: 9162 RVA: 0x001F4BAC File Offset: 0x001F2DAC
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B55 RID: 11093
		private readonly ResourceManager Resources = new ResourceManager(typeof(RemoveNameColumnInPettyCash));
	}
}
