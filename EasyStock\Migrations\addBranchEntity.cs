﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Data.Entity.Migrations.Model;
using System.Resources;

namespace EasyStock.Migrations
{
    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class addBranchEntity : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(addBranchEntity));

        string IMigrationMetadata.Id => "202104142144184_addBranchEntity";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable(
         "dbo.Branches",
         c => new
         {
             ID = c.Int(nullable: false, identity: true),
             Name = c.String(nullable: false, maxLength: 250),
             Phone = c.String(maxLength: 50),
             City = c.String(maxLength: 150),
             Address = c.String(maxLength: 250),
         })
         .PrimaryKey(t => t.ID);

            Sql("INSERT INTO dbo.Branches (Name) VALUES (N'Succursale par défaut')");

            AddColumn("dbo.BillingDetails", "BranchID", c => c.Int());
            AddColumn("dbo.Bills", "BranchID", c => c.Int(nullable: false));
            AddColumn("dbo.Journals", "BranchID", c => c.Int(nullable: false));
            AddColumn("dbo.CashNotes", "BranchID", c => c.Int(nullable: false));
            AddColumn("dbo.CashTransfers", "BranchID", c => c.Int(nullable: false));
            AddColumn("dbo.Personals", "BranchID", c => c.Int());
            AddColumn("dbo.Stores", "BranchID", c => c.Int(nullable: false));
            AddColumn("dbo.RevExpEntries", "BranchID", c => c.Int(nullable: false));
            AddColumn("dbo.UserSettingsProfiles", "DefaultBranch", c => c.Int(nullable: false));

            CreateIndex("dbo.Bills", "BranchID");
            CreateIndex("dbo.CashNotes", "BranchID");
            CreateIndex("dbo.CashTransfers", "BranchID");
            CreateIndex("dbo.Personals", "BranchID");
            CreateIndex("dbo.Stores", "BranchID");

            // Add foreign key constraints
            AddForeignKey("dbo.Bills", "BranchID", "dbo.Branches", "ID");
            AddForeignKey("dbo.CashNotes", "BranchID", "dbo.Branches", "ID");
            AddForeignKey("dbo.CashTransfers", "BranchID", "dbo.Branches", "ID");
            AddForeignKey("dbo.Personals", "BranchID", "dbo.Branches", "ID");
            AddForeignKey("dbo.Stores", "BranchID", "dbo.Branches", "ID");

            // Drop columns if needed
            DropColumn("dbo.BillingDetails", "StoreLink");
            DropColumn("dbo.Journals", "StoreID");
            DropColumn("dbo.CashNotes", "StoreID");
            DropColumn("dbo.CashTransfers", "StoreID");
            DropColumn("dbo.RevExpEntries", "StoreID");
        }

        public override void Down()
        {
            AddColumn("dbo.RevExpEntries", "StoreID", (ColumnBuilder c) => c.Int(false));
            AddColumn("dbo.CashTransfers", "StoreID", (ColumnBuilder c) => c.Int(false));
            AddColumn("dbo.CashNotes", "StoreID", (ColumnBuilder c) => c.Int(false));
            AddColumn("dbo.Journals", "StoreID", (ColumnBuilder c) => c.Int(false));
            AddColumn("dbo.BillingDetails", "StoreLink", (ColumnBuilder c) => c.Int(false));
            DropForeignKey("dbo.Stores", "BranchID", "dbo.Branches");
            DropForeignKey("dbo.Personals", "BranchID", "dbo.Branches");
            DropForeignKey("dbo.CashTransfers", "BranchID", "dbo.Branches");
            DropForeignKey("dbo.CashNotes", "BranchID", "dbo.Branches");
            DropForeignKey("dbo.Bills", "BranchID", "dbo.Branches");
            DropIndex("dbo.Stores", new string[1] { "BranchID" });
            DropIndex("dbo.Personals", new string[1] { "BranchID" });
            DropIndex("dbo.CashTransfers", new string[1] { "BranchID" });
            DropIndex("dbo.CashNotes", new string[1] { "BranchID" });
            DropIndex("dbo.Bills", new string[1] { "BranchID" });
            DropColumn("dbo.UserSettingsProfiles", "DefaultBranch");
            DropColumn("dbo.RevExpEntries", "BranchID");
            DropColumn("dbo.Stores", "BranchID");
            DropColumn("dbo.Personals", "BranchID");
            DropColumn("dbo.CashTransfers", "BranchID");
            DropColumn("dbo.CashNotes", "BranchID");
            DropColumn("dbo.Journals", "BranchID");
            DropColumn("dbo.Bills", "BranchID");
            DropColumn("dbo.BillingDetails", "BranchID");
            DropTable("dbo.Branches");
        }
    }
}
