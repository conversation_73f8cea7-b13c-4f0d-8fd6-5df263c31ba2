﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x02000496 RID: 1174
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class addTaxPercToInvoicesDetails : DbMigration, IMigrationMetadata
	{
		// Token: 0x06002331 RID: 9009 RVA: 0x0001243D File Offset: 0x0001063D
		public override void Up()
		{
			base.AddColumn("dbo.InvoiceDetails", "DiscountPercentage", (ColumnBuilder c) => c.Double(new bool?(false), new double?(0.0), null, null, null, null), null);
		}

		// Token: 0x06002332 RID: 9010 RVA: 0x00012471 File Offset: 0x00010671
		public override void Down()
		{
			base.DropColumn("dbo.InvoiceDetails", "DiscountPercentage", null);
		}

		// Token: 0x17000B8E RID: 2958
		// (get) Token: 0x06002333 RID: 9011 RVA: 0x001F1654 File Offset: 0x001EF854
		string IMigrationMetadata.Id
		{
			get
			{
				return "202106090944357_addTaxPercToInvoicesDetails";
			}
		}

		// Token: 0x17000B8F RID: 2959
		// (get) Token: 0x06002334 RID: 9012 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000B90 RID: 2960
		// (get) Token: 0x06002335 RID: 9013 RVA: 0x001F166C File Offset: 0x001EF86C
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B12 RID: 11026
		private readonly ResourceManager Resources = new ResourceManager(typeof(addTaxPercToInvoicesDetails));
	}
}
