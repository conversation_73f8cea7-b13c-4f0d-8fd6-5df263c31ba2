﻿using DevExpress.XtraEditors;
using DevExpress.XtraEditors.DXErrorProvider;
using DevExpress.XtraLayout.Utils;
using DevExpress.XtraSplashScreen;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.Models;
using EasyStock.Views;
using EasyStock.Views.Financial;
using System;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Threading;
using System.Windows.Forms;

namespace EasyStock.MainViews
{
    public partial class LogonForm : XtraForm
    {
        public static LogonForm Instance
        {
            get
            {
                if (instance == null || instance.IsDisposed)
                {
                    instance = new LogonForm();
                }
                return instance;
            }
        }

        public LogonForm()
        {
            InitializeComponent();
            base.StartPosition = FormStartPosition.CenterScreen;
            btn_Close.Click += Btn_Close_Click;
            btn_LogIn.Click += Btn_LogIn_Click;
            base.Shown += LogonForm_Shown;
        }

        private void LogonForm_Shown(object sender, EventArgs e)
        {
            if (ApplicationSettings.Default.RememberMe)
            {
                checkEdit1.Checked = ApplicationSettings.Default.RememberMe;
                UserNameTextEdit.Text = ApplicationSettings.Default.UserName;
                UserPasswordTextEdit.Text = ApplicationSettings.Default.Password;
            }
        }

        private void Btn_LogIn_Click(object sender, EventArgs e)
        {
            dxValidationProvider1.ValidationMode = ValidationMode.Manual;
            if (!dxValidationProvider1.Validate())
            {
                dxValidationProvider1.ValidationMode = ValidationMode.Auto;
                return;
            }
            IOverlaySplashScreenHandle handeler = SplashScreenManager.ShowOverlayForm(this);
            using (ERPDataContext db = new ERPDataContext())
            {
                User user = db.Users.Include((User x) => x.AccessProfile).Include((User x) => x.AccessProfile.Details).Include((User x) => x.SettingsProfile)
                    .FirstOrDefault((User x) => x.UserName.ToLower().Trim() == UserNameTextEdit.Text.ToLower().Trim());
                Branch branch;
                if (user != null && BCrypt.Net.BCrypt.Verify(UserPasswordTextEdit.Text, user.Password))
                {
                    if (user.Type == UserType.Suspended)
                    {
                        Vip.Notification.Alert.ShowError("Cet utilisateur a été suspendu.\n Veuillez consulter l'administrateur système.");
                        SplashScreenManager.CloseOverlayForm(handeler);
                        return;
                    }
                    if (checkEdit1.Checked)
                    {
                        ApplicationSettings.Default.RememberMe = true;
                        ApplicationSettings.Default.UserName = UserNameTextEdit.Text;
                        ApplicationSettings.Default.Password = UserPasswordTextEdit.Text;
                        ApplicationSettings.Save();
                    }
                    CurrentSession.CurrentUser = user;

                    SplashScreenManager.CloseOverlayForm(handeler);
                    branch = null;
                    if (user.SettingsProfile.DefaultBranch != 0)
                    {
                        branch = db.Branches.SingleOrDefault((Branch x) => x.ID == user.SettingsProfile.DefaultBranch);
                        goto IL_060e;
                    }
                    XtraInputBoxArgs args = new XtraInputBoxArgs();
                    args.Caption = "Entrée en succursale";
                    args.Prompt = "Choisissez une succursale";
                    args.DefaultButtonIndex = 0;
                    LookUpEdit editor = new LookUpEdit();
                    if (user.SettingsProfile.CanChangeBranch)
                    {
                        editor.BindToDataSource(db.Branches.ToList());
                    }
                    else
                    {
                        editor.BindToDataSource(db.Branches.Where((Branch x) => x.ID == user.SettingsProfile.DefaultBranch).ToList());
                    }
                    if (user.SettingsProfile.DefaultBranch > 0)
                    {
                        editor.EditValue = user.SettingsProfile.DefaultBranch;
                    }
                    else
                    {
                        editor.EditValue = 1;
                    }
                    args.Editor = editor;
                    args.DefaultResponse = editor.EditValue;
                    int? result = XtraInputBox.Show(args) as int?;
                    if (result.HasValue && result > 0)
                    {
                        branch = db.Branches.SingleOrDefault((Branch x) => (int?)x.ID == result);
                    }
                    if (branch != null)
                    {
                        goto IL_060e;
                    }
                    Vip.Notification.Alert.ShowError("Échec de la connexion à la succursale");
                }
                goto end_IL_0040;
            IL_060e:
                CurrentSession.CurrentBranch = branch;
                Close();
                if (user.SettingsProfile.ForceStartDrawerPeriod)
                {
                    while (true)
                    {
                        DrawerPeriod drawerPeriod = (from x in db.DrawerPeriods
                                                     where x.PeriodEnd.HasValue == false
                                                     where x.PeriodUserID == user.ID
                                                     select x).FirstOrDefault();
                        if (drawerPeriod != null)
                        {
                            break;
                        }
                        Vip.Notification.Alert.ShowWarning("Vous devez ouvrir votre journal");
                        new OpenDrawerPeriodForm().ShowDialog();
                    }
                }
                HomeForm.Instance.Show();
                return;
            end_IL_0040:;
            }
            SplashScreenManager.CloseOverlayForm(handeler);
            simpleLabelItem2.Visibility = LayoutVisibility.Always;
            Shake(this);
        }

        private void Btn_Close_Click(object sender, EventArgs e)
        {
            Close();
            Application.Exit();
        }

        private static void Shake(Form form)
        {
            Point original = form.Location;
            Random rnd = new Random(1337);
            for (int i = 0; i < 20; i++)
            {
                form.Location = new Point(original.X + rnd.Next(-10, 10), original.Y);
                Thread.Sleep(10);
            }
            form.Location = original;
        }

        private void dropDownButton1_Click(object sender, EventArgs e)
        {
            new ConnectToServerForm().ShowDialog();
        }

        private void btn_LogIn_Click_1(object sender, EventArgs e)
        {
        }
        private static LogonForm instance;
    }
}
