﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class TaxDeclarationMigration : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(TaxDeclarationMigration));

        string IMigrationMetadata.Id => "202107281938018_TaxDeclarationMigration";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.TaxDeclarations", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                FileNumber = c.String(),
                FromDate = c.DateTime(false),
                ToDate = c.DateTime(false),
                BranchID = c.Int(),
                Notes = c.String(),
                Sales = c.Double(false),
                SalesReturn = c.Double(false),
                CitizenSales = c.Double(false),
                CitizenSalesReturn = c.Double(false),
                LocalSales = c.Double(false),
                LocalSalesReturn = c.Double(false),
                ExportsSales = c.Double(false),
                ExportsSalesReturn = c.Double(false),
                ExemptSales = c.Double(false),
                ExemptSalesReturn = c.Double(false),
                Purchase = c.Double(false),
                PurchaseReturn = c.Double(false),
                PurchaseImport = c.Double(false),
                PurchaseImportReturn = c.Double(false),
                PurchaseImportTax = c.Double(false),
                PurchaseImportReverse = c.Double(false),
                PurchaseImportReverseReturn = c.Double(false),
                PurchaseImportReverseTax = c.Double(false),
                PurchaseZeroTax = c.Double(false),
                PurchaseZeroTaxReturn = c.Double(false),
                PurchaseExemptTax = c.Double(false),
                PurchaseExemptTaxReturn = c.Double(false),
                CurrentNetTax = c.Double(false),
                PreviousCorrection = c.Double(false),
                PreviousPostedTax = c.Double(false),
                NetTax = c.Double(false)
            }).PrimaryKey(t => t.ID);
        }

        public override void Down()
        {
            DropTable("dbo.TaxDeclarations");
        }
    }
}
