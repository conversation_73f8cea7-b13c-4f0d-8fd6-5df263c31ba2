﻿namespace EasyStock.HR.Views
{
	// Token: 0x02000032 RID: 50
	public partial class EmpDelayListView : global::EasyStock.HR.MainViews.MasterView
	{
		// Token: 0x0600014D RID: 333 RVA: 0x0000B944 File Offset: 0x00009B44
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600014E RID: 334 RVA: 0x0000B97C File Offset: 0x00009B7C
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.empOvertimeDelayBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colEmpID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repoemp = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.employeeBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.colRegulationID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.reporeg = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.overtimeAndDelayRegulationBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.colDayDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDuration = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colbsencePaid = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colNotes = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.empOvertimeDelayBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoemp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.employeeBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.reporeg)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.overtimeAndDelayRegulationBindingSource)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl1
            // 
            this.gridControl1.DataSource = this.empOvertimeDelayBindingSource;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 26);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repoemp,
            this.reporeg});
            this.gridControl1.Size = new System.Drawing.Size(800, 400);
            this.gridControl1.TabIndex = 4;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // empOvertimeDelayBindingSource
            // 
            this.empOvertimeDelayBindingSource.DataSource = typeof(EasyStock.HR.Models.EmpOvertimeDelay);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colID,
            this.colEmpID,
            this.colRegulationID,
            this.colDayDate,
            this.colDuration,
            this.colbsencePaid,
            this.colNotes});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            // 
            // colID
            // 
            this.colID.FieldName = "ID";
            this.colID.Name = "colID";
            this.colID.OptionsColumn.ReadOnly = true;
            this.colID.Visible = true;
            this.colID.VisibleIndex = 0;
            // 
            // colEmpID
            // 
            this.colEmpID.ColumnEdit = this.repoemp;
            this.colEmpID.FieldName = "EmpID";
            this.colEmpID.Name = "colEmpID";
            this.colEmpID.Visible = true;
            this.colEmpID.VisibleIndex = 1;
            // 
            // repoemp
            // 
            this.repoemp.AutoHeight = false;
            this.repoemp.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repoemp.DataSource = this.employeeBindingSource;
            this.repoemp.DisplayMember = "Name";
            this.repoemp.Name = "repoemp";
            this.repoemp.ValueMember = "ID";
            // 
            // employeeBindingSource
            // 
            this.employeeBindingSource.DataSource = typeof(EasyStock.HR.Models.Employee);
            // 
            // colRegulationID
            // 
            this.colRegulationID.ColumnEdit = this.reporeg;
            this.colRegulationID.FieldName = "RegulationID";
            this.colRegulationID.Name = "colRegulationID";
            this.colRegulationID.Visible = true;
            this.colRegulationID.VisibleIndex = 2;
            // 
            // reporeg
            // 
            this.reporeg.AutoHeight = false;
            this.reporeg.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.reporeg.DataSource = this.overtimeAndDelayRegulationBindingSource;
            this.reporeg.DisplayMember = "Name";
            this.reporeg.Name = "reporeg";
            this.reporeg.ValueMember = "ID";
            // 
            // overtimeAndDelayRegulationBindingSource
            // 
            this.overtimeAndDelayRegulationBindingSource.DataSource = typeof(EasyStock.HR.Models.OvertimeAndDelayRegulation);
            // 
            // colDayDate
            // 
            this.colDayDate.FieldName = "DayDate";
            this.colDayDate.Name = "colDayDate";
            this.colDayDate.Visible = true;
            this.colDayDate.VisibleIndex = 3;
            // 
            // colDuration
            // 
            this.colDuration.FieldName = "Duration";
            this.colDuration.Name = "colDuration";
            this.colDuration.Visible = true;
            this.colDuration.VisibleIndex = 4;
            // 
            // colbsencePaid
            // 
            this.colbsencePaid.FieldName = "bsencePaid";
            this.colbsencePaid.Name = "colbsencePaid";
            this.colbsencePaid.Visible = true;
            this.colbsencePaid.VisibleIndex = 5;
            // 
            // colNotes
            // 
            this.colNotes.FieldName = "Notes";
            this.colNotes.Name = "colNotes";
            this.colNotes.Visible = true;
            this.colNotes.VisibleIndex = 6;
            // 
            // EmpDelayListView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.gridControl1);
            this.Name = "EmpDelayListView";
            this.Text = "Retards";
            this.Controls.SetChildIndex(this.gridControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.empOvertimeDelayBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoemp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.employeeBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.reporeg)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.overtimeAndDelayRegulationBindingSource)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x04000185 RID: 389
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04000186 RID: 390
		private global::DevExpress.XtraGrid.GridControl gridControl1;

		// Token: 0x04000187 RID: 391
		private global::System.Windows.Forms.BindingSource empOvertimeDelayBindingSource;

		// Token: 0x04000188 RID: 392
		private global::DevExpress.XtraGrid.Views.Grid.GridView gridView1;

		// Token: 0x04000189 RID: 393
		private global::DevExpress.XtraGrid.Columns.GridColumn colID;

		// Token: 0x0400018A RID: 394
		private global::DevExpress.XtraGrid.Columns.GridColumn colEmpID;

		// Token: 0x0400018B RID: 395
		private global::DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repoemp;

		// Token: 0x0400018C RID: 396
		private global::System.Windows.Forms.BindingSource employeeBindingSource;

		// Token: 0x0400018D RID: 397
		private global::DevExpress.XtraGrid.Columns.GridColumn colRegulationID;

		// Token: 0x0400018E RID: 398
		private global::DevExpress.XtraGrid.Columns.GridColumn colDayDate;

		// Token: 0x0400018F RID: 399
		private global::DevExpress.XtraGrid.Columns.GridColumn colDuration;

		// Token: 0x04000190 RID: 400
		private global::DevExpress.XtraGrid.Columns.GridColumn colbsencePaid;

		// Token: 0x04000191 RID: 401
		private global::DevExpress.XtraGrid.Columns.GridColumn colNotes;

		// Token: 0x04000192 RID: 402
		private global::DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit reporeg;

		// Token: 0x04000193 RID: 403
		private global::System.Windows.Forms.BindingSource overtimeAndDelayRegulationBindingSource;
	}
}
