﻿using DevExpress.XtraEditors;
using DevExpress.XtraLayout.Utils;
using System;
using System.Windows.Forms;

namespace EasyStock.Controls
{
    public partial class ProgressView : XtraForm
    {
        public ProgressView(bool allowCancalition = true)
        {
            this.InitializeComponent();
            this.IsCanceled = false;
            this.layoutControlItem2.Visibility = (allowCancalition ? LayoutVisibility.Always : LayoutVisibility.Never);
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            bool flag = XtraMessageBox.Show("Voulez-vous annuler? ", this.Text, MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation) == DialogResult.Yes;
            if (flag)
            {
                this.IsCanceled = true;
            }
        }

        public bool IsCanceled;
    }
}
