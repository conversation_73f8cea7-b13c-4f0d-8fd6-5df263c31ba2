﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x0200049A RID: 1178
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class addFieldsToSalesInvoices2 : DbMigration, IMigrationMetadata
	{
		// Token: 0x0600234B RID: 9035 RVA: 0x001F27DC File Offset: 0x001F09DC
		public override void Up()
		{
			base.AddColumn("dbo.SalesInvoices", "DeliveryOrderNumber", (ColumnBuilder c) => c.String(null, null, null, null, null, null, null, null, null), null);
			base.AddColumn("dbo.SalesInvoices", "PostDate", (ColumnBuilder c) => c.DateTime(null, null, null, null, null, null, null), null);
			base.AddColumn("dbo.SalesInvoices", "DropOffLocation", (ColumnBuilder c) => c.String(null, null, null, null, null, null, null, null, null), null);
			base.AddColumn("dbo.SalesInvoices", "DropOffAddress", (ColumnBuilder c) => c.String(null, null, null, null, null, null, null, null, null), null);
			base.AddColumn("dbo.SalesInvoices", "OrderNumber", (ColumnBuilder c) => c.String(null, null, null, null, null, null, null, null, null), null);
			base.AddColumn("dbo.SalesInvoices", "DropOffType", (ColumnBuilder c) => c.String(null, null, null, null, null, null, null, null, null), null);
			base.AddColumn("dbo.UserSettingsProfiles", "ForceStartDrawerPeriod", (ColumnBuilder c) => c.Boolean(new bool?(false), null, null, null, null, null), null);
		}

		// Token: 0x0600234C RID: 9036 RVA: 0x001F2944 File Offset: 0x001F0B44
		public override void Down()
		{
			base.DropColumn("dbo.UserSettingsProfiles", "ForceStartDrawerPeriod", null);
			base.DropColumn("dbo.SalesInvoices", "DropOffType", null);
			base.DropColumn("dbo.SalesInvoices", "OrderNumber", null);
			base.DropColumn("dbo.SalesInvoices", "DropOffAddress", null);
			base.DropColumn("dbo.SalesInvoices", "DropOffLocation", null);
			base.DropColumn("dbo.SalesInvoices", "PostDate", null);
			base.DropColumn("dbo.SalesInvoices", "DeliveryOrderNumber", null);
		}

		// Token: 0x17000B94 RID: 2964
		// (get) Token: 0x0600234D RID: 9037 RVA: 0x001F29D0 File Offset: 0x001F0BD0
		string IMigrationMetadata.Id
		{
			get
			{
				return "202106281237393_addFieldsToSalesInvoices2";
			}
		}

		// Token: 0x17000B95 RID: 2965
		// (get) Token: 0x0600234E RID: 9038 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000B96 RID: 2966
		// (get) Token: 0x0600234F RID: 9039 RVA: 0x001F29E8 File Offset: 0x001F0BE8
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B20 RID: 11040
		private readonly ResourceManager Resources = new ResourceManager(typeof(addFieldsToSalesInvoices2));
	}
}
