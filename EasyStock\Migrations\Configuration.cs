﻿using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.Entity;
using System.Data.Entity.Migrations;
using System.Linq;
using System.Windows.Forms;

namespace EasyStock.Migrations
{
    internal sealed class Configuration : DbMigrationsConfiguration<ERPDataContext>
    {
        public Configuration()
        {
            base.AutomaticMigrationsEnabled = true;
            base.AutomaticMigrationDataLossAllowed = true;
            DbMigrator migrator = new DbMigrator(this);
            this._isInitialized = migrator.GetDatabaseMigrations().Any<string>();
        }

        protected override void Seed(ERPDataContext context)
        {
            this.InitializeDatabase(context);
        }

        public void InitializeDatabase(ERPDataContext context)
        {
            bool flag = !this._isInitialized;
            if (flag)
            {
                bool flag2 = context.Database.Connection.ConnectionString.Contains("localdb");
                if (flag2)
                {
                }
            }
            bool flag3 = context.Accounts.Count<Account>() == 0;
            if (flag3)
            {
                context.Database.ExecuteSqlCommand("SET IDENTITY_INSERT dbo.Accounts ON", Array.Empty<object>());
                using (DbContextTransaction transaction = context.Database.BeginTransaction())
                {
                    context.Accounts.AddRange(AccountHelper.GetTemplate());
                    context.SaveChanges();
                    transaction.Commit();
                }
                context.Database.ExecuteSqlCommand("SET IDENTITY_INSERT dbo.Accounts OFF", Array.Empty<object>());
                AccountHelper.RebuildMasterAccounts();
            }
            bool flag4 = context.Currencies.Count<Currency>() == 0;
            if (flag4)
            {
                context.Currencies.Add(new Currency
                {
                    LastRate = 1.0,
                    Name = "DA",
                    Piaster1 = "سنتيم",
                    Piaster2 = "سنتيم",
                    Piaster3 = "سنتيم",
                    Pound1 = "دينار",
                    Pound2 = "دينار",
                    Pound3 = "دينار"
                });
            }
            bool flag5 = context.InvoiceShortCuts.Count<InvoiceShortCut>() < Enum.GetValues(typeof(InvoiceShortCutAction)).OfType<InvoiceShortCutAction>().Count<InvoiceShortCutAction>();
            if (flag5)
            {
                List<InvoiceShortCutAction> localInvoiceShortCut = Enum.GetValues(typeof(InvoiceShortCutAction)).OfType<InvoiceShortCutAction>().ToList<InvoiceShortCutAction>();
                List<InvoiceShortCutAction> dbInvoiceShortCuts = (from x in context.InvoiceShortCuts
                                                                  select x.Action).ToList<InvoiceShortCutAction>();
                localInvoiceShortCut.RemoveAll((InvoiceShortCutAction x) => dbInvoiceShortCuts.Contains(x));
                foreach (InvoiceShortCutAction item in localInvoiceShortCut)
                {
                    context.InvoiceShortCuts.Add(new InvoiceShortCut
                    {
                        Action = item,
                        Key = Keys.None,
                        Modifier = Keys.None
                    });
                }
            }
            UserSettingsProfile userSettingsProfile = context.UserSettingsProfiles.FirstOrDefault<UserSettingsProfile>();
            bool flag6 = userSettingsProfile == null;
            if (flag6)
            {
                UserSettingsProfile userSettingsProfile2 = new UserSettingsProfile();
                userSettingsProfile2.Name = "Modèle par défaut";
                userSettingsProfile2.AccountSecrecyLevel = SecracyLevel.High;
                userSettingsProfile2.PrintAfterSave = true;
                userSettingsProfile2.WhenSellingItemWithQtyMoreThanAvailableQty = RedundancyOptions.ShowWarning;
                userSettingsProfile2.WhenSellingToACustomerExceededMaxCredit = RedundancyOptions.ShowWarning;
                userSettingsProfile2.CanChangeCashNoteDate = true;
                userSettingsProfile2.CanChangeCustomer = true;
                userSettingsProfile2.CanChangeDrawer = true;
                userSettingsProfile2.CanChangeItemPriceInPurchase = true;
                userSettingsProfile2.CanChangeItemPriceInSales = true;
                userSettingsProfile2.CanChangePayMethod = true;
                userSettingsProfile2.CanChangePurchaseInvoiceDate = true;
                userSettingsProfile2.CanChangeQtyInSales = true;
                userSettingsProfile2.CanChangeSalesInvoiceDate = true;
                userSettingsProfile2.CanChangeStore = true;
                userSettingsProfile2.CanChangeTax = true;
                userSettingsProfile2.CanChangeVendor = true;
                userSettingsProfile2.CanDeleteItemsInInvoices = true;
                userSettingsProfile2.CanChangeBranch = true;
                Store store = context.Stores.FirstOrDefault<Store>();
                userSettingsProfile2.DefaultStore = ((store != null) ? store.ID : 0);
                Customer customer = context.Customers.FirstOrDefault<Customer>();
                userSettingsProfile2.DefaultCustomer = ((customer != null) ? customer.ID : 0);
                Drawer drawer = context.Drawers.FirstOrDefault<Drawer>();
                userSettingsProfile2.DefaultDrawer = ((drawer != null) ? drawer.ID : 0);
                PayCard payCard = context.PayCards.FirstOrDefault<PayCard>();
                userSettingsProfile2.DefaultPayCard = ((payCard != null) ? payCard.ID : 0);
                Store store2 = context.Stores.FirstOrDefault<Store>();
                userSettingsProfile2.DefaultRawStore = ((store2 != null) ? store2.ID : 0);
                Vendor vendor = context.Vendors.FirstOrDefault<Vendor>();
                userSettingsProfile2.DefaultVendor = ((vendor != null) ? vendor.ID : 0);
                userSettingsProfile2.DefaultPayMethodInSales = PayType.Cash;
                userSettingsProfile2.InvoicePrintMode = PrintMode.ShowPreview;
                userSettingsProfile2.MaxDiscountInInvoice = 1.0;
                userSettingsProfile2.PrintCreditInvoices = true;
                userSettingsProfile2.ProductSecrecyLevel = SecracyLevel.High;
                userSettingsProfile2.ShowPayFormOnSavingNewInvoices = false;
                userSettingsProfile2.WhenSellingItemReachedReOrderLevel = RedundancyOptions.ShowWarning;
                userSettingsProfile2.WhenSellingItemWithPriceLowerThanCostPrice = RedundancyOptions.ShowWarning;
                userSettingsProfile = userSettingsProfile2;
                context.UserSettingsProfiles.Add(userSettingsProfile);
                context.SaveChanges();
            }
            UserAccessProfile accessProfile = context.UserAccessProfiles.FirstOrDefault<UserAccessProfile>();
            bool flag7 = accessProfile == null;
            if (flag7)
            {
                accessProfile = new UserAccessProfile
                {
                    Name = "Modèle par défaut",
                    Details = new BindingList<UserAccessProfileDetail>(),
                    Users = new BindingList<User>()
                };
                NavigationObject[] codeIds = NavigationObjects.AllObjects.ToArray();
                foreach (NavigationObject item2 in codeIds)
                {
                    accessProfile.Details.Add(new UserAccessProfileDetail
                    {
                        CanAdd = item2.CanAdd,
                        CanDelete = item2.CanDelete,
                        CanEdit = item2.CanEdit,
                        CanOpen = item2.CanOpen,
                        CanPrint = item2.CanPrint,
                        CanShow = item2.CanShow,
                        ViewActions = item2.ViewActions,
                        ObjectId = item2.ID,
                        ParentID = item2.ParentID,
                        Profile = accessProfile
                    });
                }
                context.UserAccessProfiles.Add(accessProfile);
                context.SaveChanges();
            }
            User user = context.Users.FirstOrDefault<User>();
            bool flag8 = user == null;
            if (flag8)
            {
                user = new User
                {
                    AccessProfile = accessProfile,
                    Name = "Administrator",
                    Password = "$2a$11$ISJr0pYSRrvmATCtjE5eP.rzyNw4xoD8fOF6lMRO6Chn5Uizh7EGC",
                    SettingsProfile = userSettingsProfile,
                    Type = UserType.Administrator,
                    UserName = "admin"
                };
                context.Users.Add(user);
            }
            context.SaveChanges();
        }
        private readonly bool _isInitialized;
    }
}
