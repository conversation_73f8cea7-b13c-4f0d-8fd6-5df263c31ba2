﻿

<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{039224D9-0485-4303-842A-06B5EEFDF284}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>EasyStock.HR</RootNamespace>
    <AssemblyName>EasyStock.HR</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Data.Desktop.v21.1">
      <HintPath>..\..\..\..\..\Program Files (x86)\Elhayani Tech\EasyStock\UpdateFiles\Debug\DevExpress.Data.Desktop.v21.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Data.v21.1">
      <HintPath>..\..\..\..\..\Program Files (x86)\Elhayani Tech\EasyStock\UpdateFiles\Debug\DevExpress.Data.v21.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v21.1">
      <HintPath>..\..\..\..\..\Program Files (x86)\Elhayani Tech\EasyStock\UpdateFiles\Debug\DevExpress.Utils.v21.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraBars.v21.1">
      <HintPath>..\..\..\..\..\Program Files (x86)\Elhayani Tech\EasyStock\UpdateFiles\Debug\DevExpress.XtraBars.v21.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v21.1">
      <HintPath>..\..\..\..\..\Program Files (x86)\Elhayani Tech\EasyStock\UpdateFiles\Debug\DevExpress.XtraEditors.v21.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraGrid.v21.1">
      <HintPath>..\..\..\..\..\Program Files (x86)\Elhayani Tech\EasyStock\UpdateFiles\Debug\DevExpress.XtraGrid.v21.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraLayout.v21.1">
      <HintPath>..\..\..\..\..\Program Files (x86)\Elhayani Tech\EasyStock\UpdateFiles\Debug\DevExpress.XtraLayout.v21.1.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework">
      <HintPath>..\..\..\..\..\Program Files (x86)\Elhayani Tech\EasyStock\UpdateFiles\Debug\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EasyStock.Common">
      <HintPath>..\..\..\..\..\Program Files (x86)\Elhayani Tech\EasyStock\UpdateFiles\Debug\EasyStock.Common.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BLL\AbsenceRegulationBLL.cs" />
    <Compile Include="BLL\DepartmentBLL.cs" />
    <Compile Include="BLL\EmpAttendanceBLL.cs" />
    <Compile Include="BLL\EmployeeBLL.cs" />
    <Compile Include="BLL\ExternalBLL.cs" />
    <Compile Include="BLL\GroupBLL.cs" />
    <Compile Include="BLL\JobBLL.cs" />
    <Compile Include="BLL\OvertimeAndDelayRegulationBLL.cs" />
    <Compile Include="BLL\PenaltyRewardBLL.cs" />
    <Compile Include="BLL\ReferenceBLL.cs" />
    <Compile Include="BLL\SalaryExtensionBLL.cs" />
    <Compile Include="BLL\ShiftBLL.cs" />
    <Compile Include="BLL\WorkLeaveReturnBLL.cs" />
    <Compile Include="CalculationType.cs" />
    <Compile Include="Classes\Settings.cs" />
    <Compile Include="Class\Static.cs" />
    <Compile Include="ContractTypes.cs" />
    <Compile Include="CustomModels\CustomAccount.cs" />
    <Compile Include="CustomModels\CustomBranch.cs" />
    <Compile Include="EAbsencePaid.cs" />
    <Compile Include="EAbsenceType.cs" />
    <Compile Include="ECalculationType.cs" />
    <Compile Include="EDeductionType.cs" />
    <Compile Include="EMethod.cs" />
    <Compile Include="EmpState.cs" />
    <Compile Include="EOvertimeDelay.cs" />
    <Compile Include="ESalaryCalculation.cs" />
    <Compile Include="ESalaryPeriod.cs" />
    <Compile Include="EShiftType.cs" />
    <Compile Include="EStatus.cs" />
    <Compile Include="EType.cs" />
    <Compile Include="EVacationType.cs" />
    <Compile Include="ExtensionType.cs" />
    <Compile Include="GenderType.cs" />
    <Compile Include="HRDataContext.cs" />
    <Compile Include="MainViews\MasterView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainViews\MasterView.Designer.cs">
      <DependentUpon>MasterView.cs</DependentUpon>
    </Compile>
    <Compile Include="MaritalStatus.cs" />
    <Compile Include="Migrations\addempphoto.cs" />
    <Compile Include="Migrations\addtableWorkLeaveReturn.cs" />
    <Compile Include="Migrations\allowEmpPropertyExpensesAccNotRequired.cs" />
    <Compile Include="Migrations\allowEmpPropertyNull.cs" />
    <Compile Include="Migrations\changesEmployee.cs" />
    <Compile Include="Migrations\changesEmployeeexpensesAndAcuredAccountToId.cs" />
    <Compile Include="Migrations\Configuration.cs" />
    <Compile Include="Migrations\initial.cs" />
    <Compile Include="Migrations\penaltyrewardChangepropEmp.cs" />
    <Compile Include="Migrations\penaltyrewardTable.cs" />
    <Compile Include="Migrations\tbReferenceandEmpProperties.cs" />
    <Compile Include="Migrations\UpdateEmpSalaryExtientionTable.cs" />
    <Compile Include="Migrations\UpdateLoanTable.cs" />
    <Compile Include="MilitarilyStatus.cs" />
    <Compile Include="Models\AbsenceRegulation.cs" />
    <Compile Include="Models\Department.cs" />
    <Compile Include="Models\EmpAbsence.cs" />
    <Compile Include="Models\EmpAttendance.cs" />
    <Compile Include="Models\EmpLoan.cs" />
    <Compile Include="Models\EmpLoanDetails.cs" />
    <Compile Include="Models\Employee.cs" />
    <Compile Include="Models\EmpMission.cs" />
    <Compile Include="Models\EmpOvertimeDelay.cs" />
    <Compile Include="Models\EmpSalaryExtension.cs" />
    <Compile Include="Models\EmpShift.cs" />
    <Compile Include="Models\EmpVacation.cs" />
    <Compile Include="Models\Group.cs" />
    <Compile Include="Models\Job.cs" />
    <Compile Include="Models\OfficialVacation.cs" />
    <Compile Include="Models\OvertimeAndDelayRegulation.cs" />
    <Compile Include="Models\OvertimeAndDelayRegulationMinutesTable.cs" />
    <Compile Include="Models\PenaltyReward.cs" />
    <Compile Include="Models\Reference.cs" />
    <Compile Include="Models\SalaryExtension.cs" />
    <Compile Include="Models\SalaryRegulation.cs" />
    <Compile Include="Models\SalaryRegulationExtension.cs" />
    <Compile Include="Models\Shift.cs" />
    <Compile Include="Models\ShiftDay.cs" />
    <Compile Include="Models\TimeTable.cs" />
    <Compile Include="Models\WorkLeaveReturn.cs" />
    <Compile Include="NavigationObjects.cs" />
    <Compile Include="PenaltyRewardType.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.cs" />
    <Compile Include="ReferenceType.cs" />
    <Compile Include="Views\AbcenceRegulationView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\AbcenceRegulationView.Designer.cs">
      <DependentUpon>AbcenceRegulationView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\DepartmentView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\DepartmentView.Designer.cs">
      <DependentUpon>DepartmentView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpAbsenceListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpAbsenceListView.Designer.cs">
      <DependentUpon>EmpAbsenceListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpAbsenceView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpAbsenceView.Designer.cs">
      <DependentUpon>EmpAbsenceView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpAttendenceView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpAttendenceView.Designer.cs">
      <DependentUpon>EmpAttendenceView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpDelayListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpDelayListView.Designer.cs">
      <DependentUpon>EmpDelayListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpDelayView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpDelayView.Designer.cs">
      <DependentUpon>EmpDelayView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpListView.Designer.cs">
      <DependentUpon>EmpListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpLoanListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpLoanListView.Designer.cs">
      <DependentUpon>EmpLoanListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpLoanView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpLoanView.Designer.cs">
      <DependentUpon>EmpLoanView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmployeeView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmployeeView.Designer.cs">
      <DependentUpon>EmployeeView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpMissionListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpMissionListView.Designer.cs">
      <DependentUpon>EmpMissionListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpMissionView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpMissionView.Designer.cs">
      <DependentUpon>EmpMissionView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpOvertimeListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpOvertimeListView.Designer.cs">
      <DependentUpon>EmpOvertimeListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpOvertimeView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpOvertimeView.Designer.cs">
      <DependentUpon>EmpOvertimeView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpShiftListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpShiftListView.Designer.cs">
      <DependentUpon>EmpShiftListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpShiftView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpShiftView.Designer.cs">
      <DependentUpon>EmpShiftView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpVacationListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpVacationListView.Designer.cs">
      <DependentUpon>EmpVacationListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpVacationView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpVacationView.Designer.cs">
      <DependentUpon>EmpVacationView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpView.Designer.cs">
      <DependentUpon>EmpView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\GroupView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\GroupView.Designer.cs">
      <DependentUpon>GroupView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\JobView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\JobView.Designer.cs">
      <DependentUpon>JobView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\OfficialVacationView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\OfficialVacationView.Designer.cs">
      <DependentUpon>OfficialVacationView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\OvertimeAndDelayRegulationView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\OvertimeAndDelayRegulationView.Designer.cs">
      <DependentUpon>OvertimeAndDelayRegulationView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\PenaltyListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\PenaltyListView.Designer.cs">
      <DependentUpon>PenaltyListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\PenaltyView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\PenaltyView.Designer.cs">
      <DependentUpon>PenaltyView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\RewardListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\RewardListView.Designer.cs">
      <DependentUpon>RewardListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\RewardView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\RewardView.Designer.cs">
      <DependentUpon>RewardView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\SalaryExtensionView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\SalaryExtensionView.Designer.cs">
      <DependentUpon>SalaryExtensionView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\SalaryRegulationView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\SalaryRegulationView.Designer.cs">
      <DependentUpon>SalaryRegulationView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ShiftView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ShiftView.Designer.cs">
      <DependentUpon>ShiftView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\TimeTableView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\TimeTableView.Designer.cs">
      <DependentUpon>TimeTableView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Work\WorkLeaveListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Work\WorkLeaveListView.Designer.cs">
      <DependentUpon>WorkLeaveListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Work\WorkLeaveView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Work\WorkLeaveView.Designer.cs">
      <DependentUpon>WorkLeaveView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Work\WorkReturnListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Work\WorkReturnListView.Designer.cs">
      <DependentUpon>WorkReturnListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Work\WorkReturnView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Work\WorkReturnView.Designer.cs">
      <DependentUpon>WorkReturnView.cs</DependentUpon>
    </Compile>
    <Compile Include="WorkLeaveReturnType.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="dll.licenses" />
    <EmbeddedResource Include="MainViews\MasterView.resources" />
    <EmbeddedResource Include="Migrations\addempphoto.resources" />
    <EmbeddedResource Include="Migrations\addtableWorkLeaveReturn.resources" />
    <EmbeddedResource Include="Migrations\allowEmpPropertyExpensesAccNotRequired.resources" />
    <EmbeddedResource Include="Migrations\allowEmpPropertyNull.resources" />
    <EmbeddedResource Include="Migrations\changesEmployee.resources" />
    <EmbeddedResource Include="Migrations\changesEmployeeexpensesAndAcuredAccountToId.resources" />
    <EmbeddedResource Include="Migrations\initial.resources" />
    <EmbeddedResource Include="Migrations\penaltyrewardChangepropEmp.resources" />
    <EmbeddedResource Include="Migrations\penaltyrewardTable.resources" />
    <EmbeddedResource Include="Migrations\tbReferenceandEmpProperties.resources" />
    <EmbeddedResource Include="Migrations\UpdateEmpSalaryExtientionTable.resources" />
    <EmbeddedResource Include="Migrations\UpdateLoanTable.resources" />
    <EmbeddedResource Include="Properties\Resources.resources" />
    <EmbeddedResource Include="Views\AbcenceRegulationView.resources" />
    <EmbeddedResource Include="Views\DepartmentView.resources" />
    <EmbeddedResource Include="Views\EmpAbsenceListView.resources" />
    <EmbeddedResource Include="Views\EmpAbsenceView.resources" />
    <EmbeddedResource Include="Views\EmpAttendenceView.resources" />
    <EmbeddedResource Include="Views\EmpDelayListView.resources" />
    <EmbeddedResource Include="Views\EmpDelayView.resources" />
    <EmbeddedResource Include="Views\EmpListView.resources" />
    <EmbeddedResource Include="Views\EmpLoanListView.resources" />
    <EmbeddedResource Include="Views\EmpLoanView.resources" />
    <EmbeddedResource Include="Views\EmployeeView.resources" />
    <EmbeddedResource Include="Views\EmpMissionListView.resources" />
    <EmbeddedResource Include="Views\EmpMissionView.resources" />
    <EmbeddedResource Include="Views\EmpOvertimeListView.resources" />
    <EmbeddedResource Include="Views\EmpOvertimeView.resources" />
    <EmbeddedResource Include="Views\EmpShiftListView.resources" />
    <EmbeddedResource Include="Views\EmpShiftView.resources" />
    <EmbeddedResource Include="Views\EmpVacationListView.resources" />
    <EmbeddedResource Include="Views\EmpVacationView.resources" />
    <EmbeddedResource Include="Views\EmpView.resources" />
    <EmbeddedResource Include="Views\GroupView.resources" />
    <EmbeddedResource Include="Views\JobView.resources" />
    <EmbeddedResource Include="Views\OfficialVacationView.resources" />
    <EmbeddedResource Include="Views\OvertimeAndDelayRegulationView.resources" />
    <EmbeddedResource Include="Views\PenaltyRewardView\PenaltyListView.resources" />
    <EmbeddedResource Include="Views\PenaltyRewardView\PenaltyView.resources" />
    <EmbeddedResource Include="Views\PenaltyRewardView\RewardListView.resources" />
    <EmbeddedResource Include="Views\PenaltyRewardView\RewardView.resources" />
    <EmbeddedResource Include="Views\SalaryExtensionView.resources" />
    <EmbeddedResource Include="Views\SalaryRegulationView.resources" />
    <EmbeddedResource Include="Views\ShiftView.resources" />
    <EmbeddedResource Include="Views\TimeTableView.resources" />
    <EmbeddedResource Include="Views\Work\WorkLeaveListView.resources" />
    <EmbeddedResource Include="Views\Work\WorkLeaveView.resources" />
    <EmbeddedResource Include="Views\Work\WorkReturnListView.resources" />
    <EmbeddedResource Include="Views\Work\WorkReturnView.resources" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>