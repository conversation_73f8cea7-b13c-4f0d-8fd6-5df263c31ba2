﻿using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;

namespace EasyStock.Classes
{

    public static class ProductHelper
    {
        public struct ItemBalanceInStore
        {
            public int ItemID;

            public int StoreID;

            public int UnitID;

            public double Balance;
        }

        public class CostWithTansactionSource
        {
            public double Cost { get; set; }

            public List<TransactionLinkModel> Sources { get; set; }
        }

        [CompilerGenerated]
        private sealed class _003C_003Ec__DisplayClass10_0
        {
            public int productID;

            public int storeID;

            public DateTime transDate;

            public int ExecludeRow;

            internal bool _003CGetCostOfNextProduct_003Eb__3(ProductUnit x)
            {
                return x.ProductID == productID;
            }

            internal bool _003CGetCostOfNextProduct_003Eb__7(ProductUnit x)
            {
                return x.ProductID == productID;
            }
        }

        public static void GetNewCode(this Product product)
        {
            using ERPDataContext db = new ERPDataContext();
            int maxLength = (from x in db.Products.AsNoTracking()
                             select x.Code).Max((Expression<Func<string, int?>>)((string x) => x.Length)).GetValueOrDefault();
            if (maxLength == 0)
            {
                product.Code = "PRD0001";
                return;
            }
            string maxCode = (from x in db.Products.AsNoTracking()
                              where x.Code.Length == maxLength
                              orderby x.ID descending
                              select x).FirstOrDefault().Code;
            if (maxCode != string.Empty)
            {
                product.Code = maxCode.GetNextSequence();
            }
        }

        public static void GetNewCode(this GroupOfProducts product)
        {
            using ERPDataContext db = new ERPDataContext();
            int maxLength = (from x in db.GroupsOfProducts.AsNoTracking()
                             select x.Code).Max((Expression<Func<string, int?>>)((string x) => x.Length)).GetValueOrDefault();
            if (maxLength == 0)
            {
                product.Code = "GOP0001";
                return;
            }
            string maxCode = (from x in db.GroupsOfProducts.AsNoTracking()
                              where x.Code.Length == maxLength
                              orderby x.ID descending
                              select x).FirstOrDefault().Code;
            if (maxCode != string.Empty)
            {
                product.Code = maxCode.GetNextSequence();
            }
        }

        public static void GetNewUnitBarcode(this ProductUnitBarcode unitBarcode)
        {
            int maxLength = unitBarcode.Unit.Barcodes.Select((ProductUnitBarcode x) => x.Barcode).Max((Func<string, int?>)((string x) => x.Length)).GetValueOrDefault();
            int unitIndex = unitBarcode.Product.Units.ToList().IndexOf(unitBarcode.Unit) + 1;
            if (maxLength == 0)
            {
                unitBarcode.Barcode = unitBarcode.Product.Code + "U0" + unitIndex + "B1";
                return;
            }
            string maxCode = unitBarcode.Unit.Barcodes.Where((ProductUnitBarcode x) => x.Barcode.Length == maxLength).Max((ProductUnitBarcode x) => x.Barcode);
            if (maxCode != string.Empty)
            {
                unitBarcode.Barcode = maxCode.GetNextSequence();
            }
        }

        public static void RefreshProductBalance(int itemID, int storeID, List<ItemBalanceInStore> list)
        {
            Task mainTask = Task.Factory.StartNew(delegate
            {
                double balance = 0.0;
                List<ProductUnit> units = null;
                Task task = Task.Factory.StartNew(delegate
                {
                    ERPDataContext eRPDataContext = new ERPDataContext();
                    double valueOrDefault = (from x in eRPDataContext.ProductTransactions
                                             where (int)x.TransactionState == 2
                                             where x.ProductID == itemID && x.StoreID == storeID && (int)x.TransactionType == 0
                                             select x).Sum((Expression<Func<ProductTransaction, double?>>)((ProductTransaction x) => x.Quantity * x.Factor)).GetValueOrDefault();
                    double valueOrDefault2 = (from x in eRPDataContext.ProductTransactions
                                              where (int)x.TransactionState == 2
                                              where x.ProductID == itemID && x.StoreID == storeID && (int)x.TransactionType == 1
                                              select x).Sum((Expression<Func<ProductTransaction, double?>>)((ProductTransaction x) => x.Quantity * x.Factor)).GetValueOrDefault();
                    balance = valueOrDefault - valueOrDefault2;
                    units = eRPDataContext.ProductUnits.Where((ProductUnit x) => x.ProductID == itemID).ToList();
                });
                Task.WaitAll(task);
                list.RemoveAll((ItemBalanceInStore x) => x.ItemID == itemID);
                units.ForEach(delegate (ProductUnit u)
                {
                    list.Add(new ItemBalanceInStore
                    {
                        ItemID = itemID,
                        StoreID = storeID,
                        UnitID = u.ID,
                        Balance = balance / u.Factor
                    });
                });
            });
        }

        public static string GetDetailedBalance(int productID, double balance)
        {
            List<ProductUnit> units = (from x in CurrentSession.ProductUnits
                                       where x.ProductID == productID
                                       orderby x.Factor descending
                                       select x).ToList();
            List<string> unitsQuantities = new List<string>();
            double remainingBalance = Math.Abs(balance);
            string oper = ((balance < 0.0) ? "-" : "");
            foreach (ProductUnit unit in units)
            {
                double unitBalance = remainingBalance / unit.Factor;
                if (Math.Truncate(unitBalance) >= 1.0)
                {
                    unitsQuantities.Add($"{Math.Truncate(unitBalance):N0} {unit.UnitName.Name}");
                    remainingBalance = GetFraction(unitBalance) * unit.Factor;
                }
            }
            return oper + string.Join(" et ", unitsQuantities);
        }

        private static double GetFraction(double value)
        {
            return value % 1.0;
        }

        public static double GetProductBalanceInStore(int productID, int storeID, DateTime transDate)
        {
            using ERPDataContext db = new ERPDataContext();
            IQueryable<ProductTransaction> query = from x in db.ProductTransactions
                                                   where (int)x.TransactionState == 2
                                                   select x into sl
                                                   where sl.ProductID == productID && sl.StoreID == storeID && sl.Date <= transDate
                                                   select sl;
            double TotalQtyOut = query.Where((ProductTransaction q) => (int)q.TransactionType == 1).Sum((Expression<Func<ProductTransaction, double?>>)((ProductTransaction q) => q.Quantity * q.Factor)).GetValueOrDefault();
            double TotalQtyIn = query.Where((ProductTransaction q) => (int)q.TransactionType == 0).Sum((Expression<Func<ProductTransaction, double?>>)((ProductTransaction q) => q.Quantity * q.Factor)).GetValueOrDefault();
            return TotalQtyIn - TotalQtyOut;
        }

        public static async Task<CostWithTansactionSource> GetCostOfNextProductAsync(int productID, int storeID, double qty, DateTime transDate, int ExecludeRow)
        {
            return await Task.Factory.StartNew(() => GetCostOfNextProduct(productID, storeID, qty, transDate, ExecludeRow));
        }

        public static CostWithTansactionSource GetCostOfNextProduct(int productID, int storeID, double qty, DateTime transDate, int ExecludeRow)
        {
            _003C_003Ec__DisplayClass10_0 CS_0024_003C_003E8__locals0 = new _003C_003Ec__DisplayClass10_0();
            CS_0024_003C_003E8__locals0.productID = productID;
            CS_0024_003C_003E8__locals0.storeID = storeID;
            CS_0024_003C_003E8__locals0.transDate = transDate;
            CS_0024_003C_003E8__locals0.ExecludeRow = ExecludeRow;
            List<TransactionLinkModel> sources = new List<TransactionLinkModel>();
            CostWithTansactionSource cost = new CostWithTansactionSource();
            cost.Sources = sources;
            using ERPDataContext db = new ERPDataContext();
            IOrderedQueryable<ProductTransaction> query = from x in db.ProductTransactions.AsNoTracking()
                                                          where (int)x.TransactionState == 2
                                                          select x into sl
                                                          where sl.ProductID == CS_0024_003C_003E8__locals0.productID && sl.StoreID == CS_0024_003C_003E8__locals0.storeID && sl.Date <= CS_0024_003C_003E8__locals0.transDate && sl.ID != CS_0024_003C_003E8__locals0.ExecludeRow
                                                          orderby sl.Date
                                                          select sl;
            if (query.Count() == 0)
            {
                cost.Cost = (from x in CurrentSession.ProductUnits
                             where x.ProductID == CS_0024_003C_003E8__locals0.productID
                             orderby x.Factor descending
                             select x).First().BuyPrice;
                return cost;
            }
            double TotalQtyOut = query.Where((ProductTransaction q) => (int)q.TransactionType == 1).Sum((Expression<Func<ProductTransaction, double?>>)((ProductTransaction q) => q.Quantity)).GetValueOrDefault();
            double Balance = GetProductBalanceInStore(CS_0024_003C_003E8__locals0.productID, CS_0024_003C_003E8__locals0.storeID, CS_0024_003C_003E8__locals0.transDate);
            if (Balance <= 0.0)
            {
                cost.Cost = (from x in CurrentSession.ProductUnits
                             where x.ProductID == CS_0024_003C_003E8__locals0.productID
                             orderby x.Factor descending
                             select x).First().BuyPrice;
                return cost;
            }
            List<ProductTransaction> subQurey = query.Where((ProductTransaction q) => query.Where((ProductTransaction q1) => (int)q1.TransactionType == 0 && q1.Date <= q.Date).Sum((ProductTransaction q1) => q1.Quantity) > TotalQtyOut && (int)q.TransactionType == 0).ToList();
            double subQureyBalance = subQurey.Where((ProductTransaction q) => q.TransactionType == ProductTransactionType.In).Sum((ProductTransaction q) => q.Quantity) - subQurey.Where((ProductTransaction q) => q.TransactionType == ProductTransactionType.Out).Sum((ProductTransaction q) => q.Quantity);
            if (subQureyBalance > Balance)
            {
                double diff = subQureyBalance - Balance;
                subQurey[0].Quantity -= diff;
            }
            DbSet<Store> stores = db.Stores;
            ParameterExpression parameterExpression = Expression.Parameter(typeof(Store), "x");
            var costCalculationMethod = db.Stores.Single(x => x.ID == CS_0024_003C_003E8__locals0.storeID).CostCalculationMethod;

            switch (costCalculationMethod)
            {
                case CostCalculationMethod.FIFO:
                    {
                        double fifo;
                        if (subQurey[0].Quantity < qty)
                        {
                            int i = 0;
                            double qtyX = qty;
                            double SumPrice = 0.0;
                            while (qtyX > 0.0 && i < subQurey.Count())
                            {
                                ProductTransaction row = subQurey[i];
                                double qty1 = ((qtyX > row.Quantity) ? row.Quantity : qtyX);
                                sources.Add(new TransactionLinkModel
                                {
                                    SourceTransactionID = row.ID,
                                    Quantity = qty1
                                });
                                SumPrice += qty1 * (row.CostValue / row.Factor);
                                qtyX -= qty1;
                                i++;
                            }
                            fifo = SumPrice / (qty - qtyX);
                        }
                        else
                        {
                            fifo = subQurey.First().CostValue / subQurey.First().Factor;
                            sources.Add(new TransactionLinkModel
                            {
                                SourceTransactionID = subQurey.First().ID,
                                Quantity = qty
                            });
                        }
                        cost.Cost = fifo;
                        break;
                    }
                case CostCalculationMethod.LIFO:
                    {
                        subQurey = subQurey.OrderByDescending((ProductTransaction q) => q.Date).ToList();
                        double lifo;
                        if (subQurey[0].Quantity < qty)
                        {
                            int i = 0;
                            double qtyX = qty;
                            double SumPrice = 0.0;
                            while (qtyX > 0.0 && i < subQurey.Count())
                            {
                                ProductTransaction row = subQurey[i];
                                double qty1 = ((qtyX > row.Quantity) ? row.Quantity : qtyX);
                                sources.Add(new TransactionLinkModel
                                {
                                    SourceTransactionID = row.ID,
                                    Quantity = qty1
                                });
                                SumPrice += qty1 * (row.CostValue / row.Factor);
                                qtyX -= qty1;
                                i++;
                            }
                            lifo = SumPrice / (qty - qtyX);
                        }
                        else
                        {
                            lifo = subQurey.First().CostValue / subQurey.First().Factor;
                            sources.Add(new TransactionLinkModel
                            {
                                SourceTransactionID = subQurey.First().ID,
                                Quantity = qty
                            });
                        }
                        cost.Cost = lifo;
                        break;
                    }
                case CostCalculationMethod.Average:
                    {
                        double WAC = subQurey.Select((ProductTransaction q) => q.Quantity * (q.CostValue / q.Factor)).Sum((double q) => q) / Balance;
                        cost.Cost = WAC;
                        break;
                    }
            }
            return cost;
        }
    }
}
