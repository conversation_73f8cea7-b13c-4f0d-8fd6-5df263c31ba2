﻿<XtraSerializer version="1.0" application="DataLayoutControl">
  <property name="#LayoutVersion" />
  <property name="OptionsFocus" isnull="true" iskey="true">
    <property name="ActivateSelectedControlOnGotFocus">true</property>
    <property name="AllowFocusControlOnLabelClick">false</property>
    <property name="EnableAutoTabOrder">true</property>
    <property name="AllowFocusControlOnActivatedTabPage">false</property>
    <property name="AllowFocusGroups">true</property>
    <property name="AllowFocusTabbedGroups">true</property>
    <property name="AllowFocusReadonlyEditors">true</property>
    <property name="MoveFocusRightToLeft">true</property>
    <property name="MoveFocusDirection">AcrossThenDown</property>
  </property>
  <property name="LookAndFeel" isnull="true" iskey="true">
    <property name="SkinName">Basic</property>
    <property name="Style">Skin</property>
    <property name="UseDefaultLookAndFeel">true</property>
    <property name="UseWindowsXPTheme">false</property>
  </property>
  <property name="Items" iskey="true" value="70">
    <property name="Item1" isnull="true" iskey="true">
      <property name="TypeName">LayoutGroup</property>
      <property name="TabbedGroupParentName" />
      <property name="GroupBordersVisible">false</property>
      <property name="AllowDrawBackground">true</property>
      <property name="EnableIndentsWithoutBorders">True</property>
      <property name="OptionsItemText" isnull="true" iskey="true">
        <property name="TextToControlDistance">3</property>
        <property name="TextAlignMode">UseParentOptions</property>
      </property>
      <property name="CaptionImageVisible">true</property>
      <property name="FlowDirection">LeftToRight</property>
      <property name="LayoutMode">Regular</property>
      <property name="CaptionImageLocation">Default</property>
      <property name="CaptionImageIndex">-1</property>
      <property name="AllowBorderColorBlending">false</property>
      <property name="ExpandOnDoubleClick">false</property>
      <property name="Expanded">true</property>
      <property name="DefaultLayoutType">Vertical</property>
      <property name="ShowTabPageCloseButton">false</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsTableLayoutGroup" isnull="true" iskey="true">
        <property name="RowDefinitions" iskey="true" value="0" />
        <property name="ColumnDefinitions" iskey="true" value="0" />
      </property>
      <property name="Size">@4,Width=1186@3,Height=889</property>
      <property name="ExpandButtonVisible">false</property>
      <property name="ExpandButtonMode">Normal</property>
      <property name="HeaderButtonsLocation">Default</property>
      <property name="GroupStyle">Inherited</property>
      <property name="TextLocation">Top</property>
      <property name="TabPageWidth">0</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">Root</property>
      <property name="ParentName" />
      <property name="TextVisible">false</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Root</property>
      <property name="CustomizationFormText">Root</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
    </property>
    <property name="Item2" isnull="true" iskey="true">
      <property name="TypeName">LayoutGroup</property>
      <property name="TabbedGroupParentName" />
      <property name="GroupBordersVisible">false</property>
      <property name="AllowDrawBackground">false</property>
      <property name="EnableIndentsWithoutBorders">Default</property>
      <property name="OptionsItemText" isnull="true" iskey="true">
        <property name="TextToControlDistance">3</property>
        <property name="TextAlignMode">UseParentOptions</property>
      </property>
      <property name="CaptionImageVisible">true</property>
      <property name="FlowDirection">LeftToRight</property>
      <property name="LayoutMode">Regular</property>
      <property name="CaptionImageLocation">Default</property>
      <property name="CaptionImageIndex">-1</property>
      <property name="AllowBorderColorBlending">false</property>
      <property name="ExpandOnDoubleClick">false</property>
      <property name="Expanded">true</property>
      <property name="DefaultLayoutType">Vertical</property>
      <property name="ShowTabPageCloseButton">false</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsTableLayoutGroup" isnull="true" iskey="true">
        <property name="RowDefinitions" iskey="true" value="0" />
        <property name="ColumnDefinitions" iskey="true" value="0" />
      </property>
      <property name="Size">@4,Width=1166@3,Height=869</property>
      <property name="ExpandButtonVisible">false</property>
      <property name="ExpandButtonMode">Normal</property>
      <property name="HeaderButtonsLocation">Default</property>
      <property name="GroupStyle">Inherited</property>
      <property name="TextLocation">Top</property>
      <property name="TabPageWidth">0</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">autoGeneratedGroup0</property>
      <property name="ParentName">Root</property>
      <property name="TextVisible">true</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">autoGeneratedGroup0</property>
      <property name="CustomizationFormText">autoGeneratedGroup0</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
    </property>
    <property name="Item3" isnull="true" iskey="true">
      <property name="TypeName">LayoutGroup</property>
      <property name="TabbedGroupParentName" />
      <property name="GroupBordersVisible">true</property>
      <property name="AllowDrawBackground">true</property>
      <property name="EnableIndentsWithoutBorders">Default</property>
      <property name="OptionsItemText" isnull="true" iskey="true">
        <property name="TextToControlDistance">3</property>
        <property name="TextAlignMode">UseParentOptions</property>
      </property>
      <property name="CaptionImageVisible">true</property>
      <property name="FlowDirection">LeftToRight</property>
      <property name="LayoutMode">Regular</property>
      <property name="CaptionImageLocation">Default</property>
      <property name="CaptionImageIndex">-1</property>
      <property name="AllowBorderColorBlending">false</property>
      <property name="ExpandOnDoubleClick">false</property>
      <property name="Expanded">true</property>
      <property name="DefaultLayoutType">Vertical</property>
      <property name="ShowTabPageCloseButton">false</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsTableLayoutGroup" isnull="true" iskey="true">
        <property name="RowDefinitions" iskey="true" value="0" />
        <property name="ColumnDefinitions" iskey="true" value="0" />
      </property>
      <property name="Size">@3,Width=682@3,Height=869</property>
      <property name="ExpandButtonVisible">false</property>
      <property name="ExpandButtonMode">Normal</property>
      <property name="HeaderButtonsLocation">Default</property>
      <property name="GroupStyle">Inherited</property>
      <property name="TextLocation">Top</property>
      <property name="TabPageWidth">0</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">lyc_Details</property>
      <property name="ParentName">autoGeneratedGroup0</property>
      <property name="TextVisible">true</property>
      <property name="Location">@3,X=356@1,Y=0</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Articles</property>
      <property name="CustomizationFormText">الاصناف</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
    </property>
    <property name="Item4" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">DetailsGridControl</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@1,Height=0</property>
      <property name="MinSize">@3,Width=104@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForDetails</property>
      <property name="ParentName">lyc_Details</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@3,Width=658@3,Height=825</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">ItemForDetails</property>
      <property name="CustomizationFormText">ItemForDetails</property>
      <property name="StartNewLine">true</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item5" isnull="true" iskey="true">
      <property name="TypeName">LayoutGroup</property>
      <property name="TabbedGroupParentName" />
      <property name="GroupBordersVisible">true</property>
      <property name="AllowDrawBackground">true</property>
      <property name="EnableIndentsWithoutBorders">Default</property>
      <property name="OptionsItemText" isnull="true" iskey="true">
        <property name="TextToControlDistance">3</property>
        <property name="TextAlignMode">UseParentOptions</property>
      </property>
      <property name="CaptionImageVisible">true</property>
      <property name="FlowDirection">LeftToRight</property>
      <property name="LayoutMode">Regular</property>
      <property name="CaptionImageLocation">Default</property>
      <property name="CaptionImageIndex">-1</property>
      <property name="AllowBorderColorBlending">false</property>
      <property name="ExpandOnDoubleClick">false</property>
      <property name="Expanded">true</property>
      <property name="DefaultLayoutType">Vertical</property>
      <property name="ShowTabPageCloseButton">false</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsTableLayoutGroup" isnull="true" iskey="true">
        <property name="RowDefinitions" iskey="true" value="0" />
        <property name="ColumnDefinitions" iskey="true" value="0" />
      </property>
      <property name="Size">@3,Width=356@3,Height=869</property>
      <property name="ExpandButtonVisible">false</property>
      <property name="ExpandButtonMode">Normal</property>
      <property name="HeaderButtonsLocation">Default</property>
      <property name="GroupStyle">Inherited</property>
      <property name="TextLocation">Top</property>
      <property name="TabPageWidth">0</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlGroup11</property>
      <property name="ParentName">autoGeneratedGroup0</property>
      <property name="TextVisible">true</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Détails de la facture</property>
      <property name="CustomizationFormText">بيانات الفاتوره</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
    </property>
    <property name="Item6" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">DateDateEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=175@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForDate</property>
      <property name="ParentName">layoutControlGroup11</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@2,Y=24</property>
      <property name="Size">@3,Width=350@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Date</property>
      <property name="CustomizationFormText">التاريخ</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item7" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">StoreIDLookUpEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=175@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForStore</property>
      <property name="ParentName">layoutControlGroup11</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@2,Y=72</property>
      <property name="Size">@3,Width=350@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Dépôt</property>
      <property name="CustomizationFormText">المخزن</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item8" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">CustomerIDGridLookUpEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=182@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForCustomer</property>
      <property name="ParentName">layoutControlGroup11</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@2,Y=96</property>
      <property name="Size">@3,Width=350@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Client</property>
      <property name="CustomizationFormText">العميل</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item9" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">IDTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=175@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForID</property>
      <property name="ParentName">layoutControlGroup11</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@3,X=175@1,Y=0</property>
      <property name="Size">@3,Width=175@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Numéro</property>
      <property name="CustomizationFormText">رقم</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item10" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">CostCenterIDLookUpEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=175@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForCostCenterID</property>
      <property name="ParentName">layoutControlGroup11</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@3,Y=120</property>
      <property name="Size">@3,Width=350@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Centre de coût</property>
      <property name="CustomizationFormText">مركز التكلفة</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item11" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">BranchIDLookUpEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=175@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForBranchID</property>
      <property name="ParentName">layoutControlGroup11</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@2,Y=48</property>
      <property name="Size">@3,Width=350@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Succursale</property>
      <property name="CustomizationFormText">الفرع</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item12" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">CodeTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=175@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForCode</property>
      <property name="ParentName">layoutControlGroup11</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@3,Width=175@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Code</property>
      <property name="CustomizationFormText">الكود</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item13" isnull="true" iskey="true">
      <property name="TypeName">LayoutGroup</property>
      <property name="TabbedGroupParentName" />
      <property name="GroupBordersVisible">true</property>
      <property name="AllowDrawBackground">true</property>
      <property name="EnableIndentsWithoutBorders">Default</property>
      <property name="OptionsItemText" isnull="true" iskey="true">
        <property name="TextToControlDistance">3</property>
        <property name="TextAlignMode">UseParentOptions</property>
      </property>
      <property name="CaptionImageVisible">true</property>
      <property name="FlowDirection">LeftToRight</property>
      <property name="LayoutMode">Regular</property>
      <property name="CaptionImageLocation">Default</property>
      <property name="CaptionImageIndex">-1</property>
      <property name="AllowBorderColorBlending">false</property>
      <property name="ExpandOnDoubleClick">false</property>
      <property name="Expanded">true</property>
      <property name="DefaultLayoutType">Vertical</property>
      <property name="ShowTabPageCloseButton">false</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsTableLayoutGroup" isnull="true" iskey="true">
        <property name="RowDefinitions" iskey="true" value="0" />
        <property name="ColumnDefinitions" iskey="true" value="0" />
      </property>
      <property name="Size">@3,Width=350@3,Height=285</property>
      <property name="ExpandButtonVisible">false</property>
      <property name="ExpandButtonMode">Normal</property>
      <property name="HeaderButtonsLocation">Default</property>
      <property name="GroupStyle">Inherited</property>
      <property name="TextLocation">Top</property>
      <property name="TabPageWidth">0</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlGroup10</property>
      <property name="ParentName">layoutControlGroup11</property>
      <property name="TextVisible">true</property>
      <property name="Location">@1,X=0@3,Y=558</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Paramètres</property>
      <property name="CustomizationFormText">اعدادات</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
    </property>
    <property name="Item14" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">SwitchCatchBarcode</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=28</property>
      <property name="MinSize">@3,Width=230@2,Height=28</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem7</property>
      <property name="ParentName">layoutControlGroup10</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@3,Width=326@2,Height=28</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Capture du Code-barres</property>
      <property name="CustomizationFormText">التقاط الباركود</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item15" isnull="true" iskey="true">
      <property name="TypeName">LayoutGroup</property>
      <property name="TabbedGroupParentName" />
      <property name="GroupBordersVisible">true</property>
      <property name="AllowDrawBackground">true</property>
      <property name="EnableIndentsWithoutBorders">Default</property>
      <property name="OptionsItemText" isnull="true" iskey="true">
        <property name="TextToControlDistance">3</property>
        <property name="TextAlignMode">UseParentOptions</property>
      </property>
      <property name="CaptionImageVisible">true</property>
      <property name="FlowDirection">LeftToRight</property>
      <property name="LayoutMode">Regular</property>
      <property name="CaptionImageLocation">Default</property>
      <property name="CaptionImageIndex">-1</property>
      <property name="AllowBorderColorBlending">false</property>
      <property name="ExpandOnDoubleClick">false</property>
      <property name="Expanded">true</property>
      <property name="DefaultLayoutType">Vertical</property>
      <property name="ShowTabPageCloseButton">false</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsTableLayoutGroup" isnull="true" iskey="true">
        <property name="RowDefinitions" iskey="true" value="0" />
        <property name="ColumnDefinitions" iskey="true" value="0" />
      </property>
      <property name="Size">@3,Width=326@3,Height=213</property>
      <property name="ExpandButtonVisible">false</property>
      <property name="ExpandButtonMode">Normal</property>
      <property name="HeaderButtonsLocation">Default</property>
      <property name="GroupStyle">Inherited</property>
      <property name="TextLocation">Top</property>
      <property name="TabPageWidth">0</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">autoGroupForالقيمه</property>
      <property name="ParentName">layoutControlGroup10</property>
      <property name="TextVisible">true</property>
      <property name="Location">@1,X=0@2,Y=28</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Montant</property>
      <property name="CustomizationFormText">القيمه</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
    </property>
    <property name="Item16" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">TaxSpinEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@1,Height=0</property>
      <property name="MinSize">@3,Width=175@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForTax</property>
      <property name="ParentName">autoGroupForالقيمه</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@2,Y=24</property>
      <property name="Size">@3,Width=302@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Taxe ( TVA )</property>
      <property name="CustomizationFormText">ق.ضريبه</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item17" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">OtherExpensesSpinEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=175@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForOtherExpenses</property>
      <property name="ParentName">autoGroupForالقيمه</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@2,Y=72</property>
      <property name="Size">@3,Width=302@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Autres Frais</property>
      <property name="CustomizationFormText">تكاليف اخري</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item18" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">TotalSpinEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@1,Height=0</property>
      <property name="MinSize">@3,Width=175@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForTotal</property>
      <property name="ParentName">autoGroupForالقيمه</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@3,Width=302@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Montant HT</property>
      <property name="CustomizationFormText">الاجمالي</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item19" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">DiscountPercentageTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Custom</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@2,Width=86@1,Height=0</property>
      <property name="MinSize">@2,Width=86@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForDiscountPercentage</property>
      <property name="ParentName">autoGroupForالقيمه</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@3,X=216@2,Y=48</property>
      <property name="Size">@2,Width=86@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">ن.خصم</property>
      <property name="CustomizationFormText">ن.خصم</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item20" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">NetSpinEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Custom</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@3,Width=266@2,Height=72</property>
      <property name="MinSize">@3,Width=266@2,Height=72</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForNet</property>
      <property name="ParentName">autoGroupForالقيمه</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=17</property>
      <property name="Location">@1,X=0@2,Y=97</property>
      <property name="Size">@3,Width=302@2,Height=72</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Total TTC</property>
      <property name="CustomizationFormText">الصافي</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Right</property>
    </property>
    <property name="Item21" isnull="true" iskey="true">
      <property name="Size">@3,Width=302@1,Height=1</property>
      <property name="TypeName">SimpleSeparator</property>
      <property name="TextVisible">false</property>
      <property name="MinSize">@1,Width=1@1,Height=1</property>
      <property name="MaxSize">@1,Width=0@1,Height=1</property>
      <property name="TextLocation">Default</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="Image" isnull="true" />
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageIndex">-1</property>
      <property name="ImageToTextDistance">5</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ControlName" isnull="true" />
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Custom</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">simpleSeparator1</property>
      <property name="ParentName">autoGroupForالقيمه</property>
      <property name="Location">@1,X=0@2,Y=96</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">simpleSeparator1</property>
      <property name="CustomizationFormText">simpleSeparator1</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
    </property>
    <property name="Item22" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">DiscountSpinEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@1,Height=0</property>
      <property name="MinSize">@3,Width=175@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForDiscount</property>
      <property name="ParentName">autoGroupForالقيمه</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@2,Y=48</property>
      <property name="Size">@3,Width=216@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Remise</property>
      <property name="CustomizationFormText">ق.خصم</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item23" isnull="true" iskey="true">
      <property name="TypeName">LayoutGroup</property>
      <property name="TabbedGroupParentName" />
      <property name="GroupBordersVisible">true</property>
      <property name="AllowDrawBackground">true</property>
      <property name="EnableIndentsWithoutBorders">Default</property>
      <property name="OptionsItemText" isnull="true" iskey="true">
        <property name="TextToControlDistance">3</property>
        <property name="TextAlignMode">UseParentOptions</property>
      </property>
      <property name="CaptionImageVisible">true</property>
      <property name="FlowDirection">LeftToRight</property>
      <property name="LayoutMode">Regular</property>
      <property name="CaptionImageLocation">Default</property>
      <property name="CaptionImageIndex">-1</property>
      <property name="AllowBorderColorBlending">false</property>
      <property name="ExpandOnDoubleClick">false</property>
      <property name="Expanded">false</property>
      <property name="DefaultLayoutType">Vertical</property>
      <property name="ShowTabPageCloseButton">false</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsTableLayoutGroup" isnull="true" iskey="true">
        <property name="RowDefinitions" iskey="true" value="0" />
        <property name="ColumnDefinitions" iskey="true" value="0" />
      </property>
      <property name="Size">@3,Width=350@2,Height=31</property>
      <property name="ExpandButtonVisible">true</property>
      <property name="ExpandButtonMode">Normal</property>
      <property name="HeaderButtonsLocation">Default</property>
      <property name="GroupStyle">Inherited</property>
      <property name="TextLocation">Top</property>
      <property name="TabPageWidth">0</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlGroup8</property>
      <property name="ParentName">layoutControlGroup11</property>
      <property name="TextVisible">true</property>
      <property name="Location">@1,X=0@3,Y=144</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Client Information</property>
      <property name="CustomizationFormText">معلومات العميل</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
    </property>
    <property name="Item24" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">CityTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=175@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForCity</property>
      <property name="ParentName">layoutControlGroup8</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@3,Width=344@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Ville</property>
      <property name="CustomizationFormText">المدينه</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item25" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">AddressTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=175@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForAddress</property>
      <property name="ParentName">layoutControlGroup8</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@2,Y=24</property>
      <property name="Size">@3,Width=344@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Adresse</property>
      <property name="CustomizationFormText">العنوان</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item26" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">PhoneTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=175@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForPhone</property>
      <property name="ParentName">layoutControlGroup8</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@2,Y=48</property>
      <property name="Size">@3,Width=344@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Téléphone</property>
      <property name="CustomizationFormText">الهاتف</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item27" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">MobileTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=175@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForMobile</property>
      <property name="ParentName">layoutControlGroup8</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@2,Y=72</property>
      <property name="Size">@3,Width=344@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Mobile</property>
      <property name="CustomizationFormText">الموبيل</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item28" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">BalanceTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=175@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem1</property>
      <property name="ParentName">layoutControlGroup8</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@2,Y=96</property>
      <property name="Size">@3,Width=227@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Solde du compte</property>
      <property name="CustomizationFormText">رصيد الحساب</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item29" isnull="true" iskey="true">
      <property name="TypeName">EmptySpaceItem</property>
      <property name="TextVisible">false</property>
      <property name="TextLocation">Default</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="Image" isnull="true" />
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageIndex">-1</property>
      <property name="ImageToTextDistance">5</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="MinSize">@2,Width=10@2,Height=10</property>
      <property name="MaxSize">@1,Width=0@1,Height=0</property>
      <property name="ControlName" isnull="true" />
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">emptySpaceItem2</property>
      <property name="ParentName">layoutControlGroup8</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@1,X=0@3,Y=120</property>
      <property name="Size">@3,Width=344@2,Height=10</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">emptySpaceItem2</property>
      <property name="CustomizationFormText">emptySpaceItem2</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
    </property>
    <property name="Item30" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">BalanceTypeTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@2,Width=54@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem2</property>
      <property name="ParentName">layoutControlGroup8</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@3,X=227@2,Y=96</property>
      <property name="Size">@3,Width=117@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">layoutControlItem2</property>
      <property name="CustomizationFormText">layoutControlItem2</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item31" isnull="true" iskey="true">
      <property name="TypeName">LayoutGroup</property>
      <property name="TabbedGroupParentName" />
      <property name="GroupBordersVisible">true</property>
      <property name="AllowDrawBackground">true</property>
      <property name="EnableIndentsWithoutBorders">Default</property>
      <property name="OptionsItemText" isnull="true" iskey="true">
        <property name="TextToControlDistance">3</property>
        <property name="TextAlignMode">UseParentOptions</property>
      </property>
      <property name="CaptionImageVisible">true</property>
      <property name="FlowDirection">LeftToRight</property>
      <property name="LayoutMode">Table</property>
      <property name="CaptionImageLocation">Default</property>
      <property name="CaptionImageIndex">-1</property>
      <property name="AllowBorderColorBlending">false</property>
      <property name="ExpandOnDoubleClick">false</property>
      <property name="Expanded">true</property>
      <property name="DefaultLayoutType">Vertical</property>
      <property name="ShowTabPageCloseButton">false</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsTableLayoutGroup" isnull="true" iskey="true">
        <property name="RowDefinitions" iskey="true" value="2">
          <property name="Item1" isnull="true" iskey="true">
            <property name="Height">50</property>
            <property name="SizeType">Percent</property>
            <property name="Visible">true</property>
          </property>
          <property name="Item2" isnull="true" iskey="true">
            <property name="Height">50</property>
            <property name="SizeType">Percent</property>
            <property name="Visible">true</property>
          </property>
        </property>
        <property name="ColumnDefinitions" iskey="true" value="2">
          <property name="Item1" isnull="true" iskey="true">
            <property name="Width">50</property>
            <property name="SizeType">Percent</property>
            <property name="Visible">true</property>
          </property>
          <property name="Item2" isnull="true" iskey="true">
            <property name="Width">50</property>
            <property name="SizeType">Percent</property>
            <property name="Visible">true</property>
          </property>
        </property>
      </property>
      <property name="Size">@3,Width=350@3,Height=383</property>
      <property name="ExpandButtonVisible">false</property>
      <property name="ExpandButtonMode">Normal</property>
      <property name="HeaderButtonsLocation">Default</property>
      <property name="GroupStyle">Inherited</property>
      <property name="TextLocation">Top</property>
      <property name="TabPageWidth">0</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlGroup7</property>
      <property name="ParentName">layoutControlGroup11</property>
      <property name="TextVisible">true</property>
      <property name="Location">@1,X=0@3,Y=175</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text"> </property>
      <property name="CustomizationFormText"> </property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
    </property>
    <property name="Item32" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">NotesMemoEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Custom</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@1,Height=0</property>
      <property name="MinSize">@2,Width=67@2,Height=36</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">2</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForNotes</property>
      <property name="ParentName">layoutControlGroup7</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@3,Width=117@2,Height=13</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@3,Width=326@3,Height=169</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Notes</property>
      <property name="CustomizationFormText">الملاحظات</property>
      <property name="StartNewLine">true</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Top</property>
    </property>
    <property name="Item33" isnull="true" iskey="true">
      <property name="TypeName">LayoutGroup</property>
      <property name="TabbedGroupParentName" />
      <property name="GroupBordersVisible">true</property>
      <property name="AllowDrawBackground">true</property>
      <property name="EnableIndentsWithoutBorders">Default</property>
      <property name="OptionsItemText" isnull="true" iskey="true">
        <property name="TextToControlDistance">3</property>
        <property name="TextAlignMode">UseParentOptions</property>
      </property>
      <property name="CaptionImageVisible">true</property>
      <property name="FlowDirection">LeftToRight</property>
      <property name="LayoutMode">Regular</property>
      <property name="CaptionImageLocation">Default</property>
      <property name="CaptionImageIndex">-1</property>
      <property name="AllowBorderColorBlending">false</property>
      <property name="ExpandOnDoubleClick">false</property>
      <property name="Expanded">true</property>
      <property name="DefaultLayoutType">Vertical</property>
      <property name="ShowTabPageCloseButton">false</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsTableLayoutGroup" isnull="true" iskey="true">
        <property name="RowDefinitions" iskey="true" value="0" />
        <property name="ColumnDefinitions" iskey="true" value="0" />
      </property>
      <property name="Size">@3,Width=163@3,Height=170</property>
      <property name="ExpandButtonVisible">false</property>
      <property name="ExpandButtonMode">Normal</property>
      <property name="HeaderButtonsLocation">Default</property>
      <property name="GroupStyle">Inherited</property>
      <property name="TextLocation">Top</property>
      <property name="TabPageWidth">0</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">1</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlGroup2</property>
      <property name="ParentName">layoutControlGroup7</property>
      <property name="TextVisible">true</property>
      <property name="Location">@1,X=0@3,Y=169</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Payé</property>
      <property name="CustomizationFormText">المدفوع</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
    </property>
    <property name="Item34" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">PaidSpinEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">CustomSize</property>
      <property name="SizeConstraintsType">Custom</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@1,Height=0</property>
      <property name="MinSize">@2,Width=52@2,Height=62</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem4</property>
      <property name="ParentName">layoutControlGroup2</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@3,Width=139@3,Height=126</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">المدفوع</property>
      <property name="CustomizationFormText">المدفوع</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Top</property>
    </property>
    <property name="Item35" isnull="true" iskey="true">
      <property name="TypeName">LayoutGroup</property>
      <property name="TabbedGroupParentName" />
      <property name="GroupBordersVisible">true</property>
      <property name="AllowDrawBackground">true</property>
      <property name="EnableIndentsWithoutBorders">Default</property>
      <property name="OptionsItemText" isnull="true" iskey="true">
        <property name="TextToControlDistance">3</property>
        <property name="TextAlignMode">UseParentOptions</property>
      </property>
      <property name="CaptionImageVisible">true</property>
      <property name="FlowDirection">LeftToRight</property>
      <property name="LayoutMode">Regular</property>
      <property name="CaptionImageLocation">Default</property>
      <property name="CaptionImageIndex">-1</property>
      <property name="AllowBorderColorBlending">false</property>
      <property name="ExpandOnDoubleClick">false</property>
      <property name="Expanded">true</property>
      <property name="DefaultLayoutType">Vertical</property>
      <property name="ShowTabPageCloseButton">false</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsTableLayoutGroup" isnull="true" iskey="true">
        <property name="RowDefinitions" iskey="true" value="0" />
        <property name="ColumnDefinitions" iskey="true" value="0" />
      </property>
      <property name="Size">@3,Width=163@3,Height=170</property>
      <property name="ExpandButtonVisible">false</property>
      <property name="ExpandButtonMode">Normal</property>
      <property name="HeaderButtonsLocation">Default</property>
      <property name="GroupStyle">Inherited</property>
      <property name="TextLocation">Top</property>
      <property name="TabPageWidth">0</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">1</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">1</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlGroup6</property>
      <property name="ParentName">layoutControlGroup7</property>
      <property name="TextVisible">true</property>
      <property name="Location">@3,X=163@3,Y=169</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Reste</property>
      <property name="CustomizationFormText">المتبقي</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
    </property>
    <property name="Item36" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">RemainingTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">CustomSize</property>
      <property name="SizeConstraintsType">Custom</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@1,Height=0</property>
      <property name="MinSize">@2,Width=52@2,Height=62</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem3</property>
      <property name="ParentName">layoutControlGroup6</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@3,Width=139@3,Height=126</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">المتبقي</property>
      <property name="CustomizationFormText">المتبقي</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Top</property>
    </property>
    <property name="Item37" isnull="true" iskey="true">
      <property name="TypeName">LayoutGroup</property>
      <property name="TabbedGroupParentName" />
      <property name="GroupBordersVisible">true</property>
      <property name="AllowDrawBackground">true</property>
      <property name="EnableIndentsWithoutBorders">Default</property>
      <property name="OptionsItemText" isnull="true" iskey="true">
        <property name="TextToControlDistance">3</property>
        <property name="TextAlignMode">UseParentOptions</property>
      </property>
      <property name="CaptionImageVisible">true</property>
      <property name="FlowDirection">LeftToRight</property>
      <property name="LayoutMode">Regular</property>
      <property name="CaptionImageLocation">Default</property>
      <property name="CaptionImageIndex">-1</property>
      <property name="AllowBorderColorBlending">false</property>
      <property name="ExpandOnDoubleClick">false</property>
      <property name="Expanded">true</property>
      <property name="DefaultLayoutType">Vertical</property>
      <property name="ShowTabPageCloseButton">false</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsTableLayoutGroup" isnull="true" iskey="true">
        <property name="RowDefinitions" iskey="true" value="0" />
        <property name="ColumnDefinitions" iskey="true" value="0" />
      </property>
      <property name="Size">@3,Width=128@3,Height=869</property>
      <property name="ExpandButtonVisible">false</property>
      <property name="ExpandButtonMode">Normal</property>
      <property name="HeaderButtonsLocation">Default</property>
      <property name="GroupStyle">Inherited</property>
      <property name="TextLocation">Top</property>
      <property name="TabPageWidth">0</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlGroup4</property>
      <property name="ParentName">autoGeneratedGroup0</property>
      <property name="TextVisible">true</property>
      <property name="Location">@4,X=1038@1,Y=0</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Rechercher</property>
      <property name="CustomizationFormText">بحث عن صنف</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
    </property>
    <property name="Item38" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">gridControl1</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@1,Height=0</property>
      <property name="MinSize">@3,Width=104@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem8</property>
      <property name="ParentName">layoutControlGroup4</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@1,X=0@2,Y=24</property>
      <property name="Size">@3,Width=104@3,Height=775</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">layoutControlItem8</property>
      <property name="CustomizationFormText">layoutControlItem8</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item39" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">textEdit1</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@2,Width=54@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem9</property>
      <property name="ParentName">layoutControlGroup4</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@3,Width=104@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">layoutControlItem9</property>
      <property name="CustomizationFormText">layoutControlItem9</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item40" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">simpleButton1</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=26</property>
      <property name="MinSize">@2,Width=49@2,Height=26</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem10</property>
      <property name="ParentName">layoutControlGroup4</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@1,X=0@3,Y=799</property>
      <property name="Size">@3,Width=104@2,Height=26</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">layoutControlItem10</property>
      <property name="CustomizationFormText">layoutControlItem10</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item41" isnull="true" iskey="true">
      <property name="TypeName">LayoutGroup</property>
      <property name="TabbedGroupParentName" />
      <property name="GroupBordersVisible">true</property>
      <property name="AllowDrawBackground">true</property>
      <property name="EnableIndentsWithoutBorders">Default</property>
      <property name="OptionsItemText" isnull="true" iskey="true">
        <property name="TextToControlDistance">3</property>
        <property name="TextAlignMode">UseParentOptions</property>
      </property>
      <property name="CaptionImageVisible">true</property>
      <property name="FlowDirection">LeftToRight</property>
      <property name="LayoutMode">Table</property>
      <property name="CaptionImageLocation">Default</property>
      <property name="CaptionImageIndex">-1</property>
      <property name="AllowBorderColorBlending">false</property>
      <property name="ExpandOnDoubleClick">false</property>
      <property name="Expanded">true</property>
      <property name="DefaultLayoutType">Vertical</property>
      <property name="ShowTabPageCloseButton">false</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsTableLayoutGroup" isnull="true" iskey="true">
        <property name="RowDefinitions" iskey="true" value="2">
          <property name="Item1" isnull="true" iskey="true">
            <property name="Height">24</property>
            <property name="SizeType">AutoSize</property>
            <property name="Visible">true</property>
          </property>
          <property name="Item2" isnull="true" iskey="true">
            <property name="Height">146</property>
            <property name="SizeType">AutoSize</property>
            <property name="Visible">true</property>
          </property>
        </property>
        <property name="ColumnDefinitions" iskey="true" value="4">
          <property name="Item1" isnull="true" iskey="true">
            <property name="Width">25</property>
            <property name="SizeType">Percent</property>
            <property name="Visible">true</property>
          </property>
          <property name="Item2" isnull="true" iskey="true">
            <property name="Width">25</property>
            <property name="SizeType">Percent</property>
            <property name="Visible">true</property>
          </property>
          <property name="Item3" isnull="true" iskey="true">
            <property name="Width">25</property>
            <property name="SizeType">Percent</property>
            <property name="Visible">true</property>
          </property>
          <property name="Item4" isnull="true" iskey="true">
            <property name="Width">25</property>
            <property name="SizeType">Percent</property>
            <property name="Visible">true</property>
          </property>
        </property>
      </property>
      <property name="Size">@3,Width=562@3,Height=214</property>
      <property name="ExpandButtonVisible">false</property>
      <property name="ExpandButtonMode">Normal</property>
      <property name="HeaderButtonsLocation">Default</property>
      <property name="GroupStyle">Inherited</property>
      <property name="TextLocation">Top</property>
      <property name="TabPageWidth">0</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">PayMethodsGroup</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">true</property>
      <property name="Location">@3,X=190@3,Y=462</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">المدفوعات</property>
      <property name="CustomizationFormText">المدفوعات</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
    </property>
    <property name="Item42" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">PayDetailsGridControl</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@1,Height=0</property>
      <property name="MinSize">@3,Width=104@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">1</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">4</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem5</property>
      <property name="ParentName">PayMethodsGroup</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@1,X=0@2,Y=24</property>
      <property name="Size">@3,Width=538@3,Height=146</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">layoutControlItem5</property>
      <property name="CustomizationFormText">ItemForPayDetails</property>
      <property name="StartNewLine">true</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item43" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">btn_PayScreen</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=26</property>
      <property name="MinSize">@3,Width=102@2,Height=26</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem6</property>
      <property name="ParentName">PayMethodsGroup</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@3,Width=134@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">layoutControlItem3</property>
      <property name="CustomizationFormText">layoutControlItem3</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item44" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">btn_PayCash</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=26</property>
      <property name="MinSize">@2,Width=52@2,Height=26</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">1</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem11</property>
      <property name="ParentName">PayMethodsGroup</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@3,X=134@1,Y=0</property>
      <property name="Size">@3,Width=134@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">layoutControlItem4</property>
      <property name="CustomizationFormText">layoutControlItem4</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item45" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">btn_PayCard</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=26</property>
      <property name="MinSize">@2,Width=40@2,Height=26</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">2</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem22</property>
      <property name="ParentName">PayMethodsGroup</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@3,X=268@1,Y=0</property>
      <property name="Size">@3,Width=134@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">layoutControlItem5</property>
      <property name="CustomizationFormText">layoutControlItem5</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item46" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">btn_PayCredit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=26</property>
      <property name="MinSize">@2,Width=42@2,Height=26</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">3</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem23</property>
      <property name="ParentName">PayMethodsGroup</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@3,X=402@1,Y=0</property>
      <property name="Size">@3,Width=136@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">layoutControlItem6</property>
      <property name="CustomizationFormText">layoutControlItem6</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item47" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">SourceTypeImageComboBoxEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=139@2,Height=24</property>
      <property name="ControlAlignment">TopLeft</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem55</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@2,Width=80@2,Height=13</property>
      <property name="Location">@1,X=0@3,Y=194</property>
      <property name="Size">@3,Width=236@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Type de source</property>
      <property name="CustomizationFormText">Type de source</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item48" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">SourceIDLookUpEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@2,Width=54@2,Height=24</property>
      <property name="ControlAlignment">TopLeft</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem56</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@3,X=236@3,Y=194</property>
      <property name="Size">@2,Width=54@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Code Source</property>
      <property name="CustomizationFormText">Code Source</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item49" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">labelControl3</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=23</property>
      <property name="MinSize">@2,Width=20@2,Height=23</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem24</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@3,X=460@2,Y=80</property>
      <property name="Size">@2,Width=33@2,Height=40</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">layoutControlItem24</property>
      <property name="CustomizationFormText">layoutControlItem22</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item50" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">lbl_CardShortcut</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=23</property>
      <property name="MinSize">@2,Width=20@2,Height=23</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem69</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@3,X=460@2,Y=40</property>
      <property name="Size">@2,Width=33@2,Height=40</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">layoutControlItem69</property>
      <property name="CustomizationFormText">layoutControlItem21</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item51" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">lbl_CashShortcut</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=23</property>
      <property name="MinSize">@2,Width=20@2,Height=23</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem68</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@3,X=460@1,Y=0</property>
      <property name="Size">@2,Width=33@2,Height=40</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">layoutControlItem68</property>
      <property name="CustomizationFormText">layoutControlItem20</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item52" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">spinEdit_Bank</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=40</property>
      <property name="MinSize">@2,Width=56@2,Height=40</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">item0</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@3,X=254@2,Y=80</property>
      <property name="Size">@3,Width=206@2,Height=40</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">layoutControlItem7</property>
      <property name="CustomizationFormText">layoutControlItem19</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item53" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">CardLookUpEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Custom</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@1,Height=0</property>
      <property name="MinSize">@1,Width=1@1,Height=1</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem66</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@2,Width=75@2,Height=13</property>
      <property name="Location">@1,X=0@2,Y=40</property>
      <property name="Size">@3,Width=254@2,Height=40</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Carte</property>
      <property name="CustomizationFormText">شبكه</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item54" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">DrawerLookUpEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Custom</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@1,Height=0</property>
      <property name="MinSize">@1,Width=1@1,Height=1</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem65</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@2,Width=75@2,Height=13</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@3,Width=254@2,Height=40</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Espèces</property>
      <property name="CustomizationFormText">نقدي</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item55" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">spn_Card</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=40</property>
      <property name="MinSize">@2,Width=56@2,Height=40</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">item1</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@3,X=254@2,Y=40</property>
      <property name="Size">@3,Width=206@2,Height=40</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">item1</property>
      <property name="CustomizationFormText">layoutControlItem14</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item56" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">spn_Cash</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=40</property>
      <property name="MinSize">@3,Width=111@2,Height=40</property>
      <property name="ControlAlignment">TopLeft</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">item9</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@2,Width=50@2,Height=20</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@1,Width=0@1,Height=0</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">item9</property>
      <property name="CustomizationFormText">item9</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item57" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">spn_TotalPaid</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=40</property>
      <property name="MinSize">@3,Width=111@2,Height=40</property>
      <property name="ControlAlignment">TopLeft</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">Item </property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@2,Width=50@2,Height=20</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@1,Width=0@1,Height=0</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Item </property>
      <property name="CustomizationFormText">Item </property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item58" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">spn_Remain</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=40</property>
      <property name="MinSize">@3,Width=111@2,Height=40</property>
      <property name="ControlAlignment">TopLeft</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">item2</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@2,Width=50@2,Height=20</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@1,Width=0@1,Height=0</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">item2</property>
      <property name="CustomizationFormText">item2</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item59" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">simpleButton2</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=26</property>
      <property name="MinSize">@2,Width=28@2,Height=26</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">item3</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@3,X=242@1,Y=0</property>
      <property name="Size">@2,Width=54@2,Height=26</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">item3</property>
      <property name="CustomizationFormText">layoutControlItem11</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item60" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">DueDateDateEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=134@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForDueDate</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@2,Width=75@2,Height=13</property>
      <property name="Location">@1,X=0@2,Y=26</property>
      <property name="Size">@3,Width=242@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Date d'échéance</property>
      <property name="CustomizationFormText">تاريخ الاستحقاق</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item61" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">BankLookUpEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Custom</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@1,Height=0</property>
      <property name="MinSize">@1,Width=1@1,Height=1</property>
      <property name="ControlAlignment">TopLeft</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlItem67</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@2,Width=75@2,Height=13</property>
      <property name="Location">@1,X=0@2,Y=80</property>
      <property name="Size">@3,Width=254@2,Height=40</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">Banque</property>
      <property name="CustomizationFormText">شبكه</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item62" isnull="true" iskey="true">
      <property name="TypeName">LayoutGroup</property>
      <property name="TabbedGroupParentName" />
      <property name="GroupBordersVisible">true</property>
      <property name="AllowDrawBackground">true</property>
      <property name="EnableIndentsWithoutBorders">Default</property>
      <property name="OptionsItemText" isnull="true" iskey="true">
        <property name="TextToControlDistance">3</property>
        <property name="TextAlignMode">UseParentOptions</property>
      </property>
      <property name="CaptionImageVisible">true</property>
      <property name="FlowDirection">LeftToRight</property>
      <property name="LayoutMode">Regular</property>
      <property name="CaptionImageLocation">Default</property>
      <property name="CaptionImageIndex">-1</property>
      <property name="AllowBorderColorBlending">false</property>
      <property name="ExpandOnDoubleClick">false</property>
      <property name="Expanded">true</property>
      <property name="DefaultLayoutType">Vertical</property>
      <property name="ShowTabPageCloseButton">false</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsTableLayoutGroup" isnull="true" iskey="true">
        <property name="RowDefinitions" iskey="true" value="0" />
        <property name="ColumnDefinitions" iskey="true" value="0" />
      </property>
      <property name="Size">@3,Width=401@2,Height=36</property>
      <property name="ExpandButtonVisible">false</property>
      <property name="ExpandButtonMode">Normal</property>
      <property name="HeaderButtonsLocation">Default</property>
      <property name="GroupStyle">Inherited</property>
      <property name="TextLocation">Top</property>
      <property name="TabPageWidth">0</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">layoutControlGroup1</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">false</property>
      <property name="Location">@3,X=560@1,Y=0</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">layoutControlGroup1</property>
      <property name="CustomizationFormText">layoutControlGroup1</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
    </property>
    <property name="Item63" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">DeliveryOrderNumberTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=30</property>
      <property name="MinSize">@3,Width=145@2,Height=30</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForDeliveryOrderNumber</property>
      <property name="ParentName">layoutControlGroup1</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@2,Width=81@2,Height=13</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@3,Width=395@2,Height=30</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">N° de commande</property>
      <property name="CustomizationFormText">رقم امر التوريد</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item64" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">PostDateDateEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=109@2,Height=24</property>
      <property name="ControlAlignment">TopLeft</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">item4</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@2,Width=50@2,Height=20</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@1,Width=0@1,Height=0</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">item4</property>
      <property name="CustomizationFormText">item4</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item65" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">DropOffLocationTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=109@2,Height=24</property>
      <property name="ControlAlignment">TopLeft</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">item5</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@2,Width=50@2,Height=20</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@1,Width=0@1,Height=0</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">item5</property>
      <property name="CustomizationFormText">item5</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item66" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">OrderNumberTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=109@2,Height=24</property>
      <property name="ControlAlignment">TopLeft</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">item6</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@2,Width=50@2,Height=20</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@1,Width=0@1,Height=0</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">item6</property>
      <property name="CustomizationFormText">item6</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item67" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">DropOffTypeTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=109@2,Height=24</property>
      <property name="ControlAlignment">TopLeft</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">item7</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@2,Width=50@2,Height=20</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@1,Width=0@1,Height=0</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">item7</property>
      <property name="CustomizationFormText">item7</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item68" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">DropOffAddressTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@3,Width=109@2,Height=24</property>
      <property name="ControlAlignment">TopLeft</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">item8</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">true</property>
      <property name="TextSize">@2,Width=50@2,Height=20</property>
      <property name="Location">@1,X=0@1,Y=0</property>
      <property name="Size">@1,Width=0@1,Height=0</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">item8</property>
      <property name="CustomizationFormText">item8</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item69" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">StoreIDTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Custom</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@2,Width=54@2,Height=24</property>
      <property name="MinSize">@2,Width=54@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForStoreID</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@1,X=0@2,Y=72</property>
      <property name="Size">@2,Width=54@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">ItemForStoreID</property>
      <property name="CustomizationFormText">ItemForStoreID</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
    <property name="Item70" isnull="true" iskey="true">
      <property name="TypeName">LayoutControlItem</property>
      <property name="ControlName">CustomerIDTextEdit</property>
      <property name="AllowHtmlStringInCaption">false</property>
      <property name="TextAlignMode">UseParentOptions</property>
      <property name="SizeConstraintsType">Default</property>
      <property name="Image" isnull="true" />
      <property name="ImageIndex">-1</property>
      <property name="ImageAlignment">MiddleLeft</property>
      <property name="ImageToTextDistance">5</property>
      <property name="SvgImage" isnull="true" />
      <property name="SvgImageSize">@1,Width=0@1,Height=0</property>
      <property name="MaxSize">@1,Width=0@2,Height=24</property>
      <property name="MinSize">@2,Width=54@2,Height=24</property>
      <property name="ControlAlignment">TopRight</property>
      <property name="ContentVisible">true</property>
      <property name="HighlightFocusedItem">Default</property>
      <property name="TrimClientAreaToControl">true</property>
      <property name="ContentVertAlignment">Default</property>
      <property name="ContentHorzAlignment">Default</property>
      <property name="AllowGlyphSkinning">Default</property>
      <property name="OptionsCustomization" isnull="true" iskey="true">
        <property name="AllowDrag">Default</property>
        <property name="AllowDrop">Default</property>
      </property>
      <property name="OptionsTableLayoutItem" isnull="true" iskey="true">
        <property name="RowIndex">0</property>
        <property name="RowSpan">1</property>
        <property name="ColumnIndex">0</property>
        <property name="ColumnSpan">1</property>
      </property>
      <property name="OptionsToolTip" isnull="true" iskey="true">
        <property name="ToolTip" />
        <property name="ToolTipTitle" />
        <property name="ToolTipIconType">None</property>
        <property name="ImmediateToolTip">false</property>
        <property name="AllowHtmlString">Default</property>
        <property name="IconToolTip" />
        <property name="IconToolTipTitle" />
        <property name="IconToolTipIconType">None</property>
        <property name="EnableIconToolTip">true</property>
        <property name="IconImmediateToolTip">false</property>
        <property name="IconAllowHtmlString">Default</property>
      </property>
      <property name="Name">ItemForCustomerID</property>
      <property name="ParentName">Customization</property>
      <property name="TextVisible">false</property>
      <property name="TextSize">@1,Width=0@1,Height=0</property>
      <property name="Location">@1,X=0@2,Y=96</property>
      <property name="Size">@2,Width=54@2,Height=24</property>
      <property name="ShowInCustomizationForm">true</property>
      <property name="Text">ItemForCustomerID</property>
      <property name="CustomizationFormText">ItemForCustomerID</property>
      <property name="StartNewLine">false</property>
      <property name="Visibility">Always</property>
      <property name="TextLocation">Default</property>
    </property>
  </property>
</XtraSerializer>