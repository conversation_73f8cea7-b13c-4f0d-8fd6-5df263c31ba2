   .winmd.dll.exe O   (C:\Users\<USER>\Desktop\Hass\packages.config#C:\Users\<USER>\Desktop\Hass\app.config`C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.AbsenceRegulation.datasourceaC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.AbsenceRegulations.datasourceYC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.Department.datasourceZC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.Departments.datasourceYC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpAbsence.datasourceZC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpAbsences.datasource\C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpAttendance.datasource]C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpAttendances.datasourceVC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpLoan.datasource]C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpLoanDetails.datasourceWC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpLoans.datasourceWC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.Employee.datasourceXC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.Employees.datasourceYC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpMission.datasourceZC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpMissions.datasource_C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpOvertimeDelay.datasource`C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpOvertimeDelays.datasourceaC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpSalaryExtension.datasourcebC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpSalaryExtensions.datasourceWC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpShift.datasourceXC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpShifts.datasourceZC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpVacation.datasource[C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.EmpVacations.datasourceTC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.Group.datasourceUC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.Groups.datasourceRC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.Job.datasourceSC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.Jobs.datasource_C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.OfficialVacation.datasource`C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.OfficialVacations.datasourceiC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.OvertimeAndDelayRegulation.datasourceuC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.OvertimeAndDelayRegulationMinutesTable.datasourcevC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.OvertimeAndDelayRegulationMinutesTables.datasourcejC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.OvertimeAndDelayRegulations.datasource\C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.PenaltyReward.datasource]C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.PenaltyRewards.datasourceXC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.Reference.datasourceYC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.References.datasource^C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.SalaryExtension.datasource_C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.SalaryExtensions.datasource_C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.SalaryRegulation.datasourcehC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.SalaryRegulationExtension.datasourceiC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.SalaryRegulationExtensions.datasource`C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.SalaryRegulations.datasourceTC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.Shift.datasourceWC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.ShiftDay.datasourceXC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.ShiftDays.datasourceUC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.Shifts.datasourceeC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.TimeTable+LeaveDayType.datasourceXC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.TimeTable.datasourceYC:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.TimeTables.datasource^C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.WorkLeaveReturn.datasource_C:\Users\<USER>\Desktop\Hass\Properties\DataSources\EasyStock.HR.Models.WorkLeaveReturns.datasource5C:\Users\<USER>\Desktop\Hass\Properties\Settings.settings1C:\Users\<USER>\Desktop\Hass\Resources\edit_32x32.png2C:\Users\<USER>\Desktop\Hass\Resources\edit_32x321.png-C:\Users\<USER>\Desktop\Hass\Resources\delete.svg2C:\Users\<USER>\Desktop\Hass\Resources\imageimport.svg[C:\Program Files\DevExpress 23.1\Components\Bin\Framework\DevExpress.Data.Desktop.v23.1.dllSC:\Program Files\DevExpress 23.1\Components\Bin\Framework\DevExpress.Data.v23.1.dllVC:\Program Files\DevExpress 23.1\Components\Bin\Framework\DevExpress.Drawing.v23.1.dll\C:\Program Files\DevExpress 23.1\Components\Bin\Framework\DevExpress.Printing.v23.1.Core.dllTC:\Program Files\DevExpress 23.1\Components\Bin\Framework\DevExpress.Utils.v23.1.dllWC:\Program Files\DevExpress 23.1\Components\Bin\Framework\DevExpress.XtraBars.v23.1.dllZC:\Program Files\DevExpress 23.1\Components\Bin\Framework\DevExpress.XtraEditors.v23.1.dllWC:\Program Files\DevExpress 23.1\Components\Bin\Framework\DevExpress.XtraGrid.v23.1.dllYC:\Program Files\DevExpress 23.1\Components\Bin\Framework\DevExpress.XtraLayout.v23.1.dllHC:\Users\<USER>\Desktop\Hass\EasyStock.Common\bin\Debug\EasyStock.Common.dllUC:\Users\<USER>\Desktop\Hass\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.dll_C:\Users\<USER>\Desktop\Hass\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.SqlServer.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\mscorlib.dll~C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.ComponentModel.DataAnnotations.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Core.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Data.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.dllgC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Drawing.dllmC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Windows.Forms.dllcC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Xml.dll       UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}D{Registry:Software\Microsoft\.NETFramework,v4.7.2,AssemblyFoldersEx}
{RawFileName}0C:\Users\<USER>\Desktop\Hass\EasyStock.HR\bin\Debug\     D{Registry:Software\Microsoft\.NETFramework,v4.7.2,AssemblyFoldersEx}YC:\Users\<USER>\Desktop\Hass\EasyStock.HR\obj\Debug\DesignTimeResolveAssemblyReferences.cache   UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\Facades\.NETFramework,Version=v4.7.2.NET Framework 4.7.2v4.7.2msil
v4.0.30319         