﻿using DevExpress.Drawing.Printing;
using DevExpress.XtraEditors;
using DevExpress.XtraLayout;
using DevExpress.XtraLayout.Utils;
using DevExpress.XtraReports.UI;
using System;
using System.ComponentModel;
using System.Drawing.Printing;

namespace EasyStock.Controls
{
    public class ReportPropertiesControl : XtraUserControl
    {
        public XtraReport Report
        {
            get
            {
                return report;
            }
            set
            {
                report = value;
                if (report != null)
                {
                    spn_PaperW.EditValue = Report.PageWidth;
                    spn_PaperH.EditValue = Report.PageHeight;
                    ComboBoxPaperKind.EditValue = report.PaperKind;
                    MarginsT.EditValue = ((MarginBand)Report.Bands[BandKind.TopMargin])?.HeightF;
                    MarginsB.EditValue = ((MarginBand)Report.Bands[BandKind.BottomMargin])?.HeightF;
                    MarginsL.EditValue = Report.Margins?.Left;
                    MarginsR.EditValue = Report.Margins?.Right;
                    Columns.EditValue = ((DetailBand)Report.Bands[BandKind.Detail])?.MultiColumn.ColumnCount;
                    checkEdit1.Checked = Report?.RollPaper ?? false;
                    ColumnWidth.EditValue = ((DetailBand)Report.Bands[BandKind.Detail])?.MultiColumn.ColumnWidth;
                    ColumnSpacing.EditValue = ((DetailBand)Report.Bands[BandKind.Detail])?.MultiColumn.ColumnSpacing;
                    comboBoxEdit1.SelectedIndex = (int)(((DetailBand)Report.Bands[BandKind.Detail])?.MultiColumn.Mode).Value;
                }
            }
        }

        public ReportPropertiesControl()
        {
            InitializeComponent();
            ComboBoxPaperKind.Properties.AddEnum<PaperKind>();
            ComboBoxPaperKind.EditValueChanged += ComboBoxPaperKind_EditValueChanged;
            BindDateChanged(layoutControl1);
            spn_PaperH.EditValueChanged += spn_PaperH_EditValueChanged;
            spn_PaperW.EditValueChanged += spn_PaperW_EditValueChanged;
            MarginsT.EditValueChanged += MarginsT_EditValueChanged;
            MarginsB.EditValueChanged += MargensB_EditValueChanged;
            MarginsR.EditValueChanged += MargensR_EditValueChanged;
            MarginsL.EditValueChanged += MarginsL_EditValueChanged;
            Columns.EditValueChanged += Columns_EditValueChanged;
        }

        public void BindDateChanged(LayoutControl layoutControl)
        {
            layoutControl.AllowCustomization = false;
            foreach (object item in layoutControl.Controls)
            {
                if (!(item is BaseEdit edit))
                {
                    continue;
                }
                edit.EditValueChanged += delegate (object ss, EventArgs ee)
                {
                    if (((BaseEdit)ss).ContainsFocus)
                    {
                        Event?.Invoke(ss, ee);
                    }
                };
            }
        }

        private void ComboBoxPaperKind_EditValueChanged(object sender, EventArgs e)
        {
            if (Report != null && ((sender is BaseEdit edit) ? edit.EditValue : null) is DXPaperKind paperKind && paperKind != Report.PaperKind)
            {
                Report.PaperKind = paperKind;
                SpinEdit spinEdit = spn_PaperH;
                bool enabled = (spn_PaperW.Enabled = paperKind == DXPaperKind.Custom);
                spinEdit.Enabled = enabled;
                spn_PaperW.EditValue = Report.PageWidth;
                spn_PaperH.EditValue = Report.PageHeight;
            }
        }

        private void spn_PaperH_EditValueChanged(object sender, EventArgs e)
        {
            if (Report != null)
            {
                Report.PageHeight = (int)spn_PaperH.Value;
                Band band = Report.Bands[BandKind.Detail];
                if (band != null && Report.PaperKind == DXPaperKind.Custom)
                {
                    band.HeightF = (int)spn_PaperH.Value;
                }
            }
        }

        private void spn_PaperW_EditValueChanged(object sender, EventArgs e)
        {
            if (Report != null)
            {
                Report.PageWidth = (int)spn_PaperW.Value;
            }
        }

        private void MarginsT_EditValueChanged(object sender, EventArgs e)
        {
            if (Report != null)
            {
                MarginBand band = (MarginBand)Report.Bands[BandKind.TopMargin];
                if (band != null)
                {
                    band.HeightF = (int)MarginsT.Value;
                }
            }
        }

        private void MargensB_EditValueChanged(object sender, EventArgs e)
        {
            if (Report != null)
            {
                MarginBand band = (MarginBand)Report.Bands[BandKind.BottomMargin];
                if (band != null)
                {
                    band.HeightF = (int)MarginsB.Value;
                }
            }
        }

        private void MargensR_EditValueChanged(object sender, EventArgs e)
        {
            if (Report != null)
            {
                Report.Margins.Right = (int)MarginsR.Value;
            }
        }

        private void MarginsL_EditValueChanged(object sender, EventArgs e)
        {
            if (Report != null)
            {
                Report.Margins.Left = (int)MarginsL.Value;
            }
        }

        private void Columns_EditValueChanged(object sender, EventArgs e)
        {
            DetailBand band = (DetailBand)Report.Bands[BandKind.Detail];
            if (band != null)
            {
                band.MultiColumn.ColumnCount = (int)Columns.Value;
            }
        }

        private void checkEdit1_CheckedChanged(object sender, EventArgs e)
        {
            if (Report != null)
            {
                Report.RollPaper = checkEdit1.Checked;
            }
        }

        private void comboBoxEdit1_SelectedIndexChanged(object sender, EventArgs e)
        {
            DetailBand band = (DetailBand)Report.Bands[BandKind.Detail];
            if (band != null && ((comboBoxEdit1.SelectedIndex >= 0) & (comboBoxEdit1.SelectedIndex <= 3)))
            {
                band.MultiColumn.Mode = (MultiColumnMode)comboBoxEdit1.SelectedIndex;
            }
            if (comboBoxEdit1.SelectedIndex == 0)
            {
                LayoutControlItem layoutControlItem = layoutControlItem8;
                LayoutControlItem layoutControlItem2 = layoutControlItem9;
                LayoutVisibility layoutVisibility2 = (layoutControlItem10.Visibility = LayoutVisibility.Never);
                LayoutVisibility visibility = (layoutControlItem2.Visibility = layoutVisibility2);
                layoutControlItem.Visibility = visibility;
            }
        }

        private void ColumnSpacing_EditValueChanged(object sender, EventArgs e)
        {
            DetailBand band = (DetailBand)Report.Bands[BandKind.Detail];
            if (band != null)
            {
                band.MultiColumn.ColumnSpacing = (int)ColumnSpacing.Value;
            }
        }

        private void ColumnWidth_EditValueChanged(object sender, EventArgs e)
        {
            DetailBand band = (DetailBand)Report.Bands[BandKind.Detail];
            if (band != null)
            {
                band.MultiColumn.ColumnSpacing = (int)ColumnWidth.Value;
            }
        }
        protected override void Dispose(bool disposing)
        {
            bool flag = disposing && this.components != null;
            if (flag)
            {
                this.components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            this.ComboBoxPaperKind = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.spn_PaperH = new DevExpress.XtraEditors.SpinEdit();
            this.spn_PaperW = new DevExpress.XtraEditors.SpinEdit();
            this.MarginsT = new DevExpress.XtraEditors.SpinEdit();
            this.MarginsB = new DevExpress.XtraEditors.SpinEdit();
            this.MarginsR = new DevExpress.XtraEditors.SpinEdit();
            this.MarginsL = new DevExpress.XtraEditors.SpinEdit();
            this.Columns = new DevExpress.XtraEditors.SpinEdit();
            this.ColumnSpacing = new DevExpress.XtraEditors.SpinEdit();
            this.ColumnWidth = new DevExpress.XtraEditors.SpinEdit();
            this.comboBoxEdit1 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.checkEdit1 = new DevExpress.XtraEditors.CheckEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem3 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem4 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem5 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem6 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem7 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem9 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem8 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem10 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem11 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem12 = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).BeginInit();
            this.layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ComboBoxPaperKind.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spn_PaperH.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spn_PaperW.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.MarginsT.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.MarginsB.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.MarginsR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.MarginsL.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Columns.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ColumnSpacing.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ColumnWidth.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem12)).BeginInit();
            this.SuspendLayout();
            this.layoutControl1.Controls.Add(this.ComboBoxPaperKind);
            this.layoutControl1.Controls.Add(this.spn_PaperH);
            this.layoutControl1.Controls.Add(this.spn_PaperW);
            this.layoutControl1.Controls.Add(this.MarginsT);
            this.layoutControl1.Controls.Add(this.MarginsB);
            this.layoutControl1.Controls.Add(this.MarginsR);
            this.layoutControl1.Controls.Add(this.MarginsL);
            this.layoutControl1.Controls.Add(this.Columns);
            this.layoutControl1.Controls.Add(this.ColumnSpacing);
            this.layoutControl1.Controls.Add(this.ColumnWidth);
            this.layoutControl1.Controls.Add(this.comboBoxEdit1);
            this.layoutControl1.Controls.Add(this.checkEdit1);
            this.layoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl1.Location = new System.Drawing.Point(0, 0);
            this.layoutControl1.Name = "layoutControl1";
            this.layoutControl1.Root = this.Root;
            this.layoutControl1.Size = new System.Drawing.Size(441, 489);
            this.layoutControl1.TabIndex = 0;
            this.layoutControl1.Text = "layoutControl1";
            this.ComboBoxPaperKind.Location = new System.Drawing.Point(164, 12);
            this.ComboBoxPaperKind.Name = "ComboBoxPaperKind";
            this.ComboBoxPaperKind.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ComboBoxPaperKind.Size = new System.Drawing.Size(265, 20);
            this.ComboBoxPaperKind.StyleController = this.layoutControl1;
            this.ComboBoxPaperKind.TabIndex = 4;
            this.spn_PaperH.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spn_PaperH.Location = new System.Drawing.Point(164, 36);
            this.spn_PaperH.Name = "spn_PaperH";
            this.spn_PaperH.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.spn_PaperH.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.spn_PaperH.Properties.IsFloatValue = false;
            this.spn_PaperH.Properties.Mask.EditMask = "N00";
            this.spn_PaperH.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.None;
            this.spn_PaperH.Size = new System.Drawing.Size(155, 20);
            this.spn_PaperH.StyleController = this.layoutControl1;
            this.spn_PaperH.TabIndex = 5;
            this.spn_PaperW.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spn_PaperW.Location = new System.Drawing.Point(164, 60);
            this.spn_PaperW.Name = "spn_PaperW";
            this.spn_PaperW.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.spn_PaperW.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.spn_PaperW.Properties.IsFloatValue = false;
            this.spn_PaperW.Properties.Mask.EditMask = "N00";
            this.spn_PaperW.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.None;
            this.spn_PaperW.Size = new System.Drawing.Size(265, 20);
            this.spn_PaperW.StyleController = this.layoutControl1;
            this.spn_PaperW.TabIndex = 6;
            this.MarginsT.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.MarginsT.Location = new System.Drawing.Point(164, 84);
            this.MarginsT.Name = "MarginsT";
            this.MarginsT.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.MarginsT.Properties.IsFloatValue = false;
            this.MarginsT.Properties.Mask.EditMask = "N00";
            this.MarginsT.Size = new System.Drawing.Size(265, 20);
            this.MarginsT.StyleController = this.layoutControl1;
            this.MarginsT.TabIndex = 7;
            this.MarginsB.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.MarginsB.Location = new System.Drawing.Point(164, 108);
            this.MarginsB.Name = "MarginsB";
            this.MarginsB.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.MarginsB.Properties.IsFloatValue = false;
            this.MarginsB.Properties.Mask.EditMask = "N00";
            this.MarginsB.Size = new System.Drawing.Size(265, 20);
            this.MarginsB.StyleController = this.layoutControl1;
            this.MarginsB.TabIndex = 7;
            this.MarginsR.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.MarginsR.Location = new System.Drawing.Point(164, 132);
            this.MarginsR.Name = "MarginsR";
            this.MarginsR.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.MarginsR.Properties.IsFloatValue = false;
            this.MarginsR.Properties.Mask.EditMask = "N00";
            this.MarginsR.Size = new System.Drawing.Size(265, 20);
            this.MarginsR.StyleController = this.layoutControl1;
            this.MarginsR.TabIndex = 7;
            this.MarginsL.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.MarginsL.Location = new System.Drawing.Point(164, 156);
            this.MarginsL.Name = "MarginsL";
            this.MarginsL.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.MarginsL.Properties.IsFloatValue = false;
            this.MarginsL.Properties.Mask.EditMask = "N00";
            this.MarginsL.Size = new System.Drawing.Size(265, 20);
            this.MarginsL.StyleController = this.layoutControl1;
            this.MarginsL.TabIndex = 7;
            this.Columns.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.Columns.Location = new System.Drawing.Point(164, 204);
            this.Columns.Name = "Columns";
            this.Columns.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.Columns.Properties.IsFloatValue = false;
            this.Columns.Properties.Mask.EditMask = "N00";
            this.Columns.Size = new System.Drawing.Size(265, 20);
            this.Columns.StyleController = this.layoutControl1;
            this.Columns.TabIndex = 7;
            this.ColumnSpacing.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.ColumnSpacing.Location = new System.Drawing.Point(164, 228);
            this.ColumnSpacing.Name = "ColumnSpacing";
            this.ColumnSpacing.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ColumnSpacing.Properties.IsFloatValue = false;
            this.ColumnSpacing.Properties.Mask.EditMask = "N00";
            this.ColumnSpacing.Size = new System.Drawing.Size(265, 20);
            this.ColumnSpacing.StyleController = this.layoutControl1;
            this.ColumnSpacing.TabIndex = 8;
            this.ColumnWidth.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.ColumnWidth.Location = new System.Drawing.Point(164, 252);
            this.ColumnWidth.Name = "ColumnWidth";
            this.ColumnWidth.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ColumnWidth.Properties.IsFloatValue = false;
            this.ColumnWidth.Properties.Mask.EditMask = "N00";
            this.ColumnWidth.Size = new System.Drawing.Size(265, 20);
            this.ColumnWidth.StyleController = this.layoutControl1;
            this.ColumnWidth.TabIndex = 9;
            this.comboBoxEdit1.EditValue = "Sans";
            this.comboBoxEdit1.Location = new System.Drawing.Point(164, 180);
            this.comboBoxEdit1.Name = "comboBoxEdit1";
            this.comboBoxEdit1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEdit1.Properties.Items.AddRange(new object[] {
            "Sans",
            "Utiliser le nombre de colonnes",
            "Utiliser la largeur de colonne"});
            this.comboBoxEdit1.Size = new System.Drawing.Size(265, 20);
            this.comboBoxEdit1.StyleController = this.layoutControl1;
            this.comboBoxEdit1.TabIndex = 10;
            this.checkEdit1.Location = new System.Drawing.Point(323, 36);
            this.checkEdit1.Name = "checkEdit1";
            this.checkEdit1.Properties.Caption = "Papier en rouleau";
            this.checkEdit1.Size = new System.Drawing.Size(106, 19);
            this.checkEdit1.StyleController = this.layoutControl1;
            this.checkEdit1.TabIndex = 11;
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem1,
            this.layoutControlItem2,
            this.layoutControlItem3,
            this.layoutControlItem4,
            this.layoutControlItem5,
            this.layoutControlItem6,
            this.layoutControlItem7,
            this.layoutControlItem9,
            this.layoutControlItem8,
            this.layoutControlItem10,
            this.layoutControlItem11,
            this.layoutControlItem12});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(441, 489);
            this.Root.TextVisible = false;
            this.layoutControlItem1.Control = this.ComboBoxPaperKind;
            this.layoutControlItem1.ControlAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.layoutControlItem1.CustomizationFormText = "Type de papier";
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(421, 24);
            this.layoutControlItem1.Text = "Type de papier";
            this.layoutControlItem1.TextSize = new System.Drawing.Size(148, 13);
            this.layoutControlItem2.Control = this.spn_PaperH;
            this.layoutControlItem2.ControlAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.layoutControlItem2.CustomizationFormText = "Longueur";
            this.layoutControlItem2.Location = new System.Drawing.Point(0, 24);
            this.layoutControlItem2.Name = "layoutControlItem2";
            this.layoutControlItem2.Size = new System.Drawing.Size(311, 24);
            this.layoutControlItem2.Text = "Longueur";
            this.layoutControlItem2.TextSize = new System.Drawing.Size(148, 13);
            this.layoutControlItem3.Control = this.spn_PaperW;
            this.layoutControlItem3.ControlAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.layoutControlItem3.CustomizationFormText = "Largeur";
            this.layoutControlItem3.Location = new System.Drawing.Point(0, 48);
            this.layoutControlItem3.Name = "layoutControlItem3";
            this.layoutControlItem3.Size = new System.Drawing.Size(421, 24);
            this.layoutControlItem3.Text = "Largeur";
            this.layoutControlItem3.TextSize = new System.Drawing.Size(148, 13);
            this.layoutControlItem4.Control = this.MarginsT;
            this.layoutControlItem4.ControlAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.layoutControlItem4.Location = new System.Drawing.Point(0, 72);
            this.layoutControlItem4.Name = "layoutControlItem4";
            this.layoutControlItem4.Size = new System.Drawing.Size(421, 24);
            this.layoutControlItem4.Text = "Marge supérieure";
            this.layoutControlItem4.TextSize = new System.Drawing.Size(148, 13);
            this.layoutControlItem5.Control = this.MarginsB;
            this.layoutControlItem5.ControlAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.layoutControlItem5.Location = new System.Drawing.Point(0, 96);
            this.layoutControlItem5.Name = "layoutControlItem5";
            this.layoutControlItem5.Size = new System.Drawing.Size(421, 24);
            this.layoutControlItem5.Text = "Marge inférieure";
            this.layoutControlItem5.TextSize = new System.Drawing.Size(148, 13);
            this.layoutControlItem6.Control = this.MarginsR;
            this.layoutControlItem6.ControlAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.layoutControlItem6.Location = new System.Drawing.Point(0, 120);
            this.layoutControlItem6.Name = "layoutControlItem6";
            this.layoutControlItem6.Size = new System.Drawing.Size(421, 24);
            this.layoutControlItem6.Text = "Marge droite";
            this.layoutControlItem6.TextSize = new System.Drawing.Size(148, 13);
            this.layoutControlItem7.Control = this.MarginsL;
            this.layoutControlItem7.ControlAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.layoutControlItem7.Location = new System.Drawing.Point(0, 144);
            this.layoutControlItem7.Name = "layoutControlItem7";
            this.layoutControlItem7.Size = new System.Drawing.Size(421, 24);
            this.layoutControlItem7.Text = "Marge gauche";
            this.layoutControlItem7.TextSize = new System.Drawing.Size(148, 13);
            this.layoutControlItem9.Control = this.Columns;
            this.layoutControlItem9.ControlAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.layoutControlItem9.Location = new System.Drawing.Point(0, 192);
            this.layoutControlItem9.Name = "layoutControlItem9";
            this.layoutControlItem9.Size = new System.Drawing.Size(421, 24);
            this.layoutControlItem9.Text = "Nombre de colonnes";
            this.layoutControlItem9.TextSize = new System.Drawing.Size(148, 13);
            this.layoutControlItem8.Control = this.ColumnSpacing;
            this.layoutControlItem8.Location = new System.Drawing.Point(0, 216);
            this.layoutControlItem8.Name = "layoutControlItem8";
            this.layoutControlItem8.Size = new System.Drawing.Size(421, 24);
            this.layoutControlItem8.Text = "Espacement entre les colonnes";
            this.layoutControlItem8.TextSize = new System.Drawing.Size(148, 13);
            this.layoutControlItem10.Control = this.ColumnWidth;
            this.layoutControlItem10.Location = new System.Drawing.Point(0, 240);
            this.layoutControlItem10.Name = "layoutControlItem10";
            this.layoutControlItem10.Size = new System.Drawing.Size(421, 229);
            this.layoutControlItem10.Text = "Largeur de la colonne";
            this.layoutControlItem10.TextSize = new System.Drawing.Size(148, 13);
            this.layoutControlItem11.Control = this.comboBoxEdit1;
            this.layoutControlItem11.Location = new System.Drawing.Point(0, 168);
            this.layoutControlItem11.Name = "layoutControlItem11";
            this.layoutControlItem11.Size = new System.Drawing.Size(421, 24);
            this.layoutControlItem11.Text = "Système de colonnes";
            this.layoutControlItem11.TextSize = new System.Drawing.Size(148, 13);
            this.layoutControlItem12.Control = this.checkEdit1;
            this.layoutControlItem12.Location = new System.Drawing.Point(311, 24);
            this.layoutControlItem12.Name = "layoutControlItem12";
            this.layoutControlItem12.Size = new System.Drawing.Size(110, 24);
            this.layoutControlItem12.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem12.TextVisible = false;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.layoutControl1);
            this.Name = "ReportPropertiesControl";
            this.Size = new System.Drawing.Size(441, 489);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).EndInit();
            this.layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ComboBoxPaperKind.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spn_PaperH.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spn_PaperW.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.MarginsT.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.MarginsB.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.MarginsR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.MarginsL.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Columns.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ColumnSpacing.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ColumnWidth.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem12)).EndInit();
            this.ResumeLayout(false);

        }

        private XtraReport report;

        public EventHandler Event;

        private IContainer components = null;

        private LayoutControl layoutControl1;

        private LayoutControlGroup Root;

        private ImageComboBoxEdit ComboBoxPaperKind;

        private SpinEdit spn_PaperH;

        private SpinEdit spn_PaperW;

        private SpinEdit MarginsT;

        private SpinEdit MarginsB;

        private SpinEdit MarginsR;

        private SpinEdit MarginsL;

        private SpinEdit Columns;

        private LayoutControlItem layoutControlItem1;

        private LayoutControlItem layoutControlItem2;

        private LayoutControlItem layoutControlItem3;

        private LayoutControlItem layoutControlItem4;

        private LayoutControlItem layoutControlItem5;

        private LayoutControlItem layoutControlItem6;

        private LayoutControlItem layoutControlItem7;

        private LayoutControlItem layoutControlItem9;

        private SpinEdit ColumnSpacing;

        private SpinEdit ColumnWidth;

        private ComboBoxEdit comboBoxEdit1;

        private LayoutControlItem layoutControlItem8;

        private LayoutControlItem layoutControlItem10;

        private LayoutControlItem layoutControlItem11;

        private CheckEdit checkEdit1;

        private LayoutControlItem layoutControlItem12;
    }
}
