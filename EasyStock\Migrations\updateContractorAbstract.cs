﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x020004B6 RID: 1206
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class updateContractorAbstract : DbMigration, IMigrationMetadata
	{
		// Token: 0x060023E2 RID: 9186 RVA: 0x00012864 File Offset: 0x00010A64
		public override void Up()
		{
			base.RenameColumn("dbo.ContractorAbstracts", "Type_ID", "WorkTypeID", null);
			base.RenameIndex("dbo.ContractorAbstracts", "IX_Type_ID", "IX_WorkTypeID", null);
		}

		// Token: 0x060023E3 RID: 9187 RVA: 0x00012895 File Offset: 0x00010A95
		public override void Down()
		{
			base.RenameIndex("dbo.ContractorAbstracts", "IX_WorkTypeID", "IX_Type_ID", null);
			base.RenameColumn("dbo.ContractorAbstracts", "WorkTypeID", "Type_ID", null);
		}

		// Token: 0x17000BC1 RID: 3009
		// (get) Token: 0x060023E4 RID: 9188 RVA: 0x001F51BC File Offset: 0x001F33BC
		string IMigrationMetadata.Id
		{
			get
			{
				return "202108181938418_updateContractorAbstract";
			}
		}

		// Token: 0x17000BC2 RID: 3010
		// (get) Token: 0x060023E5 RID: 9189 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000BC3 RID: 3011
		// (get) Token: 0x060023E6 RID: 9190 RVA: 0x001F51D4 File Offset: 0x001F33D4
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B5F RID: 11103
		private readonly ResourceManager Resources = new ResourceManager(typeof(updateContractorAbstract));
	}
}
