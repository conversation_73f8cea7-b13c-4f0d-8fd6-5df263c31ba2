﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Détails de correction de stock")]
    [Table("StockBalanceAfterCorrectionDetail")]
    public class StockBalanceAfterCorrectionDetail : ProductTransaction
    {
        [Display(Name = "Inventaire réalisé")]
        public bool IsDone
        {
            get
            {
                return this.isDone;
            }
            set
            {
                base.SetProperty<bool>(ref this.isDone, value, "IsDone");
            }
        }

        public int StockBalanceCorrectionID { get; set; }

        public StockBalanceCorrection StockBalanceCorrection { get; set; }

        private bool isDone;
    }
}
