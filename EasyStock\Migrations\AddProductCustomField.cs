﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class AddProductCustomField : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(AddProductCustomField));

        string IMigrationMetadata.Id => "202101141244202_AddProductCustomField";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.ProductCustomFields", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                CustomField1 = c.String(),
                CustomField2 = c.String(),
                CustomField3 = c.String(),
                CustomField4 = c.String()
            }).PrimaryKey(t => t.ID);
            AddColumn("dbo.Products", "CustomField1", (ColumnBuilder c) => c.String());
            AddColumn("dbo.Products", "CustomField2", (ColumnBuilder c) => c.String());
            AddColumn("dbo.Products", "CustomField3", (ColumnBuilder c) => c.String());
            AddColumn("dbo.Products", "CustomField4", (ColumnBuilder c) => c.String());
        }

        public override void Down()
        {
            DropColumn("dbo.Products", "CustomField4");
            DropColumn("dbo.Products", "CustomField3");
            DropColumn("dbo.Products", "CustomField2");
            DropColumn("dbo.Products", "CustomField1");
            DropTable("dbo.ProductCustomFields");
        }
    }
}
