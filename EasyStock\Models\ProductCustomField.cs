﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Champs affectés aux article")]
    public class ProductCustomField
    {

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [ReadOnly(true)]
        public int ID { get; set; }


        public string CustomField1 { get; set; }

        public string CustomField2 { get; set; }


        public string CustomField3 { get; set; }

        public string CustomField4 { get; set; }
    }
}
