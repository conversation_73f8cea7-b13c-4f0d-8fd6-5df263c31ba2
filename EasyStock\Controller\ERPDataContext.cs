﻿using DevExpress.XtraRichEdit.Import.Html;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.MainViews;
using EasyStock.Migrations;
using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.Migrations;
using System.Data.Entity.ModelConfiguration.Configuration;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Linq;
using System.Reflection;

namespace EasyStock.Controller
{

    public class ERPDataContext : DbContext
    {
        internal sealed class DataDbInitializer : MigrateDatabaseToLatestVersion<ERPDataContext, Configuration>
        {
        }

        private static SystemSettings systemSettings;

        private static bool SavingTrackedItems;

        public static List<ChangeTrackerItem> LastChangeTrackerItems;

        public virtual DbSet<Personal> Personals { get; set; }

        public virtual DbSet<Customer> Customers { get; set; }

        public virtual DbSet<Vendor> Vendors { get; set; }

        public virtual DbSet<PersonalGroup> PersonalsGroups { get; set; }

        public virtual DbSet<CustomerGroup> CustomersGroups { get; set; }

        public virtual DbSet<VendorGroup> VendorsGroups { get; set; }

        public virtual DbSet<Account> Accounts { get; set; }

        public virtual DbSet<BillingDetail> BillingDetails { get; set; }

        public virtual DbSet<Drawer> Drawers { get; set; }

        public virtual DbSet<Bank> Banks { get; set; }

        public virtual DbSet<PayCard> PayCards { get; set; }

        public virtual DbSet<Store> Stores { get; set; }


        public static Store store
        {
            get
            {
                using ERPDataContext db = new ERPDataContext();
                if (db.Stores.Count() == 0)
                {
                    db.Stores.Add(new Store
                    {
                        ID = 0,
                        Name = "Default Dépot",
                        Address = "El eulma Setif Algeria",
                        City = "el eulma",
                        Phone = "**********",
                    });
                    db.SaveChanges();
                }
                return db.Stores.FirstOrDefault();
            }
            set
            {
                using ERPDataContext db = new ERPDataContext();
                db.Stores.AddOrUpdate(value);
                db.SaveChanges();
            }
        }
        public virtual DbSet<Product> Products { get; set; }

        public virtual DbSet<ProductColor> ProductColors { get; set; }

        public virtual DbSet<ProductCompany> ProductCompanies { get; set; }

        public virtual DbSet<ProductVendor> ProductVendors { get; set; }

        public virtual DbSet<ProductCategory> ProductCategories { get; set; }

        public virtual DbSet<ProductSize> ProductSizes { get; set; }

        public virtual DbSet<ProductUnit> ProductUnits { get; set; }

        public virtual DbSet<ProductUnitBarcode> ProductUnitBarCodes { get; set; }

        public virtual DbSet<UnitOfMeasurement> UnitOfMeasurements { get; set; }

        public virtual DbSet<Bill> Bills { get; set; }

        public virtual DbSet<OpenBalanceBill> OpenBalanceBills { get; set; }

        public virtual DbSet<OutgoingBill> OutgoingBills { get; set; }

        public virtual DbSet<ProductDamageBill> ProductDamageBills { get; set; }

        public virtual DbSet<SalesInvoice> SalesInvoices { get; set; }

        public virtual DbSet<SalesReturnInvoice> SalesReturnInvoices { get; set; }

        public virtual DbSet<InvoiceDetail> InvoicesDetails { get; set; }

        public virtual DbSet<ProductTransaction> ProductTransactions { get; set; }

        public virtual DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }

        public virtual DbSet<PurchaseReturnInvoice> PurchaseReturnInvoices { get; set; }

        public virtual DbSet<PayDetail> PayDetails { get; set; }

        public virtual DbSet<Currency> Currencies { get; set; }

        public virtual DbSet<Journal> Journals { get; set; }

        public virtual DbSet<JournalDetail> JournalDetails { get; set; }

        public virtual DbSet<InvoiceShortCut> InvoiceShortCuts { get; set; }

        public virtual DbSet<CashNote> CashNotes { get; set; }

        public virtual DbSet<RevExpEntry> RevExpEntries { get; set; }

        public virtual DbSet<RevExpEntryDetail> RevExpEntriesDetails { get; set; }

        public virtual DbSet<UserAccessProfile> UserAccessProfiles { get; set; }

        public virtual DbSet<UserAccessProfileDetail> UserAccessProfileDetails { get; set; }

        public virtual DbSet<User> Users { get; set; }

        public virtual DbSet<UserSettingsProfile> UserSettingsProfiles { get; set; }

        public virtual DbSet<SimilarProduct> SimilarProducts { get; set; }

        public virtual DbSet<GroupOfProducts> GroupsOfProducts { get; set; }

        public virtual DbSet<GroupOfProductsDetails> GroupsOfProductsDetails { get; set; }

        public virtual DbSet<StockTransferBill> StockTransferBills { get; set; }

        public virtual DbSet<ProductStoreLocation> ProductStoreLocations { get; set; }

        public virtual DbSet<TransactionLinkModel> TransactionLinks { get; set; }

        public virtual DbSet<ReportTemplate> ReportTemplates { get; set; }

        public virtual DbSet<PrintedBarcode> PrintedBarcodes { get; set; }

        public virtual DbSet<DrawerPeriod> DrawerPeriods { get; set; }

        public virtual DbSet<DrawerPeriodTransSummeryItem> DrawerPeriodTransSummeryItems { get; set; }

        public virtual DbSet<CashTransfer> CashTransfers { get; set; }

        public virtual DbSet<UserLog> UserLogs { get; set; }

        public virtual DbSet<ChangeTrackerItem> ChangeTrackerItems { get; set; }

        public virtual DbSet<CostCenter> CostCenters { get; set; }

        public virtual DbSet<Branch> Branches { get; set; }

        public virtual DbSet<FrequentlyUsedScreen> FrequentlyUsedScreens { get; set; }

        public virtual DbSet<StockBalanceCorrection> StockBalanceCorrections { get; set; }

        public virtual DbSet<StockBalanceAfterCorrectionDetail> StockBalanceAfterCorrectionDetails { get; set; }

        public virtual DbSet<StockBalanceBeforeCorrectionDetail> StockBalanceBeforeCorrectionDetails { get; set; }

        public virtual DbSet<SalesOrder> SalesOrders { get; set; }

        public virtual DbSet<SalesOrderDetail> SalesOrderDetails { get; set; }

        public virtual DbSet<SalesPriceOfferDetail> SalesPriceOfferDetails { get; set; }

        public virtual DbSet<SalesPriceOffer> SalesPriceOffers { get; set; }

        public virtual DbSet<PettyCash> PettyCashes { get; set; }

        public virtual DbSet<PettyCashCloseOut> PettyCashCloses { get; set; }

        public virtual DbSet<PettyCashHolder> PettyCashHolders { get; set; }

        public virtual DbSet<DefaultExpenses> DefaultExpenses { get; set; }

        public virtual DbSet<ActualCost> ActualCosts { get; set; }

        public virtual DbSet<DefaultMaterialConsumption> DefaultMaterialConsumptions { get; set; }

        public virtual DbSet<ActualMaterialConsumption> ActualMaterialConsumptions { get; set; }

        public virtual DbSet<BOMExpenses> BOMExpenses { get; set; }

        public virtual DbSet<BOMDetails> BOMDetails { get; set; }

        public virtual DbSet<WorkOrder> WorkOrders { get; set; }

        public virtual DbSet<WorkOrderDetails> WorkOrderDetails { get; set; }

        public virtual DbSet<BillOfMaterials> BillOfMaterials { get; set; }

        public virtual DbSet<WorkType> WorkTypes { get; set; }

        public virtual DbSet<ContractorAbstract> ContractorAbstracts { get; set; }

        public virtual DbSet<VendorInvoice> VendorInvoices { get; set; }

        [EditorBrowsable(EditorBrowsableState.Never)]
        public virtual DbSet<CompanyInfo> Infos { get; set; }

        public static CompanyInfo CompanyInfo
        {
            get
            {
                using ERPDataContext db = new ERPDataContext();
                if (db.Infos.Count() == 0)
                {
                    db.Infos.Add(new CompanyInfo
                    {
                        ID = 0,
                        Name = "OraScoop Mars Algeria",
                        Address = "El eulma Setif Algeria",
                        City = "el eulma",
                        CommercialBook = "000001111111111",
                        CompanyTaxCard = "22222222222222222",
                        CompanyNIS = "222222222222222223",
                        CompanyRIB = "222222222222222223",
                        CompanyART = "222222222222222223",
                        Mobile = "*********",
                        Phone = "**********",
                        FinancialYearEnd = new DateTime(db.GetServerTime().Year, 12, 31),
                        FinancialYearStart = new DateTime(db.GetServerTime().Year, 1, 1)
                    });
                    db.SaveChanges();
                }
                return db.Infos.FirstOrDefault();
            }
            set
            {
                using ERPDataContext db = new ERPDataContext();
                db.Infos.AddOrUpdate(value);
                db.SaveChanges();
                HomeForm.Instance.SetNameAndLogo();
            }
        }

        [EditorBrowsable(EditorBrowsableState.Never)]
        public virtual DbSet<SystemSettings> settings { get; set; }

        public static SystemSettings SystemSettings
        {
            get
            {
                if (systemSettings == null)
                {
                    using ERPDataContext db = new ERPDataContext();
                    if (db.settings.Count() == 0)
                    {
                        db.settings.Add(new SystemSettings
                        {
                            EmployeesDueAccount = AccountHelper.Accounts.EmployeesDueAccount.ID,
                            WagesAccount = AccountHelper.Accounts.WagesAccount.ID,
                            DueSalerysAccount = AccountHelper.Accounts.DueSalerysAccount.ID,
                            DrawerAccount = AccountHelper.Accounts.DrawerAccount.ID,
                            BanksAccount = AccountHelper.Accounts.BanksAccount.ID,
                            CustomersAccount = AccountHelper.Accounts.CustomersAccount.ID,
                            NotesReceivableAccount = AccountHelper.Accounts.NotesReceivableAccount.ID,
                            InventoryAccount = AccountHelper.Accounts.InventoryAccount.ID,
                            VendorsAccount = AccountHelper.Accounts.VendorsAccount.ID,
                            CapitalAccount = AccountHelper.Accounts.CapitalAccount.ID,
                            NotesPayableAccount = AccountHelper.Accounts.NotesPayableAccount.ID,
                            TaxAccount = AccountHelper.Accounts.TaxAccount.ID,
                            MerchandisingAccount = AccountHelper.Accounts.MerchandisingAccount.ID,
                            PurchasesAccount = AccountHelper.Accounts.PurchasesAccount.ID,
                            PurchasesReturnAccount = AccountHelper.Accounts.PurchasesReturnAccount.ID,
                            SalesAccount = AccountHelper.Accounts.SalesAccount.ID,
                            SalesReturnAccount = AccountHelper.Accounts.SalesReturnAccount.ID,
                            OpenInventoryAccount = AccountHelper.Accounts.OpenInventoryAccount.ID,
                            CloseInventoryAccount = AccountHelper.Accounts.CloseInventoryAccount.ID,
                            PurchaseDiscountAccount = AccountHelper.Accounts.PurchaseDiscountAccount.ID,
                            SalesDiscountAccount = AccountHelper.Accounts.SalesDiscountAccount.ID,
                            FixedAssetsAccount = AccountHelper.Accounts.FixedAssetsAccount.ID,
                            SalesDeductTaxAccount = AccountHelper.Accounts.SalesDeductTaxAccount.ID,
                            PurchaseDeductTaxAccount = AccountHelper.Accounts.PurchaseDeductTaxAccount.ID,
                            CostOfSoldGoodsAccount = AccountHelper.Accounts.CostOfSoldGoodsAccount.ID,
                            DepreciationAccount = AccountHelper.Accounts.DepreciationAccount.ID,
                            ExpensesAccount = AccountHelper.Accounts.ExpensesAccount.ID,
                            RevenueAccount = AccountHelper.Accounts.RevenueAccount.ID,
                            PurchaseAddTaxAccount = AccountHelper.Accounts.PurchaseAddTaxAccount.ID,
                            SalesAddTaxAccount = AccountHelper.Accounts.SalesAddTaxAccount.ID,
                            InvoicesCodeRedundancy = RedundancyOptions.Deny,
                            InvoicesCodeRedundancyOnSave = true
                        });
                        db.SaveChanges();
                    }
                    systemSettings = db.settings.FirstOrDefault();
                }
                return systemSettings;
            }
            set
            {
                using ERPDataContext db = new ERPDataContext();
                db.settings.AddOrUpdate(value);
                db.SaveChanges();
            }
        }

        [EditorBrowsable(EditorBrowsableState.Never)]
        public virtual DbSet<ProductCustomField> productCustomField { get; set; }

        public static ProductCustomField ProductCustomFields
        {

            get
            {
                using ERPDataContext db = new ERPDataContext();
                if (db.productCustomField.Count() == 0)
                {
                    db.productCustomField.Add(new ProductCustomField
                    {
                        CustomField1 = "Champ personnalisé 1",
                        CustomField2 = "Champ personnalisé 2",
                        CustomField3 = "Champ personnalisé 3",
                        CustomField4 = "Champ personnalisé 4"
                    });
                    db.SaveChanges();
                }
                return db.productCustomField.FirstOrDefault();
            }
            set
            {
                using ERPDataContext db = new ERPDataContext();
                db.productCustomField.AddOrUpdate(value);
                db.SaveChanges();
            }
        }

        public ERPDataContext()
            : base(ApplicationSettings.Default.ConnectionString)
        {
        }

        static ERPDataContext()
        {
            Database.SetInitializer(new DataDbInitializer());
            Configuration configuration = new Configuration();
            DbMigrator migrator = new DbMigrator(configuration);
            if (migrator.GetPendingMigrations().Any())
            {
                migrator.Update();
            }
        }

        private static void InitializeDataStore()
        {
            Database.SetInitializer(new MigrateDatabaseToLatestVersion<ERPDataContext, Configuration>());
        }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Personal>().Map(delegate (EntityMappingConfiguration<Customer> m)
            {
                m.Requires("PersonalType").HasValue((byte)0);
            }).Map(delegate (EntityMappingConfiguration<Vendor> m)
            {
                m.Requires("PersonalType").HasValue((byte)1);
            });
            modelBuilder.Entity<PersonalGroup>().Map(delegate (EntityMappingConfiguration<CustomerGroup> m)
            {
                m.Requires("PersonalType").HasValue((byte)0);
            }).Map(delegate (EntityMappingConfiguration<VendorGroup> m)
            {
                m.Requires("PersonalType").HasValue((byte)1);
            });
            modelBuilder.Conventions.Remove<ManyToManyCascadeDeleteConvention>();
            modelBuilder.Conventions.Remove<OneToManyCascadeDeleteConvention>();
            base.OnModelCreating(modelBuilder);
        }

        public override int SaveChanges()
        {
            IEnumerable<DbEntityEntry> items = base.ChangeTracker.Entries();
            DisplayStates(items);
            return base.SaveChanges();
        }

        private static void DisplayStates(IEnumerable<DbEntityEntry> entries)
        {
            if (LastChangeTrackerItems == null)
            {
                LastChangeTrackerItems = new List<ChangeTrackerItem>();
            }
            if (SavingTrackedItems)
            {
                return;
            }
            foreach (DbEntityEntry entry in entries.Where((DbEntityEntry e) => e.State == EntityState.Modified || e.State == EntityState.Added || e.State == EntityState.Deleted))
            {
                IEnumerable<DbEntityEntry> modifiedEntries = entries.Where((DbEntityEntry e) => e.State == EntityState.Modified || e.State == EntityState.Added || e.State == EntityState.Deleted);
                TypeInfo entityAttribut = entry.Entity.GetType().GetTypeInfo();
                string entitName = entry.Entity.GetType().Name;
                string entityDisplayName = entityAttribut.GetCustomAttributes(typeof(DisplayNameAttribute), inherit: true).Cast<DisplayNameAttribute>().SingleOrDefault()?.DisplayName;
                string changedProperties = "";
                string properties = "";
                IEnumerable<string> propertiesNames = ((entry.State != EntityState.Deleted) ? entry.CurrentValues.PropertyNames : entry.OriginalValues.PropertyNames);
                foreach (string propName in propertiesNames)
                {
                    object current = null;
                    if (entry.State != EntityState.Deleted)
                    {
                        current = entry.CurrentValues?[propName];
                    }
                    object original = null;
                    if (entry.State != EntityState.Added)
                    {
                        original = entry.OriginalValues?[propName];
                    }
                    MemberInfo property = entry.Entity.GetType().GetProperty(propName);
                    string displayName = property.GetCustomAttributes(typeof(DisplayAttribute), inherit: true).Cast<DisplayAttribute>().SingleOrDefault()?.Name;
                    int valueLenght = (current?.ToString()?.Length).GetValueOrDefault() + (original?.ToString()?.Length).GetValueOrDefault();
                    if (valueLenght >= 150)
                    {
                        valueLenght = 149;
                    }
                    if (displayName != null)
                    {
                        if (current == null)
                        {
                            current = original;
                        }
                        if (current != null && current.ToString().Length > 0 && current.ToString() != "0")
                        {
                            properties = properties + displayName + " : " + current?.ToString() + Environment.NewLine;
                        }
                        if (current != null && original != null && !current.Equals(original))
                        {
                            changedProperties = changedProperties + displayName + " : " + original?.ToString() + " => " + current?.ToString() + Environment.NewLine;
                        }
                    }
                }
                ChangeTrackerItem item = new ChangeTrackerItem
                {
                    EntityName = entitName,
                    EntityDisplayName = entityDisplayName,
                    State = entry.State,
                    ChangedProperties = changedProperties,
                    Properties = properties
                };
                LastChangeTrackerItems.Add(item);
            }
        }

        public static void SaveTrackedItems(int entityID, string entityCode, string screenName, string screen, WindowActions actions, int userId)
        {
            SavingTrackedItems = true;
            UserLog log = new UserLog
            {
                ScreenName = screenName,
                Screen = screen,
                Action = actions,
                EntityCode = entityCode,
                EntityID = entityID,
                UserID = userId
            };
            if (LastChangeTrackerItems != null)
            {
                log.ChangeTrackerItems = new List<ChangeTrackerItem>((from c in LastChangeTrackerItems
                                                                      orderby c.EntityName, c.State descending
                                                                      select c).ToList());
            }
            using (ERPDataContext db = new ERPDataContext())
            {
                log.Date = db.GetServerTime();
                db.UserLogs.Add(log);
                db.SaveChanges();
            }
            SavingTrackedItems = false;
        }

        public static void ClearTrackedItems()
        {
            LastChangeTrackerItems = new List<ChangeTrackerItem>();
        }
    }
}
