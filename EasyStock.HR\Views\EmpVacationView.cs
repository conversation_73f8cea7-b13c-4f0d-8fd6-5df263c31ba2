﻿using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using EasyStock.Common;
using EasyStock.HR.BLL;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class EmpVacationView : MasterView
    {
        public static EmpVacationView Instance
        {
            get
            {
                bool flag = EmpVacationView.instance == null || EmpVacationView.instance.IsDisposed;
                if (flag)
                {
                    EmpVacationView.instance = new EmpVacationView();
                }
                return EmpVacationView.instance;
            }
        }

        public EmpVacation vacation
        {
            get
            {
                return this.empVacationBindingSource.Current as EmpVacation;
            }
            set
            {
                this.empVacationBindingSource.DataSource = value;
            }
        }

        public EmpVacationView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            this.context = new HRDataContext();
            base.Shown += this.EmpVacationView_Shown;
            this.FromDateDateEdit.EditValueChanged += this.FromDateDateEdit_EditValueChanged;
            this.ToDateDateEdit.EditValueChanged += this.ToDateDateEdit_EditValueChanged;
        }

        private void ToDateDateEdit_EditValueChanged(object sender, EventArgs e)
        {
            this.getDuration();
        }

        private void FromDateDateEdit_EditValueChanged(object sender, EventArgs e)
        {
            this.getDuration();
        }

        private void getDuration()
        {
            int days = (this.vacation.ToDate - this.vacation.FromDate).Days + 1;
            this.vacation.Duration = days;
        }

        private void EmpVacationView_Shown(object sender, EventArgs e)
        {
            bool flag = this.vacation == null;
            if (flag)
            {
                this.New();
            }
        }

        public override void New()
        {
            this.vacation = new EmpVacation
            {
                FromDate = this.context.GetServerTime(),
                ToDate = this.context.GetServerTime()
            };
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.getDuration();
                this.context.EmpVacations.AddOrUpdate(new EmpVacation[]
                {
                    this.vacation
                });
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public void GoTo(int id)
        {
            EmpVacation sourceDepr = this.context.EmpVacations.SingleOrDefault((EmpVacation x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.vacation = sourceDepr;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable");
            }
        }

        public override void RefreshData()
        {
            this.EmpIDTextEdit.Properties.DataSource = EmployeeBLL.GetActive();
            this.EmpIDTextEdit.Properties.DisplayMember = "Name";
            this.EmpIDTextEdit.Properties.ValueMember = "ID";
            this.EmpIDTextEdit.Properties.NullText = "";
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("ID", "Code"));
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Nom"));
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("PayPeriod", "Période de paie"));
            base.RefreshData();
        }

        public override void Delete()
        {
        }

        private static EmpVacationView instance;

        private HRDataContext context;
    }
}
