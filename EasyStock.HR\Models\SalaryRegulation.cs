﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    [DisplayColumn("Règlement de salaire")]
    public class SalaryRegulation : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Nom")]
        [StringLength(150)]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public string Name
        {
            get
            {
                return this.name;
            }
            set
            {
                base.SetProperty<string>(ref this.name, value, "Name");
            }
        }

        [Display(Name = "Centre de coût")]
        public int? CostCenter
        {
            get
            {
                return this.costCenter;
            }
            set
            {
                base.SetProperty<int?>(ref this.costCenter, value, "CostCenter");
            }
        }

        [Display(Name = "Compte des dépenses")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int ExpensesAccount
        {
            get
            {
                return this.expensesAccount;
            }
            set
            {
                base.SetProperty<int>(ref this.expensesAccount, value, "ExpensesAccount");
            }
        }

        [Display(Name = "Compte des prestations")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int BenefitsAccount
        {
            get
            {
                return this.benefitsAccount;
            }
            set
            {
                base.SetProperty<int>(ref this.benefitsAccount, value, "BenefitsAccount");
            }
        }

        [Display(Name = "Valeur par jour")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double DayValue
        {
            get
            {
                return this.dayValue;
            }
            set
            {
                base.SetProperty<double>(ref this.dayValue, value, "DayValue");
            }
        }

        [Display(Name = "Valeur par heure")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double HourValue
        {
            get
            {
                return this.hourValue;
            }
            set
            {
                base.SetProperty<double>(ref this.hourValue, value, "HourValue");
            }
        }

        [Display(Name = "Période de salaire")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public ESalaryPeriod SalaryPeriod { get; set; }

        [Display(Name = "Méthode de calcul du salaire de base")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public ESalaryCalculation SalaryCalculation { get; set; }

        [Display(Name = "Salaire par défaut")]
        public double DefaultSalary
        {
            get
            {
                return this.defaultSalary;
            }
            set
            {
                base.SetProperty<double>(ref this.defaultSalary, value, "DefaultSalary");
            }
        }

        [Display(Name = "Équation")]
        public string Equation
        {
            get
            {
                return this.equation;
            }
            set
            {
                base.SetProperty<string>(ref this.equation, value, "Equation");
            }
        }

        [Display(Name = "Tableau des prestations")]
        private BindingList<SalaryRegulationExtension> BenefitList { get; set; }

        [Display(Name = "Tableau des déductions")]
        private BindingList<SalaryRegulationExtension> DeductionList { get; set; }

        private int id;

        private string name;

        private int? costCenter;

        private int expensesAccount;

        private int benefitsAccount;

        private double dayValue;

        private double hourValue;

        private double defaultSalary;

        private string equation;
    }
}
