﻿using DevExpress.LookAndFeel;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.Models;
using EasyStock.Properties;
using EasyStock.Views;
using EasyStock.Views.Financial;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyStock.MainViews
{
    public partial class HomeForm : TabForm
    {
        public static HomeForm Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new HomeForm();
                }
                return instance;
            }
        }


        public HomeForm()
        {
            InitializeComponent();
            tabFormControl1.PageClosing += TabFormControl1_PageClosing;
            CurrentSession.LoadAll();
            AddMasterNavigationObjects();
            SetNameAndLogo();
            base.FormClosing += HomeForm_FormClosing;
            SetFont();
            base.Shown += HomeForm_Shown;
        }

        private void HomeForm_Shown(object sender, EventArgs e)
        {
            if (CurrentSession.CurrentUser.SettingsProfile.DefaultScreen > 0)
            {
                NavigationObject navigationObject = NavigationObjects.AllAllowedObjects.SingleOrDefault(x => x.ID == CurrentSession.CurrentUser.SettingsProfile.DefaultScreen);
                if (navigationObject != null && navigationObject.HasFormContractor)
                {
                    OpenForm(navigationObject.Form);
                }
            }
            else
            {
                OpenForm(HomeScreen.Instance);
            }
        }

        private void HomeForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (XtraMessageBox.Show(this, "Voulez-vous quitter le programme ?", "Confirmation de fermeture", MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation) == DialogResult.No)
            {
                e.Cancel = true;
                return;
            }

            using (ERPDataContext eRPDataContext = new ERPDataContext())
            {
                DrawerPeriod drawerPeriod = eRPDataContext.DrawerPeriods
                    .FirstOrDefault(x => x.PeriodEnd == null && x.PeriodUserID == CurrentSession.CurrentUser.ID);

                if (drawerPeriod != null && XtraMessageBox.Show(this, "Voulez-vous fermer le journal actuel ?", "Confirmation de fermeture", MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation) == DialogResult.Yes)
                {
                    new CloseDrawerPeriodForm(drawerPeriod.ID).ShowDialog();
                }
            }

            foreach (TabFormPage item in tabFormControl1.Pages.ToList())
            {
                if (item?.ContentContainer is Form form)
                {
                    form.Close();
                    if (form != null && !form.IsDisposed)
                    {
                        e.Cancel = true;
                        return;
                    }
                }
            }

            LookAndFeelSettings.Save();
            Application.ExitThread();
        }

        private void TabFormControl1_PageClosing(object sender, PageClosingEventArgs e)
        {
            if (e.Page.ContentContainer is Form form && form != null && !form.IsDisposed && !form.Disposing)
            {
                form.Close();
                e.Cancel = form != null && !form.IsDisposed;

                this.Invalidate();
                //this.Update();
                //this.Refresh();
                //this.Focus();

            }
        }

        public void SetNameAndLogo()
        {
            Text = ERPDataContext.CompanyInfo.Name;
            if (ERPDataContext.CompanyInfo.Logo != null)
            {
                base.IconOptions.Image = ERPDataContext.CompanyInfo.Logo;
            }
        }

        public void AddMasterNavigationObjects()
        {
            foreach (NavigationObject item in NavigationObjects.AllAllowedObjects.Where((NavigationObject x) => !x.ParentID.HasValue))
            {
                BarSubItem barSubItem = new BarSubItem();
                barSubItem.Caption = item.DisplayCaption;
                AddSlaveNavigationObjects(item.ID, barSubItem);
                tabFormControl1.Items.Add(barSubItem);
                tabFormControl1.TitleItemLinks.Add(barSubItem);
            }
        }

        public void AddSlaveNavigationObjects(int parentID, BarSubItem MainItem)
        {
            List<NavigationObject> allAllowedObjects = NavigationObjects.AllAllowedObjects;
            Func<NavigationObject, bool> predicate = delegate (NavigationObject x)
            {
                int? parentID2 = x.ParentID;
                return parentID2.HasValue && parentID2.Value == parentID;
            };
            foreach (NavigationObject item in allAllowedObjects.Where(predicate))
            {
                if (item.HasFormContractor)
                {
                    BarButtonItem barButtonItem = new BarButtonItem();
                    barButtonItem.Caption = item.DisplayCaption;
                    barButtonItem.Id = item.ID;
                    barButtonItem.Tag = item;
                    barButtonItem.ItemClick += BarItem_ItemClick;
                    tabFormControl1.Items.Add(barButtonItem);
                    MainItem.LinksPersistInfo.Add(new LinkPersistInfo(barButtonItem, item.BegainGroup));
                }
                else
                {
                    BarSubItem barSubItem = new BarSubItem();
                    barSubItem.Caption = item.DisplayCaption;
                    AddSlaveNavigationObjects(item.ID, barSubItem);
                    tabFormControl1.Items.Add(barSubItem);
                    MainItem.LinksPersistInfo.Add(new LinkPersistInfo(barSubItem, item.BegainGroup));
                }
            }
        }

        public static void BarItem_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (e.Item.Tag is NavigationObject navigationObject && navigationObject.Form != null)
            {
                LogScreenOpening(navigationObject.ID, CurrentSession.CurrentUser.ID);
                OpenForm(navigationObject.Form);
            }
        }

        public static void LogScreenOpening(int screenID, int userID)
        {
            Task.Factory.StartNew(delegate
            {
                using ERPDataContext eRPDataContext = new ERPDataContext();
                DateTime serverTime = eRPDataContext.GetServerTime();
                int dayOfWeek = (int)serverTime.DayOfWeek;
                int hour = serverTime.Hour;
                FrequentlyUsedScreen frequentlyUsedScreen = eRPDataContext.FrequentlyUsedScreens.FirstOrDefault((FrequentlyUsedScreen x) => x.ScreenID == screenID && x.UserID == userID && x.DayOfWeek == dayOfWeek && x.Hour == hour);
                if (frequentlyUsedScreen == null)
                {
                    frequentlyUsedScreen = new FrequentlyUsedScreen
                    {
                        ScreenID = screenID,
                        UserID = userID,
                        DayOfWeek = dayOfWeek,
                        Hour = hour
                    };
                    eRPDataContext.FrequentlyUsedScreens.Add(frequentlyUsedScreen);
                }
                frequentlyUsedScreen.Count++;
                eRPDataContext.SaveChanges();
            });
        }

        public static void OpenForm(Form form, bool inDialog = false, bool withOverride = false, bool forceOpen = false)
        {
            if (!(form is ReportForm) && !forceOpen)
            {
                Form form2 = Application.OpenForms[form.Name];
                if (form2 != null)
                {
                    form = form2;
                    instance.tabFormControl1.SelectedPage = instance.tabFormControl1.Pages.Single((TabFormPage x) => x.ContentContainer == form);
                    form.BringToFront();
                    return;
                }
            }
            if (CurrentSession.CurrentUser.Type == UserType.User && !CurrentSession.CurrentUser.AccessProfile.CanOpenWindow(form) && !withOverride)
            {
                XtraMessageBox.Show("Vous n'avez pas les autorisations nécessaires");
                return;
            }
            if (form is SystemSettingsForm || inDialog)
            {
                form.ShowDialog();
                return;
            }
            form.TopLevel = false;
            form.Dock = DockStyle.Fill;
            form.FormBorderStyle = FormBorderStyle.None;
            TabFormPage page = new TabFormPage();
            page.Text = form.Text;
            page.ContentContainer = form;
            form.TextChanged += delegate (object sender, EventArgs e)
            {
                page.Text = (sender as Form).Text;
            };
            form.Show();
            instance.tabFormControl1.Pages.Add(page);
            instance.tabFormControl1.SelectedPage = page;
            form.FormClosed += delegate
            {
                instance.tabFormControl1.Pages.Remove(page);
            };
        }

        private void barCheckItem1_CheckedChanged(object sender, ItemClickEventArgs e)
        {
            WindowsFormsSettings.TouchUIMode = (و.Checked ? TouchUIMode.True : TouchUIMode.False);
        }

        private void barButtonItem3_ItemClick(object sender, ItemClickEventArgs e)
        {
            using FontDialog fontDialog = new FontDialog();
            fontDialog.Font = WindowsFormsSettings.DefaultFont;
            if (fontDialog.ShowDialog() == DialogResult.OK)
            {
                Settings.Default.SystemFont = fontDialog.Font;
                Settings.Default.Save();
            }
            SetFont();
        }

        public void SetFont()
        {
            WindowsFormsSettings.DefaultFont = Settings.Default.SystemFont;
            AppearanceObject.DefaultFont = Settings.Default.SystemFont;
            AppearanceObject.DefaultMenuFont = Settings.Default.SystemFont;
            WindowsFormsSettings.DefaultFont = Settings.Default.SystemFont;
            AppearanceObject.DefaultFont = Settings.Default.SystemFont;
            foreach (object item in tabFormControl1.Items)
            {
                if (item is BarItem)
                {
                    (item as BarItem).ItemAppearance.SetFont(Settings.Default.SystemFont);
                }
            }
        }

        private void barCheckItem2_CheckedChanged(object sender, ItemClickEventArgs e)
        {
            WindowsFormsSettings.CompactUIMode = ((!barCheckItem2.Checked) ? DefaultBoolean.False : DefaultBoolean.True);
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            CurrentTimeLable.Caption = DateTime.Now.ToString("hh:mm:ss tt");
        }

        private void CurrentBranchLable_ItemClick(object sender, ItemClickEventArgs e)
        {
            using ERPDataContext eRPDataContext = new ERPDataContext();
            XtraInputBoxArgs xtraInputBoxArgs = new XtraInputBoxArgs();
            xtraInputBoxArgs.Caption = "Changer la filiale actuelle";
            xtraInputBoxArgs.Prompt = "Choisir la filiale";
            xtraInputBoxArgs.DefaultButtonIndex = 0;
            LookUpEdit lookUpEdit = new LookUpEdit();
            if (CurrentSession.CurrentUser.SettingsProfile.CanChangeBranch)
            {
                lookUpEdit.BindToDataSource(eRPDataContext.Branches.ToList());
            }
            else
            {
                lookUpEdit.BindToDataSource(eRPDataContext.Branches.Where((Branch x) => x.ID == CurrentSession.CurrentUser.SettingsProfile.DefaultBranch).ToList());
            }
            if (CurrentSession.CurrentUser.SettingsProfile.DefaultBranch > 0)
            {
                lookUpEdit.EditValue = CurrentSession.CurrentUser.SettingsProfile.DefaultBranch;
            }
            else
            {
                lookUpEdit.EditValue = 1;
            }
            xtraInputBoxArgs.Editor = lookUpEdit;
            xtraInputBoxArgs.DefaultResponse = lookUpEdit.EditValue;
            int? result = XtraInputBox.Show(xtraInputBoxArgs) as int?;
            Branch branch = null;
            if (result.HasValue && result > 0)
            {
                branch = (CurrentSession.CurrentBranch = eRPDataContext.Branches.SingleOrDefault((Branch x) => (int?)x.ID == result));
            }
            if (branch == null)
            {
                XtraMessageBox.Show("Échec de la connexion à la filiale", "", MessageBoxButtons.OK, MessageBoxIcon.Hand);
            }
        }

        private void barButtonItem4_ItemClick(object sender, ItemClickEventArgs e)
        {
            OpenForm(HomeScreen.Instance, inDialog: false, withOverride: true);
        }

        private void barButtonItem5_ItemClick(object sender, ItemClickEventArgs e)
        {
        }

        private static HomeForm instance;

        private void tabFormControl1_PageClosed(object sender, PageClosedEventArgs e)
        {
        }

        private void barButtonItem6_ItemClick(object sender, ItemClickEventArgs e)
        {
            var path = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "mc", "lmc.exe");
            Process.Start(new ProcessStartInfo(path));
        }

        private void barButtonItem5_ItemClick_1(object sender, ItemClickEventArgs e)
        {
            try
            {
                Process p = null;
                if (p == null)
                {
                    p = new Process();
                    p.StartInfo.FileName = "Calc.exe";
                    p.Start();
                }
                else
                {
                    p.Close();
                    p.Dispose();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Excepton" + ex.Message);
            }
        }
    }

}
