﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using EasyStock.Common;
using EasyStock.HR.BLL;
using EasyStock.HR.Class;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;

namespace EasyStock.HR.Views
{
	// Token: 0x0200003C RID: 60
	public partial class EmpShiftListView : MasterView
	{
		// Token: 0x170000B5 RID: 181
		// (get) Token: 0x060001C2 RID: 450 RVA: 0x000197E0 File Offset: 0x000179E0
		public static EmpShiftListView Instance
		{
			get
			{
				bool flag = EmpShiftListView.instance == null || EmpShiftListView.instance.IsDisposed;
				if (flag)
				{
					EmpShiftListView.instance = new EmpShiftListView();
				}
				return EmpShiftListView.instance;
			}
		}

		// Token: 0x060001C3 RID: 451 RVA: 0x0001981C File Offset: 0x00017A1C
		public EmpShiftListView()
		{
			this.InitializeComponent();
			this.context = new HRDataContext();
			this.InitializeComponent();
			this.gridView1.OptionsBehavior.Editable = false;
			this.gridView1.FocusRectStyle = DrawFocusRectStyle.RowFocus;
			this.gridView1.OptionsSelection.MultiSelect = true;
			this.gridView1.SetAlternatingColors();
			this.gridView1.DoubleClick += this.GridView1_DoubleClick;
			this.btn_Save.Visibility = BarItemVisibility.Never;
			this.btn_Print.Visibility = BarItemVisibility.Always;
			this.btn_Refresh.Visibility = BarItemVisibility.Always;
			this.btn_Print.Enabled = true;
		}

		// Token: 0x060001C4 RID: 452 RVA: 0x000198DC File Offset: 0x00017ADC
		private void GridView1_DoubleClick(object sender, EventArgs e)
		{
			DXMouseEventArgs ea = e as DXMouseEventArgs;
			GridView view = sender as GridView;
			GridHitInfo info = view.CalcHitInfo(ea.Location);
			bool flag = info.InRow || info.InRowCell;
			if (flag)
			{
				int id = Convert.ToInt32(view.GetFocusedRowCellValue("ID"));
				this.Edit(id);
			}
		}

		// Token: 0x060001C5 RID: 453 RVA: 0x00019937 File Offset: 0x00017B37
		public virtual void Edit(int id)
		{
			Static.OpenForm(EmpShiftView.Instance);
			EmpShiftView.Instance.GoTo(id);
		}

		// Token: 0x060001C6 RID: 454 RVA: 0x00019951 File Offset: 0x00017B51
		public override void New()
		{
			Static.OpenForm(EmpShiftView.Instance);
			EmpShiftView.Instance.btn_New.PerformClick();
		}

		// Token: 0x060001C7 RID: 455 RVA: 0x0001996F File Offset: 0x00017B6F
		public override void RefreshData()
		{
			this.gridControl1.DataSource = this.context.EmpShifts.ToList<EmpShift>();
			this.repoemp.DataSource = EmployeeBLL.GetAll();
			base.RefreshData();
		}

		// Token: 0x060001C8 RID: 456 RVA: 0x000199A6 File Offset: 0x00017BA6
		public override void Print()
		{
			base.Print();
		}

		// Token: 0x060001C9 RID: 457 RVA: 0x000199B0 File Offset: 0x00017BB0
		public override void Delete()
		{
			base.Delete();
		}

		// Token: 0x0400029F RID: 671
		private static EmpShiftListView instance;

		// Token: 0x040002A0 RID: 672
		private HRDataContext context;
	}
}
