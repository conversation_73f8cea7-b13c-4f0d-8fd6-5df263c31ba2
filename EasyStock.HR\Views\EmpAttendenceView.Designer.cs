﻿namespace EasyStock.HR.Views
{
	// Token: 0x02000031 RID: 49
	public partial class EmpAttendenceView : global::EasyStock.HR.MainViews.MasterView
	{
		// Token: 0x06000143 RID: 323 RVA: 0x0000B0A8 File Offset: 0x000092A8
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000144 RID: 324 RVA: 0x0000B0E0 File Offset: 0x000092E0
		private void InitializeComponent()
		{
			this.components = new global::System.ComponentModel.Container();
			global::System.ComponentModel.ComponentResourceManager resources = new global::System.ComponentModel.ComponentResourceManager(typeof(global::EasyStock.HR.Views.EmpAttendenceView));
			this.gridControl1 = new global::DevExpress.XtraGrid.GridControl();
			this.gridView1 = new global::DevExpress.XtraGrid.Views.Grid.GridView();
			this.empAttendanceBindingSource = new global::System.Windows.Forms.BindingSource(this.components);
			this.colID = new global::DevExpress.XtraGrid.Columns.GridColumn();
			this.colEmpID = new global::DevExpress.XtraGrid.Columns.GridColumn();
			this.colDay = new global::DevExpress.XtraGrid.Columns.GridColumn();
			this.colShift1Attend = new global::DevExpress.XtraGrid.Columns.GridColumn();
			this.colShift1AttendRule = new global::DevExpress.XtraGrid.Columns.GridColumn();
			this.colShift1Leave = new global::DevExpress.XtraGrid.Columns.GridColumn();
			this.colShift1LeaveRule = new global::DevExpress.XtraGrid.Columns.GridColumn();
			this.colShift2Attend = new global::DevExpress.XtraGrid.Columns.GridColumn();
			this.colShift2AttendRule = new global::DevExpress.XtraGrid.Columns.GridColumn();
			this.colShift2Leave = new global::DevExpress.XtraGrid.Columns.GridColumn();
			this.colShift2LeaveRule = new global::DevExpress.XtraGrid.Columns.GridColumn();
			this.colNotes = new global::DevExpress.XtraGrid.Columns.GridColumn();
			((global::System.ComponentModel.ISupportInitialize)this.gridControl1).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.gridView1).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.empAttendanceBindingSource).BeginInit();
			base.SuspendLayout();
			this.gridControl1.DataSource = this.empAttendanceBindingSource;
			this.gridControl1.Dock = global::System.Windows.Forms.DockStyle.Fill;
			this.gridControl1.Location = new global::System.Drawing.Point(0, 24);
			this.gridControl1.MainView = this.gridView1;
			this.gridControl1.Name = "gridControl1";
			this.gridControl1.Size = new global::System.Drawing.Size(800, 404);
			this.gridControl1.TabIndex = 4;
			this.gridControl1.ViewCollection.AddRange(new global::DevExpress.XtraGrid.Views.Base.BaseView[]
			{
				this.gridView1
			});
			this.gridView1.Columns.AddRange(new global::DevExpress.XtraGrid.Columns.GridColumn[]
			{
				this.colID,
				this.colEmpID,
				this.colDay,
				this.colShift1Attend,
				this.colShift1AttendRule,
				this.colShift1Leave,
				this.colShift1LeaveRule,
				this.colShift2Attend,
				this.colShift2AttendRule,
				this.colShift2Leave,
				this.colShift2LeaveRule,
				this.colNotes
			});
			this.gridView1.GridControl = this.gridControl1;
			this.gridView1.Name = "gridView1";
			this.empAttendanceBindingSource.DataSource = typeof(global::EasyStock.HR.Models.EmpAttendance);
			this.colID.FieldName = "ID";
			this.colID.Name = "colID";
			this.colID.OptionsColumn.ReadOnly = true;
			this.colID.Visible = true;
			this.colID.VisibleIndex = 0;
			this.colEmpID.FieldName = "EmpID";
			this.colEmpID.Name = "colEmpID";
			this.colEmpID.Visible = true;
			this.colEmpID.VisibleIndex = 1;
			this.colDay.FieldName = "Day";
			this.colDay.Name = "colDay";
			this.colDay.Visible = true;
			this.colDay.VisibleIndex = 2;
			this.colShift1Attend.FieldName = "Shift1Attend";
			this.colShift1Attend.Name = "colShift1Attend";
			this.colShift1Attend.Visible = true;
			this.colShift1Attend.VisibleIndex = 3;
			this.colShift1AttendRule.FieldName = "Shift1AttendRule";
			this.colShift1AttendRule.Name = "colShift1AttendRule";
			this.colShift1AttendRule.Visible = true;
			this.colShift1AttendRule.VisibleIndex = 4;
			this.colShift1Leave.FieldName = "Shift1Leave";
			this.colShift1Leave.Name = "colShift1Leave";
			this.colShift1Leave.Visible = true;
			this.colShift1Leave.VisibleIndex = 5;
			this.colShift1LeaveRule.FieldName = "Shift1LeaveRule";
			this.colShift1LeaveRule.Name = "colShift1LeaveRule";
			this.colShift1LeaveRule.Visible = true;
			this.colShift1LeaveRule.VisibleIndex = 6;
			this.colShift2Attend.FieldName = "Shift2Attend";
			this.colShift2Attend.Name = "colShift2Attend";
			this.colShift2Attend.Visible = true;
			this.colShift2Attend.VisibleIndex = 7;
			this.colShift2AttendRule.FieldName = "Shift2AttendRule";
			this.colShift2AttendRule.Name = "colShift2AttendRule";
			this.colShift2AttendRule.Visible = true;
			this.colShift2AttendRule.VisibleIndex = 8;
			this.colShift2Leave.FieldName = "Shift2Leave";
			this.colShift2Leave.Name = "colShift2Leave";
			this.colShift2Leave.Visible = true;
			this.colShift2Leave.VisibleIndex = 9;
			this.colShift2LeaveRule.FieldName = "Shift2LeaveRule";
			this.colShift2LeaveRule.Name = "colShift2LeaveRule";
			this.colShift2LeaveRule.Visible = true;
			this.colShift2LeaveRule.VisibleIndex = 10;
			this.colNotes.FieldName = "Notes";
			this.colNotes.Name = "colNotes";
			this.colNotes.Visible = true;
			this.colNotes.VisibleIndex = 11;
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(6f, 13f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new global::System.Drawing.Size(800, 450);
			base.Controls.Add(this.gridControl1);
			base.Name = "EmpAttendenceView";
			this.Text = "EmpAttendenceView";
			base.Controls.SetChildIndex(this.gridControl1, 0);
			((global::System.ComponentModel.ISupportInitialize)this.gridControl1).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.gridView1).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.empAttendanceBindingSource).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000173 RID: 371
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04000174 RID: 372
		private global::DevExpress.XtraGrid.GridControl gridControl1;

		// Token: 0x04000175 RID: 373
		private global::DevExpress.XtraGrid.Views.Grid.GridView gridView1;

		// Token: 0x04000176 RID: 374
		private global::System.Windows.Forms.BindingSource empAttendanceBindingSource;

		// Token: 0x04000177 RID: 375
		private global::DevExpress.XtraGrid.Columns.GridColumn colID;

		// Token: 0x04000178 RID: 376
		private global::DevExpress.XtraGrid.Columns.GridColumn colEmpID;

		// Token: 0x04000179 RID: 377
		private global::DevExpress.XtraGrid.Columns.GridColumn colDay;

		// Token: 0x0400017A RID: 378
		private global::DevExpress.XtraGrid.Columns.GridColumn colShift1Attend;

		// Token: 0x0400017B RID: 379
		private global::DevExpress.XtraGrid.Columns.GridColumn colShift1AttendRule;

		// Token: 0x0400017C RID: 380
		private global::DevExpress.XtraGrid.Columns.GridColumn colShift1Leave;

		// Token: 0x0400017D RID: 381
		private global::DevExpress.XtraGrid.Columns.GridColumn colShift1LeaveRule;

		// Token: 0x0400017E RID: 382
		private global::DevExpress.XtraGrid.Columns.GridColumn colShift2Attend;

		// Token: 0x0400017F RID: 383
		private global::DevExpress.XtraGrid.Columns.GridColumn colShift2AttendRule;

		// Token: 0x04000180 RID: 384
		private global::DevExpress.XtraGrid.Columns.GridColumn colShift2Leave;

		// Token: 0x04000181 RID: 385
		private global::DevExpress.XtraGrid.Columns.GridColumn colShift2LeaveRule;

		// Token: 0x04000182 RID: 386
		private global::DevExpress.XtraGrid.Columns.GridColumn colNotes;
	}
}
