namespace EasyStock.HR.Models
{
    using System;
    using System.Collections.Generic;

    public partial class EmpLoans
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public EmpLoans()
        {
            EmpLoanDetails = new HashSet<EmpLoanDetails>();
        }

        public int ID { get; set; }

        public DateTime Date { get; set; }

        public int AccountNo { get; set; }

        public int EmpID { get; set; }

        public double Balance { get; set; }

        public double TotalAmount { get; set; }

        public int InstalmentNo { get; set; }

        public string Notes { get; set; }

        public int BranchID { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<EmpLoanDetails> EmpLoanDetails { get; set; }
    }
}
