﻿using EasyStock.Classes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace EasyStock.Models
{
    public class SalesOrderDetail : BaseNotifyPropertyChangedModel, IProductRowDetail
    {
        private int id;

        private Product _product;

        private ProductUnit _unit;

        private double _quantity;

        private double _price;
#pragma warning disable CS0169
        private DateTime _date;
#pragma warning restore CS0169
        private double discountPercentage;

        [Display(Name = "N°")]
        public int ID
        {
            get
            {
                return id;
            }
            set
            {
                SetProperty(ref id, value, "ID");
            }
        }

        public SalesOrder SalesOrder { get; set; }

        public int SalesOrderID { get; set; }

        [NotMapped]
        [Display(Name = "Article")]
        public Product Product
        {
            get
            {
                if (_product == null || _product.ID != ProductID)
                {
                    _product = CurrentSession.Products.SingleOrDefault((Product x) => x.ID == ProductID);
                }
                return _product;
            }
        }

        [Display(Name = "Article")]
        public int ProductID { get; set; }

        [NotMapped]
        [Display(Name = "Unité")]
        public ProductUnit Unit
        {
            get
            {
                if (_unit == null || _unit.ID != UnitID)
                {
                    _unit = CurrentSession.ProductUnits.SingleOrDefault((ProductUnit x) => x.ID == UnitID);
                }
                return _unit;
            }
        }

        [Display(Name = "Unité")]
        [Range(1, int.MaxValue, ErrorMessage = "Une unité doit être sélectionnée")]
        public int UnitID { get; set; }

        public double Factor { get; set; }

        [Display(Name = "Couleur")]
        public int? ColorID { get; set; }

        [Display(Name = "Taille")]
        public int? SizeID { get; set; }

        [Display(Name = "Date d'expiration")]
        public DateTime? Expire { get; set; }

        [Display(Name = "Quantité")]
        [Range(0.0, double.MaxValue, ErrorMessage = "La quantité doit être supérieure à 0")]
        public double Quantity
        {
            get
            {
                return _quantity;
            }
            set
            {
                SetProperty(ref _quantity, value, "Quantity");
            }
        }

        public double RowQuantity => Quantity * Factor;

        [Display(Name = "Prix")]
        public double Price
        {
            get
            {
                return _price;
            }
            set
            {
                SetProperty(ref _price, value, "Price");
            }
        }

        [NotMapped]
        [Display(Name = "Total")]
        public double Total => Price * Quantity;

        [Display(Name = "Code Article")]
        public string ProductCode { get; set; }

        [Display(Name = "Remise")]
        public double Discount { get; set; }

        [Display(Name = "Pourcentage de remise")]
        [Range(0.0, 0.99, ErrorMessage = "Le pourcentage de remise doit être compris entre 0 et 99 %")]
        public double DiscountPercentage
        {
            get
            {
                return discountPercentage;
            }
            set
            {
                SetProperty(ref discountPercentage, value, "DiscountPercentage");
            }
        }

        [NotMapped]
        [Display(Name = "Net")]
        public double Net => Total + Tax - Discount;

        [Display(Name = "Taxe")]
        public double Tax { get; set; }

        [NotMapped]
        [Display(Name = "Pourcentage de taxe")]
        [Range(0.0, 0.99, ErrorMessage = "Le pourcentage de taxe doit être compris entre 0 et 99 %")]
        public double TaxPercentage { get; set; }

        public ProductTransaction ShallowCopy()
        {
            return (ProductTransaction)MemberwiseClone();
        }
    }
}
