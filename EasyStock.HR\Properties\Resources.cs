﻿using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;
using DevExpress.Utils.Svg;

namespace EasyStock.HR.Properties
{
	// Token: 0x02000051 RID: 81
	[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
	[DebuggerNonUserCode]
	[CompilerGenerated]
	internal class Resources
	{
		// Token: 0x0600031B RID: 795 RVA: 0x0003BAB1 File Offset: 0x00039CB1
		internal Resources()
		{
		}

		// Token: 0x170000DB RID: 219
		// (get) Token: 0x0600031C RID: 796 RVA: 0x0003BABC File Offset: 0x00039CBC
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static ResourceManager ResourceManager
		{
			get
			{
				bool flag = Resources.resourceMan == null;
				if (flag)
				{
					ResourceManager temp = new ResourceManager("EasyStock.HR.Properties.Resources", typeof(Resources).Assembly);
					Resources.resourceMan = temp;
				}
				return Resources.resourceMan;
			}
		}

		// Token: 0x170000DC RID: 220
		// (get) Token: 0x0600031D RID: 797 RVA: 0x0003BB04 File Offset: 0x00039D04
		// (set) Token: 0x0600031E RID: 798 RVA: 0x0003BB1B File Offset: 0x00039D1B
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static CultureInfo Culture
		{
			get
			{
				return Resources.resourceCulture;
			}
			set
			{
				Resources.resourceCulture = value;
			}
		}

		// Token: 0x170000DD RID: 221
		// (get) Token: 0x0600031F RID: 799 RVA: 0x0003BB24 File Offset: 0x00039D24
		internal static SvgImage actions_edit
		{
			get
			{
				object obj = Resources.ResourceManager.GetObject("actions_edit", Resources.resourceCulture);
				return (SvgImage)obj;
			}
		}

		// Token: 0x170000DE RID: 222
		// (get) Token: 0x06000320 RID: 800 RVA: 0x0003BB54 File Offset: 0x00039D54
		internal static Bitmap edit_32x32
		{
			get
			{
				object obj = Resources.ResourceManager.GetObject("edit_32x32", Resources.resourceCulture);
				return (Bitmap)obj;
			}
		}

		// Token: 0x040004FE RID: 1278
		private static ResourceManager resourceMan;

		// Token: 0x040004FF RID: 1279
		private static CultureInfo resourceCulture;
	}
}
