﻿using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Mask;
using EasyStock.HR.BLL;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;

namespace EasyStock.HR.Views.PenaltyRewardView
{
    public partial class RewardView : MasterView
    {
        public static RewardView Instance
        {
            get
            {
                bool flag = RewardView.instance == null || RewardView.instance.IsDisposed;
                if (flag)
                {
                    RewardView.instance = new RewardView();
                }
                return RewardView.instance;
            }
        }

        public PenaltyReward Reward
        {
            get
            {
                return this.RewardbindingSource.Current as PenaltyReward;
            }
            set
            {
                this.RewardbindingSource.DataSource = value;
            }
        }

        public RewardView()
        {
            this.InitializeComponent();
            base.Shown += this.RewardView_Shown;
            this.Text = "Enregistrer une récompense";
            this.BindingLookupControls();
            this.NoOfDaysSpinEdit.EditValueChanged += this.NoOfDaysSpinEdit_EditValueChanged;
            this.DateDateEdit.Properties.Mask.MaskType = MaskType.DateTime;
            this.DateDateEdit.Properties.Mask.EditMask = "dd/MM/yyyy";
            this.DateDateEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
        }

        private void NoOfDaysSpinEdit_EditValueChanged(object sender, EventArgs e)
        {
            SpinEdit control = sender as SpinEdit;
            bool flag = control != null;
            if (flag)
            {
                int NoOfDays = Convert.ToInt32(control.Value);
                this.AmountSpinEdit.Value = Convert.ToDecimal(EmployeeBLL.EmpCalcDay(this.Reward.EmployeeId, NoOfDays));
            }
        }

        private void RewardView_Shown(object sender, EventArgs e)
        {
            bool flag = this.Reward == null;
            if (flag)
            {
                this.New();
            }
        }

        public void GoTo(int id)
        {
            PenaltyReward _Reward = PenaltyRewardBLL.Get(id, PenaltyRewardType.Reward);
            bool flag = _Reward != null;
            if (flag)
            {
                this.Reward = _Reward;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("L'enregistrement est introuvable. ");
            }
        }

        public override void New()
        {
            this.Reward = new PenaltyReward
            {
                Type = PenaltyRewardType.Reward,
                calcType = CalculationType.Days,
                Date = DateTime.Now
            };
            base.New();
        }

        public override void Save()
        {
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                int res = PenaltyRewardBLL.AddOrUpdate(this.Reward);
                base.Save();
            }
        }

        public override void RefreshData()
        {
            this.New();
            base.RefreshData();
        }

        public override void Delete()
        {
            bool flag = this.Reward == null || this.Reward.ID == 0;
            if (!flag)
            {
                int res = PenaltyRewardBLL.Delete(this.Reward.ID);
                bool flag2 = res > 0;
                if (flag2)
                {
                    base.Delete();
                }
            }
        }

        public void BindingLookupControls()
        {
            this.EmployeeIdLookUpEdit.Properties.DataSource = EmployeeBLL.GetAll();
            this.EmployeeIdLookUpEdit.Properties.DisplayMember = "Name";
            this.EmployeeIdLookUpEdit.Properties.ValueMember = "ID";
            this.EmployeeIdLookUpEdit.Properties.NullText = "";
            this.EmployeeIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("ID", "Code"));
            this.EmployeeIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "L'employé"));
            this.EmployeeIdLookUpEdit.EditValueChanged += this.EmployeeIdLookUpEdit_EditValueChanged;
            this.calcTypeRadioGroup.Properties.AddEnum<CalculationType>();
            this.calcTypeRadioGroup.SelectedIndexChanged += this.CalcTypeRadioGroup_SelectedIndexChanged;
        }

        private void EmployeeIdLookUpEdit_EditValueChanged(object sender, EventArgs e)
        {
            LookUpEdit emplookupedit = sender as LookUpEdit;
            Employee emp = emplookupedit.GetSelectedDataRow() as Employee;
            bool flag = emp != null;
            if (flag)
            {
                this.lblForNoRewardsValue.Text = PenaltyRewardBLL.GetCountForEmp(emp.ID, PenaltyRewardType.Reward).ToString();
            }
        }

        private void CalcTypeRadioGroup_SelectedIndexChanged(object sender, EventArgs e)
        {
            RadioGroup edit = sender as RadioGroup;
            bool flag = edit.SelectedIndex == 0;
            if (flag)
            {
                this.NoOfDaysSpinEdit.Enabled = true;
                this.AmountSpinEdit.Enabled = false;
            }
            else
            {
                this.NoOfDaysSpinEdit.Enabled = false;
                this.AmountSpinEdit.Enabled = true;
            }
        }

        private static RewardView instance;
    }
}
