﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class AddVendorInvoice : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(AddVendorInvoice));

        string IMigrationMetadata.Id => "202108182132581_AddVendorInvoice";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.VendorInvoices", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Code = c.Int(false),
                VendorID = c.Int(false),
                Date = c.DateTime(false),
                WorkTypeID = c.Int(false),
                NetAmount = c.Double(false),
                PaidAmount = c.Double(false),
                DrawerID = c.Int(),
                JournalID = c.Int(false),
                Notes = c.String()
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Drawers", t => t.DrawerID).ForeignKey("dbo.Journals", t => t.JournalID)
                .ForeignKey("dbo.WorkTypes", t => t.WorkTypeID)
                .Index(t => t.WorkTypeID)
                .Index(t => t.DrawerID)
                .Index(t => t.JournalID);
        }

        public override void Down()
        {
            DropForeignKey("dbo.VendorInvoices", "WorkTypeID", "dbo.WorkTypes");
            DropForeignKey("dbo.VendorInvoices", "JournalID", "dbo.Journals");
            DropForeignKey("dbo.VendorInvoices", "DrawerID", "dbo.Drawers");
            DropIndex("dbo.VendorInvoices", new string[1] { "JournalID" });
            DropIndex("dbo.VendorInvoices", new string[1] { "DrawerID" });
            DropIndex("dbo.VendorInvoices", new string[1] { "WorkTypeID" });
            DropTable("dbo.VendorInvoices");
        }
    }
}
