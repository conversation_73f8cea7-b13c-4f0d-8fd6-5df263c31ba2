﻿using System;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Views;
using EasyStock.Views.Financial;

namespace EasyStock.Classes
{
	// Token: 0x02000570 RID: 1392
	public static class Utilities
	{
		// Token: 0x0600298A RID: 10634 RVA: 0x0021FC28 File Offset: 0x0021DE28
		public static void OpenProcess(SystemProcess process, int processID)
		{
			ERPDataContext db = new ERPDataContext();
			switch (process)
			{
			case SystemProcess.AccountOpenBalance:
				HomeForm.OpenForm(AccountsFrom.Instance, false, false, false);
				AccountsFrom.Instance.GoTo(processID);
				break;
			case SystemProcess.Purchase:
				HomeForm.OpenForm(PurchaseInvoiceForm.Instance, false, false, false);
				PurchaseInvoiceForm.Instance.GoTo(processID);
				break;
			case SystemProcess.PurchaseReturn:
				HomeForm.OpenForm(PurchaseReturnInvoiceForm.Instance, false, false, false);
				PurchaseReturnInvoiceForm.Instance.GoTo(processID);
				break;
			case SystemProcess.Sales:
				HomeForm.OpenForm(SalesInvoiceForm.Instance, false, false, false);
				SalesInvoiceForm.Instance.GoTo(processID);
				break;
			case SystemProcess.SalesReturn:
				HomeForm.OpenForm(SalesReturnInvoiceForm.Instance, false, false, false);
				SalesReturnInvoiceForm.Instance.GoTo(processID);
				break;
			case SystemProcess.CashNoteIn:
				HomeForm.OpenForm(CashNoteInView.Instance, false, false, false);
				CashNoteInView.Instance.GoTo(processID);
				break;
			case SystemProcess.CashNoteOut:
				HomeForm.OpenForm(CashNoteOutView.Instance, false, false, false);
				CashNoteOutView.Instance.GoTo(processID);
				break;
			case SystemProcess.Revenue:
				HomeForm.OpenForm(RevenueEntryView.Instance, false, false, false);
				RevenueEntryView.Instance.GoTo(processID);
				break;
			case SystemProcess.Expense:
				HomeForm.OpenForm(ExpenseEntryView.Instance, false, false, false);
				ExpenseEntryView.Instance.GoTo(processID);
				break;
			case SystemProcess.ProductDamage:
				HomeForm.OpenForm(ProductDamageBillForm.Instance, false, false, false);
				ProductDamageBillForm.Instance.GoTo(processID);
				break;
			case SystemProcess.StockTransfer:
				HomeForm.OpenForm(StockTransferBillForm.Instance, false, false, false);
				StockTransferBillForm.Instance.GoTo(processID);
				break;
			case SystemProcess.JournalEntery:
				HomeForm.OpenForm(JournalView.Instance, false, false, false);
				JournalView.Instance.GoTo(processID);
				break;
			case SystemProcess.DrawerPeriodClosing:
			case SystemProcess.DrawerSuttlement:
				HomeForm.OpenForm(DrawerPeriodForm.Instance, false, false, false);
				DrawerPeriodForm.Instance.GoTo(processID);
				break;
			case SystemProcess.CashTransfer:
				HomeForm.OpenForm(CashTransferView.Instance, false, false, false);
				CashTransferView.Instance.GoTo(processID);
				break;
			case SystemProcess.StockBalanceCorrection:
				HomeForm.OpenForm(StockBalanceCorrectionView.Instance, false, false, false);
				StockBalanceCorrectionView.Instance.GoTo(processID);
				break;
			}
		}
	}
}
