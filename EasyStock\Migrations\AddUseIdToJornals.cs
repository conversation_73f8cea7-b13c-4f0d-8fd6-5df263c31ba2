﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x0200048E RID: 1166
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class AddUseIdToJornals : DbMigration, IMigrationMetadata
	{
		// Token: 0x0600230C RID: 8972 RVA: 0x000122A8 File Offset: 0x000104A8
		public override void Up()
		{
			base.AddColumn("dbo.Journals", "UserID", (ColumnBuilder c) => c.Int(new bool?(false), false, new int?(1), null, null, null, null), null);
		}

		// Token: 0x0600230D RID: 8973 RVA: 0x000122DC File Offset: 0x000104DC
		public override void Down()
		{
			base.DropColumn("dbo.Journals", "UserID", null);
		}

		// Token: 0x17000B82 RID: 2946
		// (get) Token: 0x0600230E RID: 8974 RVA: 0x001F14A0 File Offset: 0x001EF6A0
		string IMigrationMetadata.Id
		{
			get
			{
				return "202106011822264_AddUseIdToJornals";
			}
		}

		// Token: 0x17000B83 RID: 2947
		// (get) Token: 0x0600230F RID: 8975 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000B84 RID: 2948
		// (get) Token: 0x06002310 RID: 8976 RVA: 0x001F14B8 File Offset: 0x001EF6B8
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B05 RID: 11013
		private readonly ResourceManager Resources = new ResourceManager(typeof(AddUseIdToJornals));
	}
}
