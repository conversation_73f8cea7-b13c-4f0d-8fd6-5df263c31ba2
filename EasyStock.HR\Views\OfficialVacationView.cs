﻿using DevExpress.XtraEditors;
using EasyStock.Common;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class OfficialVacationView : MasterView
    {
        public static OfficialVacationView Instance
        {
            get
            {
                bool flag = OfficialVacationView.instance == null || OfficialVacationView.instance.IsDisposed;
                if (flag)
                {
                    OfficialVacationView.instance = new OfficialVacationView();
                }
                return OfficialVacationView.instance;
            }
        }

        public OfficialVacation Vacation
        {
            get
            {
                return this.officialVacationBindingSource.Current as OfficialVacation;
            }
            set
            {
                this.officialVacationBindingSource.DataSource = value;
            }
        }

        public OfficialVacationView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            this.context = new HRDataContext();
            this.gridView1.AddEditButton(new EventHandler(this.RepositoryItemButtonEdit1_Click));
            this.gridView1.SetAlternatingColors();
            base.Shown += this.OfficialVacationView_Shown;
        }

        private void OfficialVacationView_Shown(object sender, EventArgs e)
        {
            bool flag = this.Vacation == null;
            if (flag)
            {
                this.New();
            }
        }

        private void RepositoryItemButtonEdit1_Click(object sender, EventArgs e)
        {
            OfficialVacation row = this.gridView1.GetRow(this.gridView1.FocusedRowHandle) as OfficialVacation;
            bool flag = row != null;
            if (flag)
            {
                this.GoTo(row.ID);
            }
        }

        public void GoTo(int id)
        {
            OfficialVacation sourceDepr = this.context.OfficialVacations.SingleOrDefault((OfficialVacation x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.Vacation = sourceDepr;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable");
            }
        }

        public override void New()
        {
            this.Vacation = new OfficialVacation();
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.context.OfficialVacations.AddOrUpdate(new OfficialVacation[]
                {
                    this.Vacation
                });
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.gridControl1.DataSource = this.context.OfficialVacations.ToList<OfficialVacation>();
            base.RefreshData();
        }

        public override void Delete()
        {
        }

        private static OfficialVacationView instance;

        private HRDataContext context;
    }
}
