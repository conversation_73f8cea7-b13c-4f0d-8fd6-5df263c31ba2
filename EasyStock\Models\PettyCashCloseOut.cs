﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [Table("PettyCashCloseOut")]
    [DisplayColumn("Clôture de la caisse de petite monnaie")]
    public class PettyCashCloseOut
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "N°")]
        [ReadOnly(true)]
        public int ID { get; set; }

        [Display(Name = "Caisse de petite monnaie")]
        [Range(1, **********, ErrorMessage = "*")]
        public int PettyCashID { get; set; }

        public PettyCash PettyCash { get; set; }

        [Display(Name = "Montant de la caisse")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double Amount { get; set; }

        [Display(Name = "Total des dépenses")]
        public double AmountSpent { get; set; }

        [Display(Name = "Montant restant")]
        public double RemainigAmount
        {
            get
            {
                return this.Amount - this.AmountSpent;
            }
        }

        [Display(Name = "Date de clôture")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public DateTime DateClosed { get; set; }

        [Display(Name = "Type de compte de clôture")]
        public CloseAccountType CloseAccountType { get; set; }

        [Display(Name = "Compte de clôture")]
        [Range(1, **********, ErrorMessage = "*")]
        public int CloseAccountID { get; set; }
    }
}
