﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    [DisplayColumn("Heures supplémentaires et retards")]
    public class EmpOvertimeDelay : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Employé")]
        [Range(1, 2147483647, ErrorMessage = "*")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int EmpID
        {
            get
            {
                return this.empid;
            }
            set
            {
                base.SetProperty<int>(ref this.empid, value, "EmpID");
            }
        }

        [Display(Name = "Réglement")]
        public int RegulationID { get; set; }

        [Display(Name = "Jour")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public DateTime DayDate
        {
            get
            {
                return this.daydate;
            }
            set
            {
                base.SetProperty<DateTime>(ref this.daydate, value, "DayDate");
            }
        }

        [Display(Name = "Durée")]
        public int Duration
        {
            get
            {
                return this.duration;
            }
            set
            {
                base.SetProperty<int>(ref this.duration, value, "Duration");
            }
        }

        [Display(Name = "Rémunéré")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public EAbsencePaid AbsencePaid { get; set; }

        [Display(Name = "Notes")]
        public string Notes
        {
            get
            {
                return this.notes;
            }
            set
            {
                base.SetProperty<string>(ref this.notes, value, "Notes");
            }
        }

        [Display(Name = "Type")]
        public EOvertimeDelay Type { get; set; }

        private int id;

        private int empid;

        private DateTime daydate;

        private int duration;

        private string notes;
    }
}
