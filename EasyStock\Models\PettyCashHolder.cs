﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [Table("PettyCashHolders")]
    [DisplayColumn("Titulaire de la caisse de petite monnaie")]
    public class PettyCashHolder : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "N°")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Nom")]
        [StringLength(150)]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public string Name
        {
            get
            {
                return this.name;
            }
            set
            {
                base.SetProperty<string>(ref this.name, value, "Name");
            }
        }

        [Display(Name = "Montant maximum")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double MaxCash
        {
            get
            {
                return this.maxCash;
            }
            set
            {
                base.SetProperty<double>(ref this.maxCash, value, "MaxCash");
            }
        }

        public int PermenantAccountID
        {
            get
            {
                return this.PermanentaccountID;
            }
            set
            {
                base.SetProperty<int>(ref this.PermanentaccountID, value, "PermanentAccountID");
            }
        }

        public Account PermenantAccount { get; set; }

        public int TemporaryAccountID
        {
            get
            {
                return this.TemporaryaccountID;
            }
            set
            {
                base.SetProperty<int>(ref this.TemporaryaccountID, value, "TemporaryAccountID");
            }
        }

        public Account TemporaryAccount { get; set; }

        [Display(Name = "Désactivé")]
        public bool Disable
        {
            get
            {
                return this.disable;
            }
            set
            {
                base.SetProperty<bool>(ref this.disable, value, "Disable");
            }
        }

        private int id;

        private string name;

        private double maxCash;

        private int PermanentaccountID;

        private int TemporaryaccountID;

        private bool disable;
    }
}
