﻿using DevExpress.Data;
using DevExpress.Utils;
using DevExpress.Utils.Menu;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using EasyStock.Common;
using EasyStock.HR.BLL;
using EasyStock.HR.Class;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace EasyStock.HR.Views.PenaltyRewardView
{
    public partial class RewardListView : MasterView
    {
        public static RewardListView Instance
        {
            get
            {
                bool flag = RewardListView.instance == null || RewardListView.instance.IsDisposed;
                if (flag)
                {
                    RewardListView.instance = new RewardListView();
                }
                return RewardListView.instance;
            }
        }

        public RewardListView()
        {
            this.InitializeComponent();
            this.btn_Save.Visibility = BarItemVisibility.Never;
            this.btn_Print.Visibility = BarItemVisibility.Always;
            this.btn_Refresh.Visibility = BarItemVisibility.Always;
            this.btn_Print.Enabled = true;
            this.Text = "Les récompenses";
            this.InitializeGrid();
            this.dateEditFrom.Properties.Mask.EditMask = "dd/MM/yyyy";
            this.dateEditFrom.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.dateEditTo.Properties.Mask.EditMask = "dd/MM/yyyy";
            this.dateEditTo.Properties.Mask.UseMaskAsDisplayFormat = true;
        }

        private void InitializeGrid()
        {
            this.gridView1.SetAlternatingColors();
            this.gridView1.OptionsSelection.MultiSelect = true;
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsBehavior.AutoPopulateColumns = false;
            this.gridView1.Columns.AddRange(new GridColumn[]
            {
                new GridColumn
                {
                    FieldName = "ID",
                    Visible = false
                },
                new GridColumn
                {
                    FieldName = "Department",
                    Visible = true,
                    UnboundDataType = typeof(string),
                },
                new GridColumn
                {
                    FieldName = "Employee",
                    Visible = true
                },
                new GridColumn
                {
                    FieldName = "Date",
                    Visible = true
                },
                new GridColumn
                {
                    FieldName = "Type",
                    Visible = true,
                },
                new GridColumn
                {
                    FieldName = "calcType",
                    Visible = true,
                },
                new GridColumn
                {
                    FieldName = "NoOfDays",
                    Visible = true
                },
                new GridColumn
                {
                    FieldName = "Amount",
                    Visible = true
                },
                new GridColumn
                {
                    FieldName = "Remarks",
                    Visible = true
                }
            });
            this.gridView1.OptionsView.ShowFooter = true;
            this.gridView1.Columns["NoOfDays"].Summary.Add(SummaryItemType.Sum, "NoOfDays", "Jours {0}");
            this.gridView1.Columns["Amount"].Summary.Add(SummaryItemType.Sum, "Amount", "Total {0}");
            this.gridView1.CustomColumnDisplayText += this.GridView1_CustomColumnDisplayText;
            this.gridView1.CustomUnboundColumnData += this.GridView1_CustomUnboundColumnData;
            this.gridView1.DoubleClick += this.GridView1_DoubleClick;
            this.gridView1.PopupMenuShowing += this.GridView1_PopupMenuShowing;
        }

        private void GridView1_PopupMenuShowing(object sender, DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs e)
        {
            bool flag = e.HitInfo.InRow || e.HitInfo.InRowCell;
            if (flag)
            {
                DXMenuItem dxButtonEdit = new DXMenuItem
                {
                    Caption = "Modifier"
                };
                dxButtonEdit.Click += this.DxButtonEdit_Click;
                e.Menu.Items.Add(dxButtonEdit);
                DXMenuItem dxButtonDelete = new DXMenuItem
                {
                    Caption = "Supprimer"
                };
                dxButtonDelete.Click += this.DxButtonDelete_Click;
                e.Menu.Items.Add(dxButtonDelete);
                DXMenuItem dxButtonPrint = new DXMenuItem
                {
                    Caption = "Imprimer"
                };
                dxButtonPrint.Click += this.DxButtonPrint_Click;
                e.Menu.Items.Add(dxButtonPrint);
                DXMenuItem dxButtonEditEmp = new DXMenuItem
                {
                    Caption = ""
                };
                dxButtonEditEmp.Click += this.DxButtonEditEmp_Click;
                e.Menu.Items.Add(dxButtonEditEmp);
            }
        }

        private void DxButtonEditEmp_Click(object sender, EventArgs e)
        {
            GridView grid = sender as GridView;
            int[] handles = this.gridView1.GetSelectedRows();
            bool flag = handles.Length != 0;
            if (flag)
            {
                Employee empValue = this.gridView1.GetRowCellValue(handles[0], "Employee") as Employee;
                bool flag2 = empValue != null;
                if (flag2)
                {
                    Static.OpenForm(EmpView.Instance);
                    EmpView.Instance.GoTo(empValue.ID);
                }
            }
        }

        private void DxButtonDelete_Click(object sender, EventArgs e)
        {
            this.btn_Delete.PerformClick();
        }

        private void DxButtonPrint_Click(object sender, EventArgs e)
        {
            this.btn_Print.PerformClick();
        }

        private void DxButtonEdit_Click(object sender, EventArgs e)
        {
            List<int> ids = this.GetSelectedIds();
            int id = (ids.Count > 0) ? ids.FirstOrDefault<int>() : 0;
            this.Edit(id);
        }

        private List<int> GetSelectedIds()
        {
            int[] handles = this.gridView1.GetSelectedRows();
            List<int> ids = new List<int>();
            foreach (int handel in handles)
            {
                ids.Add(Convert.ToInt32(this.gridView1.GetRowCellValue(handel, "ID")));
            }
            bool flag = ids.Count == 0;
            if (flag)
            {
                XtraMessageBox.Show("Veuillez sélectionner au moins un enregistrement.");
            }
            return ids;
        }

        public virtual void Edit(int id)
        {
            Static.OpenForm(RewardView.Instance);
            RewardView.Instance.GoTo(id);
        }

        public override void Print()
        {
            List<int> ids = this.GetSelectedIds();
            base.Print();
        }

        public override void Delete()
        {
            List<int> ids = this.GetSelectedIds();
            bool flag = ids.Count == 0;
            if (!flag)
            {
                int res = PenaltyRewardBLL.Delete(ids);
                base.Delete();
            }
        }

        private void GridView1_DoubleClick(object sender, EventArgs e)
        {
            DXMouseEventArgs ea = e as DXMouseEventArgs;
            GridView view = sender as GridView;
            GridHitInfo info = view.CalcHitInfo(ea.Location);
            bool flag = info.InRow || info.InRowCell;
            if (flag)
            {
                int id = Convert.ToInt32(view.GetFocusedRowCellValue("ID"));
                this.Edit(id);
            }
        }

        private void GridView1_CustomUnboundColumnData(object sender, CustomColumnDataEventArgs e)
        {
            GridView grid = sender as GridView;
            bool flag = e.Column.FieldName == "Department";
            if (flag)
            {
                Employee empValue = this.gridView1.GetRowCellValue(e.ListSourceRowIndex, "Employee") as Employee;
                bool flag2 = empValue != null;
                if (flag2)
                {
                    Employee employee = EmployeeBLL.Get(empValue.ID);
                    Department dep = (employee != null) ? employee.Department : null;
                    bool isGetData = e.IsGetData;
                    if (isGetData)
                    {
                        e.Value = ((dep != null) ? dep.Name : null);
                    }
                }
            }
        }

        private void GridView1_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            bool flag = e.Column.FieldName == "Employee";
            if (flag)
            {
                Employee g = e.Value as Employee;
                e.DisplayText = ((g != null) ? g.Name : null);
            }
            bool flag2 = e.Column.FieldName == "Date";
            if (flag2)
            {
                e.DisplayText = Convert.ToDateTime(e.Value).ToString("dd/MM/yyyy");
            }
        }

        public override void RefreshData()
        {
            this.searchByDate();
            base.RefreshData();
        }

        public void searchByDate()
        {
            DateTime? from = null;
            DateTime? to = null;
            bool flag = this.dateEditFrom.EditValue != null;
            if (flag)
            {
                from = new DateTime?(Convert.ToDateTime(this.dateEditFrom.EditValue));
            }
            bool flag2 = this.dateEditTo.EditValue != null;
            if (flag2)
            {
                to = new DateTime?(Convert.ToDateTime(this.dateEditTo.EditValue));
            }
            List<PenaltyReward> list = PenaltyRewardBLL.GetAll(PenaltyRewardType.Reward, from, to);
            this.gridControl1.DataSource = list;
        }

        private void btnResetSearch_Click(object sender, EventArgs e)
        {
            this.dateEditFrom.EditValue = null;
            this.dateEditTo.EditValue = null;
            this.searchByDate();
        }

        private void gridControl1_Click(object sender, EventArgs e)
        {
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            this.searchByDate();
        }

        private static RewardListView instance;
    }
}
