﻿namespace EasyStock.Classes
{
    internal class ConvertMoneyFrench
    {
        public ConvertMoneyFrench()
        {

        }
        public string ConvertToText(decimal amoUnt)
        {
            if (amoUnt == 0)
                return "ZÉRO Dinars";

            var integerPart = (long)amoUnt;
            var fractionalPart = (int)((amoUnt - integerPart) * 100);

            var integerText =enLettre(integerPart.ToString());
            string fractionalText = fractionalPart > 0 ? " et " + enLettre(fractionalPart.ToString()) + " Centimes" : "";

            return $"{integerText} Dinars{fractionalText}".Trim();
        }
        public string enLettre(string number)
        {
            number = number.Trim();
            if (number.Length == 1)
                return SingleDigite(number);
            else if (number.Length == 2)
                return Twodigite(number);
            else if (number.Length == 3)
                return ThreeDigite(number);
            else if (number.Length >= 4 && number.Length < 6)
                return Foutosixdigit(number);
            else if (number.Length >= 6 && number.Length < 9)
                return SixdgigittoNine(number);
            else if (number.Length >= 9 && number.Length <= 12)
                return Ninetotwelvedigit(number);
            else
                return "";
        }

        private string SingleDigite(string singledigit)

        {

            int i;

            var r = int.TryParse(singledigit, out i);

            if (r)

            {
                switch (i)
                {
                    case 1:
                        return "Un";

                    case 2:

                        return "Deux";

                    case 3:

                        return "Trois";

                    case 4:

                        return "Quatre";

                    case 5:

                        return "Cinq";

                    case 6:

                        return "Six";

                    case 7:

                        return "Sept";

                    case 8:

                        return "Huit";

                    case 9:

                        return "Neuf";

                    default:

                        break;

                }

            }

            return "";

        }

        private string Twodigite(string twodigit)

        {

            int i = 0;

            var r = int.TryParse(twodigit, out i);

            if (r)

            {

                if (i <= 20)

                {

                    switch (i)

                    {

                        case 1:

                            return "Un";

                        case 2:

                            return "Deux";

                        case 3:

                            return "Trois";

                        case 4:

                            return "Quatre";

                        case 5:

                            return "Cinq";

                        case 6:

                            return "Six";

                        case 7:

                            return "Sept";

                        case 8:

                            return "Huit";

                        case 9:

                            return "Neuf";

                        case 10:

                            return "Dix";

                        case 11:

                            return "Onze";

                        case 12:

                            return "Douze";

                        case 13:

                            return "Treize";

                        case 14:

                            return "Quatorze";

                        case 15:

                            return "Quinze";

                        case 16:

                            return "Seize";

                        case 17:

                            return "Dix-Sept";

                        case 18:

                            return "Dix-Huit";

                        case 19:

                            return "Dix-Neuf";

                        case 20:

                            return "Vingt";

                        default:

                            break;

                    }

                }

                else if (i > 20 & i < 30)

                {

                    var twodigitarray = twodigit.ToCharArray();

                    if (twodigitarray[1].ToString() == "1")

                    {

                        return "Vingt Et Un";

                    }

                    else

                    {

                        return "Vingt-" + SingleDigite(twodigitarray[1].ToString());

                    }

                }

                else if (i == 30)

                {

                    return "Trente";

                }

                else if (i > 30 & i < 40)

                {

                    var twodigitarray = twodigit.ToCharArray();

                    if (twodigitarray[1].ToString() == "1")

                    {

                        return "Trente Et Un";

                    }

                    else

                    {

                        return "Trente-" + SingleDigite(twodigitarray[1].ToString());

                    }

                }

                else if (i == 40)

                {

                    return "Quarante";

                }

                else if (i > 40 & i < 50)

                {

                    var twodigitarray = twodigit.ToCharArray();

                    if (twodigitarray[1].ToString() == "1")

                    {

                        return "Quarante Et Un";

                    }

                    else

                    {

                        return "Quarante-" + SingleDigite(twodigitarray[1].ToString());

                    }

                }

                else if (i == 50)

                {

                    return "Cinquante";

                }

                else if (i > 50 & i < 60)

                {

                    var twodigitarray = twodigit.ToCharArray();

                    if (twodigitarray[1].ToString() == "1")

                    {

                        return "Cinquante Et Un";

                    }

                    else

                    {

                        return "Cinquante-" + SingleDigite(twodigitarray[1].ToString());

                    }

                }

                else if (i == 60)

                {

                    return "Soixante";

                }

                else if (i > 60 & i < 70)

                {

                    var twodigitarray = twodigit.ToCharArray();

                    if (twodigitarray[1].ToString() == "1")

                    {

                        return "Soixante Et Un";

                    }

                    else

                    {

                        return "Soixante-" + SingleDigite(twodigitarray[1].ToString());

                    }

                }

                else if (i == 70)

                {

                    return "Soixante-Dix";

                }

                else if (i > 70 & i < 80)

                {

                    var twodigitarray = twodigit.ToCharArray();

                    if (twodigitarray[1].ToString() == "1")

                    {

                        return "Soixante-Onze";

                    }

                    else

                    {

                        int b = int.Parse(twodigitarray[1].ToString());

                        int plusten = b + 10;

                        return "Soixante-" + Twodigite(plusten.ToString());

                    }

                }

                else if (i == 80)

                {

                    return "Quatre-Vingts";

                }

                else if (i > 80 & i < 90)

                {

                    var twodigitarray = twodigit.ToCharArray();

                    if (twodigitarray[1].ToString() == "1")

                    {

                        return "Quatre-Vingts Et Un";

                    }

                    else

                    {

                        return "Quatre-Vingts-" + SingleDigite(twodigitarray[1].ToString());

                    }

                }

                else if (i == 90)

                {

                    return "Quatre-Vingts-Dix";

                }

                else if (i > 90 & i < 100)

                {

                    var twodigitarray = twodigit.ToCharArray();

                    if (twodigitarray[1].ToString() == "1")

                    {

                        return "Quatre-Vingts-Onze";

                    }

                    else

                    {

                        int b = int.Parse(twodigitarray[1].ToString());

                        int plusten = b + 10;

                        return "Quatre-Vingts-" + Twodigite(plusten.ToString());

                    }

                }

            }

            return "";

        }

        private string ThreeDigite(string threedigit)

        {

            int i = 0;

            var r = int.TryParse(threedigit, out i);

            if (r && i < 1000)

            {

                var threedigitarray = threedigit.ToCharArray();

                if (i < 100)

                {

                    return Twodigite(i.ToString());

                }

                else if (i == 100)

                {

                    return "Cent";

                }

                else if (threedigitarray[1].ToString() == "0" && threedigitarray[2].ToString() == "0")

                {

                    return SingleDigite(threedigitarray[0].ToString()) + " Cent ";

                }

                else if (i > 100 & i < 200)

                {

                    return "Cent " + Twodigite(threedigitarray[1].ToString() + threedigitarray[2].ToString());

                }

                else if (i >= 200 & i < 1000)

                {

                    return SingleDigite(threedigitarray[0].ToString()) + " Cent " + Twodigite(threedigitarray[1].ToString() + threedigitarray[2].ToString());

                }

            }

            return "";

        }

        private string Foutosixdigit(string fourtosixdigit)

        {

            int i = 0;

            var r = int.TryParse(fourtosixdigit, out i);

            if (r && i < 1000000)

            {

                var fourtosixdigitarray = fourtosixdigit.ToCharArray();

                if (i == 1000)

                {

                    return "Mille";

                }

                else if (i < 1000)

                {

                    return ThreeDigite(i.ToString());

                }

                else if (i > 1000 & i < 2000)

                {

                    return "Mille " + ThreeDigite(fourtosixdigitarray[1].ToString() + fourtosixdigitarray[2].ToString() + fourtosixdigitarray[3].ToString());

                }

                else if (i >= 2000 & i < 1000000)

                {

                    string thoudanddigit = fourtosixdigit.Remove(fourtosixdigit.Length - 3);

                    string hUndereddisigt = fourtosixdigit.Substring(fourtosixdigit.Length - 3);

                    return ThreeDigite(thoudanddigit) + " Mille " + ThreeDigite(hUndereddisigt);

                }

            }

            return "";

        }

        private string SixdgigittoNine(string sixtoninedisigt)

        {

            decimal i;

            var r = decimal.TryParse(sixtoninedisigt, out i);

            if (r && i < 1000000000)

            {

                var sixtoinedigitarray = sixtoninedisigt.ToCharArray();

                if (i < 1000000000 && i >= 1000000)

                {

                    string milliondidigt = sixtoninedisigt.Remove(sixtoninedisigt.Length - 6);

                    string thousanddigit = sixtoninedisigt.Substring(sixtoninedisigt.Length - 6, 3);

                    string otherdgit = sixtoninedisigt.Substring(sixtoninedisigt.Length - 3);

                    if (thousanddigit == "000")

                    {

                        return ThreeDigite(milliondidigt) + " Million " + ThreeDigite(thousanddigit) + ThreeDigite(otherdgit);

                    }

                    else

                    {

                        return ThreeDigite(milliondidigt) + " Million " + ThreeDigite(thousanddigit) + " Mille " + ThreeDigite(otherdgit);

                    }

                }

                else if (i < 1000000)

                {

                    return Foutosixdigit(i.ToString());

                }

            }

            return "";

        }

        public string Ninetotwelvedigit(string nicetotwelvedidigt)

        {

            decimal i;

            var r = decimal.TryParse(nicetotwelvedidigt, out i);

            if (r && i < 1000000000000)

            {

                var sixtoinedigitarray = nicetotwelvedidigt.ToCharArray();

                if (i < 1000000000000 && i >= 1000000000)

                {

                    string milliarddigit = nicetotwelvedidigt.Remove(nicetotwelvedidigt.Length - 9);

                    string milliondidigt = nicetotwelvedidigt.Substring(nicetotwelvedidigt.Length - 9, 3);

                    string thousanddigit = nicetotwelvedidigt.Substring(nicetotwelvedidigt.Length - 6, 3);

                    string otherdgit = nicetotwelvedidigt.Substring(nicetotwelvedidigt.Length - 3);

                    //return ThreeDigite(milliarddigit) + " milliard " + ThreeDigite(milliondidigt) + ThreeDigite(thousanddigit) + ThreeDigite(otherdgit);

                    //return milliarddigit + milliondidigt + thousanddigit + otherdgit;

                    if (milliondidigt == "000" & thousanddigit == "000")

                    {

                        return ThreeDigite(milliarddigit) + " Milliard " + ThreeDigite(milliondidigt) + ThreeDigite(thousanddigit) + ThreeDigite(otherdgit);

                    }

                    else if (milliondidigt == "000" & thousanddigit != "000")

                    {

                        return ThreeDigite(milliarddigit) + " Milliard " + ThreeDigite(milliondidigt) + ThreeDigite(thousanddigit) + " Mille " + ThreeDigite(otherdgit);

                    }

                    else if (milliondidigt != "000" & thousanddigit == "000")

                    {

                        return ThreeDigite(milliarddigit) + " Milliard " + ThreeDigite(milliondidigt) + " Million " + ThreeDigite(thousanddigit) + ThreeDigite(otherdgit);

                    }

                    else if (milliondidigt != "000" & thousanddigit != "000")

                    {

                        return ThreeDigite(milliarddigit) + " Milliard " + ThreeDigite(milliondidigt) + " Million " + ThreeDigite(thousanddigit) + " mille " + ThreeDigite(otherdgit);

                    }

                    else

                    {

                    }

                }

                else if (i < 1000000000)

                {

                    return SixdgigittoNine(i.ToString());

                }

            }

            return "";

        }

    }

}
