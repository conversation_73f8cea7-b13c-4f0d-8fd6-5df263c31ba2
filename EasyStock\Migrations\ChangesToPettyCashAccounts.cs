﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class ChangesToPettyCashAccounts : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(ChangesToPettyCashAccounts));

        string IMigrationMetadata.Id => "202109111857316_ChangesToPettyCashAccounts";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            RenameColumn("dbo.PettyCashHolders", "AccountID", "PermenantAccountID");
            RenameIndex("dbo.PettyCashHolders", "IX_AccountID", "IX_PermenantAccountID");
            AddColumn("dbo.PettyCashHolders", "TemporaryAccountID", (ColumnBuilder c) => c.Int(false));
            AddColumn("dbo.SystemSettings", "PermanentPettyCashAccount", (ColumnBuilder c) => c.Int(false));
            AddColumn("dbo.SystemSettings", "TemporaryPettyCashAccount", (ColumnBuilder c) => c.Int(false));
            CreateIndex("dbo.PettyCashHolders", "TemporaryAccountID");
            AddForeignKey("dbo.PettyCashHolders", "TemporaryAccountID", "dbo.Accounts", "ID");
        }

        public override void Down()
        {
            DropForeignKey("dbo.PettyCashHolders", "TemporaryAccountID", "dbo.Accounts");
            DropIndex("dbo.PettyCashHolders", new string[1] { "TemporaryAccountID" });
            DropColumn("dbo.SystemSettings", "TemporaryPettyCashAccount");
            DropColumn("dbo.SystemSettings", "PermanentPettyCashAccount");
            DropColumn("dbo.PettyCashHolders", "TemporaryAccountID");
            RenameIndex("dbo.PettyCashHolders", "IX_PermenantAccountID", "IX_AccountID");
            RenameColumn("dbo.PettyCashHolders", "PermenantAccountID", "AccountID");
        }
    }
}
