﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class printedBarcodeModel : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(printedBarcodeModel));

        string IMigrationMetadata.Id => "202102171231449_printedBarcodeModel";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.PrintedBarcodes", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                ProductID = c.Int(false),
                UnitID = c.Int(false),
                ColorID = c.Int(),
                SizeID = c.Int(),
                Serial = c.String(),
                Expire = c.DateTime(),
                Quantity = c.Int(false),
                Type = c.Int(false),
                BillID = c.Int(),
                SourceID = c.Int()
            }).PrimaryKey(t => t.ID);
        }

        public override void Down()
        {
            DropTable("dbo.PrintedBarcodes");
        }
    }
}
