﻿namespace EasyStock.MainViews
{
	public partial class HomeForm : global::DevExpress.XtraBars.TabForm
	{
        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tabFormControl1 = new DevExpress.XtraBars.TabFormControl();
            this.skinDropDownButtonItem1 = new DevExpress.XtraBars.SkinDropDownButtonItem();
            this.skinPaletteDropDownButtonItem1 = new DevExpress.XtraBars.SkinPaletteDropDownButtonItem();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem2 = new DevExpress.XtraBars.BarButtonItem();
            this.و = new DevExpress.XtraBars.BarCheckItem();
            this.barButtonItem3 = new DevExpress.XtraBars.BarButtonItem();
            this.barCheckItem2 = new DevExpress.XtraBars.BarCheckItem();
            this.barButtonItem4 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem5 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem6 = new DevExpress.XtraBars.BarButtonItem();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar3 = new DevExpress.XtraBars.Bar();
            this.barHeaderItem1 = new DevExpress.XtraBars.BarHeaderItem();
            this.CurrentBranchLable = new DevExpress.XtraBars.BarButtonItem();
            this.barHeaderItem2 = new DevExpress.XtraBars.BarHeaderItem();
            this.CurrentUserLable = new DevExpress.XtraBars.BarStaticItem();
            this.barHeaderItem3 = new DevExpress.XtraBars.BarHeaderItem();
            this.CurrentTimeLable = new DevExpress.XtraBars.BarStaticItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            ((System.ComponentModel.ISupportInitialize)(this.tabFormControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            this.SuspendLayout();
            // 
            // tabFormControl1
            // 
            this.tabFormControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.skinDropDownButtonItem1,
            this.skinPaletteDropDownButtonItem1,
            this.barButtonItem1,
            this.barButtonItem2,
            this.و,
            this.barButtonItem3,
            this.barCheckItem2,
            this.barButtonItem4,
            this.barButtonItem5,
            this.barButtonItem6});
            this.tabFormControl1.Location = new System.Drawing.Point(0, 0);
            this.tabFormControl1.Name = "tabFormControl1";
            this.tabFormControl1.ShowAddPageButton = false;
            this.tabFormControl1.Size = new System.Drawing.Size(1092, 75);
            this.tabFormControl1.TabForm = this;
            this.tabFormControl1.TabIndex = 0;
            this.tabFormControl1.TabRightItemLinks.Add(this.barButtonItem4);
            this.tabFormControl1.TabRightItemLinks.Add(this.barCheckItem2);
            this.tabFormControl1.TabRightItemLinks.Add(this.و);
            this.tabFormControl1.TabRightItemLinks.Add(this.barButtonItem5);
            this.tabFormControl1.TabRightItemLinks.Add(this.barButtonItem3);
            this.tabFormControl1.TabRightItemLinks.Add(this.barButtonItem6);
            this.tabFormControl1.TabRightItemLinks.Add(this.skinDropDownButtonItem1);
            this.tabFormControl1.TabRightItemLinks.Add(this.skinPaletteDropDownButtonItem1);
            this.tabFormControl1.TabStop = false;
            // 
            // skinDropDownButtonItem1
            // 
            this.skinDropDownButtonItem1.Id = 0;
            this.skinDropDownButtonItem1.Name = "skinDropDownButtonItem1";
            // 
            // skinPaletteDropDownButtonItem1
            // 
            this.skinPaletteDropDownButtonItem1.Enabled = false;
            this.skinPaletteDropDownButtonItem1.Id = 1;
            this.skinPaletteDropDownButtonItem1.Name = "skinPaletteDropDownButtonItem1";
            // 
            // barButtonItem1
            // 
            this.barButtonItem1.Caption = "barButtonItem1";
            this.barButtonItem1.Id = 2;
            this.barButtonItem1.Name = "barButtonItem1";
            // 
            // barButtonItem2
            // 
            this.barButtonItem2.Caption = "barButtonItem2";
            this.barButtonItem2.Id = 3;
            this.barButtonItem2.Name = "barButtonItem2";
            // 
            // و
            // 
            this.و.Caption = "barCheckItem1";
            this.و.Hint = "Mode tactile";
            this.و.Id = 0;
            this.و.ImageOptions.Image = global::EasyStock.Properties.Resources.touchmode_16x16;
            this.و.ImageOptions.LargeImage = global::EasyStock.Properties.Resources.touchmode_32x32;
            this.و.Name = "و";
            this.و.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.barCheckItem1_CheckedChanged);
            // 
            // barButtonItem3
            // 
            this.barButtonItem3.Caption = "font";
            this.barButtonItem3.Hint = "Changer la police";
            this.barButtonItem3.Id = 0;
            this.barButtonItem3.ImageOptions.SvgImage = global::EasyStock.Properties.Resources.fontsize;
            this.barButtonItem3.Name = "barButtonItem3";
            this.barButtonItem3.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem3_ItemClick);
            // 
            // barCheckItem2
            // 
            this.barCheckItem2.Caption = "barCheckItem2";
            this.barCheckItem2.Hint = "Mode réduire la taille";
            this.barCheckItem2.Id = 0;
            this.barCheckItem2.ImageOptions.SvgImage = global::EasyStock.Properties.Resources.compressweekend;
            this.barCheckItem2.Name = "barCheckItem2";
            this.barCheckItem2.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.barCheckItem2_CheckedChanged);
            // 
            // barButtonItem4
            // 
            this.barButtonItem4.Caption = "Écran principal";
            this.barButtonItem4.Id = 0;
            this.barButtonItem4.ImageOptions.SvgImage = global::EasyStock.Properties.Resources.bo_address1;
            this.barButtonItem4.Name = "barButtonItem4";
            this.barButtonItem4.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem4_ItemClick);
            // 
            // barButtonItem5
            // 
            this.barButtonItem5.Caption = "Calc";
            this.barButtonItem5.Id = 0;
            this.barButtonItem5.ImageOptions.SvgImage = global::EasyStock.Properties.Resources.business_calculator;
            this.barButtonItem5.Name = "barButtonItem5";
            this.barButtonItem5.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem5_ItemClick_1);
            // 
            // barButtonItem6
            // 
            this.barButtonItem6.Caption = "LMC";
            this.barButtonItem6.Id = 0;
            this.barButtonItem6.ImageOptions.SvgImageSize = new System.Drawing.Size(32, 32);
            this.barButtonItem6.Name = "barButtonItem6";
            this.barButtonItem6.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem6_ItemClick);
            // 
            // barManager1
            // 
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar3});
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barHeaderItem1,
            this.barHeaderItem2,
            this.CurrentUserLable,
            this.barHeaderItem3,
            this.CurrentTimeLable,
            this.CurrentBranchLable});
            this.barManager1.MaxItemId = 8;
            this.barManager1.StatusBar = this.bar3;
            // 
            // bar3
            // 
            this.bar3.BarName = "Status bar";
            this.bar3.CanDockStyle = DevExpress.XtraBars.BarCanDockStyle.Bottom;
            this.bar3.DockCol = 0;
            this.bar3.DockRow = 0;
            this.bar3.DockStyle = DevExpress.XtraBars.BarDockStyle.Bottom;
            this.bar3.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barHeaderItem1),
            new DevExpress.XtraBars.LinkPersistInfo(this.CurrentBranchLable),
            new DevExpress.XtraBars.LinkPersistInfo(this.barHeaderItem2),
            new DevExpress.XtraBars.LinkPersistInfo(this.CurrentUserLable),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.Caption, this.barHeaderItem3, "Date"),
            new DevExpress.XtraBars.LinkPersistInfo(this.CurrentTimeLable)});
            this.bar3.OptionsBar.AllowQuickCustomization = false;
            this.bar3.OptionsBar.DrawDragBorder = false;
            this.bar3.OptionsBar.UseWholeRow = true;
            this.bar3.Text = "Status bar";
            // 
            // barHeaderItem1
            // 
            this.barHeaderItem1.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.barHeaderItem1.Appearance.Options.UseBackColor = true;
            this.barHeaderItem1.Caption = "Succursale :";
            this.barHeaderItem1.Id = 0;
            this.barHeaderItem1.Name = "barHeaderItem1";
            // 
            // CurrentBranchLable
            // 
            this.CurrentBranchLable.Caption = "barButtonItem4";
            this.CurrentBranchLable.Id = 7;
            this.CurrentBranchLable.Name = "CurrentBranchLable";
            this.CurrentBranchLable.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.CurrentBranchLable_ItemClick);
            // 
            // barHeaderItem2
            // 
            this.barHeaderItem2.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.barHeaderItem2.Appearance.Options.UseBackColor = true;
            this.barHeaderItem2.Caption = "Utilisateur :";
            this.barHeaderItem2.Id = 2;
            this.barHeaderItem2.Name = "barHeaderItem2";
            // 
            // CurrentUserLable
            // 
            this.CurrentUserLable.Caption = "barStaticItem1";
            this.CurrentUserLable.Id = 3;
            this.CurrentUserLable.Name = "CurrentUserLable";
            // 
            // barHeaderItem3
            // 
            this.barHeaderItem3.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.barHeaderItem3.Appearance.Options.UseBackColor = true;
            this.barHeaderItem3.Caption = "Temps";
            this.barHeaderItem3.Id = 5;
            this.barHeaderItem3.Name = "barHeaderItem3";
            // 
            // CurrentTimeLable
            // 
            this.CurrentTimeLable.Caption = "barStaticItem1";
            this.CurrentTimeLable.Id = 6;
            this.CurrentTimeLable.Name = "CurrentTimeLable";
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.barManager1;
            this.barDockControlTop.Size = new System.Drawing.Size(1092, 0);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 491);
            this.barDockControlBottom.Manager = this.barManager1;
            this.barDockControlBottom.Size = new System.Drawing.Size(1092, 24);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 0);
            this.barDockControlLeft.Manager = this.barManager1;
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 491);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1092, 0);
            this.barDockControlRight.Manager = this.barManager1;
            this.barDockControlRight.Size = new System.Drawing.Size(0, 491);
            // 
            // timer1
            // 
            this.timer1.Enabled = true;
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // HomeForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1092, 515);
            this.Controls.Add(this.tabFormControl1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.DoubleBuffered = true;
            this.Name = "HomeForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.SurfaceMaterial = DevExpress.XtraEditors.SurfaceMaterial.Acrylic;
            this.TabFormControl = this.tabFormControl1;
            this.Text = " ";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            ((System.ComponentModel.ISupportInitialize)(this.tabFormControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private global::System.ComponentModel.IContainer components;

		public global::DevExpress.XtraBars.TabFormControl tabFormControl1;

		private global::DevExpress.XtraBars.SkinDropDownButtonItem skinDropDownButtonItem1;

		private global::DevExpress.XtraBars.SkinPaletteDropDownButtonItem skinPaletteDropDownButtonItem1;

		private global::DevExpress.XtraBars.BarButtonItem barButtonItem1;

		private global::DevExpress.XtraBars.BarButtonItem barButtonItem2;

		private global::DevExpress.XtraBars.BarCheckItem و;

		private global::DevExpress.XtraBars.BarButtonItem barButtonItem3;

		private global::DevExpress.XtraBars.BarCheckItem barCheckItem2;

		private global::DevExpress.XtraBars.BarDockControl barDockControlLeft;

		private global::DevExpress.XtraBars.BarManager barManager1;

		private global::DevExpress.XtraBars.Bar bar3;

		private global::DevExpress.XtraBars.BarHeaderItem barHeaderItem1;

		private global::DevExpress.XtraBars.BarHeaderItem barHeaderItem2;

		public global::DevExpress.XtraBars.BarStaticItem CurrentUserLable;

		private global::DevExpress.XtraBars.BarHeaderItem barHeaderItem3;

		public global::DevExpress.XtraBars.BarStaticItem CurrentTimeLable;

		private global::DevExpress.XtraBars.BarDockControl barDockControlTop;

		private global::DevExpress.XtraBars.BarDockControl barDockControlBottom;

		private global::DevExpress.XtraBars.BarDockControl barDockControlRight;

		private global::System.Windows.Forms.Timer timer1;

		public global::DevExpress.XtraBars.BarButtonItem CurrentBranchLable;

		private global::DevExpress.XtraBars.BarButtonItem barButtonItem4;
        private DevExpress.XtraBars.BarButtonItem barButtonItem5;
        private DevExpress.XtraBars.BarButtonItem barButtonItem6;
    }
}
