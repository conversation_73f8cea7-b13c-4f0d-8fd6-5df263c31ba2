﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Modèle des paramètres des utilisateurs")]
    public class UserSettingsProfile : BaseNotifyPropertyChangedModel
    {
        [Display(Name = "N°")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID
        {
            get
            {
                return this._Id;
            }
            set
            {
                base.SetProperty<int>(ref this._Id, value, "ID");
            }
        }

        [Display(Name = "Nom")]
        [Required(AllowEmptyStrings = false, ErrorMessage = "Ce champ est obligatoire")]
        public string Name
        {
            get
            {
                return this._name;
            }
            set
            {
                base.SetProperty<string>(ref this._name, value, "Name");
            }
        }

        [Display(Name = "Utilisateurs")]
        public BindingList<User> Users
        {
            get
            {
                return this.users;
            }
            set
            {
                base.SetProperty<BindingList<User>>(ref this.users, value, "Users");
            }
        }

        [Display(Name = "Caisse", GroupName = "{Tabs}/Général/Éléments par défaut")]
        public int DefaultDrawer { get; set; }

        [Display(Name = "Terminal de paiement", GroupName = "{Tabs}/Général/Éléments par défaut")]
        public int DefaultPayCard { get; set; }

        [Display(Name = "Fournisseur", GroupName = "{Tabs}/Général/Éléments par défaut")]
        public int DefaultVendor { get; set; }

        [Display(Name = "Client", GroupName = "{Tabs}/Général/Éléments par défaut")]
        public int DefaultCustomer { get; set; }

        [Display(Name = "Dépôt", GroupName = "{Tabs}/Général/Éléments par défaut")]
        public int DefaultStore { get; set; }

        [Display(Name = "Succursale", GroupName = "{Tabs}/Général/Éléments par défaut")]
        public int DefaultBranch { get; set; }

        [Display(Name = "Peut être modifié", GroupName = "{Tabs}/Général/Éléments par défaut")]
        public bool CanChangeBranch { get; set; }

        [Display(Name = "Écran par défaut", GroupName = "{Tabs}/Général/Éléments par défaut")]
        public int DefaultScreen { get; set; }

        [Display(Name = "Entrepôt de matières premières", GroupName = "{Tabs}/Général/Éléments par défaut")]
        public int DefaultRawStore { get; set; }

        [Display(Name = "Peut être modifié", GroupName = "{Tabs}/Général/Éléments par défaut")]
        public bool CanChangeStore { get; set; }

        [Display(Name = "Peut être modifié", GroupName = "{Tabs}/Général/Éléments par défaut")]
        public bool CanChangeDrawer { get; set; }

        [Display(Name = "Peut être modifié", GroupName = "{Tabs}/Général/Éléments par défaut")]
        public bool CanChangeCustomer { get; set; }

        [Display(Name = "Peut être modifié", GroupName = "{Tabs}/Général/Éléments par défaut")]
        public bool CanChangeVendor { get; set; }

        [Display(Name = "Type de prévisualisation d'impression", GroupName = "{Tabs}/Général")]
        public PrintMode InvoicePrintMode { get; set; }

        [Display(Name = "Impression après enregistrement", GroupName = "{Tabs}/Général")]
        public bool PrintAfterSave { get; set; }

        [Display(Name = "Autoriser la modification de la taxe", GroupName = "{Tabs}/Ventes")]
        public bool CanChangeTax { get; set; }

        [Display(Name = "Autoriser la suppression des articles de la facture", GroupName = "{Tabs}/Ventes")]
        public bool CanDeleteItemsInInvoices { get; set; }

        [Display(Name = "Type de paiement par défaut pour les ventes", GroupName = "{Tabs}/Ventes")]
        public PayType DefaultPayMethodInSales { get; set; }

        [Display(Name = "Peut changer le mode de paiement", GroupName = "{Tabs}/Ventes")]
        public bool CanChangePayMethod { get; set; }

        [Display(Name = "Lors de la vente à un client dépassant la limite de crédit", GroupName = "{Tabs}/Ventes")]
        public RedundancyOptions WhenSellingToACustomerExceededMaxCredit { get; set; }

        [Display(Name = "Autoriser la modification du prix de l'article", GroupName = "{Tabs}/Ventes")]
        public bool CanChangeItemPriceInSales { get; set; }

        [Display(Name = "Lors de la vente d'un article dont le stock atteint le niveau de commande", GroupName = "{Tabs}/Ventes")]
        public RedundancyOptions WhenSellingItemReachedReOrderLevel { get; set; }

        [Display(Name = "Lors de la vente d'une quantité d'article supérieure à celle disponible", GroupName = "{Tabs}/Ventes")]
        public RedundancyOptions WhenSellingItemWithQtyMoreThanAvailableQty { get; set; }

        [Display(Name = "Lors de la vente à un prix inférieur au prix d'achat", GroupName = "{Tabs}/Ventes")]
        public RedundancyOptions WhenSellingItemWithPriceLowerThanCostPrice { get; set; }

        [Display(Name = "Remise maximale autorisée pour la facture", GroupName = "{Tabs}/Ventes")]
        public double MaxDiscountInInvoice { get; set; }

        [Display(Name = "Autoriser la modification de la date de la facture de vente", GroupName = "{Tabs}/Ventes")]
        public bool CanChangeSalesInvoiceDate { get; set; }

        [Display(Name = "Autoriser la modification de la quantité", GroupName = "{Tabs}/Ventes")]
        public bool CanChangeQtyInSales { get; set; }

        [Display(Name = "Imprimer les factures à crédit", GroupName = "{Tabs}/Ventes")]
        public bool PrintCreditInvoices { get; set; }

        [Display(Name = "Afficher l'écran de paiement lors de l'enregistrement", GroupName = "{Tabs}/Ventes")]
        public bool ShowPayFormOnSavingNewInvoices { get; set; } = true;

        [Display(Name = "Autoriser la modification du prix d'achat", GroupName = "{Tabs}/Inventaire")]
        public bool CanChangeItemPriceInPurchase { get; set; }

        [Display(Name = "Autoriser la modification de la date de la facture d'achat", GroupName = "{Tabs}/Inventaire")]
        public bool CanChangePurchaseInvoiceDate { get; set; }

        [Display(Name = "Niveau de confidentialité maximum pour les articles", GroupName = "{Tabs}/Inventaire")]
        public SecracyLevel ProductSecrecyLevel { get; set; }

        [Display(Name = "Niveau de confidentialité maximum pour les comptes", GroupName = "{Tabs}/Financière")]
        public SecracyLevel AccountSecrecyLevel { get; set; }

        [Display(Name = "Permet de changer la date des reçus de caisse et des paiements", GroupName = "{Tabs}/Financière")]
        public bool CanChangeCashNoteDate { get; set; }

        [Display(Name = "Obliger l'utilisateur à commencer une période de caisse", GroupName = "{Tabs}/Financière")]
        public bool ForceStartDrawerPeriod { get; set; } = false;

        [Display(Name = "Modèle d'impression par défaut pour la facture de vente", GroupName = "{Tabs}/Imprimés")]
        public int? DefaultSalesPrintTemplate { get; set; }

        [Display(Name = "Imprimante par défaut pour la facture de vente", GroupName = "{Tabs}/Imprimés")]
        public string DefaultSalesPrinterName { get; set; }

        private int _Id;

        private string _name;

        private BindingList<User> users;
    }
}
