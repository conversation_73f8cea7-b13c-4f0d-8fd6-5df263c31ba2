﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace EasyStock.GUI
{

    internal class CheckArrowLabel : Label
    {
        public delegate void CheckChangedEventHandler(object sender, CheckChangedEventArgs e);

        public class CheckChangedEventArgs : EventArgs
        {
            public bool IsChecked { get; set; }

            public bool IsbyClick { get; set; }

            public CheckChangedEventArgs(bool _IsChecked, bool _IsbyClick)
            {
                IsChecked = _IsChecked;
                IsbyClick = _IsbyClick;
            }
        }

        public enum InvoiceSourcesType
        {
            Inventory = 1,
            Cash
        }

        public enum Direction
        {
            DownToUp,
            UpToDown,
            LeftToRight,
            RightToLeft
        }

        public bool IsClicking;

        private bool _checked;

        public override bool AutoSize => false;

        public bool Checked
        {
            get
            {
                return _checked;
            }
            set
            {
                _checked = value;
                if (this.CheckChanged != null)
                {
                    this.CheckChanged(this, new CheckChangedEventArgs(_checked, IsClicking));
                }
                Invalidate(invalidateChildren: true);
            }
        }

        public Direction ArrowDirection { get; set; }

        public bool ReadOnly { get; set; }

        public Color CheckedColor { get; set; }

        public Color UnCheckedColor { get; set; }

        public SystemProcess From { get; set; }

        public SystemProcess To { get; set; }

        public InvoiceSourcesType SourceType { get; set; }

        public event CheckChangedEventHandler CheckChanged;

        public CheckArrowLabel()
        {
            CheckedColor = Color.RoyalBlue;
            UnCheckedColor = Color.LightGray;
            base.Paint += CheckArrowLable_Paint;
            base.Click += CheckArrowLable_Click;
        }

        private void HandleLabelCheckChanged(object sender, CheckChangedEventArgs e)
        {
            OnLabelsCheckChanged(e);
        }

        protected virtual void OnLabelsCheckChanged(CheckChangedEventArgs e)
        {
            this.CheckChanged?.Invoke(this, e);
        }

        private void CheckArrowLable_Click(object sender, EventArgs e)
        {
            if (!ReadOnly)
            {
                IsClicking = true;
                Checked = !Checked;
                Invalidate(invalidateChildren: true);
                IsClicking = false;
            }
        }

        private void CheckArrowLable_Paint(object sender, PaintEventArgs e)
        {
            Color color = (Checked ? CheckedColor : UnCheckedColor);
            Pen pen = new Pen(color, 2f);
            Point[] pnt = new Point[4];
            switch (ArrowDirection)
            {
                case Direction.DownToUp:
                    e.Graphics.DrawLine(pen, base.Size.Width / 2, 0, base.Size.Width / 2, base.Size.Height);
                    pnt[0].X = base.Size.Width / 2 - 1;
                    pnt[0].Y = base.Size.Height / 2 - 3;
                    pnt[1].X = base.Size.Width / 2 + 1;
                    pnt[1].Y = base.Size.Height / 2 - 2;
                    pnt[2].X = base.Size.Width / 2 + 6;
                    pnt[2].Y = base.Size.Height / 2 + 6;
                    pnt[3].X = base.Size.Width / 2 - 6;
                    pnt[3].Y = base.Size.Height / 2 + 6;
                    break;
                case Direction.UpToDown:
                    e.Graphics.DrawLine(pen, base.Size.Width / 2, 0, base.Size.Width / 2, base.Size.Height);
                    pnt[0].X = base.Size.Width / 2 + 1;
                    pnt[0].Y = base.Size.Height / 2 + 2;
                    pnt[1].X = base.Size.Width / 2 - 1;
                    pnt[1].Y = base.Size.Height / 2 + 2;
                    pnt[2].X = base.Size.Width / 2 - 6;
                    pnt[2].Y = base.Size.Height / 2 - 6;
                    pnt[3].X = base.Size.Width / 2 + 6;
                    pnt[3].Y = base.Size.Height / 2 - 6;
                    break;
                case Direction.LeftToRight:
                    e.Graphics.DrawLine(pen, 0, base.Size.Height / 2, base.Size.Width, base.Size.Height / 2);
                    pnt[0].X = base.Size.Width / 2 + 2;
                    pnt[0].Y = base.Size.Height / 2 - 1;
                    pnt[1].X = base.Size.Width / 2 + 2;
                    pnt[1].Y = base.Size.Height / 2 + 1;
                    pnt[2].X = base.Size.Width / 2 - 6;
                    pnt[2].Y = base.Size.Height / 2 + 6;
                    pnt[3].X = base.Size.Width / 2 - 6;
                    pnt[3].Y = base.Size.Height / 2 - 6;
                    break;
                case Direction.RightToLeft:
                    e.Graphics.DrawLine(pen, 0, base.Size.Height / 2, base.Size.Width, base.Size.Height / 2);
                    pnt[0].X = base.Size.Width / 2 - 2;
                    pnt[0].Y = base.Size.Height / 2 + 1;
                    pnt[1].X = base.Size.Width / 2 - 3;
                    pnt[1].Y = base.Size.Height / 2 - 1;
                    pnt[2].X = base.Size.Width / 2 + 6;
                    pnt[2].Y = base.Size.Height / 2 - 6;
                    pnt[3].X = base.Size.Width / 2 + 6;
                    pnt[3].Y = base.Size.Height / 2 + 6;
                    break;
            }
            e.Graphics.FillPolygon(new SolidBrush(color), pnt);
        }
    }
}
