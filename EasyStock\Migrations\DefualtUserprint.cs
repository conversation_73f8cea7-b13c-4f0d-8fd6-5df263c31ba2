﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x020004D7 RID: 1239
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class DefualtUserprint : DbMigration, IMigrationMetadata
	{
		// Token: 0x060024B6 RID: 9398 RVA: 0x001FD410 File Offset: 0x001FB610
		public override void Up()
		{
			base.AddColumn("dbo.UserSettingsProfiles", "DefaultSalesPrintTemplate", (ColumnBuilder c) => c.Int(null, false, null, null, null, null, null), null);
			base.AddColumn("dbo.UserSettingsProfiles", "DefaultSalesPrinterName", (ColumnBuilder c) => c.String(null, null, null, null, null, null, null, null, null), null);
		}

		// Token: 0x060024B7 RID: 9399 RVA: 0x00012E08 File Offset: 0x00011008
		public override void Down()
		{
			base.DropColumn("dbo.UserSettingsProfiles", "DefaultSalesPrinterName", null);
			base.DropColumn("dbo.UserSettingsProfiles", "DefaultSalesPrintTemplate", null);
		}

		// Token: 0x17000BF7 RID: 3063
		// (get) Token: 0x060024B8 RID: 9400 RVA: 0x001FD480 File Offset: 0x001FB680
		string IMigrationMetadata.Id
		{
			get
			{
				return "202103051116337_DefualtUser print";
			}
		}

		// Token: 0x17000BF8 RID: 3064
		// (get) Token: 0x060024B9 RID: 9401 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000BF9 RID: 3065
		// (get) Token: 0x060024BA RID: 9402 RVA: 0x001FD498 File Offset: 0x001FB698
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002BCA RID: 11210
		private readonly ResourceManager Resources = new ResourceManager(typeof(DefualtUserprint));
	}
}
