﻿using DevExpress.XtraEditors;
using EasyStock.Common;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class DepartmentView : MasterView
    {
        public static DepartmentView Instance
        {
            get
            {
                bool flag = DepartmentView.instance == null || DepartmentView.instance.IsDisposed;
                if (flag)
                {
                    DepartmentView.instance = new DepartmentView();
                }
                return DepartmentView.instance;
            }
        }

        public Department department
        {
            get
            {
                return this.departmentBindingSource.Current as Department;
            }
            set
            {
                this.departmentBindingSource.DataSource = value;
            }
        }

        public DepartmentView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);

            try
            {
                context = new HRDataContext();
            }
            catch (TypeInitializationException ex)
            {
                Console.WriteLine($"TypeInitializationException: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"InnerException: {ex.InnerException.Message}");
                }
            }
            this.gridView1.AddEditButton(new EventHandler(this.RepositoryItemButtonEdit1_Click));
            this.gridView1.SetAlternatingColors();
            base.Shown += this.DepartmentView_Shown;
        }

        private void DepartmentView_Shown(object sender, EventArgs e)
        {
            bool flag = this.department == null;
            if (flag)
            {
                this.New();
            }
        }

        private void RepositoryItemButtonEdit1_Click(object sender, EventArgs e)
        {
            Department row = this.gridView1.GetRow(this.gridView1.FocusedRowHandle) as Department;
            bool flag = row != null;
            if (flag)
            {
                this.GoTo(row.ID);
            }
        }

        public void GoTo(int id)
        {
            Department sourceDepr = this.context.Departments.SingleOrDefault((Department x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.department = sourceDepr;
                this.ParentIDLookUpEdit.Properties.DataSource = (from depr in this.context.Departments
                                                                 where depr.ID != sourceDepr.ID
                                                                 select depr).ToList<Department>();
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable.");
            }
        }

        public override void New()
        {
            this.department = new Department();
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.context.Departments.AddOrUpdate(new Department[]
                {
                    this.department
                });
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.gridControl1.DataSource = this.context.Departments.ToList<Department>();
            this.ParentIDLookUpEdit.Properties.DataSource = this.context.Departments.ToList<Department>();
            base.RefreshData();
        }

        public override void Delete()
        {
        }

        private static DepartmentView instance;

        private HRDataContext context;
    }
}
