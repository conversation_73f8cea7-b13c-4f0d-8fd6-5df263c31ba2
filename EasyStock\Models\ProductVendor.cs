﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    public class ProductVendor
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [ReadOnly(true)]
        [Display(Name = "Code")]
        public int ID { get; set; }

        [Required]
        [StringLength(250)]
        [Display(Name = "Nom")]
        public string Name { get; set; }
    }
}
