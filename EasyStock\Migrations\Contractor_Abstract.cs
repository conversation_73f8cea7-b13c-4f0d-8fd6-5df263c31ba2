﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class Contractor_Abstract : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(Contractor_Abstract));

        string IMigrationMetadata.Id => "202108181210161_Contractor_Abstract";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.ContractorAbstracts", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Code = c.Int(false),
                ContractorID = c.Int(false),
                Date = c.DateTime(false),
                NetAmount = c.Double(false),
                PaidAmount = c.Double(false),
                DrawerID = c.Int(),
                JournalID = c.Int(false),
                Notes = c.String(),
                Type_ID = c.Int(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Drawers", t => t.DrawerID).ForeignKey("dbo.Journals", t => t.JournalID)
                .ForeignKey("dbo.WorkTypes", t => t.Type_ID)
                .Index(t => t.DrawerID)
                .Index(t => t.JournalID)
                .Index(t => t.Type_ID);
            CreateTable("dbo.WorkTypes", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Name = c.String(false, 400)
            }).PrimaryKey(t => t.ID);
        }

        public override void Down()
        {
            DropForeignKey("dbo.ContractorAbstracts", "Type_ID", "dbo.WorkTypes");
            DropForeignKey("dbo.ContractorAbstracts", "JournalID", "dbo.Journals");
            DropForeignKey("dbo.ContractorAbstracts", "DrawerID", "dbo.Drawers");
            DropIndex("dbo.ContractorAbstracts", new string[1] { "Type_ID" });
            DropIndex("dbo.ContractorAbstracts", new string[1] { "JournalID" });
            DropIndex("dbo.ContractorAbstracts", new string[1] { "DrawerID" });
            DropTable("dbo.WorkTypes");
            DropTable("dbo.ContractorAbstracts");
        }
    }
}
