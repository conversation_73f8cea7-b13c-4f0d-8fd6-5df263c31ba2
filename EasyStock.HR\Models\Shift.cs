﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    [DisplayColumn("Postes")]
    public class Shift : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Nom")]
        [StringLength(150)]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public string Name
        {
            get
            {
                return this.name;
            }
            set
            {
                base.SetProperty<string>(ref this.name, value, "Name");
            }
        }

        [Display(Name = "Date de début")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public DateTime StartDate
        {
            get
            {
                return this.startDate;
            }
            set
            {
                base.SetProperty<DateTime>(ref this.startDate, value, "StartDate");
            }
        }

        [Display(Name = "Répéter chaque ")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int RepeatEvery
        {
            get
            {
                return this.repeatEvery;
            }
            set
            {
                base.SetProperty<int>(ref this.repeatEvery, value, "RepeatEvery");
            }
        }

        public BindingList<ShiftDay> ShiftDays { get; set; }

        private int id;

        private string name;

        private DateTime startDate;

        private int repeatEvery;
    }
}
