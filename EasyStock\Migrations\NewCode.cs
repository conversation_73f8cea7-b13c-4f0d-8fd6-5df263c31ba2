﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
    // Token: 0x020004BD RID: 1213
    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class NewCode : DbMigration, IMigrationMetadata
    {
        // Token: 0x06002405 RID: 9221 RVA: 0x001F570C File Offset: 0x001F390C
        public override void Up()
        {
            base.AlterColumn("dbo.ContractorAbstracts", "Code", (ColumnBuilder c) => c.String(null, null, null, null, null, null, null, null, null), null);
            base.AlterColumn("dbo.VendorInvoices", "Code", (ColumnBuilder c) => c.String(null, null, null, null, null, null, null, null, null), null);
        }

        // Token: 0x06002406 RID: 9222 RVA: 0x001F577C File Offset: 0x001F397C
        public override void Down()
        {
            base.AlterColumn("dbo.VendorInvoices", "Code", (ColumnBuilder c) => c.String(new bool?(false), null, null, null, null, null, null, null, null), null);
            base.AlterColumn("dbo.ContractorAbstracts", "Code", (ColumnBuilder c) => c.String(new bool?(false), null, null, null, null, null, null, null, null), null);
        }

        // Token: 0x17000BCD RID: 3021
        // (get) Token: 0x06002407 RID: 9223 RVA: 0x001F57EC File Offset: 0x001F39EC
        string IMigrationMetadata.Id
        {
            get
            {
                return "202108191145551_NewCode";
            }
        }

        // Token: 0x17000BCE RID: 3022
        // (get) Token: 0x06002408 RID: 9224 RVA: 0x001EFA00 File Offset: 0x001EDC00
        string IMigrationMetadata.Source
        {
            get
            {
                return null;
            }
        }

        // Token: 0x17000BCF RID: 3023
        // (get) Token: 0x06002409 RID: 9225 RVA: 0x001F5804 File Offset: 0x001F3A04
        string IMigrationMetadata.Target
        {
            get
            {
                return this.Resources.GetString("Target");
            }
        }

        // Token: 0x04002B6B RID: 11115
        private readonly ResourceManager Resources = new ResourceManager(typeof(NewCode));
    }
}
