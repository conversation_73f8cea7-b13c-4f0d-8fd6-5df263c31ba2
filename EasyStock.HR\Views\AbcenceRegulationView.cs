﻿using DevExpress.XtraEditors;
using EasyStock.Common;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class AbcenceRegulationView : MasterView
    {
        public static AbcenceRegulationView Instance
        {
            get
            {
                bool flag = AbcenceRegulationView.instance == null || AbcenceRegulationView.instance.IsDisposed;
                if (flag)
                {
                    AbcenceRegulationView.instance = new AbcenceRegulationView();
                }
                return AbcenceRegulationView.instance;
            }
        }

        public AbsenceRegulation regulation
        {
            get
            {
                return this.absenceRegulationBindingSource.Current as AbsenceRegulation;
            }
            set
            {
                this.absenceRegulationBindingSource.DataSource = value;
            }
        }

        public AbcenceRegulationView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            this.context = new HRDataContext();
            this.gridView1.AddEditButton(new EventHandler(this.RepositoryItemButtonEdit1_Click));
            this.gridView1.SetAlternatingColors();
            base.Shown += this.AbcenceRegulationView_Shown;
        }

        private void AbcenceRegulationView_Shown(object sender, EventArgs e)
        {
            bool flag = this.regulation == null;
            if (flag)
            {
                this.New();
            }
        }

        private void RepositoryItemButtonEdit1_Click(object sender, EventArgs e)
        {
            AbsenceRegulation row = this.gridView1.GetRow(this.gridView1.FocusedRowHandle) as AbsenceRegulation;
            bool flag = row != null;
            if (flag)
            {
                this.GoTo(row.ID);
            }
        }

        public void GoTo(int id)
        {
            AbsenceRegulation sourceRegulation = this.context.AbsenceRegulations.SingleOrDefault((AbsenceRegulation x) => x.ID == id);
            bool flag = sourceRegulation != null;
            if (flag)
            {
                this.regulation = sourceRegulation;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable.");
            }
        }

        public override void New()
        {
            this.regulation = new AbsenceRegulation();
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.context.AbsenceRegulations.AddOrUpdate(new AbsenceRegulation[]
                {
                    this.regulation
                });
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.gridControl1.DataSource = this.context.AbsenceRegulations.ToList<AbsenceRegulation>();
            base.RefreshData();
        }

        public override void Delete()
        {
        }

        private static AbcenceRegulationView instance;

        private HRDataContext context;
    }
}
