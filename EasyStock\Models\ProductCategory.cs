﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Catégorie de article")]
    public class ProductCategory
    {

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [ReadOnly(true)]
        [Display(Name = "Identifiant")]
        public int ID { get; set; }

        [Display(Name = "Code")]
        [MaxLength(25)]
        [ReadOnly(true)]
        public string Code { get; set; }

        [MaxLength(500)]
        [Display(Name = "Nom de la Catégorie")]
        [Required(AllowEmptyStrings = false, ErrorMessage = "Le nom de la Catégorie est requis")]
        public string Name { get; set; }


        [Display(Name = "Catégorie parente")]
        public ProductCategory ParentGroup { get; set; }

        [Display(Name = "Identifiant de la Catégorie parente")]
        public int? ParentGroupID { get; set; }
    }

}
