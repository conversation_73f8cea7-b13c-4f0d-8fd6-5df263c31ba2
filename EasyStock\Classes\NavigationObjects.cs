﻿using EasyStock.Common;
using EasyStock.Models;
using EasyStock.ReportViews;
using EasyStock.Views;
using EasyStock.Views.Financial;
using EasyStock.Views.Production;
using EasyStock.Views.Sales;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Reflection;

namespace EasyStock.Classes
{

    public static class NavigationObjects
    {


        public static NavigationObject Settings = new NavigationObject("F̲ichier")
        {
            BackColor = ColorTranslator.FromHtml("#546e7a")
        };

        public static NavigationObject CompanyInformations = new NavigationObject("Informations sur l'entreprise", Settings, () => CompanyInfoForm.Instance, begainGroup: true, (WindowActions)100);


        public static NavigationObject BasicInformation = new NavigationObject("Clients et Fournisseurs")
        {
            BackColor = ColorTranslator.FromHtml("#880e4f")
        };

        public static NavigationObject StoresInformation = new NavigationObject("Dépôt", Settings, () => StoresView.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject ProductInformation = new NavigationObject("Articles")
        {
            BackColor = ColorTranslator.FromHtml("#795548")
        };

        public static NavigationObject ProductDefining = new NavigationObject("Ajouter", ProductInformation, () => ProductForm.Instance, begainGroup: false, WindowActions.All);

        public static NavigationObject ProductsDefining = new NavigationObject("Liste des Articles", ProductInformation, () => ProductsForm.Instance, begainGroup: false, (WindowActions)96);

        public static NavigationObject CustomersInformation = new NavigationObject("Clients", BasicInformation, () => CustomersForm.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject VendorsInformation = new NavigationObject("Fournisseurs", BasicInformation, () => VendorsForm.Instance, begainGroup: false, WindowActions.All);

        public static NavigationObject CustomersGroupInformation = new NavigationObject("Clients Catégories", BasicInformation, () => CustomersGroupsForm.Instance, begainGroup: false, WindowActions.All);

        public static NavigationObject VendorsGroupInformation = new NavigationObject("Fournisseurs Catégories", BasicInformation, () => VendorsGroupsForm.Instance, begainGroup: false, WindowActions.All);

        public static NavigationObject BillingDetailsInformation = new NavigationObject("Mode de paiement")
        {
            BackColor = ColorTranslator.FromHtml("#4a148c")
        };

        public static NavigationObject DrawersInformation = new NavigationObject("Tiroirs", BillingDetailsInformation, () => DrawersForm.Instance, begainGroup: false, WindowActions.All);

        public static NavigationObject BanksInformation = new NavigationObject("Banques", BillingDetailsInformation, () => BanksForm.Instance, begainGroup: false, WindowActions.All);

        public static NavigationObject PayCardsInformation = new NavigationObject("Cartes de paiement", BillingDetailsInformation, () => PayCardsForm.Instance, begainGroup: false, WindowActions.All);

        public static NavigationObject Accounting = new NavigationObject("Comptabilité")
        {
            BackColor = ColorTranslator.FromHtml("#311b92")
        };


        public static NavigationObject AccountTry = new NavigationObject("Plan comptable", Accounting, () => AccountsFrom.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject Inventory = new NavigationObject("Inventaire")
        {
            BackColor = ColorTranslator.FromHtml("#1a237e")
        };

        public static NavigationObject OpenBalanceBill = new NavigationObject("Nouvelle facture d'avoir", Inventory, () => OpenBalanceBillForm.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject Sales = new NavigationObject("Ventes")
        {
            BackColor = ColorTranslator.FromHtml("#0d47a1")
        };
        public static NavigationObject NewSalesInvoices = new NavigationObject("Nouvelle facture de vente", Sales, () => SalesInvoiceForm.Instance, begainGroup: false, WindowActions.All)
        {
        };

        public static NavigationObject SalesInvoicesList = new NavigationObject("Liste des factures de vente", Sales, () => SalesInvoicesListView.Instance, begainGroup: false, (WindowActions)120);

        public static NavigationObject Purchase = new NavigationObject("Achats")
        {
            BackColor = ColorTranslator.FromHtml("#01579b")
        };

        public static NavigationObject NewPurchaseInvoices = new NavigationObject("Nouvelle facture d'achat", Purchase, () => PurchaseInvoiceForm.Instance, begainGroup: false, WindowActions.All)
        {
        };
        public static NavigationObject PurchaseInvoicesList = new NavigationObject("Liste des factures d'achat", Purchase, () => PurchaseInvoicesListView.Instance, begainGroup: false, (WindowActions)120);

        public static NavigationObject Finance = new NavigationObject("Finance")
        {
            BackColor = ColorTranslator.FromHtml("#006064")
        };

        public static NavigationObject CashNoteIn = new NavigationObject("Reçu de Versement", Finance, () => CashNoteInView.Instance, begainGroup: true, WindowActions.All)
        {
        };

        public static NavigationObject CashNoteInList = new NavigationObject("Liste des Reçus de Versement", Finance, () => CashNoteInListView.Instance, begainGroup: false, (WindowActions)120);

        public static NavigationObject CashNoteOut = new NavigationObject("Reçu de paiement", Finance, () => CashNoteOutView.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject CashNoteOutList = new NavigationObject("Liste des reçus de paiement", Finance, () => CashNoteOutListView.Instance, begainGroup: false, (WindowActions)120)
        {
        };

        public static NavigationObject AddRevenue = new NavigationObject("Enregistrer les recettes", Finance, () => RevenueEntryView.Instance, begainGroup: true, WindowActions.All)
        {
        };

        public static NavigationObject RevenueList = new NavigationObject("Liste des recettes", Finance, () => RevenueListView.Instance, begainGroup: false, (WindowActions)120);

        public static NavigationObject AddExpense = new NavigationObject("Enregistrer les dépenses", Finance, () => ExpenseEntryView.Instance, begainGroup: true, WindowActions.All)
        {
        };

        public static NavigationObject ExpenseList = new NavigationObject("Liste des dépenses", Finance, () => ExpenseListView.Instance, begainGroup: false, (WindowActions)120);

        public static NavigationObject UserManagement = new NavigationObject("Gestion des utilisateurs", Settings, begainGroup: true);

        public static NavigationObject UserAccessProfileAdd = new NavigationObject("Ajouter un profil d'accès", UserManagement, () => UserAccessProfileView.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject UserAccessProfileList = new NavigationObject("Liste des profils d'accès", UserManagement, () => UserAccessProfileListView.Instance, begainGroup: false, WindowActions.All);

        public static NavigationObject AddUser = new NavigationObject("Ajouter un utilisateur", UserManagement, () => UserView.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject UsersList = new NavigationObject("Liste des utilisateurs", UserManagement, () => UserListView.Instance, begainGroup: false, WindowActions.All);

        public static NavigationObject UserSettingProfileAdd = new NavigationObject("Ajouter un profil de paramètres", UserManagement, () => UserSettingsProfileView.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject UserSettingProfileList = new NavigationObject("Liste des profils de paramètres", UserManagement, () => UserSettingsProfileListView.Instance, begainGroup: false, WindowActions.All);

        public static NavigationObject ProductsCategories = new NavigationObject("Afficher les catégories de produits", ProductInformation, () => ProductCategoryForm.Instance, begainGroup: true, (WindowActions)110);

        public static NavigationObject AddProductsGroup = new NavigationObject("Ajouter un groupe de produits", ProductInformation, () => GroupOfProductsView.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject ProductsGroupsList = new NavigationObject("Liste des groupes de produits", ProductInformation, () => GroupsOfProductsListView.Instance, begainGroup: false, (WindowActions)96);

        public static NavigationObject ProductDamageBill = new NavigationObject("Nouveau bordereau de dommage de produits", Inventory, () => ProductDamageBillForm.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject ProductDamageBillList = new NavigationObject("Liste des bordereaux de dommage de produits", Inventory, () => ProductDamageBillsListView.Instance, begainGroup: false, (WindowActions)120);

        public static NavigationObject NewStockTransfer = new NavigationObject("Nouveau bordereau de transfert de stock", Inventory, () => StockTransferBillForm.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject StockTransferBills = new NavigationObject("Liste des bordereaux de transfert de stock", Inventory, () => StockTransferBillsListView.Instance, begainGroup: false, (WindowActions)120);

        public static NavigationObject NewSalesReturnInvoices = new NavigationObject("Nouvelle facture de retour de vente", Sales, () => SalesReturnInvoiceForm.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject SalesReturnInvoicesList = new NavigationObject("Liste des factures de retour de vente", Sales, () => SalesReturnInvoicesListView.Instance, begainGroup: false, (WindowActions)120);

        public static NavigationObject NewPurchaseReturnInvoices = new NavigationObject("Nouvelle facture de retour d'achat", Purchase, () => PurchaseReturnInvoiceForm.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject PurchaseReturnInvoicesList = new NavigationObject("Liste des factures de retour d'achat", Purchase, () => PurchaseReturnInvoicesListView.Instance, begainGroup: false, (WindowActions)120);

        public static NavigationObject AddJournal = new NavigationObject("Ajouter une écriture comptable", Accounting, () => JournalView.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject ViewJournals = new NavigationObject("Réviser les écritures comptables", Accounting, () => JournalListView.Instance, begainGroup: false, (WindowActions)120);

        public static NavigationObject SystemSettings = new NavigationObject("Paramètres du système", Settings, () => SystemSettingsForm.Instance, begainGroup: true, (WindowActions)100);

        public static NavigationObject AddListOfProducts = new NavigationObject("Ajouter / Importer des articles", ProductInformation, () => AddProductsView.Instance, begainGroup: false, (WindowActions)96);

        public static NavigationObject DrawerDailyLogViewObj = new NavigationObject("Relevé de trésorerie", Accounting, () => DrawerDailyLogView.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject CurrenciesObj = new NavigationObject("Devises", Accounting, () => CurrenciesView.Instance, begainGroup: true, (WindowActions)100);

        public static NavigationObject ReportTemplateViewObj = new NavigationObject("Modèles de rapport", Settings, () => ReportTemplatesView.Instance, begainGroup: true, (WindowActions)96);

        public static NavigationObject PrintBarcodes = new NavigationObject("Imprimer des codes-barres", Inventory, () => PrintBarcodeView.Instance, begainGroup: true, (WindowActions)96);

        public static NavigationObject POSFormObj = new NavigationObject("Point de vente", Sales, () => POSForm.Instance, begainGroup: false, WindowActions.All);

        public static NavigationObject OpenDrawerPeriodOBJ = new NavigationObject("Ouvrir une journée de caisse", Accounting, () => OpenDrawerPeriodForm.Instance, begainGroup: true, (WindowActions)122);

        public static NavigationObject CloseDrawerPeriodFormOBJ = new NavigationObject("Clôture de la caisse", Accounting, () => CloseDrawerPeriodForm.Instance, begainGroup: false, (WindowActions)122);

        public static NavigationObject DrawerPeriodsListOBJ = new NavigationObject("Revue des journaux de caisse", Accounting, () => DrawerPeriodsList.Instance, begainGroup: false, (WindowActions)104);

        public static NavigationObject CashTransferViewOBJ = new NavigationObject("Nouveau transfert d'argent", Accounting, () => CashTransferView.Instance, begainGroup: true, WindowActions.All);
        public static NavigationObject CashTransferListViewOBJ = new NavigationObject("Liste des transferts d'argent", Accounting, () => CashTransferListView.Instance, begainGroup: false, (WindowActions)120);
        public static NavigationObject UserLogViewOBJ = new NavigationObject("Journal des mouvements des utilisateurs", UserManagement, () => UserLogView.Instance, begainGroup: false, (WindowActions)120);

        public static NavigationObject Reports = new NavigationObject("Rapports")
        {
            BackColor = ColorTranslator.FromHtml("#004d40")
        };

        public static NavigationObject AccountReports = new ReportNavigationObject("Comptes")
        {
            BackColor = ColorTranslator.FromHtml("#1b5e20")
        };

        public static NavigationObject StatmentOfAccount = new ReportNavigationObject("Relevé de compte", AccountReports, () => new StatmentOfAccount(), begainGroup: true, "Résumé des transactions récentes effectuées sur un compte pendant une période donnée dans le registre des comptes")
        {
        };

        public static NavigationObject TrialBalance = new ReportNavigationObject("Balance de vérification", AccountReports, () => new TrialBalance(), begainGroup: true, "Liste des comptes à une date donnée montrant les soldes débiteurs et créditeurs des opérations effectuées, utilisée pour détecter des erreurs pouvant conduire à des totaux non équilibrés")
        {
        };

        public static NavigationObject IncomeStatmentReportViewOBJ = new ReportNavigationObject("État des résultats", AccountReports, () => new IncomeStatmentReportView(), begainGroup: true, "Tableau récapitulatif des comptes de résultats montrant les revenus sur une période donnée")
        {
        };

        public static NavigationObject BalanceSheetReportViewOBJ = new ReportNavigationObject("Bilan général", AccountReports, () => new BalanceSheetReportView(), begainGroup: true, "État financier d'une entité à une date spécifique permettant d'obtenir des informations importantes telles que la facilité de l'entité, la nature et la valeur de l'investissement dans le projet, ainsi que les droits des créanciers et des actionnaires par rapport à ces ressources")
        {
        };

        public static NavigationObject MerchandisingAccountStatmentReport = new ReportNavigationObject("État des comptes de marchandisage", AccountReports, () => new MerchandisingAccountStatment(), begainGroup: true, "Compte visant à montrer le résultat d'une activité commerciale en termes de profit ou de perte en analysant les opérations effectuées sur les stocks")
        {
        };

        public static NavigationObject AccountBalanceREportOBJ = new ReportNavigationObject("Rapport des soldes de compte", AccountReports, () => new AccountsBalanceReportView(), begainGroup: true, "État montrant les soldes des comptes dans le livre comptable")
        {
        };

        public static NavigationObject InventoryReports = new ReportNavigationObject("Inventaires")
        {
            BackColor = ColorTranslator.FromHtml("#bc5100")
        };

        public static NavigationObject ProductStoreTransaction = new ReportNavigationObject("Mouvement détaillé des articles", InventoryReports, () => new ProductStoreTransaction(), begainGroup: true, "Rapport montrant les mouvements d'un produit pendant une période spécifique, détaillant toutes les entrées et sorties")
        {
        };
        public static NavigationObject ProductBalanceInStores = new ReportNavigationObject("Soldes des produits dans les magasins", InventoryReports, () => new ProductBalanceInStores(), begainGroup: true, "Permet de connaître les soldes des produits dans les magasins")
        {
        };

        public static NavigationObject ProductBalanceDetailedInStores = new ReportNavigationObject("Soldes détaillés des produits dans les magasins", InventoryReports, () => new ProductBalanceDetailedInStores(), begainGroup: true, "Permet de connaître les soldes détaillés des produits, indiquant le solde de chaque couleur et taille dans l'unité du produit")
        {
        };

        public static NavigationObject ProductReachedReorderLevelReport = new ReportNavigationObject("Produits ayant atteint le niveau de réapprovisionnement", InventoryReports, () => new ProductReachedReorderLevel(), begainGroup: true, "Affiche les produits dont les soldes ont atteint le niveau de réapprovisionnement")
        {
        };

        public static NavigationObject ProductInOutBalanceReport = new ReportNavigationObject("Mouvements d'entrée/sortie des produits", InventoryReports, () => new ProductInOutBalance(), begainGroup: true, "Rapport résumant les opérations d'entrée et de sortie effectuées sur les produits")
        {
        };

        public static NavigationObject SalesProductCostREportOBJ = new ReportNavigationObject("Profit ou perte des produits", InventoryReports, () => new SalesProductCostView(), begainGroup: true, "Rapport indiquant le bénéfice ou la perte des produits sur une période donnée")
        {
        };

        public static NavigationObject ProductBalanceREportOBJ = new ReportNavigationObject("Validité des produits dans les magasins", InventoryReports, () => new ProductStoreValidityReportView(), begainGroup: true, "Rapport indiquant les soldes des produits ayant une validité spécifique, en indiquant le solde à chaque date de validité")
        {
        };

        public static NavigationObject TotalProductTransREportOBJ = new ReportNavigationObject("Total des mouvements des produits dans les magasins", InventoryReports, () => new TotalProductTransactionsReportView(), begainGroup: true);

        public static NavigationObject SalesReports = new ReportNavigationObject("Ventes")
        {
            BackColor = ColorTranslator.FromHtml("#795548")
        };

        public static NavigationObject CustomerOperationsReport = new ReportNavigationObject("Mouvements client", SalesReports, () => new PersonalOperations(PersonalType.Customer), begainGroup: true);

        public static NavigationObject SalesReport = new ReportNavigationObject("Factures de vente", SalesReports, () => new InvoicesListReport(SystemProcess.Sales), begainGroup: true);

        public static NavigationObject SalesReturnReport = new ReportNavigationObject("Factures de retour de vente", SalesReports, () => new InvoicesListReport(SystemProcess.SalesReturn), begainGroup: true);

        public static NavigationObject SalesTaxREportOBJ = new ReportNavigationObject("Déclaration fiscale des factures de vente", SalesReports, () => new SalesTaxsReportView(), begainGroup: true);

        public static NavigationObject SalesCustProductREportOBJ = new ReportNavigationObject("Ventes de produits aux clients", SalesReports, () => new SalesCustomerProductView(), begainGroup: true);

        public static NavigationObject TotalSalesCustProductREportOBJ = new ReportNavigationObject("Total des ventes de produits aux clients", SalesReports, () => new TotalSalesCustomerProductView(), begainGroup: true);

        public static NavigationObject TotalSalesInvoicesREportOBJ = new ReportNavigationObject("Total des factures de vente", SalesReports, () => new TotalSalesInvoicesReportView(), begainGroup: true);

        public static NavigationObject TotalSalesReturnREportOBJ = new ReportNavigationObject("Total des factures de retour de vente", SalesReports, () => new TotalSalesReturnReportView(), begainGroup: true);

        public static NavigationObject SalesProdCatEportOBJ = new ReportNavigationObject("Coûts des ventes par catégories de produits", SalesReports, () => new SalesProductCategoriesCostView(), begainGroup: true);

        public static NavigationObject TotalSalesProdCatEportOBJ = new ReportNavigationObject("Total des ventes par catégories de produits", SalesReports, () => new TotalSalesProductCategoriesView(), begainGroup: true);

        public static NavigationObject SalesProdCatMainEportOBJ = new ReportNavigationObject("Rapport des ventes par catégories de produits", SalesReports, () => new SalesProductCatergoriesReportView(), begainGroup: true);

        public static NavigationObject PurchaseReports = new ReportNavigationObject("Achats")
        {
            BackColor = ColorTranslator.FromHtml("#37474f")
        };
        public static NavigationObject VendorOperationsReport = new ReportNavigationObject("Mouvements du fournisseur", PurchaseReports, () => new PersonalOperations(PersonalType.Vendor), begainGroup: true);

        public static NavigationObject PurchaseReport = new ReportNavigationObject("Factures d'achats", PurchaseReports, () => new InvoicesListReport(SystemProcess.Purchase), begainGroup: true);

        public static NavigationObject PurchaseReturnReport = new ReportNavigationObject("Factures de retour d'achats", PurchaseReports, () => new InvoicesListReport(SystemProcess.PurchaseReturn), begainGroup: true);

        public static NavigationObject PurchaseTaxREportOBJ = new ReportNavigationObject("Déclaration fiscale des factures d'achats", PurchaseReports, () => new PurchaseTaxesReportView(), begainGroup: true);

        public static NavigationObject TotalPurchaseInvoicesREportOBJ = new ReportNavigationObject("Total des factures d'achats", PurchaseReports, () => new TotalPurchaseInvoicesReportView(), begainGroup: true);

        public static NavigationObject TotalPurchaseREportOBJ = new ReportNavigationObject("Total des factures de retour d'achats", PurchaseReports, () => new TotalPurchaseReturnReportView(), begainGroup: true);

        public static NavigationObject Production = new NavigationObject("Production")
        {
            BackColor = ColorTranslator.FromHtml("#C133FF")
        };

        public static NavigationObject BOMList = new NavigationObject("Liste des nomenclatures", Production, () => new BillOfMaterialsListView(), begainGroup: true, WindowActions.All);

        public static NavigationObject BOM = new NavigationObject("Nouvelle nomenclature", Production, () => BillOfMaterialsView.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject WorkOrderList = new NavigationObject("Liste des ordres de fabrication", Production, () => new WorkOrderListView(), begainGroup: true, WindowActions.All);

        public static NavigationObject WorkOrder = new NavigationObject("Nouvel ordre de fabrication", Production, () => WorkOrderView.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject ReportCenter = new NavigationObject("Centre de rapports", Reports, () => ReportCenterView.Instance, begainGroup: true, (WindowActions)96);

        public static NavigationObject CostCenterObj = new NavigationObject("Centres de coûts", Accounting, () => CostCentersView.Instance, begainGroup: true, (WindowActions)96);

        public static NavigationObject BranchesInformation = new NavigationObject("Succursales", Settings, () => BranchesView.Instance, begainGroup: true, (WindowActions)108);

        public static NavigationObject NewStockCorrection = new NavigationObject("Nouvelle correction de balance des stocks", Inventory, () => StockBalanceCorrectionView.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject OpenStockBalanceCorrectionView = new NavigationObject("Révision des corrections de balance des stocks", Inventory, () => StockBalanceCorrectionListView.Instance, begainGroup: false, (WindowActions)96);

        public static NavigationObject ProductSalesSummryFromPurchaseInvoicesOBJ = new ReportNavigationObject("Ventes des produits à partir des factures d'achat", SalesReports, () => new ProductSalesSummryFromPurchaseInvoicesView(), begainGroup: true);

        public static NavigationObject NewSalesOrder = new NavigationObject("Nouvelle commande de vente", Sales, () => SalesOrderView.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject SalesOrdersObj = new NavigationObject("Commandes de vente", Sales, () => SalesOrdersListView.Instance, begainGroup: false, (WindowActions)120);

        public static NavigationObject NewSalesPriceOffer = new NavigationObject("Nouvelle offre de prix de vente", Sales, () => SalesPriceOfferView.Instance, begainGroup: true, WindowActions.All);

        public static NavigationObject SalesPriceOfferObj = new NavigationObject("Offres de prix de vente", Sales, () => SalesPriceOffersListView.Instance, begainGroup: false, (WindowActions)120);

        public static NavigationObject PettycashList = new NavigationObject("Liste de caisse", Finance, () => new PettyCashListView(), begainGroup: true, WindowActions.All);

        public static NavigationObject Pettycash = new NavigationObject("Caisse", Finance, () => new PettyCashView(), begainGroup: true, WindowActions.All);

        public static NavigationObject PettycashHolder = new NavigationObject("Titulaire de la caisse", Finance, () => new PettyCashHolderView(), begainGroup: true, WindowActions.All);

        public static NavigationObject PettycashCloseList = new NavigationObject("Liste de clôture de caisse", Finance, () => new PettyCashCloseOutListView(), begainGroup: true, WindowActions.All);

        public static NavigationObject PettycashClose = new NavigationObject("Clôture de caisse", Finance, () => new PettyCashCloseOutView(), begainGroup: true, WindowActions.All);

        public static List<NavigationObject> AllObjects
        {
            get
            {
                List<NavigationObject> list = new List<NavigationObject>();
                list.AddRange(GetNavigationObjectsFromClassType(typeof(NavigationObjects)));
                NavigationObject HR = new NavigationObject("Ressources Humaines");
                List<NavigationObject> HRList = GetNavigationObjectsFromClassType(typeof(EasyStock.HR.NavigationObjects));
                HRList.ForEach(delegate (NavigationObject x)
                {
                    if (!x.ParentID.HasValue)
                    {
                        x.ParentID = HR.ID;
                    }
                });
                list.AddRange(HRList);
                list.Add(HR);
                return list;
            }
        }

        public static List<ReportNavigationObject> AllReportObjects
        {
            get
            {
                List<ReportNavigationObject> list = new List<ReportNavigationObject>();
                list.AddRange(GetReportNavigationObjectsFromClassType(typeof(NavigationObjects)));
                ReportNavigationObject HR = new ReportNavigationObject("Ressources Humaines");
                List<ReportNavigationObject> HRList = GetReportNavigationObjectsFromClassType(typeof(EasyStock.HR.NavigationObjects));
                HRList.ForEach(delegate (ReportNavigationObject x)
                {
                    if (!x.ParentID.HasValue)
                    {
                        x.ParentID = HR.ID;
                    }
                });
                list.AddRange(HRList);
                list.Add(HR);
                return list;
            }
        }

        public static List<NavigationObject> AllAllowedObjects
        {
            get
            {
                IEnumerable<NavigationObject> q1 = from _003C_003Eh__TransparentIdentifier0 in (from obj in AllObjects
                                                                                                from acc in CurrentSession.CurrentUser.AccessProfile.Details?.Where((UserAccessProfileDetail x) => x.ObjectId == obj.ID).DefaultIfEmpty()
                                                                                                select new { obj, acc }).Where(_003C_003Eh__TransparentIdentifier0 =>
                                                                                                {
                                                                                                    UserAccessProfileDetail acc2 = _003C_003Eh__TransparentIdentifier0.acc;
                                                                                                    return (acc2 != null && acc2.CanShow) || CurrentSession.CurrentUser.Type == UserType.Administrator;
                                                                                                })
                                                   select _003C_003Eh__TransparentIdentifier0.obj;
                return q1.ToList();
            }
        }

        public static List<ReportNavigationObject> AllAllowedReportObjects
        {
            get
            {
                IEnumerable<ReportNavigationObject> q1 = from _003C_003Eh__TransparentIdentifier0 in (from obj in AllReportObjects
                                                                                                      from acc in CurrentSession.CurrentUser.AccessProfile.Details.Where((UserAccessProfileDetail x) => x.ObjectId == obj.ID).DefaultIfEmpty()
                                                                                                      select new { obj, acc }).Where(_003C_003Eh__TransparentIdentifier0 =>
                                                                                                      {
                                                                                                          UserAccessProfileDetail acc2 = _003C_003Eh__TransparentIdentifier0.acc;
                                                                                                          return (acc2 != null && acc2.CanShow) || CurrentSession.CurrentUser.Type == UserType.Administrator;
                                                                                                      })
                                                         select _003C_003Eh__TransparentIdentifier0.obj;
                return q1.ToList();
            }
        }

        private static List<NavigationObject> GetNavigationObjectsFromClassType(Type type)
        {
            FieldInfo[] fields = type.GetFields(BindingFlags.Static | BindingFlags.Public);
            List<NavigationObject> list = new List<NavigationObject>();
            FieldInfo[] array = fields;
            foreach (FieldInfo item in array)
            {
                object obj = item.GetValue(null);
                if (obj != null && obj is NavigationObject && !(obj is ReportNavigationObject))
                {
                    list.Add((NavigationObject)obj);
                }
            }
            return list;
        }

        private static List<ReportNavigationObject> GetReportNavigationObjectsFromClassType(Type type)
        {
            FieldInfo[] fields = type.GetFields(BindingFlags.Static | BindingFlags.Public);
            List<ReportNavigationObject> list = new List<ReportNavigationObject>();
            FieldInfo[] array = fields;
            foreach (FieldInfo item in array)
            {
                object obj = item.GetValue(null);
                if (obj != null && obj is ReportNavigationObject)
                {
                    list.Add((ReportNavigationObject)obj);
                }
            }
            return list;
        }
    }
}
