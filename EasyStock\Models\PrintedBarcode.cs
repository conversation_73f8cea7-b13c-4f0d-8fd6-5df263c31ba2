﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Impression du code-barres")]
    public class PrintedBarcode
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        [Display(Name = "Article")]
        public int ProductID { get; set; }

        [Display(Name = "Unité")]
        public int UnitID { get; set; }

        [Display(Name = "Couleur")]
        public int? ColorID { get; set; }

        [Display(Name = "Taille")]
        public int? SizeID { get; set; }

        [Display(Name = "Série")]
        public string Serial { get; set; }

        [Display(Name = "Date d'expiration")]
        public DateTime? Expire { get; set; }

        [Display(Name = "Quantité")]
        public int Quantity { get; set; }

        [Display(Name = "Type de mouvement")]
        public TransactionType Type { get; set; }

        [Display(Name = "Numéro de document")]
        public int? BillID { get; set; }

        [Display(Name = "Source")]
        public int? SourceID { get; set; }
    }
}
