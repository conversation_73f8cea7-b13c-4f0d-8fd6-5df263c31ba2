﻿using DevExpress.XtraBars;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Reflection;

namespace EasyStock.Classes
{
    public static class CurrentSession
    {
        public static string RemainDays { get;set; }
        public static string Type { get;set; }
        public static string DefualtSettingPath
        {
            get
            {
                string path = Environment.GetFolderPath(Environment.SpecialFolder.Personal);
                string subFolderPath = Path.Combine(path, "EasyStock");
                Directory.CreateDirectory(subFolderPath);
                return subFolderPath;
            }
        }

        public static MoneyToTextModes MoneyToTextMode
        {
            get
            {
                return ERPDataContext.SystemSettings.MoneyToTextMode;
            }
        }

        public static bool CatchBarcode { get; set; } = true;

        public static bool FastBarcode { get; set; } = true;

        public static List<Account> EndNodeAccounts
        {
            get
            {
                List<Account> result;
                using (ERPDataContext db = new ERPDataContext())
                {
                    result = (from p in db.Accounts
                              where (int)p.Secrecy <= (int)CurrentUser.SettingsProfile.AccountSecrecyLevel && (from c in db.Accounts
                                                                                                                              where c.ParentID == (int?)p.ID
                                                                                                                              select c).Count<Account>() == 0
                              select p).ToList<Account>();
                }
                return result;
            }
        }

        public static List<CostCenter> EndNodeCostCenter
        {
            get
            {
                List<CostCenter> result;
                using (ERPDataContext db = new ERPDataContext())
                {
                    result = (from p in db.CostCenters
                              where (from c in db.CostCenters
                                     where c.ParentID == (int?)p.ID
                                     select c).Count<CostCenter>() == 0
                              select p).ToList<CostCenter>();
                }
                return result;
            }
        }

        public static Drawer DefualtDrawer
        {
            get
            {
                bool flag = _defualtDrawer == null;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        _defualtDrawer = db.Drawers.SingleOrDefault((Drawer x) => x.ID == CurrentUser.SettingsProfile.DefaultDrawer);
                    }
                }
                return _defualtDrawer;
            }
        }

        public static Bank DefualtBank
        {
            get
            {
                bool flag = _defualtBank == null;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        _defualtBank = db.Banks.FirstOrDefault<Bank>();
                    }
                }
                return _defualtBank;
            }
        }

        public static PayCard DefualtPayCard
        {
            get
            {
                bool flag = _defualtPayCard == null;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        _defualtPayCard = db.PayCards.Include((PayCard x) => x.Bank).SingleOrDefault((PayCard x) => x.ID == CurrentUser.SettingsProfile.DefaultPayCard);
                    }
                }
                return _defualtPayCard;
            }
        }

        public static Customer DefualtCustomer
        {
            get
            {
                bool flag = _defualtCustomer == null;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        _defualtCustomer = db.Customers.SingleOrDefault((Customer x) => x.ID == CurrentUser.SettingsProfile.DefaultCustomer);
                    }
                }
                return _defualtCustomer;
            }
        }

        public static Vendor DefualtVendor
        {
            get
            {
                bool flag = _defualtVendor == null;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        _defualtVendor = db.Vendors.SingleOrDefault((Vendor x) => x.ID == CurrentUser.SettingsProfile.DefaultVendor);
                    }
                }
                return _defualtVendor;
            }
        }

        public static Store DefualtStore
        {
            get
            {
                bool flag = _defualtStore == null;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        _defualtStore = db.Stores.SingleOrDefault((Store x) => x.ID == CurrentUser.SettingsProfile.DefaultStore);
                    }
                }
                return _defualtStore;
            }
        }

        public static Branch DefaultBranch
        {
            get
            {
                return CurrentBranch;
            }
        }

        public static CompanyInfo CompanyInfo
        {
            get
            {
                return ERPDataContext.CompanyInfo;
            }
        }

        public static SystemSettings SystemSettings
        {
            get
            {
                return ERPDataContext.SystemSettings;
            }
        }

        public static List<ProductColor> ProductColors
        {
            get
            {
                bool flag = _productColors == null;
                if (flag)
                {
                    _productColors = new List<ProductColor>();
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        _productColors.AddRange(db.ProductColors.ToList<ProductColor>());
                    }
                }
                return _productColors;
            }
        }

        public static List<ProductSize> ProductSizes
        {
            get
            {
                bool flag = _productSizes == null;
                if (flag)
                {
                    _productSizes = new List<ProductSize>();
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        _productSizes.AddRange(db.ProductSizes.ToList<ProductSize>());
                    }
                }
                return _productSizes;
            }
        }

        public static IList<Product> AccessableProducts
        {
            get
            {
                return (from x in Products
                        where !x.Suspended && x.SecracyLevel <= CurrentUser.SettingsProfile.ProductSecrecyLevel
                        select x).ToList<Product>();
            }
        }

        public static IList<Account> AccessableAccounts
        {
            get
            {
                IList<Account> result;
                using (ERPDataContext db = new ERPDataContext())
                {
                    result = (from x in db.Accounts
                              where (int)x.Secrecy <= (int)CurrentUser.SettingsProfile.AccountSecrecyLevel
                              select x).AsNoTracking<Account>().ToList<Account>();
                }
                return result;
            }
        }

        public static IList<Branch> AccessableBranches
        {
            get
            {
                IList<Branch> result;
                using (ERPDataContext db = new ERPDataContext())
                {
                    bool canChangeBranch = CurrentUser.SettingsProfile.CanChangeBranch;
                    if (canChangeBranch)
                    {
                        result = db.Branches.AsNoTracking().ToList<Branch>();
                    }
                    else
                    {
                        result = (from x in db.Branches.AsNoTracking()
                                  where x.ID == CurrentUser.SettingsProfile.DefaultBranch
                                  select x).ToList<Branch>();
                    }
                }
                return result;
            }
        }

        public static IList<Vendor> AccessableVendors
        {
            get
            {
                IList<Vendor> result;
                using (ERPDataContext db = new ERPDataContext())
                {
                    result = (from x in db.Vendors.Include((Vendor x) => x.Account)
                              where (int)x.Account.Secrecy <= (int)CurrentUser.SettingsProfile.AccountSecrecyLevel
                              select x).AsNoTracking<Vendor>().ToList<Vendor>();
                }
                return result;
            }
        }

        public static IList<Customer> AccessableCustomers
        {
            get
            {
                IList<Customer> result;
                using (ERPDataContext db = new ERPDataContext())
                {
                    result = (from x in db.Customers.Include((Customer x) => x.Account)
                              where (int)x.Account.Secrecy <= (int)CurrentUser.SettingsProfile.AccountSecrecyLevel
                              select x).AsNoTracking<Customer>().ToList<Customer>();
                }
                return result;
            }
        }

        public static IList<Drawer> AccessableDrawers
        {
            get
            {
                IList<Drawer> result;
                using (ERPDataContext db = new ERPDataContext())
                {
                    result = (from x in db.Drawers.Include((Drawer x) => x.Account)
                              where (int)x.Account.Secrecy <= (int)CurrentUser.SettingsProfile.AccountSecrecyLevel
                              select x).AsNoTracking<Drawer>().ToList<Drawer>();
                }
                return result;
            }
        }

        public static IList<Bank> AccessableBanks
        {
            get
            {
                IList<Bank> result;
                using (ERPDataContext db = new ERPDataContext())
                {
                    result = (from x in db.Banks.Include((Bank x) => x.Account)
                              where (int)x.Account.Secrecy <= (int)CurrentUser.SettingsProfile.AccountSecrecyLevel
                              select x).AsNoTracking<Bank>().ToList<Bank>();
                }
                return result;
            }
        }

        public static IList<PayCard> AccessablePayCards
        {
            get
            {
                IList<PayCard> result;
                using (ERPDataContext db = new ERPDataContext())
                {
                    result = (from x in db.PayCards.Include((PayCard x) => x.Bank).Include((PayCard x) => x.Bank.Account)
                              where (int)x.Bank.Account.Secrecy <= (int)CurrentUser.SettingsProfile.AccountSecrecyLevel
                              select x).AsNoTracking<PayCard>().ToList<PayCard>();
                }
                return result;
            }
        }

        public static BindingList<Product> Products
        {
            get
            {
                bool flag = _products == null;
                if (flag)
                {
                    ReloadProducts = true;
                    _products = new BindingList<Product>();
                }
                bool reloadProducts = ReloadProducts;
                if (reloadProducts)
                {
                    List<Product> products = new List<Product>();
                    ERPDataContext db = new ERPDataContext();
                    var productsQuery = (from X in db.Products.Include((Product x) => x.Units).Include((Product x) => from b in x.Units
                                                                                                                      select b.Barcodes)
                                         select new
                                         {
                                             ID = X.ID,
                                             Name = X.Name,
                                             Code = X.Code,
                                             CompanyID = X.CompanyID,
                                             GroupID = X.CategoryID,
                                             Descreption = X.Descreption,
                                             HasColor = X.HasColor,
                                             HasExpier = X.HasExpier,
                                             HasSerial = X.HasSerial,
                                             HasSize = X.HasSize,
                                             HasWarranty = X.HasWarranty,
                                             PriceIncludeTax = X.PriceIncludeTax,
                                             PurchaseTax = X.PurchaseTax,
                                             CalculateTaxAfterDiscount = X.CalculateTaxAfterDiscount,
                                             SalesTax = X.SalesTax,
                                             SecracyLevel = X.SecracyLevel,
                                             ShelfLife = X.ShelfLife,
                                             Type = X.Type,
                                             WarntyDuration = X.WarntyDuration,
                                             Suspended = X.Suspended,
                                             Units = X.Units,
                                             CustomField1 = X.CustomField1,
                                             CustomField2 = X.CustomField2,
                                             CustomField3 = X.CustomField3,
                                             CustomField4 = X.CustomField4
                                         }).ToList();
                    products = (from X in productsQuery
                                select new Product
                                {
                                    ID = X.ID,
                                    Name = X.Name,
                                    Code = X.Code,
                                    CompanyID = X.CompanyID,
                                    CategoryID = X.GroupID,
                                    Descreption = X.Descreption,
                                    HasColor = X.HasColor,
                                    HasExpier = X.HasExpier,
                                    HasSerial = X.HasSerial,
                                    HasSize = X.HasSize,
                                    HasWarranty = X.HasWarranty,
                                    PriceIncludeTax = X.PriceIncludeTax,
                                    PurchaseTax = X.PurchaseTax,
                                    CalculateTaxAfterDiscount = X.CalculateTaxAfterDiscount,
                                    SalesTax = X.SalesTax,
                                    SecracyLevel = X.SecracyLevel,
                                    ShelfLife = X.ShelfLife,
                                    Type = X.Type,
                                    WarntyDuration = X.WarntyDuration,
                                    Suspended = X.Suspended,
                                    Units = X.Units,
                                    CustomField1 = X.CustomField1,
                                    CustomField2 = X.CustomField2,
                                    CustomField3 = X.CustomField3,
                                    CustomField4 = X.CustomField4
                                }).ToList();
                    _products.Clear();
                    _products.AddRange(products);
                    ReloadProducts = false;
                }
                return _products;
            }
        }

        public static BindingList<ProductUnit> ProductUnits
        {
            get
            {
                bool flag = _productUnits == null;
                if (flag)
                {
                    ReloadProductUnits = true;
                    _productUnits = new BindingList<ProductUnit>();
                }
                bool reloadProductUnits = ReloadProductUnits;
                if (reloadProductUnits)
                {
                    List<ProductUnit> productUnits = new List<ProductUnit>();
                    ERPDataContext db = new ERPDataContext();
                    productUnits = db.ProductUnits.Include((ProductUnit x) => x.UnitName).Include((ProductUnit x) => x.Barcodes).ToList<ProductUnit>();
                    _productUnits.Clear();
                    _productUnits.AddRange(productUnits);
                    ReloadProductUnits = false;
                }
                return _productUnits;
            }
        }

        public static BindingList<ProductUnitBarcode> ProductUnitBarcodes
        {
            get
            {
                bool flag = _productUnitBarcodes == null;
                if (flag)
                {
                    ReloadProductUnitBarcodes = true;
                    _productUnitBarcodes = new BindingList<ProductUnitBarcode>();
                }
                bool reloadProductUnitBarcodes = ReloadProductUnitBarcodes;
                if (reloadProductUnitBarcodes)
                {
                    List<ProductUnitBarcode> productUnitBarcodes = new List<ProductUnitBarcode>();
                    ERPDataContext db = new ERPDataContext();
                    productUnitBarcodes = (from x in db.ProductUnitBarCodes.Include((ProductUnitBarcode x) => x.Unit).Include((ProductUnitBarcode x) => x.Unit.UnitName)
                                           where x.Unit.Product.Suspended == false
                                           select x).ToList<ProductUnitBarcode>();
                    _productUnitBarcodes.Clear();
                    _productUnitBarcodes.AddRange(productUnitBarcodes);
                    ReloadProductUnitBarcodes = false;
                }
                return _productUnitBarcodes;
            }
        }

        public static bool ShowPayFormOnSavingNewInvoices
        {
            get
            {
                return CurrentUser.SettingsProfile.ShowPayFormOnSavingNewInvoices;
            }
        }

        public static PrintMode InvoicePrintMode
        {
            get
            {
                return CurrentUser.SettingsProfile.InvoicePrintMode;
            }
        }

        public static void LoadAll()
        {
            Type t = typeof(CurrentSession);
            PropertyInfo[] propertys = t.GetProperties(BindingFlags.Static | BindingFlags.Public);
            foreach (PropertyInfo item in propertys)
            {
                object obj = item.GetValue(null);
            }
        }

        public static Branch CurrentBranch
        {
            get
            {
                return currentBranch;
            }
            set
            {
                currentBranch = value;
                BarItem currentBranchLable = HomeForm.Instance.CurrentBranchLable;
                Branch branch = currentBranch;
                currentBranchLable.Caption = ((branch != null) ? branch.Name : null);
            }
        }

        public static User CurrentUser
        {
            get
            {
                return currentUser;
            }
            set
            {
                currentUser = value;
                BarItem currentUserLable = HomeForm.Instance.CurrentUserLable;
                User user = currentUser;
                currentUserLable.Caption = ((user != null) ? user.Name : null);
            }
        }

        private static Drawer _defualtDrawer;

        private static Bank _defualtBank;

        private static PayCard _defualtPayCard;

        private static Customer _defualtCustomer;

        private static Vendor _defualtVendor;

        private static Store _defualtStore;

        private static List<ProductColor> _productColors;

        private static List<ProductSize> _productSizes;

        public static bool ReloadProducts = true;

        private static BindingList<Product> _products;

        public static bool ReloadProductUnits = true;

        private static BindingList<ProductUnit> _productUnits;

        public static bool ReloadProductUnitBarcodes = true;

        private static BindingList<ProductUnitBarcode> _productUnitBarcodes;

        private static Branch currentBranch;

        private static User currentUser;
    }
}
