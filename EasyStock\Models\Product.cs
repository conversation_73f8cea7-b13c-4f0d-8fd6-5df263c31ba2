﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using EasyStock.Classes;

namespace EasyStock.Models
{
    [DisplayName("Articles")]
    public class Product : BaseNotifyPropertyChangedModel
    {
        public Product()
        {
            this.Units = new BindingList<ProductUnit>();
            this.Locations = new BindingList<ProductStoreLocation>();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [ReadOnly(true)]
        [Display(Name = "Code", GroupName = "Données")]
        public int ID { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "N° pièce", GroupName = "Données")]
        public string Code { get; set; }

        [StringLength(250)]
        [Required(ErrorMessageResourceName = "ErrorCantBeEmpry", ErrorMessageResourceType = typeof(LangResource))]
        [Display(Name = "Nom", GroupName = "Données")]
        public string Name { get; set; }

        [Display(Name = "Type de Article", GroupName = "Données")]
        public ProductType Type { get; set; }

        [Display(Name = "Description", GroupName = "Données")]
        public string Descreption { get; set; }

        [Display(Name = "Catégorie", GroupName = "Données")]
        public ProductCategory Category { get; set; }

        [Display(Name = "Catégorie", GroupName = "Données")]
        public int? CategoryID { get; set; }

        [Display(Name = "Fabricant", GroupName = "Données")]
        public ProductCompany Company { get; set; }

        [Display(Name = "Fabricant", GroupName = "Données")]
        public int? CompanyID { get; set; }

        [Display(Name = "Pays d'origine", GroupName = "Données")]
        public ProductVendor ProductVendor
        {
            get { return this.productVendor; }
            set { base.SetProperty<ProductVendor>(ref this.productVendor, value, "ProductVendor"); }
        }

        [Display(Name = "Pays d'origine", GroupName = "Données")]
        public int? ProductVendorID
        {
            get { return this.productVendorID; }
            set { base.SetProperty<int?>(ref this.productVendorID, value, "ProductVendorID"); }
        }

        [Display(Name = "Suspendu", GroupName = "Données")]
        public bool Suspended { get; set; }

        [Display(Name = "Image")]
        [Column(TypeName = "image")]
        public byte[] ImageArray { get; set; }

        [NotMapped]
        [Display(Name = "Image du produit", GroupName = "Données/Image du produit")]
        public Image Image
        {
            get
            {
                Image img;
                try
                {
                    if (this.ImageArray == null)
                        return null;

                    MemoryStream strm = new MemoryStream(this.ImageArray, false);
                    img = Image.FromStream(strm);
                }
                catch
                {
                    img = null;
                }
                return img;
            }
            set
            {
                MemoryStream strm = new MemoryStream();
                try
                {
                    if (value != null)
                        value.Save(strm, ImageFormat.Png);

                    this.ImageArray = strm.ToArray();
                }
                catch
                {
                    this.ImageArray = strm.ToArray();
                }
            }
        }
        [Display(Name = "Expiration", GroupName = "{Tabs}/Propriétés de l'article")]
        public bool HasExpier { get; set; }

        [Display(Name = "Série", GroupName = "{Tabs}/Propriétés de l'article")]
        public bool HasSerial { get; set; }

        [Display(Name = "Garantie", GroupName = "{Tabs}/Propriétés de l'article")]
        public bool HasWarranty { get; set; }

        [Display(Name = "Durée de conservation (en jours)", GroupName = "{Tabs}/Propriétés de l'article")]
        public int ShelfLife { get; set; }

        [Display(Name = "Durée de la garantie (en mois)", GroupName = "{Tabs}/Propriétés de l'article")]
        public int WarntyDuration { get; set; }

        [Display(Name = "Couleur", GroupName = "{Tabs}/Propriétés de l'article")]
        public bool HasColor { get; set; }

        [Display(Name = "Taille", GroupName = "{Tabs}/Propriétés de l'article")]
        public bool HasSize { get; set; }

        [Display(Name = "Champ personnalisé 1", GroupName = "{Tabs}/Propriétés de l'article")]
        public string CustomField1 { get; set; }

        [Display(Name = "Champ personnalisé 2", GroupName = "{Tabs}/Propriétés de l'article")]
        public string CustomField2 { get; set; }

        [Display(Name = "Champ personnalisé 3", GroupName = "{Tabs}/Propriétés de l'article")]
        public string CustomField3 { get; set; }

        [Display(Name = "Champ personnalisé 4", GroupName = "{Tabs}/Propriétés de l'article")]
        public string CustomField4 { get; set; }

        [Display(Name = "Niveau de confidentialité")]
        public SecracyLevel SecracyLevel { get; set; }

        [Display(Name = "Unités de mesure", GroupName = "{Tabs}/Unités de mesure")]
        public virtual BindingList<ProductUnit> Units { get; set; }

        [Display(Name = "Stock minimum", GroupName = "{Tabs}/Niveaux de stock")]
        public int MinStockLevel { get; set; }

        [Display(Name = "Stock maximum", GroupName = "{Tabs}/Niveaux de stock")]
        public int MaxStockLevel { get; set; }

        [Display(Name = "Taux de Taxe sur les ventes", GroupName = "{Tabs}/Taxes")]
        [Range(0.0, 0.99, ErrorMessage = "Le Taux de Taxe sur les vente doit être compris entre 0 et 99 %.")]
        public double SalesTax { get; set; }

        [Display(Name = "Taux de Taxe sur les achats", GroupName = "{Tabs}/Taxes")]
        [Range(0.0, 0.99, ErrorMessage = "Le Taux de Taxe sur les achats doit être compris entre 0 et 99 %.")]
        public double PurchaseTax { get; set; }

        [Display(Name = "Calcul de la Taxe après remise", GroupName = "{Tabs}/Taxes")]
        public bool CalculateTaxAfterDiscount { get; set; }

        [Display(Name = "Prix des unités Taxe incluse", GroupName = "{Tabs}/Taxes")]
        public bool PriceIncludeTax { get; set; }

        [Display(Name = "Emplacements du produit")]
        public BindingList<ProductStoreLocation> Locations { get; set; }


        public Product ShallowCopy()
        {
            return (Product)base.MemberwiseClone();
        }

        private ProductVendor productVendor;
        private int? productVendorID;
    }
    }
