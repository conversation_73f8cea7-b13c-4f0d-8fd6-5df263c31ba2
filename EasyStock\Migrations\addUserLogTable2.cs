﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x020004DF RID: 1247
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class addUserLogTable2 : DbMigration, IMigrationMetadata
	{
		// Token: 0x060024DD RID: 9437 RVA: 0x00012EE7 File Offset: 0x000110E7
		public override void Up()
		{
			base.AddColumn("dbo.UserLogs", "Date", (ColumnBuilder c) => c.DateTime(new bool?(false), null, null, null, null, null, null), null);
		}

		// Token: 0x060024DE RID: 9438 RVA: 0x00012F1B File Offset: 0x0001111B
		public override void Down()
		{
			base.DropColumn("dbo.UserLogs", "Date", null);
		}

		// Token: 0x17000C03 RID: 3075
		// (get) Token: 0x060024DF RID: 9439 RVA: 0x001FDF08 File Offset: 0x001FC108
		string IMigrationMetadata.Id
		{
			get
			{
				return "202103221243252_addUserLogTable2";
			}
		}

		// Token: 0x17000C04 RID: 3076
		// (get) Token: 0x060024E0 RID: 9440 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000C05 RID: 3077
		// (get) Token: 0x060024E1 RID: 9441 RVA: 0x001FDF20 File Offset: 0x001FC120
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002BD9 RID: 11225
		private readonly ResourceManager Resources = new ResourceManager(typeof(addUserLogTable2));
	}
}
