﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    [DisplayColumn("Départements")]
    public class Department : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }
        [Display(Name = "Nom")]
        [StringLength(150)]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public string Name
        {
            get
            {
                return this.name;
            }
            set
            {
                base.SetProperty<string>(ref this.name, value, "Name");
            }
        }

        [Display(Name = "Département parent")]
        public int? ParentID
        {
            get
            {
                return this.parentID;
            }
            set
            {
                base.SetProperty<int?>(ref this.parentID, value, "ParentID");
            }
        }

        [Display(Name = "Nom du responsable")]
        [StringLength(150)]
        public string MangerName
        {
            get
            {
                return this.mangerName;
            }
            set
            {
                base.SetProperty<string>(ref this.mangerName, value, "MangerName");
            }
        }

        [Display(Name = "Notes")]
        public string Notes
        {
            get
            {
                return this.notes;
            }
            set
            {
                base.SetProperty<string>(ref this.notes, value, "Notes");
            }
        }

        private int id;

        private string name;

        private int? parentID;

        private string mangerName;

        private string notes;
    }
}
