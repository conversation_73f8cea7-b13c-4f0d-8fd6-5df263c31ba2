﻿using EasyStock.Classes;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.Models
{
    [DisplayName("Détails des écritures de journal")]
    public class JournalDetail : BaseNotifyPropertyChangedModel
    {
        [Key]
        [Display(Name = "Code")]
        public int ID { get; set; }

        public Journal Journal { get; set; }

        public int JournalID { get; set; }

        [Display(Name = "Description")]
        public string Statement { get; set; }

        [Display(Name = "Compte")]
        public Account Account { get; set; }

        [Display(Name = "Numéro de Compte")]
        public int AccountID { get; set; }

        [Display(Name = "Débit")]
        [RequiredIf("Debit", 0.0, false, ErrorMessage = "Une valeur est requise")]
        [Range(0.0, double.MaxValue, ErrorMessage = "La valeur doit être supérieure ou égale à zéro")]
        public double Debit { get; set; }

        [RequiredIf("Debit", 0.0, false, ErrorMessage = "Une valeur est requise")]
        [Range(0.0, double.MaxValue, ErrorMessage = "La valeur doit être supérieure ou égale à zéro")]
        [Display(Name = "Crédit")]
        public double Credit { get; set; }

        [Display(Name = "Débit Local")]
        public double LocalDebit
        {
            get
            {
                return this.Debit * this.CurrencyRate;
            }
        }

        [Display(Name = "Crédit Local")]
        public double LocalCredit
        {
            get
            {
                return this.Credit * this.CurrencyRate;
            }
        }

        [Display(Name = "Code de la Devise")]
        public int CurrencyID { get; set; }

        [Display(Name = "Devise")]
        public Currency Currency { get; set; }

        [Display(Name = "Taux de Change")]
        public double CurrencyRate { get; set; }

        public CostCenter CostCenter { get; set; }

        [Display(Name = "Centre de Coût")]
        public int? CostCenterID
        {
            get
            {
                return this.costCenterID;
            }
            set
            {
                base.SetProperty<int?>(ref this.costCenterID, value, "CostCenterID");
            }
        }

        [Display(Name = "Date d'Échéance")]
        public DateTime? DueDate { get; set; }

        private int? costCenterID;
    }
}
