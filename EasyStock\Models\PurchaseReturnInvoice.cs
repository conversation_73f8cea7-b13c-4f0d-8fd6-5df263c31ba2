﻿using EasyStock.Common;
using EasyStock.Controller;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Factures de retour d'achats")]
    [Table("PurchaseReturnInvoices")]
    public class PurchaseReturnInvoice : Bill
    {
        [Display(Name = "Code source")]
        public int InvoiceID
        {
            get
            {
                return this.invoiceID;
            }
            set
            {
                base.SetProperty<int>(ref this.invoiceID, value, "InvoiceID");
            }
        }

        public PurchaseInvoice Invoice
        {
            get
            {
                bool flag = this.invoice == null || this.invoice.ID != this.InvoiceID;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        this.invoice = db.PurchaseInvoices.SingleOrDefault((PurchaseInvoice x) => x.ID == this.InvoiceID);
                    }
                }
                return this.invoice;
            }
        }

        [Display(Name = "État de la transaction")]
        public TransactionState TransactionState
        {
            get
            {
                return this.transactionState;
            }
            set
            {
                base.SetProperty<TransactionState>(ref this.transactionState, value, "TransactionState");
            }
        }

        public CostCenter CostCenter { get; set; }

        [Display(Name = "Centre de coût")]
        public int? CostCenterID
        {
            get
            {
                return this.costCenterID;
            }
            set
            {
                base.SetProperty<int?>(ref this.costCenterID, value, "CostCenterID");
            }
        }

        public override void GetNewCode()
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                Type sss = base.GetType();
                int maxLength = (from x in db.PurchaseReturnInvoices.AsNoTracking()
                                 select x.Code).Max((string x) => (int?)x.Length).GetValueOrDefault();
                bool flag = maxLength == 0;
                if (flag)
                {
                    base.Code = "1";
                }
                else
                {
                    string maxCode = (from x in db.PurchaseReturnInvoices.AsNoTracking()
                                      where x.Code.Length == maxLength
                                      orderby x.ID descending
                                      select x).FirstOrDefault<PurchaseReturnInvoice>().Code;
                    bool flag2 = maxCode != string.Empty;
                    if (flag2)
                    {
                        base.Code = maxCode.GetNextSequence();
                    }
                }
            }
        }

        [NotMapped]
        public Vendor Vendor
        {
            get
            {
                bool flag = this._customer == null || this._customer.ID != this.VendorID;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        this._customer = db.Vendors.Include((Vendor x) => x.Account).SingleOrDefault((Vendor x) => x.ID == this.VendorID);
                    }
                }
                return this._customer;
            }
            set
            {
                base.SetProperty<Vendor>(ref this._customer, value, "Vendor");
            }
        }

        [Display(Name = "Fournisseur", GroupName = "Données du fournisseur")]
        [Range(1, **********, ErrorMessage = "Le fournisseur doit être sélectionné")]
        public int VendorID
        {
            get
            {
                return this._customerID;
            }
            set
            {
                base.SetProperty<int>(ref this._customerID, value, "VendorID");
            }
        }

        [NotMapped]
        [Display(Name = "Articles", GroupName = "Articles")]
        public BindingList<InvoiceDetail> Details { get; set; }

        [Display(Name = "Montant de la remise", GroupName = "Valeur")]
        public double Discount
        {
            get
            {
                return this._discount;
            }
            set
            {
                base.SetProperty<double>(ref this._discount, value, "Discount");
            }
        }

        [NotMapped]
        [Display(Name = "Pourcentage de remise")]
        [Range(0.0, 0.99, ErrorMessage = "Le pourcentage de remise doit être compris entre 0 et 99 %")]
        public double DiscountPercentage
        {
            get
            {
                bool flag = base.Total == 0.0;
                double result;
                if (flag)
                {
                    result = 0.0;
                }
                else
                {
                    result = this.Discount / base.Total;
                }
                return result;
            }
        }

        [NotMapped]
        [Display(Name = "Net", GroupName = "Valeur")]
        public double Net
        {
            get
            {
                return base.Total + this.Tax + this.OtherExpenses - this.Discount;
            }
        }

        [Display(Name = "Autres coûts", GroupName = "Valeur")]
        public double OtherExpenses
        {
            get
            {
                return this._otherExpenses;
            }
            set
            {
                base.SetProperty<double>(ref this._otherExpenses, value, "OtherExpenses");
            }
        }

        [NotMapped]
        [Display(Name = "Payé", GroupName = "Réglement")]
        public double Paid
        {
            get
            {
                BindingList<PayDetail> payDetails = this.PayDetails;
                double? num;
                if (payDetails == null)
                {
                    num = null;
                }
                else
                {
                    num = payDetails.Sum((PayDetail x) => new double?(x.LocalAmount));
                }
                double? num2 = num;
                return num2.GetValueOrDefault();
            }
        }

        [NotMapped]
        [Display(Name = "Paiements", GroupName = "Réglement")]
        public BindingList<PayDetail> PayDetails { get; set; }

        [NotMapped]
        [Display(Name = "Restant", GroupName = "Réglement")]
        public double Remaining
        {
            get
            {
                return this.Net - this.Paid;
            }
        }

        [Display(Name = "Taxe", GroupName = "Valeur")]
        public double Tax
        {
            get
            {
                return this._tax;
            }
            set
            {
                base.SetProperty<double>(ref this._tax, value, "Tax");
            }
        }

        private int invoiceID;

        [NotMapped]
        private PurchaseInvoice invoice;

        private TransactionState transactionState = TransactionState.Posted;

        private int? costCenterID;

        private Vendor _customer;

        private int _customerID;

        private double _discount;

        private double _otherExpenses;

        private double _tax;
    }
}
