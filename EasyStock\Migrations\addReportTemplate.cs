﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class addReportTemplate : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(addReportTemplate));

        string IMigrationMetadata.Id => "202102121559181_addReportTemplate";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.ReportTemplates", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Name = c.String(),
                Type = c.Int(false),
                Template = c.Binary(),
                IsDefault = c<PERSON>(false)
            }).Primary<PERSON>ey(t => t.ID);
        }

        public override void Down()
        {
            DropTable("dbo.ReportTemplates");
        }
    }
}
