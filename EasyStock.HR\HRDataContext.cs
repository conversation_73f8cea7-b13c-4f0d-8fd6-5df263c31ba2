﻿using EasyStock.HR.Classes;
using EasyStock.HR.Models;
using System.Data.Entity;
using System.Data.Entity.Migrations;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Linq;

namespace EasyStock.HR
{
    public partial class HRDataContext : DbContext
    {
        public HRDataContext() : base(Settings.ConnectionString)
        {
        }
        internal sealed class DataDbInitializer : MigrateDatabaseToLatestVersion<HRDataContext, Migrations.Configuration>
        {
        }

        public virtual DbSet<AbsenceRegulation> AbsenceRegulations { get; set; }

        public virtual DbSet<Department> Departments { get; set; }

        public virtual DbSet<Employee> Employees { get; set; }

        public virtual DbSet<Group> Groups { get; set; }

        public virtual DbSet<Job> Jobs { get; set; }

        public virtual DbSet<OfficialVacation> OfficialVacations { get; set; }

        public virtual DbSet<OvertimeAndDelayRegulation> OvertimeAndDelayRegulations { get; set; }

        public virtual DbSet<OvertimeAndDelayRegulationMinutesTable> OvertimeAndDelayRegulationMinutes { get; set; }

        public virtual DbSet<SalaryExtension> SalaryExtensions { get; set; }

        public virtual DbSet<EmpSalaryExtension> EmpSalaryExtensions { get; set; }

        public virtual DbSet<SalaryRegulation> SalaryRegulations { get; set; }

        public virtual DbSet<SalaryRegulationExtension> SalaryRegulationExtensions { get; set; }

        public virtual DbSet<Shift> Shifts { get; set; }

        public virtual DbSet<ShiftDay> ShiftDays { get; set; }

        public virtual DbSet<TimeTable> TimeTables { get; set; }

        public virtual DbSet<Reference> References { get; set; }

        public virtual DbSet<EmpAbsence> EmpAbsences { get; set; }

        public virtual DbSet<EmpMission> EmpMissions { get; set; }

        public virtual DbSet<EmpOvertimeDelay> EmpOvertimeDelays { get; set; }

        public virtual DbSet<EmpShift> EmpShifts { get; set; }

        public virtual DbSet<EmpVacation> EmpVacations { get; set; }

        public virtual DbSet<EmpAttendance> EmpAttendances { get; set; }

        public virtual DbSet<PenaltyReward> PenaltyRewards { get; set; }

        public virtual DbSet<WorkLeaveReturn> WorkLeaveReturns { get; set; }

        public virtual DbSet<EmpLoan> EmpLoans { get; set; }

        public virtual DbSet<EmpLoanDetails> EmpLoanDetails { get; set; }



        static HRDataContext()
        {
            Database.SetInitializer(new DataDbInitializer());
            Migrations.Configuration configuration = new Migrations.Configuration();
            DbMigrator migrator = new DbMigrator(configuration);
            if (migrator.GetPendingMigrations().Any())
            {
                migrator.Update();
            }
        }

        private static void InitializeDataStore()
        {
            Database.SetInitializer(new MigrateDatabaseToLatestVersion<HRDataContext, Migrations.Configuration>());
        }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            modelBuilder.Conventions.Remove<ManyToManyCascadeDeleteConvention>();
            modelBuilder.Conventions.Remove<OneToManyCascadeDeleteConvention>();
            base.OnModelCreating(modelBuilder);
        }
    }
}
