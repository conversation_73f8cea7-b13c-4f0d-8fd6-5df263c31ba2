﻿namespace EasyStock.HR.Views
{
	public partial class EmpLoanView : global::EasyStock.HR.MainViews.MasterView
	{
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.empLoanDetailsBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.empLoanBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colAmount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPaied = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.DateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.BalanceTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.TotalAmountTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.InstalmentNoTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.BranchIDTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.AccountNoTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.EmpIDTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.NotesTextEdit = new DevExpress.XtraEditors.MemoEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForBranchID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForAccountNo = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForEmpID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForBalance = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForTotalAmount = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForInstalmentNo = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForNotes = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup3 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            ((System.ComponentModel.ISupportInitialize)(this.empLoanDetailsBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.empLoanBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BalanceTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.TotalAmountTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.InstalmentNoTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BranchIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.AccountNoTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmpIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NotesTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBranchID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAccountNo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmpID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBalance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForTotalAmount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForInstalmentNo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNotes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            this.SuspendLayout();
            this.empLoanDetailsBindingSource.DataSource = typeof(EasyStock.HR.Models.EmpLoanDetails);
            this.empLoanBindingSource.DataSource = typeof(EasyStock.HR.Models.EmpLoan);
            this.dataLayoutControl1.Controls.Add(this.gridControl1);
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.DateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.BalanceTextEdit);
            this.dataLayoutControl1.Controls.Add(this.TotalAmountTextEdit);
            this.dataLayoutControl1.Controls.Add(this.InstalmentNoTextEdit);
            this.dataLayoutControl1.Controls.Add(this.BranchIDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.AccountNoTextEdit);
            this.dataLayoutControl1.Controls.Add(this.EmpIDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NotesTextEdit);
            this.dataLayoutControl1.DataSource = this.empLoanBindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 26);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(804, 359);
            this.dataLayoutControl1.TabIndex = 4;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            this.gridControl1.DataSource = this.empLoanDetailsBindingSource;
            this.gridControl1.Location = new System.Drawing.Point(445, 44);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(301, 243);
            this.gridControl1.TabIndex = 13;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colAmount,
            this.colPaied,
            this.colDate});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.colAmount.FieldName = "Amount";
            this.colAmount.Name = "colAmount";
            this.colAmount.Visible = true;
            this.colAmount.VisibleIndex = 0;
            this.colPaied.FieldName = "Paied";
            this.colPaied.Name = "colPaied";
            this.colPaied.Visible = true;
            this.colPaied.VisibleIndex = 1;
            this.colDate.FieldName = "Date";
            this.colDate.Name = "colDate";
            this.colDate.Visible = true;
            this.colDate.VisibleIndex = 2;
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empLoanBindingSource, "ID", true));
            this.IDTextEdit.Location = new System.Drawing.Point(139, 44);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.EditMask = "N0";
            this.IDTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(278, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 4;
            this.DateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empLoanBindingSource, "Date", true));
            this.DateDateEdit.EditValue = null;
            this.DateDateEdit.Location = new System.Drawing.Point(139, 92);
            this.DateDateEdit.Name = "DateDateEdit";
            this.DateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DateDateEdit.Size = new System.Drawing.Size(278, 20);
            this.DateDateEdit.StyleController = this.dataLayoutControl1;
            this.DateDateEdit.TabIndex = 6;
            this.BalanceTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empLoanBindingSource, "Balance", true));
            this.BalanceTextEdit.Location = new System.Drawing.Point(139, 164);
            this.BalanceTextEdit.Name = "BalanceTextEdit";
            this.BalanceTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.BalanceTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.BalanceTextEdit.Properties.Mask.EditMask = "F";
            this.BalanceTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.BalanceTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.BalanceTextEdit.Size = new System.Drawing.Size(278, 20);
            this.BalanceTextEdit.StyleController = this.dataLayoutControl1;
            this.BalanceTextEdit.TabIndex = 9;
            this.TotalAmountTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empLoanBindingSource, "TotalAmount", true));
            this.TotalAmountTextEdit.Location = new System.Drawing.Point(139, 188);
            this.TotalAmountTextEdit.Name = "TotalAmountTextEdit";
            this.TotalAmountTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.TotalAmountTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.TotalAmountTextEdit.Properties.Mask.EditMask = "F";
            this.TotalAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.TotalAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.TotalAmountTextEdit.Size = new System.Drawing.Size(278, 20);
            this.TotalAmountTextEdit.StyleController = this.dataLayoutControl1;
            this.TotalAmountTextEdit.TabIndex = 10;
            this.InstalmentNoTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empLoanBindingSource, "InstalmentNo", true));
            this.InstalmentNoTextEdit.Location = new System.Drawing.Point(139, 212);
            this.InstalmentNoTextEdit.Name = "InstalmentNoTextEdit";
            this.InstalmentNoTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.InstalmentNoTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.InstalmentNoTextEdit.Properties.Mask.EditMask = "N0";
            this.InstalmentNoTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.InstalmentNoTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.InstalmentNoTextEdit.Size = new System.Drawing.Size(278, 20);
            this.InstalmentNoTextEdit.StyleController = this.dataLayoutControl1;
            this.InstalmentNoTextEdit.TabIndex = 11;
            this.BranchIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empLoanBindingSource, "BranchID", true));
            this.BranchIDTextEdit.Location = new System.Drawing.Point(139, 68);
            this.BranchIDTextEdit.Name = "BranchIDTextEdit";
            this.BranchIDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.BranchIDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.BranchIDTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.BranchIDTextEdit.Properties.NullText = "";
            this.BranchIDTextEdit.Properties.PopupSizeable = false;
            this.BranchIDTextEdit.Size = new System.Drawing.Size(278, 20);
            this.BranchIDTextEdit.StyleController = this.dataLayoutControl1;
            this.BranchIDTextEdit.TabIndex = 5;
            this.AccountNoTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empLoanBindingSource, "AccountNo", true));
            this.AccountNoTextEdit.Location = new System.Drawing.Point(139, 116);
            this.AccountNoTextEdit.Name = "AccountNoTextEdit";
            this.AccountNoTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.AccountNoTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.AccountNoTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.AccountNoTextEdit.Properties.NullText = "";
            this.AccountNoTextEdit.Size = new System.Drawing.Size(278, 20);
            this.AccountNoTextEdit.StyleController = this.dataLayoutControl1;
            this.AccountNoTextEdit.TabIndex = 7;
            this.EmpIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empLoanBindingSource, "EmpID", true));
            this.EmpIDTextEdit.Location = new System.Drawing.Point(139, 140);
            this.EmpIDTextEdit.Name = "EmpIDTextEdit";
            this.EmpIDTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.EmpIDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.EmpIDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.EmpIDTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.EmpIDTextEdit.Properties.NullText = "";
            this.EmpIDTextEdit.Size = new System.Drawing.Size(278, 20);
            this.EmpIDTextEdit.StyleController = this.dataLayoutControl1;
            this.EmpIDTextEdit.TabIndex = 8;
            this.NotesTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empLoanBindingSource, "Notes", true));
            this.NotesTextEdit.Location = new System.Drawing.Point(139, 236);
            this.NotesTextEdit.Name = "NotesTextEdit";
            this.NotesTextEdit.Size = new System.Drawing.Size(278, 51);
            this.NotesTextEdit.StyleController = this.dataLayoutControl1;
            this.NotesTextEdit.TabIndex = 12;
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(804, 359);
            this.Root.TextVisible = false;
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup2,
            this.emptySpaceItem1,
            this.emptySpaceItem2,
            this.layoutControlGroup3});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(784, 339);
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForID,
            this.ItemForBranchID,
            this.ItemForDate,
            this.ItemForAccountNo,
            this.ItemForEmpID,
            this.ItemForBalance,
            this.ItemForTotalAmount,
            this.ItemForInstalmentNo,
            this.ItemForNotes});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(421, 291);
            this.layoutControlGroup2.Text = "Informations";
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.MaxSize = new System.Drawing.Size(397, 24);
            this.ItemForID.MinSize = new System.Drawing.Size(397, 24);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(397, 24);
            this.ItemForID.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForID.TextSize = new System.Drawing.Size(111, 13);
            this.ItemForBranchID.Control = this.BranchIDTextEdit;
            this.ItemForBranchID.Location = new System.Drawing.Point(0, 24);
            this.ItemForBranchID.Name = "ItemForBranchID";
            this.ItemForBranchID.Size = new System.Drawing.Size(397, 24);
            this.ItemForBranchID.TextSize = new System.Drawing.Size(111, 13);
            this.ItemForDate.Control = this.DateDateEdit;
            this.ItemForDate.Location = new System.Drawing.Point(0, 48);
            this.ItemForDate.Name = "ItemForDate";
            this.ItemForDate.Size = new System.Drawing.Size(397, 24);
            this.ItemForDate.TextSize = new System.Drawing.Size(111, 13);
            this.ItemForAccountNo.Control = this.AccountNoTextEdit;
            this.ItemForAccountNo.Location = new System.Drawing.Point(0, 72);
            this.ItemForAccountNo.Name = "ItemForAccountNo";
            this.ItemForAccountNo.Size = new System.Drawing.Size(397, 24);
            this.ItemForAccountNo.TextSize = new System.Drawing.Size(111, 13);
            this.ItemForEmpID.Control = this.EmpIDTextEdit;
            this.ItemForEmpID.Location = new System.Drawing.Point(0, 96);
            this.ItemForEmpID.Name = "ItemForEmpID";
            this.ItemForEmpID.Size = new System.Drawing.Size(397, 24);
            this.ItemForEmpID.TextSize = new System.Drawing.Size(111, 13);
            this.ItemForBalance.Control = this.BalanceTextEdit;
            this.ItemForBalance.Location = new System.Drawing.Point(0, 120);
            this.ItemForBalance.Name = "ItemForBalance";
            this.ItemForBalance.Size = new System.Drawing.Size(397, 24);
            this.ItemForBalance.TextSize = new System.Drawing.Size(111, 13);
            this.ItemForTotalAmount.Control = this.TotalAmountTextEdit;
            this.ItemForTotalAmount.Location = new System.Drawing.Point(0, 144);
            this.ItemForTotalAmount.Name = "ItemForTotalAmount";
            this.ItemForTotalAmount.Size = new System.Drawing.Size(397, 24);
            this.ItemForTotalAmount.TextSize = new System.Drawing.Size(111, 13);
            this.ItemForInstalmentNo.Control = this.InstalmentNoTextEdit;
            this.ItemForInstalmentNo.Location = new System.Drawing.Point(0, 168);
            this.ItemForInstalmentNo.Name = "ItemForInstalmentNo";
            this.ItemForInstalmentNo.Size = new System.Drawing.Size(397, 24);
            this.ItemForInstalmentNo.TextSize = new System.Drawing.Size(111, 13);
            this.ItemForNotes.Control = this.NotesTextEdit;
            this.ItemForNotes.Location = new System.Drawing.Point(0, 192);
            this.ItemForNotes.MaxSize = new System.Drawing.Size(397, 55);
            this.ItemForNotes.MinSize = new System.Drawing.Size(397, 55);
            this.ItemForNotes.Name = "ItemForNotes";
            this.ItemForNotes.Size = new System.Drawing.Size(397, 55);
            this.ItemForNotes.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForNotes.TextSize = new System.Drawing.Size(111, 13);
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 291);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(784, 48);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlGroup3.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem1});
            this.layoutControlGroup3.Location = new System.Drawing.Point(421, 0);
            this.layoutControlGroup3.Name = "layoutControlGroup3";
            this.layoutControlGroup3.Size = new System.Drawing.Size(329, 291);
            this.layoutControlGroup3.Text = "Versements";
            this.layoutControlItem1.Control = this.gridControl1;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(305, 247);
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(750, 0);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(34, 291);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.AutoValidate = System.Windows.Forms.AutoValidate.EnableAllowFocusChange;
            this.ClientSize = new System.Drawing.Size(804, 409);
            this.Controls.Add(this.dataLayoutControl1);
            this.Name = "EmpLoanView";
            this.Text = "Avance de l\'employé";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.empLoanDetailsBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.empLoanBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BalanceTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.TotalAmountTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.InstalmentNoTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BranchIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.AccountNoTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmpIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NotesTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBranchID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAccountNo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmpID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBalance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForTotalAmount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForInstalmentNo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNotes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		private global::System.ComponentModel.IContainer components = null;

		private global::System.Windows.Forms.BindingSource empLoanBindingSource;

		private global::System.Windows.Forms.BindingSource empLoanDetailsBindingSource;

		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		private global::DevExpress.XtraEditors.DateEdit DateDateEdit;

		private global::DevExpress.XtraEditors.TextEdit BalanceTextEdit;

		private global::DevExpress.XtraEditors.TextEdit TotalAmountTextEdit;

		private global::DevExpress.XtraEditors.TextEdit InstalmentNoTextEdit;

		private global::DevExpress.XtraEditors.LookUpEdit BranchIDTextEdit;

		private global::DevExpress.XtraEditors.LookUpEdit AccountNoTextEdit;

		private global::DevExpress.XtraEditors.LookUpEdit EmpIDTextEdit;

		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForBranchID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDate;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForAccountNo;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForEmpID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForBalance;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForTotalAmount;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForInstalmentNo;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForNotes;

		private global::DevExpress.XtraGrid.GridControl gridControl1;

		private global::DevExpress.XtraGrid.Views.Grid.GridView gridView1;

		private global::DevExpress.XtraEditors.MemoEdit NotesTextEdit;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;

		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup3;

		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;

		private global::DevExpress.XtraGrid.Columns.GridColumn colAmount;

		private global::DevExpress.XtraGrid.Columns.GridColumn colPaied;

		private global::DevExpress.XtraGrid.Columns.GridColumn colDate;

		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;
	}
}
