﻿using DevExpress.XtraBars.Docking2010.Views;
using DevExpress.XtraEditors;
using EasyStock.Controls;
using System.Windows.Forms;

namespace EasyStock.MainViews
{
    public partial class HomeScreen : XtraForm
    {
        private static HomeScreen instance;
        public static HomeScreen Instance
        {
            get
            {
                if (instance == null || instance.IsDisposed)
                {
                    instance = new HomeScreen();
                }
                return instance;
            }
        }

        public HomeScreen()
        {
            InitializeComponent();
            widgetView1.QueryControl += widgetView1_QueryControl;
        }

        private void widgetView1_QueryControl(object sender, QueryControlEventArgs e)
        {
            if (e.Document == frequentlyUsedTileControlDocument)
            {
                e.Control = new FrequentlyUsedTileControl();
            }
            if (e.Document == chartsViewDocument)
            {
                e.Control = new ChartsView();
            }
            if (e.Control == null)
            {
                e.Control = new Control();
            }
        }
    }
}
