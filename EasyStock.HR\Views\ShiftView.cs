﻿using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Base;
using EasyStock.Common;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.ComponentModel;
using System.Data.Entity;
using System.Data.Entity.Migrations;
using System.Globalization;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class ShiftView : MasterView
    {
        public static ShiftView Instance
        {
            get
            {
                bool flag = ShiftView.instance == null || ShiftView.instance.IsDisposed;
                if (flag)
                {
                    ShiftView.instance = new ShiftView();
                }
                return ShiftView.instance;
            }
        }

        public Shift shift
        {
            get
            {
                return this.shiftBindingSource.Current as Shift;
            }
            set
            {
                this.shiftBindingSource.DataSource = value;
                this.gridControlShiftDay.DataSource = this.shift.ShiftDays;
            }
        }

        public ShiftView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            this.context = new HRDataContext();
            this.gridView1.AddEditButton(new EventHandler(this.RepositoryItemButtonEdit1_Click));
            this.gridView1.SetAlternatingColors();
            base.Shown += this.ShiftView_Shown;
        }

        private void ShiftView_Shown(object sender, EventArgs e)
        {
            bool flag = this.shift == null;
            if (flag)
            {
                this.New();
            }
        }

        private void RepositoryItemButtonEdit1_Click(object sender, EventArgs e)
        {
            Shift row = this.gridView1.GetRow(this.gridView1.FocusedRowHandle) as Shift;
            bool flag = row != null;
            if (flag)
            {
                this.GoTo(row.ID);
            }
        }

        public void GoTo(int id)
        {
            Shift sourceDepr = this.context.Shifts.Include((Shift x) => x.ShiftDays).SingleOrDefault((Shift x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.shift = sourceDepr;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable");
            }
        }

        public override void New()
        {
            this.shift = new Shift
            {
                ShiftDays = new BindingList<ShiftDay>()
            };
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.context.Shifts.AddOrUpdate(new Shift[]
                {
                    this.shift
                });
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.gridControl1.DataSource = this.context.Shifts.ToList<Shift>();
            base.RefreshData();
        }

        public override void Delete()
        {
        }

        private void ShiftDays()
        {
            int DayNo = 0;
            try
            {
                DayNo = (int)this.RepeatEveryTextEdit.EditValue;
            }
            catch
            {
                DayNo = 0;
            }
            int RowCount = this.gridViewShiftDay.RowCount;
            for (int i = 0; i < DayNo; i++)
            {
                this.gridViewShiftDay.AddNewRow();
                this.gridViewShiftDay.SetRowCellValue(i, this.gridViewShiftDay.Columns[0], i);
            }
        }

        private void RepeatEveryTextEdit_EditValueChanged(object sender, EventArgs e)
        {
            this.ShiftDays();
        }

        private void gridViewShiftDay_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            DateTime StartDate = DateTime.Now;
            try
            {
                StartDate = (DateTime)this.StartDateDateEdit.EditValue;
            }
            catch
            {
                StartDate = DateTime.Now;
            }
            bool flag = e.Column.FieldName == "Day";
            if (flag)
            {
                StartDate = StartDate.AddDays((double)((int)e.Value));
                e.DisplayText = StartDate.ToString("dddd", new CultureInfo("ar-AE"));
            }
        }

        private void StartDateDateEdit_EditValueChanged(object sender, EventArgs e)
        {
        }

        private static ShiftView instance;

        private HRDataContext context;
    }
}
