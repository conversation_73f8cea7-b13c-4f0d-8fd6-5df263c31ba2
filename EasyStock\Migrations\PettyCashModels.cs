﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class PettyCashModels : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(PettyCashModels));

        string IMigrationMetadata.Id => "202108151403494_PettyCashModels";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.PettyCashCloseOut", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                PettyCashID = c.Int(false),
                AmountSpent = c.Double(false),
                DateClosed = c.DateTime(false),
                CloseAccountType = c.Int(false),
                CloseAccountID = c.Int(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.PettyCashes", t => t.PettyCashID).Index(t => t.PettyCashID);
            CreateTable("dbo.PettyCashes", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                BranchID = c.Int(false),
                Type = c.Int(false),
                HolderID = c.Int(false),
                Name = c.String(false, 150),
                CostCenter = c.Int(),
                Amount = c.Double(false),
                CurrencyID = c.Int(false),
                IsClosed = c.Boolean(false),
                Notes = c.String()
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Branches", t => t.BranchID).ForeignKey("dbo.PettyCashHolders", t => t.HolderID)
                .Index(t => t.BranchID)
                .Index(t => t.HolderID);
            CreateTable("dbo.PettyCashHolders", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Name = c.String(false, 150),
                MaxCash = c.Double(false),
                AccountID = c.Int(false),
                Disable = c.Boolean(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Accounts", t => t.AccountID).Index(t => t.AccountID);
        }

        public override void Down()
        {
            DropForeignKey("dbo.PettyCashCloseOut", "PettyCashID", "dbo.PettyCashes");
            DropForeignKey("dbo.PettyCashes", "HolderID", "dbo.PettyCashHolders");
            DropForeignKey("dbo.PettyCashHolders", "AccountID", "dbo.Accounts");
            DropForeignKey("dbo.PettyCashes", "BranchID", "dbo.Branches");
            DropIndex("dbo.PettyCashHolders", new string[1] { "AccountID" });
            DropIndex("dbo.PettyCashes", new string[1] { "HolderID" });
            DropIndex("dbo.PettyCashes", new string[1] { "BranchID" });
            DropIndex("dbo.PettyCashCloseOut", new string[1] { "PettyCashID" });
            DropTable("dbo.PettyCashHolders");
            DropTable("dbo.PettyCashes");
            DropTable("dbo.PettyCashCloseOut");
        }
    }
}
