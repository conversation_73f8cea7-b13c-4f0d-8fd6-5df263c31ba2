﻿using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using EasyStock.Common;
using EasyStock.HR.BLL;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class EmpMissionView : MasterView
    {
        public static EmpMissionView Instance
        {
            get
            {
                bool flag = EmpMissionView.instance == null || EmpMissionView.instance.IsDisposed;
                if (flag)
                {
                    EmpMissionView.instance = new EmpMissionView();
                }
                return EmpMissionView.instance;
            }
        }

        public EmpMission mission
        {
            get
            {
                return this.empMissionBindingSource.Current as EmpMission;
            }
            set
            {
                this.empMissionBindingSource.DataSource = value;
            }
        }

        public EmpMissionView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            this.context = new HRDataContext();
            this.FromDateDateEdit.EditValueChanged += this.FromDateDateEdit_EditValueChanged;
            this.ToDateDateEdit.EditValueChanged += this.ToDateDateEdit_EditValueChanged;
            base.Shown += this.EmpAbsenceView_Shown;
        }

        private void ToDateDateEdit_EditValueChanged(object sender, EventArgs e)
        {
            this.getDuration();
        }

        private void FromDateDateEdit_EditValueChanged(object sender, EventArgs e)
        {
            this.getDuration();
        }

        private void EmpAbsenceView_Shown(object sender, EventArgs e)
        {
            bool flag = this.mission == null;
            if (flag)
            {
                this.New();
            }
        }

        private void getDuration()
        {
            int days = (this.mission.ToDate - this.mission.FromDate).Days + 1;
            this.mission.Duration = days;
        }

        public override void New()
        {
            this.mission = new EmpMission
            {
                FromDate = this.context.GetServerTime(),
                ToDate = this.context.GetServerTime()
            };
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.getDuration();
                this.context.EmpMissions.AddOrUpdate(new EmpMission[]
                {
                    this.mission
                });
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public void GoTo(int id)
        {
            EmpMission sourceDepr = this.context.EmpMissions.SingleOrDefault((EmpMission x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.mission = sourceDepr;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable");
            }
        }

        public override void RefreshData()
        {
            this.EmpIDTextEdit.Properties.DataSource = EmployeeBLL.GetActive();
            this.EmpIDTextEdit.Properties.DisplayMember = "Name";
            this.EmpIDTextEdit.Properties.ValueMember = "ID";
            this.EmpIDTextEdit.Properties.NullText = "";
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("ID", "Code"));
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Nom"));
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("PayPeriod", "Période de paie"));

            base.RefreshData();
        }

        public override void Delete()
        {
        }

        private static EmpMissionView instance;

        private HRDataContext context;
    }
}
