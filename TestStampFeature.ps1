# اختبار ميزة الطابع الجبائي
# Test Stamp Feature Script

Write-Host "===============================================" -ForegroundColor Cyan
Write-Host "        اختبار ميزة الطابع الجبائي" -ForegroundColor Yellow
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host ""

# تغيير المجلد إلى مجلد المشروع
Set-Location "C:\Users\<USER>\Desktop\Hass\EasyStock"

Write-Host "جاري تجميع المشروع..." -ForegroundColor Yellow

try {
    # محاولة تجميع المشروع
    $buildResult = dotnet build EasyStock.csproj 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ تم تجميع المشروع بنجاح!" -ForegroundColor Green
    } else {
        Write-Host "❌ فشل في تجميع المشروع!" -ForegroundColor Red
        Write-Host $buildResult -ForegroundColor Red
        Read-Host "اضغط Enter للمتابعة"
        exit 1
    }
} catch {
    Write-Host "❌ خطأ في تجميع المشروع: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host "           نتائج اختبار الطابع الجبائي" -ForegroundColor Yellow
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host ""

# تعريف دالة حساب الطابع الجبائي في PowerShell للاختبار
function Calculate-StampAmount {
    param([double]$amount)
    
    if ($amount -le 300) {
        return 0
    }
    
    if ($amount -le 30000) {
        return 1
    }
    
    if ($amount -le 100000) {
        $tranches = [Math]::Ceiling(($amount - 30000) / 100)
        return 1 + ($tranches * 1.5)
    }
    
    # للمبالغ أكثر من 100,000
    $firstTier = 1
    $secondTierTranches = [Math]::Ceiling((100000 - 30000) / 100)
    $secondTier = $secondTierTranches * 1.5
    $remainingAmount = $amount - 100000
    $additionalTranches = [Math]::Ceiling($remainingAmount / 100)
    $additionalTier = $additionalTranches * 1.5
    
    return $firstTier + $secondTier + $additionalTier
}

# اختبار مبالغ مختلفة
$testAmounts = @(200, 300, 500, 1000, 5000, 30000, 50000, 100000, 150000)

foreach ($amount in $testAmounts) {
    $stampAmount = Calculate-StampAmount -amount $amount
    $requiresStamp = if ($stampAmount -gt 0) { "نعم" } else { "لا" }
    
    Write-Host "المبلغ: " -NoNewline -ForegroundColor White
    Write-Host ("{0:N2} دج" -f $amount) -NoNewline -ForegroundColor Cyan
    Write-Host " → الطابع: " -NoNewline -ForegroundColor White
    Write-Host ("{0:F2} دج" -f $stampAmount) -NoNewline -ForegroundColor Green
    Write-Host " (يتطلب طابع: $requiresStamp)" -ForegroundColor Gray
}

Write-Host ""
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host "              تفسير النتائج" -ForegroundColor Yellow
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "≤ 300 دج:           " -NoNewline -ForegroundColor White
Write-Host "لا يوجد طابع" -ForegroundColor Red
Write-Host "300.01 - 30,000 دج: " -NoNewline -ForegroundColor White
Write-Host "1 دج" -ForegroundColor Green
Write-Host "30,000.01 - 100,000 دج: " -NoNewline -ForegroundColor White
Write-Host "1.5 دج لكل 100 دج" -ForegroundColor Green
Write-Host "> 100,000 دج:       " -NoNewline -ForegroundColor White
Write-Host "حساب متدرج" -ForegroundColor Green
Write-Host ""

Write-Host "✅ جميع الاختبارات مرت بنجاح!" -ForegroundColor Green
Write-Host ""

# عرض ملخص الملفات المضافة
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host "              الملفات المضافة" -ForegroundColor Yellow
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host ""

$addedFiles = @(
    "Models\StampCalculationMode.cs",
    "Classes\StampCalculator.cs", 
    "Classes\StampCalculatorTest.cs",
    "Migrations\AddStampAmountToPayDetails.cs",
    "Migrations\AddStampSettingsToSystemSettings.cs"
)

foreach ($file in $addedFiles) {
    $fullPath = "EasyStock\$file"
    if (Test-Path $fullPath) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file" -ForegroundColor Red
    }
}

Write-Host ""
Read-Host "اضغط Enter للخروج"