using EasyStock.Controller;
using EasyStock.Models;
using System;

namespace EasyStock.Classes
{
    public static class StampCalculator
    {
        /// <summary>
        /// حساب الطابع الجبائي حسب القانون الجزائري
        /// </summary>
        /// <param name="amount">المبلغ المدفوع نقداً</param>
        /// <returns>قيمة الطابع الجبائي</returns>
        public static double CalculateStampAmount(double amount)
        {
            var settings = ERPDataContext.SystemSettings;
            
            if (settings.StampCalculationMode == StampCalculationMode.FixedAmount)
            {
                return settings.StampAmount;
            }
            
            // تطبيق القانون الجزائري للطابع الجبائي
            return CalculateAlgerianStamp(amount);
        }
        
        /// <summary>
        /// حساب الطابع الجبائي حسب القانون الجزائري
        /// - أقل من أو يساوي 300 دج: لا يوجد طابع
        /// - من 300.01 إلى 30,000 دج: 1 دج
        /// - من 30,000.01 إلى 100,000 دج: 1.5 دج لكل 100 دج أو جزء منها
        /// - أكثر من 100,000 دج: حساب متدرج
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>قيمة الطابع الجبائي</returns>
        private static double CalculateAlgerianStamp(double amount)
        {
            if (amount <= 300)
            {
                return 0; // لا يوجد طابع للمبالغ 300 دج أو أقل
            }
            
            if (amount <= 30000)
            {
                return 1; // 1 دج للمبالغ من 300.01 إلى 30,000 دج
            }
            
            if (amount <= 100000)
            {
                // 1.5 دج لكل 100 دج أو جزء منها للمبالغ من 30,000.01 إلى 100,000 دج
                double tranches = Math.Ceiling((double)(amount - 30000) / 100.0);
                return 1 + (tranches * 1.5); // 1 دج للشريحة الأولى + 1.5 دج لكل شريحة إضافية
            }
            
            // للمبالغ أكثر من 100,000 دج
            // الشريحة الأولى: 1 دج (من 300.01 إلى 30,000)
            // الشريحة الثانية: 1.5 دج لكل 100 دج (من 30,000.01 إلى 100,000)
            double firstTier = 1;
            double secondTierTranches = Math.Ceiling((double)(100000 - 30000) / 100.0);
            double secondTier = secondTierTranches * 1.5;
            
            // للمبالغ الإضافية فوق 100,000 دج (نفترض نفس المعدل)
            double remainingAmount = amount - 100000;
            double additionalTranches = Math.Ceiling((double)remainingAmount / 100.0);
            double additionalTier = additionalTranches * 1.5;
            
            return firstTier + secondTier + additionalTier;
        }
        
        /// <summary>
        /// تحديد ما إذا كان المبلغ يتطلب طابع جبائي
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>true إذا كان يتطلب طابع جبائي</returns>
        public static bool RequiresStamp(double amount)
        {
            var settings = ERPDataContext.SystemSettings;
            
            if (settings.StampCalculationMode == StampCalculationMode.FixedAmount)
            {
                return settings.StampAmount > 0;
            }
            
            return amount > 300; // حسب القانون الجزائري
        }
        
        /// <summary>
        /// الحصول على وصف تفصيلي لحساب الطابع الجبائي
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>وصف الحساب</returns>
        public static string GetStampCalculationDescription(double amount)
        {
            var settings = ERPDataContext.SystemSettings;
            
            if (settings.StampCalculationMode == StampCalculationMode.FixedAmount)
            {
                return $"طابع جبائي ثابت: {settings.StampAmount:F2} دج";
            }
            
            if (amount <= 300)
            {
                return "لا يوجد طابع جبائي (المبلغ ≤ 300 دج)";
            }
            
            if (amount <= 30000)
            {
                return "طابع جبائي: 1.00 دج (المبلغ ≤ 30,000 دج)";
            }
            
            if (amount <= 100000)
            {
                double tranches = Math.Ceiling((double)(amount - 30000) / 100.0);
                double stampAmount = 1 + (tranches * 1.5);
                return $"طابع جبائي: {stampAmount:F2} دج (1 دج + {tranches} × 1.5 دج)";
            }
            
            double calculatedStamp = CalculateAlgerianStamp(amount);
            return $"طابع جبائي: {calculatedStamp:F2} دج (حساب متدرج)";
        }
    }
}