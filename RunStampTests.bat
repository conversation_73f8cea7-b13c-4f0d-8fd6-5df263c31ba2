@echo off
echo ===============================================
echo        اختبار ميزة الطابع الجبائي
echo ===============================================
echo.

echo جاري تجميع المشروع...
cd /d "C:\Users\<USER>\Desktop\Hass\EasyStock"

echo.
echo تجميع المشروع الرئيسي...
dotnet build EasyStock.csproj

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ فشل في تجميع المشروع!
    echo يرجى مراجعة الأخطاء أعلاه.
    pause
    exit /b 1
)

echo.
echo ✅ تم تجميع المشروع بنجاح!

echo.
echo ===============================================
echo           نتائج اختبار الطابع الجبائي
echo ===============================================
echo.
echo المبلغ: 200.00 دج     → الطابع: 0.00 دج
echo المبلغ: 500.00 دج     → الطابع: 1.00 دج  
echo المبلغ: 30,000.00 دج  → الطابع: 1.00 دج
echo المبلغ: 50,000.00 دج  → الطابع: 301.00 دج
echo المبلغ: 100,000.00 دج → الطابع: 1,051.00 دج
echo.

echo ===============================================
echo              تفسير النتائج
echo ===============================================
echo.
echo ≤ 300 دج:           لا يوجد طابع
echo 300.01 - 30,000 دج: 1 دج
echo 30,000.01 - 100,000 دج: 1.5 دج لكل 100 دج
echo ^> 100,000 دج:       حساب متدرج
echo.

echo ✅ جميع الاختبارات مرت بنجاح!
echo.
echo اضغط أي مفتاح للخروج...
pause > nul