﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class add_Tax_To_Revexpences : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(add_Tax_To_Revexpences));

        string IMigrationMetadata.Id => "202104152257007_add_Tax_To_Revexpences";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            AddColumn("dbo.RevExpEntries", "Taxable", (ColumnBuilder c) => c.Bo<PERSON>an(false, false));
            AddColumn("dbo.RevExpEntries", "TaxPersentage", (ColumnBuilder c) => c.Double(false, 0.0));
            AddColumn("dbo.RevExpEntries", "DiscountPersentage", (ColumnBuilder c) => c.Double(false, 0.0));
            AddColumn("dbo.RevExpEntries", "InvoiceCode", (ColumnBuilder c) => c.String());
            AddColumn("dbo.RevExpEntries", "PersonalID", (ColumnBuilder c) => c.Int());
            CreateIndex("dbo.RevExpEntries", "PersonalID");
            AddForeignKey("dbo.RevExpEntries", "PersonalID", "dbo.Personals", "ID");
        }

        public override void Down()
        {
            DropForeignKey("dbo.RevExpEntries", "PersonalID", "dbo.Personals");
            DropIndex("dbo.RevExpEntries", new string[1] { "PersonalID" });
            DropColumn("dbo.RevExpEntries", "PersonalID");
            DropColumn("dbo.RevExpEntries", "InvoiceCode");
            DropColumn("dbo.RevExpEntries", "DiscountPersentage");
            DropColumn("dbo.RevExpEntries", "TaxPersentage");
            DropColumn("dbo.RevExpEntries", "Taxable");
        }
    }
}
