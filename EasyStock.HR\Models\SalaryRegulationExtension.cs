﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    public class SalaryRegulationExtension
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID { get; set; }

        public SalaryRegulation SalaryRegulation { get; set; }

        public int SalaryRegulationID { get; set; }

        public SalaryExtension SalaryExtension { get; set; }

        public int SalaryExtensionID { get; set; }
    }
}
