﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x02000482 RID: 1154
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class add_CanChangeBranchToUserSettings : DbMigration, IMigrationMetadata
	{
		// Token: 0x060022D4 RID: 8916 RVA: 0x00012060 File Offset: 0x00010260
		public override void Up()
		{
			base.AddColumn("dbo.UserSettingsProfiles", "CanChangeBranch", (ColumnBuilder c) => c.<PERSON><PERSON>(new bool?(false), new bool?(true), null, null, null, null), null);
		}

		// Token: 0x060022D5 RID: 8917 RVA: 0x00012094 File Offset: 0x00010294
		public override void Down()
		{
			base.DropColumn("dbo.UserSettingsProfiles", "CanChangeBranch", null);
		}

		// Token: 0x17000B70 RID: 2928
		// (get) Token: 0x060022D6 RID: 8918 RVA: 0x001F0A8C File Offset: 0x001EEC8C
		string IMigrationMetadata.Id
		{
			get
			{
				return "202104192139026_add_CanChangeBranchToUserSettings";
			}
		}

		// Token: 0x17000B71 RID: 2929
		// (get) Token: 0x060022D7 RID: 8919 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000B72 RID: 2930
		// (get) Token: 0x060022D8 RID: 8920 RVA: 0x001F0AA4 File Offset: 0x001EECA4
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002AF1 RID: 10993
		private readonly ResourceManager Resources = new ResourceManager(typeof(add_CanChangeBranchToUserSettings));
	}
}
