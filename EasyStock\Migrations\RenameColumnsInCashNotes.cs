﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x020004AF RID: 1199
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class RenameColumnsInCashNotes : DbMigration, IMigrationMetadata
	{
		// Token: 0x060023C0 RID: 9152 RVA: 0x00012705 File Offset: 0x00010905
		public override void Up()
		{
			base.RenameColumn("dbo.CashNotes", "InvoiceID", "LinkID", null);
			base.RenameColumn("dbo.CashNotes", "InvoicesType", "LinkType", null);
		}

		// Token: 0x060023C1 RID: 9153 RVA: 0x00006A8E File Offset: 0x00004C8E
		public override void Down()
		{
		}

		// Token: 0x17000BB5 RID: 2997
		// (get) Token: 0x060023C2 RID: 9154 RVA: 0x001F4B58 File Offset: 0x001F2D58
		string IMigrationMetadata.Id
		{
			get
			{
				return "202108151428498_RenameColumnsInCashNotes";
			}
		}

		// Token: 0x17000BB6 RID: 2998
		// (get) Token: 0x060023C3 RID: 9155 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000BB7 RID: 2999
		// (get) Token: 0x060023C4 RID: 9156 RVA: 0x001F4B70 File Offset: 0x001F2D70
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B54 RID: 11092
		private readonly ResourceManager Resources = new ResourceManager(typeof(RenameColumnsInCashNotes));
	}
}
