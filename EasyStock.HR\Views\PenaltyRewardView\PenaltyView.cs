﻿using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Mask;
using EasyStock.HR.BLL;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;

namespace EasyStock.HR.Views.PenaltyRewardView
{
    public partial class PenaltyView : MasterView
    {
        public static PenaltyView Instance
        {
            get
            {
                bool flag = PenaltyView.instance == null || PenaltyView.instance.IsDisposed;
                if (flag)
                {
                    PenaltyView.instance = new PenaltyView();
                }
                return PenaltyView.instance;
            }
        }

        public PenaltyReward Penalty
        {
            get
            {
                return this.PenaltybindingSource.Current as PenaltyReward;
            }
            set
            {
                this.PenaltybindingSource.DataSource = value;
            }
        }

        public PenaltyView()
        {
            this.InitializeComponent();
            base.Shown += this.PenaltyView_Shown;
            this.Text = "Enregistrer une pénalité";
            this.BindingLookupControls();
            this.NoOfDaysSpinEdit.EditValueChanged += this.NoOfDaysSpinEdit_EditValueChanged;
            this.DateDateEdit.Properties.Mask.MaskType = MaskType.DateTime;
            this.DateDateEdit.Properties.Mask.EditMask = "dd/MM/yyyy";
            this.DateDateEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
        }

        private void NoOfDaysSpinEdit_EditValueChanged(object sender, EventArgs e)
        {
            SpinEdit control = sender as SpinEdit;
            bool flag = control != null;
            if (flag)
            {
                int NoOfDays = Convert.ToInt32(control.Value);
                this.AmountSpinEdit.Value = Convert.ToDecimal(EmployeeBLL.EmpCalcDay(this.Penalty.EmployeeId, NoOfDays));
            }
        }

        private void PenaltyView_Shown(object sender, EventArgs e)
        {
            bool flag = this.Penalty == null;
            if (flag)
            {
                this.New();
            }
        }

        public void GoTo(int id)
        {
            PenaltyReward _penalty = PenaltyRewardBLL.Get(id, PenaltyRewardType.Penalty);
            bool flag = _penalty != null;
            if (flag)
            {
                this.Penalty = _penalty;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("L'enregistrement est introuvable. ");
            }
        }

        public override void New()
        {
            this.Penalty = new PenaltyReward
            {
                Type = PenaltyRewardType.Penalty,
                calcType = CalculationType.Days,
                Date = DateTime.Now
            };
            base.New();
        }

        public override void Save()
        {
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                int res = PenaltyRewardBLL.AddOrUpdate(this.Penalty);
                base.Save();
            }
        }

        public override void RefreshData()
        {
            this.New();
            base.RefreshData();
        }

        public override void Delete()
        {
            bool flag = this.Penalty == null || this.Penalty.ID == 0;
            if (!flag)
            {
                int res = PenaltyRewardBLL.Delete(this.Penalty.ID);
                bool flag2 = res > 0;
                if (flag2)
                {
                    base.Delete();
                }
            }
        }

        public void BindingLookupControls()
        {
            this.EmployeeIdLookUpEdit.Properties.DataSource = EmployeeBLL.GetAll();
            this.EmployeeIdLookUpEdit.Properties.DisplayMember = "Name";
            this.EmployeeIdLookUpEdit.Properties.ValueMember = "ID";
            this.EmployeeIdLookUpEdit.Properties.NullText = "";
            this.EmployeeIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("ID", "Code"));
            this.EmployeeIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "L'employé"));
            this.EmployeeIdLookUpEdit.EditValueChanged += this.EmployeeIdLookUpEdit_EditValueChanged;
            this.calcTypeRadioGroup.Properties.AddEnum<CalculationType>();
            this.calcTypeRadioGroup.SelectedIndexChanged += this.CalcTypeRadioGroup_SelectedIndexChanged;
        }

        private void EmployeeIdLookUpEdit_EditValueChanged(object sender, EventArgs e)
        {
            LookUpEdit emplookupedit = sender as LookUpEdit;
            Employee emp = emplookupedit.GetSelectedDataRow() as Employee;
            bool flag = emp != null;
            if (flag)
            {
                this.noOfPenaltyValue.Text = PenaltyRewardBLL.GetCountForEmp(emp.ID, PenaltyRewardType.Penalty).ToString();
            }
        }

        private void CalcTypeRadioGroup_SelectedIndexChanged(object sender, EventArgs e)
        {
            RadioGroup edit = sender as RadioGroup;
            bool flag = edit.SelectedIndex == 0;
            if (flag)
            {
                this.NoOfDaysSpinEdit.Enabled = true;
                this.AmountSpinEdit.Enabled = false;
            }
            else
            {
                this.NoOfDaysSpinEdit.Enabled = false;
                this.AmountSpinEdit.Enabled = true;
            }
        }

        private static PenaltyView instance;
    }
}
