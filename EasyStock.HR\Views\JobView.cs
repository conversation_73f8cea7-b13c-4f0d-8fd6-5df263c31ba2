﻿using DevExpress.XtraEditors;
using EasyStock.Common;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class JobView : MasterView
    {
        public static JobView Instance
        {
            get
            {
                bool flag = JobView.instance == null || JobView.instance.IsDisposed;
                if (flag)
                {
                    JobView.instance = new JobView();
                }
                return JobView.instance;
            }
        }

        public Job job
        {
            get
            {
                return this.jobBindingSource.Current as Job;
            }
            set
            {
                this.jobBindingSource.DataSource = value;
            }
        }

        public JobView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            this.context = new HRDataContext();
            this.gridView1.AddEditButton(new EventHandler(this.RepositoryItemButtonEdit1_Click));
            this.gridView1.SetAlternatingColors();
            base.Shown += this.JobView_Shown;
        }

        private void JobView_Shown(object sender, EventArgs e)
        {
            bool flag = this.job == null;
            if (flag)
            {
                this.New();
            }
        }

        private void RepositoryItemButtonEdit1_Click(object sender, EventArgs e)
        {
            Job row = this.gridView1.GetRow(this.gridView1.FocusedRowHandle) as Job;
            bool flag = row != null;
            if (flag)
            {
                this.GoTo(row.ID);
            }
        }

        public void GoTo(int id)
        {
            Job sourceDepr = this.context.Jobs.SingleOrDefault((Job x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.job = sourceDepr;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable");
            }
        }

        public override void New()
        {
            this.job = new Job();
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.context.Jobs.AddOrUpdate(new Job[]
                {
                    this.job
                });
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.gridControl1.DataSource = this.context.Jobs.ToList<Job>();
            base.RefreshData();
        }

        public override void Delete()
        {
        }

        private static JobView instance;

        private HRDataContext context;
    }
}
