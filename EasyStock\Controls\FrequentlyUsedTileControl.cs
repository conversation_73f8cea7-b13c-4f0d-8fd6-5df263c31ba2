﻿using DevExpress.Utils;
using DevExpress.XtraEditors;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyStock.Controls
{
    public class FrequentlyUsedTileControl : XtraUserControl
    {
        public FrequentlyUsedTileControl()
        {
            this.InitializeComponent();
            this.tileControl.ShowGroupText = true;
            this.tileControl.AppearanceGroupText.ForeColor = Color.FromArgb(64, 64, 64);
            this.tileControl.IndentBetweenItems = 22;
            this.tileControl.AllowItemHover = true;
            this.tileControl.HorizontalContentAlignment = HorzAlignment.Center;
            this.tileControl.VerticalContentAlignment = VertAlignment.Top;
            this.tileControl.ItemClick += this.TileControl_ItemClick;
            this.AddTile();
        }

        private void TileControl_ItemClick(object sender, TileItemEventArgs e)
        {
            NavigationObject obj = e.Item.Tag as NavigationObject;
            bool flag = obj != null && obj.Form != null;
            if (flag)
            {
                HomeForm.LogScreenOpening(obj.ID, CurrentSession.CurrentUser.ID);
                HomeForm.OpenForm(obj.Form, false, false, false);
            }
        }

        private async void AddTile()
        {
            tileGroup2.Items.Clear();
            List<NavigationObject> objects = null;
            await Task.Run(delegate
            {
                using (ERPDataContext eRPDataContext = new ERPDataContext())
                {
                    int currentHour = DateTime.Now.Hour;
                    int currentDAy = (int)DateTime.Now.DayOfWeek;
                    var source = (from x in eRPDataContext.FrequentlyUsedScreens.Where((FrequentlyUsedScreen x) => x.UserID == CurrentSession.CurrentUser.ID).ToList()
                                  select new
                                  {
                                      ScreenID = x.ScreenID,
                                      Hour = x.Hour,
                                      DayOfWeek = x.DayOfWeek,
                                      Score = (24 - Math.Abs(x.Hour - currentHour) + (7 - Math.Abs(x.DayOfWeek - currentDAy))) * x.Count
                                  }).ToList();
                    var outer = (from x in source
                                 group x by x.ScreenID into x
                                 select new
                                 {
                                     ScreenID = x.Key,
                                     Score = x.Sum(s => s.Score)
                                 } into x
                                 orderby x.Score descending
                                 select x).ToList();
                    objects = (from fi in outer
                               join obj in NavigationObjects.AllAllowedObjects on fi.ScreenID equals obj.ID
                               select obj into x
                               where x.HasFormContractor
                               select x).ToList();
                }
            });
            int count = 0;
            foreach (NavigationObject item in objects)
            {
                count++;
                TileItem tileItem = new TileItem
                {
                    ItemSize = ((count < 3) ? TileItemSize.Large : ((count < 8) ? TileItemSize.Wide : TileItemSize.Medium)),
                    Id = item.ID,
                    Tag = item
                };
                TileItemElement imageElement = new TileItemElement();
                imageElement.Text = item.DisplayCaption;
                imageElement.Appearance.Normal.FontSizeDelta = 3;
                imageElement.Appearance.Normal.FontStyleDelta = FontStyle.Bold;
                imageElement.TextAlignment = TileItemContentAlignment.MiddleCenter;
                tileItem.Elements.Add(imageElement);
                tileItem.AppearanceItem.Normal.BackColor = item.BackColor;
                tileGroup2.Items.Add(tileItem);
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            this.AddTile();
        }

        protected override void Dispose(bool disposing)
        {
            bool flag = disposing && this.components != null;
            if (flag)
            {
                this.components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraEditors.TileItemElement tileItemElement12 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement13 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement14 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement15 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement16 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement17 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement18 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement19 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement20 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement21 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement22 = new DevExpress.XtraEditors.TileItemElement();
            this.tileControl = new DevExpress.XtraEditors.TileControl();
            this.tileGroup2 = new DevExpress.XtraEditors.TileGroup();
            this.tileItem1 = new DevExpress.XtraEditors.TileItem();
            this.tileItem2 = new DevExpress.XtraEditors.TileItem();
            this.tileItem3 = new DevExpress.XtraEditors.TileItem();
            this.tileItem4 = new DevExpress.XtraEditors.TileItem();
            this.tileItem5 = new DevExpress.XtraEditors.TileItem();
            this.tileItem6 = new DevExpress.XtraEditors.TileItem();
            this.tileItem7 = new DevExpress.XtraEditors.TileItem();
            this.tileItem8 = new DevExpress.XtraEditors.TileItem();
            this.tileItem9 = new DevExpress.XtraEditors.TileItem();
            this.tileItem10 = new DevExpress.XtraEditors.TileItem();
            this.tileItem11 = new DevExpress.XtraEditors.TileItem();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.SuspendLayout();
            // 
            // tileControl
            // 
            this.tileControl.AllowDrag = false;
            this.tileControl.AllowDragTilesBetweenGroups = false;
            this.tileControl.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(7)))), ((int)(((byte)(99)))), ((int)(((byte)(140)))));
            this.tileControl.AppearanceItem.Normal.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(47)))), ((int)(((byte)(66)))), ((int)(((byte)(96)))));
            this.tileControl.AppearanceItem.Normal.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileControl.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileControl.AppearanceItem.Normal.Options.UseFont = true;
            this.tileControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tileControl.Groups.Add(this.tileGroup2);
            this.tileControl.HorizontalContentAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.tileControl.IndentBetweenItems = 5;
            this.tileControl.ItemSize = 90;
            this.tileControl.ItemTextShowMode = DevExpress.XtraEditors.TileItemContentShowMode.Always;
            this.tileControl.Location = new System.Drawing.Point(0, 0);
            this.tileControl.MaxId = 11;
            this.tileControl.Name = "tileControl";
            this.tileControl.OptionsAdaptiveLayout.ItemMinSize = new System.Drawing.Size(90, 90);
            this.tileControl.Orientation = System.Windows.Forms.Orientation.Vertical;
            this.tileControl.ScrollMode = DevExpress.XtraEditors.TileControlScrollMode.ScrollButtons;
            this.tileControl.Size = new System.Drawing.Size(416, 730);
            this.tileControl.TabIndex = 0;
            this.tileControl.Text = "tileControl1";
            // 
            // tileGroup2
            // 
            this.tileGroup2.Items.Add(this.tileItem1);
            this.tileGroup2.Items.Add(this.tileItem2);
            this.tileGroup2.Items.Add(this.tileItem3);
            this.tileGroup2.Items.Add(this.tileItem4);
            this.tileGroup2.Items.Add(this.tileItem5);
            this.tileGroup2.Items.Add(this.tileItem6);
            this.tileGroup2.Items.Add(this.tileItem7);
            this.tileGroup2.Items.Add(this.tileItem8);
            this.tileGroup2.Items.Add(this.tileItem9);
            this.tileGroup2.Items.Add(this.tileItem10);
            this.tileGroup2.Items.Add(this.tileItem11);
            this.tileGroup2.Name = "tileGroup2";
            // 
            // tileItem1
            // 
            tileItemElement12.Text = "tileItem1";
            this.tileItem1.Elements.Add(tileItemElement12);
            this.tileItem1.Id = 0;
            this.tileItem1.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.tileItem1.Name = "tileItem1";
            // 
            // tileItem2
            // 
            tileItemElement13.Text = "tileItem2";
            this.tileItem2.Elements.Add(tileItemElement13);
            this.tileItem2.Id = 1;
            this.tileItem2.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tileItem2.Name = "tileItem2";
            // 
            // tileItem3
            // 
            tileItemElement14.Text = "tileItem3";
            this.tileItem3.Elements.Add(tileItemElement14);
            this.tileItem3.Id = 2;
            this.tileItem3.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tileItem3.Name = "tileItem3";
            // 
            // tileItem4
            // 
            tileItemElement15.Text = "tileItem4";
            this.tileItem4.Elements.Add(tileItemElement15);
            this.tileItem4.Id = 3;
            this.tileItem4.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tileItem4.Name = "tileItem4";
            // 
            // tileItem5
            // 
            tileItemElement16.Text = "tileItem5";
            this.tileItem5.Elements.Add(tileItemElement16);
            this.tileItem5.Id = 4;
            this.tileItem5.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tileItem5.Name = "tileItem5";
            // 
            // tileItem6
            // 
            tileItemElement17.Text = "tileItem6";
            this.tileItem6.Elements.Add(tileItemElement17);
            this.tileItem6.Id = 5;
            this.tileItem6.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tileItem6.Name = "tileItem6";
            // 
            // tileItem7
            // 
            tileItemElement18.Text = "tileItem7";
            this.tileItem7.Elements.Add(tileItemElement18);
            this.tileItem7.Id = 6;
            this.tileItem7.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.tileItem7.Name = "tileItem7";
            // 
            // tileItem8
            // 
            tileItemElement19.Text = "tileItem8";
            this.tileItem8.Elements.Add(tileItemElement19);
            this.tileItem8.Id = 7;
            this.tileItem8.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.tileItem8.Name = "tileItem8";
            // 
            // tileItem9
            // 
            tileItemElement20.ImageOptions.ImageLocation = new System.Drawing.Point(-10, 80);
            tileItemElement20.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomInside;
            tileItemElement20.ImageOptions.ImageSize = new System.Drawing.Size(250, 250);
            tileItemElement20.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.None;
            tileItemElement20.ImageOptions.SvgImageSize = new System.Drawing.Size(500, 500);
            tileItemElement20.Text = "tileItem9";
            tileItemElement20.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            this.tileItem9.Elements.Add(tileItemElement20);
            this.tileItem9.Id = 8;
            this.tileItem9.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.tileItem9.Name = "tileItem9";
            // 
            // tileItem10
            // 
            tileItemElement21.Text = "tileItem10";
            this.tileItem10.Elements.Add(tileItemElement21);
            this.tileItem10.Id = 9;
            this.tileItem10.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.tileItem10.Name = "tileItem10";
            // 
            // tileItem11
            // 
            tileItemElement22.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement22.ImageOptions.ImageLocation = new System.Drawing.Point(20, -25);
            tileItemElement22.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomInside;
            tileItemElement22.ImageOptions.ImageSize = new System.Drawing.Size(500, 500);
            tileItemElement22.ImageOptions.SvgImageSize = new System.Drawing.Size(500, 500);
            tileItemElement22.Text = "tileItem11";
            this.tileItem11.Elements.Add(tileItemElement22);
            this.tileItem11.Id = 10;
            this.tileItem11.ItemSize = DevExpress.XtraEditors.TileItemSize.Large;
            this.tileItem11.Name = "tileItem11";
            // 
            // timer1
            // 
            this.timer1.Enabled = true;
            this.timer1.Interval = 10000;
            // 
            // FrequentlyUsedTileControl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.tileControl);
            this.Name = "FrequentlyUsedTileControl";
            this.Size = new System.Drawing.Size(416, 730);
            this.ResumeLayout(false);

        }

        private IContainer components = null;

        private TileControl tileControl;

        private TileGroup tileGroup2;

        private TileItem tileItem1;

        private Timer timer1;

        private TileItem tileItem2;

        private TileItem tileItem3;

        private TileItem tileItem4;

        private TileItem tileItem5;

        private TileItem tileItem6;

        private TileItem tileItem7;

        private TileItem tileItem8;

        private TileItem tileItem9;

        private TileItem tileItem10;

        private TileItem tileItem11;
    }
}
