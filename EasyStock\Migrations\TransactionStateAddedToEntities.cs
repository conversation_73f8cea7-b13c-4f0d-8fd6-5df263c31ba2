﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x020004A1 RID: 1185
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class TransactionStateAddedToEntities : DbMigration, IMigrationMetadata
	{
		// Token: 0x06002373 RID: 9075 RVA: 0x001F3418 File Offset: 0x001F1618
		public override void Up()
		{
			base.AddColumn("dbo.ProductTransactions", "TransactionState", (ColumnBuilder c) => c.Int(new bool?(false), false, new int?(2), null, null, null, null), null);
			base.AddColumn("dbo.PurchaseInvoices", "TransactionState", (ColumnBuilder c) => c.Int(new bool?(false), false, new int?(2), null, null, null, null), null);
			base.AddColumn("dbo.PurchaseReturnInvoices", "TransactionState", (ColumnBuilder c) => c.Int(new bool?(false), false, new int?(2), null, null, null, null), null);
			base.AddColumn("dbo.SalesInvoices", "TransactionState", (ColumnBuilder c) => c.Int(new bool?(false), false, new int?(2), null, null, null, null), null);
			base.AddColumn("dbo.SalesReturnInvoices", "TransactionState", (ColumnBuilder c) => c.Int(new bool?(false), false, new int?(2), null, null, null, null), null);
		}

		// Token: 0x06002374 RID: 9076 RVA: 0x001F351C File Offset: 0x001F171C
		public override void Down()
		{
			base.DropColumn("dbo.SalesReturnInvoices", "TransactionState", null);
			base.DropColumn("dbo.SalesInvoices", "TransactionState", null);
			base.DropColumn("dbo.PurchaseReturnInvoices", "TransactionState", null);
			base.DropColumn("dbo.PurchaseInvoices", "TransactionState", null);
			base.DropColumn("dbo.ProductTransactions", "TransactionState", null);
		}

		// Token: 0x17000BA0 RID: 2976
		// (get) Token: 0x06002375 RID: 9077 RVA: 0x001F3584 File Offset: 0x001F1784
		string IMigrationMetadata.Id
		{
			get
			{
				return "202107171234004_TransactionStateAddedToEntities";
			}
		}

		// Token: 0x17000BA1 RID: 2977
		// (get) Token: 0x06002376 RID: 9078 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000BA2 RID: 2978
		// (get) Token: 0x06002377 RID: 9079 RVA: 0x001F359C File Offset: 0x001F179C
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B31 RID: 11057
		private readonly ResourceManager Resources = new ResourceManager(typeof(TransactionStateAddedToEntities));
	}
}
