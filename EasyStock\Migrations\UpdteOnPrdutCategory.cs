﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class UpdteOnPrdutCategory : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(UpdteOnPrdutCategory));

        string IMigrationMetadata.Id => "202101071105415_UpdteOnPrdutCategory";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            DropForeignKey("dbo.ProductCategories", "ParentGroupID", "dbo.PersonalGroups");
        }

        public override void Down()
        {
        }
    }
}
