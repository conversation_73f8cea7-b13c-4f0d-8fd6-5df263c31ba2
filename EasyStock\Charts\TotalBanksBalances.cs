﻿using DevExpress.Utils;
using DevExpress.XtraCharts;
using EasyStock.Classes;
using EasyStock.Controller;
using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace EasyStock.Charts
{
    public class TotalBanksBalances : BaseChart
    {
        public class BalanceInDate
        {
            [Display(Name = "Solde")]
            public double Balance { get; set; }

            [Display(Name = "Date")]
            public DateTime Date { get; set; }
        }

        public override string Caption => "Solde total des banques";

        public override Color Color => ColorTranslator.FromHtml("#424242");

        public TotalBanksBalances()
        {
            Series series1 = new Series();
            SplineAreaSeriesView splineAreaSeriesView1 = new SplineAreaSeriesView();
            series1.ArgumentDataMember = "Date";
            series1.ValueDataMembersSerializable = "Balance";
            series1.Name = "Series 1";
            series1.View = splineAreaSeriesView1;
            base.SeriesSerializable = new Series[1] { series1 };
            XYDiagram diagram = (XYDiagram)base.Diagram;
            base.AnimationStartMode = ChartAnimationMode.OnDataChanged;
            diagram.AxisY.Visibility = DefaultBoolean.True;
            diagram.AxisX.Visibility = DefaultBoolean.True;
            base.Legend.Visibility = DefaultBoolean.False;
        }

        public override async Task<object> QueryData()
        {
            using (var db = new ERPDataContext())
            {
                object dataSource = null;
                double? CurrentBalance = 0.0;

                try
                {
                    Account account = await db.Accounts.FindAsync(CurrentSession.SystemSettings.BanksAccount);
                    if (account != null)
                    {
                        string AccountNumber = account.Number;
                        DateTime Lastyear = DateTime.Now.AddDays(-365.0).Date;

                        var source = db.JournalDetails
                            .Include(jd => jd.Account)
                            .Include(jd => jd.Journal)
                            .Include(jd => jd.Currency)
                            .Where(jd => jd.Account.Number.IndexOf(AccountNumber) == 0)
                            .OrderBy(jd => jd.Journal.Date);

                        var source2 = source
                            .Where(jd => jd.Journal.Date > Lastyear)
                            .OrderBy(jd => jd.Journal.Date);

                        var list = await source2
                            .GroupBy(jd => jd.Journal.Date.Date)
                            .Select(x => new BalanceInDate
                            {
                                Date = x.Key,
                                Balance = (
                                    (db.JournalDetails
                                        .Where(j => j.Account.Number.IndexOf(AccountNumber) == 0 && j.Journal.Date <= x.Key.AddDays(1))
                                        .Sum(j => j.Credit * j.CurrencyRate)) -
                                    (db.JournalDetails
                                        .Where(j => j.Account.Number.IndexOf(AccountNumber) == 0 && j.Journal.Date <= x.Key.AddDays(1))
                                        .Sum(j => j.Debit * j.CurrencyRate))
                                )
                            }).ToListAsync();

                        list.Add(new BalanceInDate
                        {
                            Date = Lastyear,
                            Balance =  account.GetBalanceAsValue(Lastyear, db)
                        });

                        var list2 = EasyStock.Common.Exteintions.EachDay(Lastyear, DateTime.Now)
                            .Select(x => new BalanceInDate { Date = x.Date })
                            .ToList();

                        for (int i = 0; i < list2.Count; i++)
                        {
                            var balanceInDate = list2[i];
                            var num = list.FirstOrDefault(x => x.Date == balanceInDate.Date)?.Balance;
                            num = (num.HasValue || i <= 0) ? (num * -1.0) : list2[i - 1].Balance;
                            balanceInDate.Balance = num.GetValueOrDefault();
                        }

                        CurrentBalance = list2.OrderByDescending(x => x.Date).FirstOrDefault()?.Balance;
                        list2 = list2.Where(x => x.Balance != 0.0).ToList();
                        dataSource = list2;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                    dataSource = new List<BalanceInDate>();
                }

                if (dataSource != null)
                {
                    TextAnnotation textAnnotation = base.Annotations.OfType<TextAnnotation>().FirstOrDefault()
                        ?? new TextAnnotation("Annotation1", CurrentBalance.ToString());

                    var xyDiagram = (XYDiagram)base.Diagram;
                    var freePosition = (FreePosition)textAnnotation.ShapePosition;
                    freePosition.DockCorner = DockCorner.LeftTop;
                    freePosition.InnerIndents.Left = 75;
                    freePosition.InnerIndents.Top = 25;

                    textAnnotation.Text = CurrentBalance.ToString();
                    textAnnotation.ShapeKind = ShapeKind.RoundedRectangle;
                    textAnnotation.ConnectorStyle = AnnotationConnectorStyle.None;
                    textAnnotation.Font = new Font("Arial", 24f, FontStyle.Bold);
                    textAnnotation.TextAlignment = StringAlignment.Center;
                    textAnnotation.TextColor = Color.Black;
                    textAnnotation.BackColor = Color.White;
                    textAnnotation.Border.Color = Color.White;
                    textAnnotation.Border.Thickness = 2;
                    textAnnotation.Padding.Top = 10;
                    textAnnotation.Padding.Bottom = 5;
                    textAnnotation.Padding.Left = 5;
                    textAnnotation.Padding.Right = 5;
                    textAnnotation.RuntimeMoving = true;

                    base.AnnotationRepository.Clear();
                    base.AnnotationRepository.Add(textAnnotation);
                }
                else
                {
                    dataSource = new List<BalanceInDate>();
                }

                return dataSource;
            }
        }

    }
}
