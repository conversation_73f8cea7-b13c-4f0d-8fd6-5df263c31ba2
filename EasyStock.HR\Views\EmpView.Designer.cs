﻿namespace EasyStock.HR.Views
{
	// Token: 0x02000040 RID: 64
	public partial class EmpView : global::EasyStock.HR.MainViews.MasterView
	{
		// Token: 0x06000219 RID: 537 RVA: 0x0001F1E4 File Offset: 0x0001D3E4
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600021A RID: 538 RVA: 0x0001F21C File Offset: 0x0001D41C
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.layoutControlGroup6 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.grdDeduction = new DevExpress.XtraGrid.GridControl();
            this.empSalaryDeductionBindingSource1 = new System.Windows.Forms.BindingSource(this.components);
            this.viewDeduction = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colSalaryExtensionId1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repoded = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.salaryExtensionBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.colValue1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colNotes1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.grdBenefits = new DevExpress.XtraGrid.GridControl();
            this.empSalaryExtensionBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.viewBenefits = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colSalaryExtensionId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repoExp = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.colValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colNotes = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemImageComboBox1 = new DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox();
            this.btnAddImg = new DevExpress.XtraEditors.SimpleButton();
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.EmpBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.CodeTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.NameTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.BirthPlaceTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.JobTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.AbsenceRegulationTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.DelayRegulationTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.OverTimeRegulationTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.SalaryRegulationTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.GenderImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.NationalIDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.InsuranceNoTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.InsuranceDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.DrivingLicenseTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.EndDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.BankNameTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.AccountNoTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.BirthDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.MaritalStatusImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.MilitarilyStatusImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.PhoneTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.EmailTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.AddressTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.FingerprintCodeTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.ContractTypeImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.ContractPeriodTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.ContactEndDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.DateOfRecruitmentDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.GroupIDTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.JobIDTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.BranchTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.DepartmentIDTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.NationalityTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.PayPeriodImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.CalcIncomeTaxCheckEdit = new DevExpress.XtraEditors.CheckEdit();
            this.FristShiftIdTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.SecondShiftIdTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.AbsenceRegistionIDTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.DelayRegulationIDTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.OverTimeRegulationIDTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.SalaryBasicSpinEdit = new DevExpress.XtraEditors.SpinEdit();
            this.SalaryVariableSpinEdit = new DevExpress.XtraEditors.SpinEdit();
            this.DayValueSpinEdit = new DevExpress.XtraEditors.SpinEdit();
            this.HourValueSpinEdit = new DevExpress.XtraEditors.SpinEdit();
            this.ExpensesAccountIdLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.AccruedAccountIdLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.PhotoPictureEdit = new DevExpress.XtraEditors.PictureEdit();
            this.BirthCountryIdLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.NationalityIdLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.ReligionIdLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.QualificationIdLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.btnRemoveImg = new DevExpress.XtraEditors.SimpleButton();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForJob = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForAbsenceRegulation = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDelayRegulation = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForOverTimeRegulation = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForSalaryRegulation = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForBirthPlace = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForNationality = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlGroup10 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.tabbedControlGroup2 = new DevExpress.XtraLayout.TabbedControlGroup();
            this.layoutControlGroup13 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup14 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup8 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForDepartmentID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForGroupID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForJobID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForCode = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForName = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForQualificationId = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForBranch = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem6 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlItem5 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlGroup9 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForPhoto = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            this.tabbedControlGroup1 = new DevExpress.XtraLayout.TabbedControlGroup();
            this.layoutControlGroup7 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup11 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem3 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlGroup17 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForAccruedAccountId = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForSalaryVariable = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForExpensesAccountId = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForPayPeriod = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForSalaryBasic = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForCalcIncomeTax = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForHourValue = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDayValue = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup12 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem4 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlGroup3 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup16 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForGender = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForBirthDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForNationalityId = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForNationalID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForInsuranceDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForMilitarilyStatus = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForAddress = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForPhone = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForAccountNo = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForBankName = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForEmail = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDrivingLicense = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForInsuranceNo = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForEndDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForReligionId = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForBirthCountryId = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForMaritalStatus = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem12 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup4 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup5 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForContractPeriod = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForContractType = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForContactEndDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDateOfRecruitment = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem11 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup15 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForFingerprintCode = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForFristShiftId = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForSecondShiftId = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForAbsenceRegistionID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDelayRegulationID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForOverTimeRegulationID = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem3 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem4 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem5 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem7 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem8 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem9 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem10 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.EmpStateTextEdit = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdDeduction)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.empSalaryDeductionBindingSource1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewDeduction)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoded)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.salaryExtensionBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdBenefits)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.empSalaryExtensionBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewBenefits)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoExp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemImageComboBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmpBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.CodeTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NameTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BirthPlaceTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.JobTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.AbsenceRegulationTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DelayRegulationTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.OverTimeRegulationTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.SalaryRegulationTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.GenderImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NationalIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.InsuranceNoTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.InsuranceDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.InsuranceDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DrivingLicenseTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EndDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EndDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BankNameTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.AccountNoTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BirthDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BirthDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.MaritalStatusImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.MilitarilyStatusImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.PhoneTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmailTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.AddressTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.FingerprintCodeTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContractTypeImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContractPeriodTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContactEndDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContactEndDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateOfRecruitmentDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateOfRecruitmentDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.GroupIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.JobIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BranchTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DepartmentIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NationalityTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.PayPeriodImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.CalcIncomeTaxCheckEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.FristShiftIdTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.SecondShiftIdTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.AbsenceRegistionIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DelayRegulationIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.OverTimeRegulationIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.SalaryBasicSpinEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.SalaryVariableSpinEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayValueSpinEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.HourValueSpinEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ExpensesAccountIdLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.AccruedAccountIdLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.PhotoPictureEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BirthCountryIdLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NationalityIdLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ReligionIdLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.QualificationIdLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForJob)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAbsenceRegulation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDelayRegulation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForOverTimeRegulation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSalaryRegulation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBirthPlace)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNationality)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabbedControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDepartmentID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForGroupID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForJobID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForQualificationId)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBranch)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForPhoto)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabbedControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAccruedAccountId)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSalaryVariable)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForExpensesAccountId)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForPayPeriod)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSalaryBasic)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCalcIncomeTax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForHourValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDayValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForGender)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBirthDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNationalityId)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNationalID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForInsuranceDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForMilitarilyStatus)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAddress)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForPhone)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAccountNo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBankName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDrivingLicense)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForInsuranceNo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEndDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForReligionId)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBirthCountryId)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForMaritalStatus)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForContractPeriod)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForContractType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForContactEndDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDateOfRecruitment)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFingerprintCode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFristShiftId)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSecondShiftId)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAbsenceRegistionID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDelayRegulationID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForOverTimeRegulationID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmpStateTextEdit.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // layoutControlGroup6
            // 
            this.layoutControlGroup6.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup6.Name = "layoutControlGroup6";
            this.layoutControlGroup6.Size = new System.Drawing.Size(261, 854);
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(594, 0);
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(594, 0);
            // 
            // dataLayoutControl1
            // 
            this.dataLayoutControl1.Controls.Add(this.labelControl1);
            this.dataLayoutControl1.Controls.Add(this.grdDeduction);
            this.dataLayoutControl1.Controls.Add(this.grdBenefits);
            this.dataLayoutControl1.Controls.Add(this.btnAddImg);
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.CodeTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NameTextEdit);
            this.dataLayoutControl1.Controls.Add(this.BirthPlaceTextEdit);
            this.dataLayoutControl1.Controls.Add(this.JobTextEdit);
            this.dataLayoutControl1.Controls.Add(this.AbsenceRegulationTextEdit);
            this.dataLayoutControl1.Controls.Add(this.DelayRegulationTextEdit);
            this.dataLayoutControl1.Controls.Add(this.OverTimeRegulationTextEdit);
            this.dataLayoutControl1.Controls.Add(this.SalaryRegulationTextEdit);
            this.dataLayoutControl1.Controls.Add(this.GenderImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.NationalIDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.InsuranceNoTextEdit);
            this.dataLayoutControl1.Controls.Add(this.InsuranceDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.DrivingLicenseTextEdit);
            this.dataLayoutControl1.Controls.Add(this.EndDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.BankNameTextEdit);
            this.dataLayoutControl1.Controls.Add(this.AccountNoTextEdit);
            this.dataLayoutControl1.Controls.Add(this.BirthDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.MaritalStatusImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.MilitarilyStatusImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.PhoneTextEdit);
            this.dataLayoutControl1.Controls.Add(this.EmailTextEdit);
            this.dataLayoutControl1.Controls.Add(this.AddressTextEdit);
            this.dataLayoutControl1.Controls.Add(this.FingerprintCodeTextEdit);
            this.dataLayoutControl1.Controls.Add(this.ContractTypeImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.ContractPeriodTextEdit);
            this.dataLayoutControl1.Controls.Add(this.ContactEndDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.DateOfRecruitmentDateEdit);
            this.dataLayoutControl1.Controls.Add(this.GroupIDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.JobIDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.BranchTextEdit);
            this.dataLayoutControl1.Controls.Add(this.DepartmentIDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NationalityTextEdit);
            this.dataLayoutControl1.Controls.Add(this.PayPeriodImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.CalcIncomeTaxCheckEdit);
            this.dataLayoutControl1.Controls.Add(this.FristShiftIdTextEdit);
            this.dataLayoutControl1.Controls.Add(this.SecondShiftIdTextEdit);
            this.dataLayoutControl1.Controls.Add(this.AbsenceRegistionIDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.DelayRegulationIDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.OverTimeRegulationIDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.SalaryBasicSpinEdit);
            this.dataLayoutControl1.Controls.Add(this.SalaryVariableSpinEdit);
            this.dataLayoutControl1.Controls.Add(this.DayValueSpinEdit);
            this.dataLayoutControl1.Controls.Add(this.HourValueSpinEdit);
            this.dataLayoutControl1.Controls.Add(this.ExpensesAccountIdLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.AccruedAccountIdLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.PhotoPictureEdit);
            this.dataLayoutControl1.Controls.Add(this.BirthCountryIdLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.NationalityIdLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.ReligionIdLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.QualificationIdLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.btnRemoveImg);
            this.dataLayoutControl1.DataSource = this.EmpBindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.HiddenItems.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup2,
            this.ItemForJob,
            this.ItemForAbsenceRegulation,
            this.ItemForDelayRegulation,
            this.ItemForOverTimeRegulation,
            this.ItemForSalaryRegulation,
            this.ItemForID,
            this.ItemForBirthPlace,
            this.ItemForNationality,
            this.layoutControlGroup10,
            this.tabbedControlGroup2});
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 26);
            this.dataLayoutControl1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(1042, 502);
            this.dataLayoutControl1.TabIndex = 4;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(338, 44);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(41, 13);
            this.labelControl1.StyleController = this.dataLayoutControl1;
            this.labelControl1.TabIndex = 67;
            this.labelControl1.Text = "En cours";
            // 
            // grdDeduction
            // 
            this.grdDeduction.DataSource = this.empSalaryDeductionBindingSource1;
            this.grdDeduction.Location = new System.Drawing.Point(526, 383);
            this.grdDeduction.MainView = this.viewDeduction;
            this.grdDeduction.Name = "grdDeduction";
            this.grdDeduction.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repoded});
            this.grdDeduction.Size = new System.Drawing.Size(463, 115);
            this.grdDeduction.TabIndex = 66;
            this.grdDeduction.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.viewDeduction});
            // 
            // empSalaryDeductionBindingSource1
            // 
            this.empSalaryDeductionBindingSource1.DataSource = typeof(EasyStock.HR.Models.EmpSalaryExtension);
            // 
            // viewDeduction
            // 
            this.viewDeduction.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colSalaryExtensionId1,
            this.colValue1,
            this.colNotes1});
            this.viewDeduction.GridControl = this.grdDeduction;
            this.viewDeduction.Name = "viewDeduction";
            // 
            // colSalaryExtensionId1
            // 
            this.colSalaryExtensionId1.ColumnEdit = this.repoded;
            this.colSalaryExtensionId1.FieldName = "SalaryExtensionId";
            this.colSalaryExtensionId1.Name = "colSalaryExtensionId1";
            this.colSalaryExtensionId1.Visible = true;
            this.colSalaryExtensionId1.VisibleIndex = 0;
            // 
            // repoded
            // 
            this.repoded.AutoHeight = false;
            this.repoded.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repoded.DataSource = this.salaryExtensionBindingSource;
            this.repoded.DisplayMember = "Name";
            this.repoded.Name = "repoded";
            this.repoded.ValueMember = "ID";
            // 
            // salaryExtensionBindingSource
            // 
            this.salaryExtensionBindingSource.DataSource = typeof(EasyStock.HR.Models.SalaryExtension);
            // 
            // colValue1
            // 
            this.colValue1.FieldName = "Value";
            this.colValue1.Name = "colValue1";
            this.colValue1.Visible = true;
            this.colValue1.VisibleIndex = 1;
            // 
            // colNotes1
            // 
            this.colNotes1.FieldName = "Notes";
            this.colNotes1.Name = "colNotes1";
            this.colNotes1.Visible = true;
            this.colNotes1.VisibleIndex = 2;
            // 
            // grdBenefits
            // 
            this.grdBenefits.DataSource = this.empSalaryExtensionBindingSource;
            this.grdBenefits.EmbeddedNavigator.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.grdBenefits.Location = new System.Drawing.Point(36, 383);
            this.grdBenefits.MainView = this.viewBenefits;
            this.grdBenefits.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.grdBenefits.Name = "grdBenefits";
            this.grdBenefits.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repoExp,
            this.repositoryItemImageComboBox1});
            this.grdBenefits.Size = new System.Drawing.Size(462, 115);
            this.grdBenefits.TabIndex = 65;
            this.grdBenefits.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.viewBenefits});
            // 
            // empSalaryExtensionBindingSource
            // 
            this.empSalaryExtensionBindingSource.DataSource = typeof(EasyStock.HR.Models.EmpSalaryExtension);
            // 
            // viewBenefits
            // 
            this.viewBenefits.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colSalaryExtensionId,
            this.colValue,
            this.colNotes});
            this.viewBenefits.DetailHeight = 284;
            this.viewBenefits.GridControl = this.grdBenefits;
            this.viewBenefits.Name = "viewBenefits";
            // 
            // colSalaryExtensionId
            // 
            this.colSalaryExtensionId.ColumnEdit = this.repoExp;
            this.colSalaryExtensionId.FieldName = "SalaryExtensionId";
            this.colSalaryExtensionId.Name = "colSalaryExtensionId";
            this.colSalaryExtensionId.Visible = true;
            this.colSalaryExtensionId.VisibleIndex = 0;
            // 
            // repoExp
            // 
            this.repoExp.AutoHeight = false;
            this.repoExp.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repoExp.DataSource = this.salaryExtensionBindingSource;
            this.repoExp.DisplayMember = "Name";
            this.repoExp.Name = "repoExp";
            this.repoExp.ValueMember = "ID";
            // 
            // colValue
            // 
            this.colValue.FieldName = "Value";
            this.colValue.Name = "colValue";
            this.colValue.Visible = true;
            this.colValue.VisibleIndex = 1;
            // 
            // colNotes
            // 
            this.colNotes.FieldName = "Notes";
            this.colNotes.Name = "colNotes";
            this.colNotes.Visible = true;
            this.colNotes.VisibleIndex = 2;
            // 
            // repositoryItemImageComboBox1
            // 
            this.repositoryItemImageComboBox1.AutoHeight = false;
            this.repositoryItemImageComboBox1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemImageComboBox1.Name = "repositoryItemImageComboBox1";
            this.repositoryItemImageComboBox1.ReadOnly = true;
            // 
            // btnAddImg
            // 
            this.btnAddImg.ImageOptions.SvgImage = global::EasyStock.HR.Properties.Resources.imageimport;
            this.btnAddImg.Location = new System.Drawing.Point(417, 116);
            this.btnAddImg.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnAddImg.Name = "btnAddImg";
            this.btnAddImg.Size = new System.Drawing.Size(43, 36);
            this.btnAddImg.StyleController = this.dataLayoutControl1;
            this.btnAddImg.TabIndex = 64;
            this.btnAddImg.Click += new System.EventHandler(this.btnAddImg_Click);
            // 
            // IDTextEdit
            // 
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "ID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.IDTextEdit.Location = new System.Drawing.Point(-209, 10);
            this.IDTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.IDTextEdit.Properties.MaskSettings.Set("mask", "N0");
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(729, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 4;
            // 
            // EmpBindingSource
            // 
            this.EmpBindingSource.DataSource = typeof(EasyStock.HR.Models.Employee);
            // 
            // CodeTextEdit
            // 
            this.CodeTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "Code", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.CodeTextEdit.Location = new System.Drawing.Point(230, 44);
            this.CodeTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.CodeTextEdit.Name = "CodeTextEdit";
            this.CodeTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.CodeTextEdit.Size = new System.Drawing.Size(104, 20);
            this.CodeTextEdit.StyleController = this.dataLayoutControl1;
            this.CodeTextEdit.TabIndex = 5;
            // 
            // NameTextEdit
            // 
            this.NameTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "Name", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NameTextEdit.Location = new System.Drawing.Point(230, 68);
            this.NameTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.NameTextEdit.Name = "NameTextEdit";
            this.NameTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.NameTextEdit.Size = new System.Drawing.Size(171, 20);
            this.NameTextEdit.StyleController = this.dataLayoutControl1;
            this.NameTextEdit.TabIndex = 6;
            // 
            // BirthPlaceTextEdit
            // 
            this.BirthPlaceTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "BirthPlace", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.BirthPlaceTextEdit.Location = new System.Drawing.Point(480, 146);
            this.BirthPlaceTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.BirthPlaceTextEdit.Name = "BirthPlaceTextEdit";
            this.BirthPlaceTextEdit.Size = new System.Drawing.Size(429, 20);
            this.BirthPlaceTextEdit.StyleController = this.dataLayoutControl1;
            this.BirthPlaceTextEdit.TabIndex = 19;
            // 
            // JobTextEdit
            // 
            this.JobTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "Job", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.JobTextEdit.Location = new System.Drawing.Point(165, 557);
            this.JobTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.JobTextEdit.Name = "JobTextEdit";
            this.JobTextEdit.Size = new System.Drawing.Size(511, 20);
            this.JobTextEdit.StyleController = this.dataLayoutControl1;
            this.JobTextEdit.TabIndex = 31;
            // 
            // AbsenceRegulationTextEdit
            // 
            this.AbsenceRegulationTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "AbsenceRegulation", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.AbsenceRegulationTextEdit.Location = new System.Drawing.Point(50, 240);
            this.AbsenceRegulationTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.AbsenceRegulationTextEdit.Name = "AbsenceRegulationTextEdit";
            this.AbsenceRegulationTextEdit.Size = new System.Drawing.Size(225, 20);
            this.AbsenceRegulationTextEdit.StyleController = this.dataLayoutControl1;
            this.AbsenceRegulationTextEdit.TabIndex = 33;
            // 
            // DelayRegulationTextEdit
            // 
            this.DelayRegulationTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "DelayRegulation", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DelayRegulationTextEdit.Location = new System.Drawing.Point(50, 283);
            this.DelayRegulationTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.DelayRegulationTextEdit.Name = "DelayRegulationTextEdit";
            this.DelayRegulationTextEdit.Size = new System.Drawing.Size(225, 20);
            this.DelayRegulationTextEdit.StyleController = this.dataLayoutControl1;
            this.DelayRegulationTextEdit.TabIndex = 35;
            // 
            // OverTimeRegulationTextEdit
            // 
            this.OverTimeRegulationTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "OverTimeRegulation", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.OverTimeRegulationTextEdit.Location = new System.Drawing.Point(50, 325);
            this.OverTimeRegulationTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.OverTimeRegulationTextEdit.Name = "OverTimeRegulationTextEdit";
            this.OverTimeRegulationTextEdit.Size = new System.Drawing.Size(225, 20);
            this.OverTimeRegulationTextEdit.StyleController = this.dataLayoutControl1;
            this.OverTimeRegulationTextEdit.TabIndex = 37;
            // 
            // SalaryRegulationTextEdit
            // 
            this.SalaryRegulationTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "SalaryRegulation", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.SalaryRegulationTextEdit.Location = new System.Drawing.Point(50, 367);
            this.SalaryRegulationTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.SalaryRegulationTextEdit.Name = "SalaryRegulationTextEdit";
            this.SalaryRegulationTextEdit.Size = new System.Drawing.Size(225, 20);
            this.SalaryRegulationTextEdit.StyleController = this.dataLayoutControl1;
            this.SalaryRegulationTextEdit.TabIndex = 39;
            // 
            // GenderImageComboBoxEdit
            // 
            this.GenderImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "Gender", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.GenderImageComboBoxEdit.Location = new System.Drawing.Point(615, 291);
            this.GenderImageComboBoxEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.GenderImageComboBoxEdit.Name = "GenderImageComboBoxEdit";
            this.GenderImageComboBoxEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.GenderImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.GenderImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.GenderImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.GenderImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Homme", EasyStock.HR.GenderType.Male, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Femme", EasyStock.HR.GenderType.Female, 1)});
            this.GenderImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.GenderImageComboBoxEdit.Size = new System.Drawing.Size(168, 20);
            this.GenderImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.GenderImageComboBoxEdit.TabIndex = 7;
            // 
            // NationalIDTextEdit
            // 
            this.NationalIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "NationalID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NationalIDTextEdit.Location = new System.Drawing.Point(617, 363);
            this.NationalIDTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.NationalIDTextEdit.Name = "NationalIDTextEdit";
            this.NationalIDTextEdit.Size = new System.Drawing.Size(166, 20);
            this.NationalIDTextEdit.StyleController = this.dataLayoutControl1;
            this.NationalIDTextEdit.TabIndex = 11;
            // 
            // InsuranceNoTextEdit
            // 
            this.InsuranceNoTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "InsuranceNo", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.InsuranceNoTextEdit.Location = new System.Drawing.Point(242, 387);
            this.InsuranceNoTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.InsuranceNoTextEdit.Name = "InsuranceNoTextEdit";
            this.InsuranceNoTextEdit.Size = new System.Drawing.Size(165, 20);
            this.InsuranceNoTextEdit.StyleController = this.dataLayoutControl1;
            this.InsuranceNoTextEdit.TabIndex = 12;
            // 
            // InsuranceDateDateEdit
            // 
            this.InsuranceDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "InsuranceDate", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.InsuranceDateDateEdit.EditValue = null;
            this.InsuranceDateDateEdit.Location = new System.Drawing.Point(617, 387);
            this.InsuranceDateDateEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.InsuranceDateDateEdit.Name = "InsuranceDateDateEdit";
            this.InsuranceDateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.InsuranceDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.InsuranceDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.InsuranceDateDateEdit.Size = new System.Drawing.Size(166, 20);
            this.InsuranceDateDateEdit.StyleController = this.dataLayoutControl1;
            this.InsuranceDateDateEdit.TabIndex = 13;
            // 
            // DrivingLicenseTextEdit
            // 
            this.DrivingLicenseTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "DrivingLicense", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DrivingLicenseTextEdit.Location = new System.Drawing.Point(242, 411);
            this.DrivingLicenseTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.DrivingLicenseTextEdit.Name = "DrivingLicenseTextEdit";
            this.DrivingLicenseTextEdit.Size = new System.Drawing.Size(165, 20);
            this.DrivingLicenseTextEdit.StyleController = this.dataLayoutControl1;
            this.DrivingLicenseTextEdit.TabIndex = 14;
            // 
            // EndDateDateEdit
            // 
            this.EndDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "EndDate", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.EndDateDateEdit.EditValue = null;
            this.EndDateDateEdit.Location = new System.Drawing.Point(242, 363);
            this.EndDateDateEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.EndDateDateEdit.Name = "EndDateDateEdit";
            this.EndDateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.EndDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.EndDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.EndDateDateEdit.Size = new System.Drawing.Size(165, 20);
            this.EndDateDateEdit.StyleController = this.dataLayoutControl1;
            this.EndDateDateEdit.TabIndex = 15;
            // 
            // BankNameTextEdit
            // 
            this.BankNameTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "BankName", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.BankNameTextEdit.Location = new System.Drawing.Point(242, 483);
            this.BankNameTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.BankNameTextEdit.Name = "BankNameTextEdit";
            this.BankNameTextEdit.Size = new System.Drawing.Size(165, 20);
            this.BankNameTextEdit.StyleController = this.dataLayoutControl1;
            this.BankNameTextEdit.TabIndex = 16;
            // 
            // AccountNoTextEdit
            // 
            this.AccountNoTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "AccountNo", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.AccountNoTextEdit.Location = new System.Drawing.Point(617, 483);
            this.AccountNoTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.AccountNoTextEdit.Name = "AccountNoTextEdit";
            this.AccountNoTextEdit.Size = new System.Drawing.Size(166, 20);
            this.AccountNoTextEdit.StyleController = this.dataLayoutControl1;
            this.AccountNoTextEdit.TabIndex = 17;
            // 
            // BirthDateDateEdit
            // 
            this.BirthDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "BirthDate", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.BirthDateDateEdit.EditValue = null;
            this.BirthDateDateEdit.Location = new System.Drawing.Point(617, 315);
            this.BirthDateDateEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.BirthDateDateEdit.Name = "BirthDateDateEdit";
            this.BirthDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.BirthDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.BirthDateDateEdit.Size = new System.Drawing.Size(166, 20);
            this.BirthDateDateEdit.StyleController = this.dataLayoutControl1;
            this.BirthDateDateEdit.TabIndex = 18;
            // 
            // MaritalStatusImageComboBoxEdit
            // 
            this.MaritalStatusImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "MaritalStatus", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.MaritalStatusImageComboBoxEdit.Location = new System.Drawing.Point(242, 291);
            this.MaritalStatusImageComboBoxEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.MaritalStatusImageComboBoxEdit.Name = "MaritalStatusImageComboBoxEdit";
            this.MaritalStatusImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.MaritalStatusImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.MaritalStatusImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.MaritalStatusImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Célibataire", EasyStock.HR.MaritalStatus.Single, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Marié", EasyStock.HR.MaritalStatus.Married, 1)});
            this.MaritalStatusImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.MaritalStatusImageComboBoxEdit.Size = new System.Drawing.Size(163, 20);
            this.MaritalStatusImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.MaritalStatusImageComboBoxEdit.TabIndex = 21;
            // 
            // MilitarilyStatusImageComboBoxEdit
            // 
            this.MilitarilyStatusImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "MilitarilyStatus", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.MilitarilyStatusImageComboBoxEdit.Location = new System.Drawing.Point(617, 411);
            this.MilitarilyStatusImageComboBoxEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.MilitarilyStatusImageComboBoxEdit.Name = "MilitarilyStatusImageComboBoxEdit";
            this.MilitarilyStatusImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.MilitarilyStatusImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.MilitarilyStatusImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.MilitarilyStatusImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Exemption", EasyStock.HR.MilitarilyStatus.Exemption, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Reporté", EasyStock.HR.MilitarilyStatus.Delayed, 1),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Accompli", EasyStock.HR.MilitarilyStatus.Completion, 2)});
            this.MilitarilyStatusImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.MilitarilyStatusImageComboBoxEdit.Size = new System.Drawing.Size(166, 20);
            this.MilitarilyStatusImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.MilitarilyStatusImageComboBoxEdit.TabIndex = 22;
            // 
            // PhoneTextEdit
            // 
            this.PhoneTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "Phone", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.PhoneTextEdit.Location = new System.Drawing.Point(617, 459);
            this.PhoneTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.PhoneTextEdit.Name = "PhoneTextEdit";
            this.PhoneTextEdit.Size = new System.Drawing.Size(166, 20);
            this.PhoneTextEdit.StyleController = this.dataLayoutControl1;
            this.PhoneTextEdit.TabIndex = 23;
            // 
            // EmailTextEdit
            // 
            this.EmailTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "Email", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.EmailTextEdit.Location = new System.Drawing.Point(242, 459);
            this.EmailTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.EmailTextEdit.Name = "EmailTextEdit";
            this.EmailTextEdit.Size = new System.Drawing.Size(165, 20);
            this.EmailTextEdit.StyleController = this.dataLayoutControl1;
            this.EmailTextEdit.TabIndex = 24;
            // 
            // AddressTextEdit
            // 
            this.AddressTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "Address", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.AddressTextEdit.Location = new System.Drawing.Point(242, 435);
            this.AddressTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.AddressTextEdit.Name = "AddressTextEdit";
            this.AddressTextEdit.Size = new System.Drawing.Size(541, 20);
            this.AddressTextEdit.StyleController = this.dataLayoutControl1;
            this.AddressTextEdit.TabIndex = 25;
            // 
            // FingerprintCodeTextEdit
            // 
            this.FingerprintCodeTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "FingerprintCode", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.FingerprintCodeTextEdit.Location = new System.Drawing.Point(242, 291);
            this.FingerprintCodeTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.FingerprintCodeTextEdit.Name = "FingerprintCodeTextEdit";
            this.FingerprintCodeTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.FingerprintCodeTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.FingerprintCodeTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.FingerprintCodeTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.FingerprintCodeTextEdit.Properties.MaskSettings.Set("mask", "N0");
            this.FingerprintCodeTextEdit.Size = new System.Drawing.Size(153, 20);
            this.FingerprintCodeTextEdit.StyleController = this.dataLayoutControl1;
            this.FingerprintCodeTextEdit.TabIndex = 26;
            // 
            // ContractTypeImageComboBoxEdit
            // 
            this.ContractTypeImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "ContractType", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ContractTypeImageComboBoxEdit.Location = new System.Drawing.Point(629, 291);
            this.ContractTypeImageComboBoxEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.ContractTypeImageComboBoxEdit.Name = "ContractTypeImageComboBoxEdit";
            this.ContractTypeImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.ContractTypeImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.ContractTypeImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ContractTypeImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Temporaire", EasyStock.HR.ContractTypes.Temporary, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Permanent", EasyStock.HR.ContractTypes.Peremenant, 1)});
            this.ContractTypeImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.ContractTypeImageComboBoxEdit.Size = new System.Drawing.Size(156, 20);
            this.ContractTypeImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.ContractTypeImageComboBoxEdit.TabIndex = 27;
            // 
            // ContractPeriodTextEdit
            // 
            this.ContractPeriodTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "ContractPeriod", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ContractPeriodTextEdit.Location = new System.Drawing.Point(629, 315);
            this.ContractPeriodTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.ContractPeriodTextEdit.Name = "ContractPeriodTextEdit";
            this.ContractPeriodTextEdit.Size = new System.Drawing.Size(156, 20);
            this.ContractPeriodTextEdit.StyleController = this.dataLayoutControl1;
            this.ContractPeriodTextEdit.TabIndex = 28;
            // 
            // ContactEndDateDateEdit
            // 
            this.ContactEndDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "ContactEndDate", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ContactEndDateDateEdit.EditValue = null;
            this.ContactEndDateDateEdit.Location = new System.Drawing.Point(629, 363);
            this.ContactEndDateDateEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.ContactEndDateDateEdit.Name = "ContactEndDateDateEdit";
            this.ContactEndDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ContactEndDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ContactEndDateDateEdit.Size = new System.Drawing.Size(156, 20);
            this.ContactEndDateDateEdit.StyleController = this.dataLayoutControl1;
            this.ContactEndDateDateEdit.TabIndex = 29;
            // 
            // DateOfRecruitmentDateEdit
            // 
            this.DateOfRecruitmentDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "DateOfRecruitment", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DateOfRecruitmentDateEdit.EditValue = null;
            this.DateOfRecruitmentDateEdit.Location = new System.Drawing.Point(629, 339);
            this.DateOfRecruitmentDateEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.DateOfRecruitmentDateEdit.Name = "DateOfRecruitmentDateEdit";
            this.DateOfRecruitmentDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DateOfRecruitmentDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DateOfRecruitmentDateEdit.Size = new System.Drawing.Size(156, 20);
            this.DateOfRecruitmentDateEdit.StyleController = this.dataLayoutControl1;
            this.DateOfRecruitmentDateEdit.TabIndex = 30;
            // 
            // GroupIDTextEdit
            // 
            this.GroupIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "GroupID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.GroupIDTextEdit.Location = new System.Drawing.Point(230, 164);
            this.GroupIDTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.GroupIDTextEdit.Name = "GroupIDTextEdit";
            this.GroupIDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.GroupIDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.GroupIDTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.GroupIDTextEdit.Properties.DisplayMember = "Name";
            this.GroupIDTextEdit.Properties.NullText = "";
            this.GroupIDTextEdit.Properties.ValueMember = "ID";
            this.GroupIDTextEdit.Size = new System.Drawing.Size(171, 20);
            this.GroupIDTextEdit.StyleController = this.dataLayoutControl1;
            this.GroupIDTextEdit.TabIndex = 10;
            // 
            // JobIDTextEdit
            // 
            this.JobIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "JobID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.JobIDTextEdit.Location = new System.Drawing.Point(230, 188);
            this.JobIDTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.JobIDTextEdit.Name = "JobIDTextEdit";
            this.JobIDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.JobIDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.JobIDTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.JobIDTextEdit.Properties.DisplayMember = "Name";
            this.JobIDTextEdit.Properties.NullText = "";
            this.JobIDTextEdit.Properties.ValueMember = "ID";
            this.JobIDTextEdit.Size = new System.Drawing.Size(171, 20);
            this.JobIDTextEdit.StyleController = this.dataLayoutControl1;
            this.JobIDTextEdit.TabIndex = 32;
            // 
            // BranchTextEdit
            // 
            this.BranchTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "Branch", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.BranchTextEdit.Location = new System.Drawing.Point(230, 116);
            this.BranchTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.BranchTextEdit.Name = "BranchTextEdit";
            this.BranchTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.BranchTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.BranchTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.BranchTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.BranchTextEdit.Properties.NullText = "";
            this.BranchTextEdit.Size = new System.Drawing.Size(171, 20);
            this.BranchTextEdit.StyleController = this.dataLayoutControl1;
            this.BranchTextEdit.TabIndex = 8;
            // 
            // DepartmentIDTextEdit
            // 
            this.DepartmentIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "DepartmentID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DepartmentIDTextEdit.Location = new System.Drawing.Point(230, 140);
            this.DepartmentIDTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.DepartmentIDTextEdit.Name = "DepartmentIDTextEdit";
            this.DepartmentIDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DepartmentIDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DepartmentIDTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DepartmentIDTextEdit.Properties.DisplayMember = "Name";
            this.DepartmentIDTextEdit.Properties.NullText = "";
            this.DepartmentIDTextEdit.Properties.ValueMember = "ID";
            this.DepartmentIDTextEdit.Size = new System.Drawing.Size(171, 20);
            this.DepartmentIDTextEdit.StyleController = this.dataLayoutControl1;
            this.DepartmentIDTextEdit.TabIndex = 9;
            // 
            // NationalityTextEdit
            // 
            this.NationalityTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "Nationality", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NationalityTextEdit.Location = new System.Drawing.Point(480, 167);
            this.NationalityTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.NationalityTextEdit.Name = "NationalityTextEdit";
            this.NationalityTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.NationalityTextEdit.Properties.NullText = "";
            this.NationalityTextEdit.Properties.PopupFilterMode = DevExpress.XtraEditors.PopupFilterMode.Contains;
            this.NationalityTextEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.NationalityTextEdit.Size = new System.Drawing.Size(99, 20);
            this.NationalityTextEdit.StyleController = this.dataLayoutControl1;
            this.NationalityTextEdit.TabIndex = 20;
            // 
            // PayPeriodImageComboBoxEdit
            // 
            this.PayPeriodImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "PayPeriod", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.PayPeriodImageComboBoxEdit.Location = new System.Drawing.Point(242, 291);
            this.PayPeriodImageComboBoxEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.PayPeriodImageComboBoxEdit.Name = "PayPeriodImageComboBoxEdit";
            this.PayPeriodImageComboBoxEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.PayPeriodImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.PayPeriodImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.PayPeriodImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.PayPeriodImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Semaine", EasyStock.HR.ESalaryPeriod.Week, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Deux semaines", EasyStock.HR.ESalaryPeriod.TwoWeek, 1),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Mois", EasyStock.HR.ESalaryPeriod.Month, 2),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("À la pièce", EasyStock.HR.ESalaryPeriod.Piece, 3)});
            this.PayPeriodImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.PayPeriodImageComboBoxEdit.Size = new System.Drawing.Size(77, 20);
            this.PayPeriodImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.PayPeriodImageComboBoxEdit.TabIndex = 46;
            // 
            // CalcIncomeTaxCheckEdit
            // 
            this.CalcIncomeTaxCheckEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "CalcIncomeTax", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.CalcIncomeTaxCheckEdit.Location = new System.Drawing.Point(36, 315);
            this.CalcIncomeTaxCheckEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.CalcIncomeTaxCheckEdit.Name = "CalcIncomeTaxCheckEdit";
            this.CalcIncomeTaxCheckEdit.Properties.Caption = "Calculer l\'impôt sur le revenu automatiquement";
            this.CalcIncomeTaxCheckEdit.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Default;
            this.CalcIncomeTaxCheckEdit.Size = new System.Drawing.Size(246, 19);
            this.CalcIncomeTaxCheckEdit.StyleController = this.dataLayoutControl1;
            this.CalcIncomeTaxCheckEdit.TabIndex = 51;
            // 
            // FristShiftIdTextEdit
            // 
            this.FristShiftIdTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "FristShiftId", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.FristShiftIdTextEdit.Location = new System.Drawing.Point(242, 315);
            this.FristShiftIdTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.FristShiftIdTextEdit.Name = "FristShiftIdTextEdit";
            this.FristShiftIdTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.FristShiftIdTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.FristShiftIdTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.FristShiftIdTextEdit.Properties.NullText = "";
            this.FristShiftIdTextEdit.Size = new System.Drawing.Size(153, 20);
            this.FristShiftIdTextEdit.StyleController = this.dataLayoutControl1;
            this.FristShiftIdTextEdit.TabIndex = 44;
            // 
            // SecondShiftIdTextEdit
            // 
            this.SecondShiftIdTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "SecondShiftId", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.SecondShiftIdTextEdit.Location = new System.Drawing.Point(242, 339);
            this.SecondShiftIdTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.SecondShiftIdTextEdit.Name = "SecondShiftIdTextEdit";
            this.SecondShiftIdTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.SecondShiftIdTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.SecondShiftIdTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.SecondShiftIdTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.SecondShiftIdTextEdit.Properties.NullText = "";
            this.SecondShiftIdTextEdit.Size = new System.Drawing.Size(153, 20);
            this.SecondShiftIdTextEdit.StyleController = this.dataLayoutControl1;
            this.SecondShiftIdTextEdit.TabIndex = 45;
            // 
            // AbsenceRegistionIDTextEdit
            // 
            this.AbsenceRegistionIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "AbsenceRegistionID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.AbsenceRegistionIDTextEdit.Location = new System.Drawing.Point(242, 363);
            this.AbsenceRegistionIDTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.AbsenceRegistionIDTextEdit.Name = "AbsenceRegistionIDTextEdit";
            this.AbsenceRegistionIDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.AbsenceRegistionIDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.AbsenceRegistionIDTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.AbsenceRegistionIDTextEdit.Properties.NullText = "";
            this.AbsenceRegistionIDTextEdit.Size = new System.Drawing.Size(153, 20);
            this.AbsenceRegistionIDTextEdit.StyleController = this.dataLayoutControl1;
            this.AbsenceRegistionIDTextEdit.TabIndex = 34;
            // 
            // DelayRegulationIDTextEdit
            // 
            this.DelayRegulationIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "DelayRegulationID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DelayRegulationIDTextEdit.Location = new System.Drawing.Point(242, 387);
            this.DelayRegulationIDTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.DelayRegulationIDTextEdit.Name = "DelayRegulationIDTextEdit";
            this.DelayRegulationIDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DelayRegulationIDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DelayRegulationIDTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DelayRegulationIDTextEdit.Properties.NullText = "";
            this.DelayRegulationIDTextEdit.Size = new System.Drawing.Size(153, 20);
            this.DelayRegulationIDTextEdit.StyleController = this.dataLayoutControl1;
            this.DelayRegulationIDTextEdit.TabIndex = 36;
            // 
            // OverTimeRegulationIDTextEdit
            // 
            this.OverTimeRegulationIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "OverTimeRegulationID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.OverTimeRegulationIDTextEdit.Location = new System.Drawing.Point(242, 411);
            this.OverTimeRegulationIDTextEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.OverTimeRegulationIDTextEdit.Name = "OverTimeRegulationIDTextEdit";
            this.OverTimeRegulationIDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.OverTimeRegulationIDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.OverTimeRegulationIDTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.OverTimeRegulationIDTextEdit.Properties.NullText = "";
            this.OverTimeRegulationIDTextEdit.Size = new System.Drawing.Size(153, 20);
            this.OverTimeRegulationIDTextEdit.StyleController = this.dataLayoutControl1;
            this.OverTimeRegulationIDTextEdit.TabIndex = 38;
            // 
            // SalaryBasicSpinEdit
            // 
            this.SalaryBasicSpinEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "SalaryBasic", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.SalaryBasicSpinEdit.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.SalaryBasicSpinEdit.Location = new System.Drawing.Point(401, 291);
            this.SalaryBasicSpinEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.SalaryBasicSpinEdit.Name = "SalaryBasicSpinEdit";
            this.SalaryBasicSpinEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.SalaryBasicSpinEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.SalaryBasicSpinEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.SalaryBasicSpinEdit.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.SalaryBasicSpinEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.SalaryBasicSpinEdit.Properties.MaskSettings.Set("mask", "F");
            this.SalaryBasicSpinEdit.Size = new System.Drawing.Size(205, 20);
            this.SalaryBasicSpinEdit.StyleController = this.dataLayoutControl1;
            this.SalaryBasicSpinEdit.TabIndex = 52;
            // 
            // SalaryVariableSpinEdit
            // 
            this.SalaryVariableSpinEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "SalaryVariable", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.SalaryVariableSpinEdit.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.SalaryVariableSpinEdit.Location = new System.Drawing.Point(688, 291);
            this.SalaryVariableSpinEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.SalaryVariableSpinEdit.Name = "SalaryVariableSpinEdit";
            this.SalaryVariableSpinEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.SalaryVariableSpinEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.SalaryVariableSpinEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.SalaryVariableSpinEdit.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.SalaryVariableSpinEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.SalaryVariableSpinEdit.Properties.MaskSettings.Set("mask", "F");
            this.SalaryVariableSpinEdit.Size = new System.Drawing.Size(109, 20);
            this.SalaryVariableSpinEdit.StyleController = this.dataLayoutControl1;
            this.SalaryVariableSpinEdit.TabIndex = 53;
            // 
            // DayValueSpinEdit
            // 
            this.DayValueSpinEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "DayValue", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DayValueSpinEdit.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.DayValueSpinEdit.Location = new System.Drawing.Point(524, 315);
            this.DayValueSpinEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.DayValueSpinEdit.Name = "DayValueSpinEdit";
            this.DayValueSpinEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.DayValueSpinEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DayValueSpinEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DayValueSpinEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DayValueSpinEdit.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.DayValueSpinEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.DayValueSpinEdit.Properties.MaskSettings.Set("mask", "F");
            this.DayValueSpinEdit.Size = new System.Drawing.Size(82, 20);
            this.DayValueSpinEdit.StyleController = this.dataLayoutControl1;
            this.DayValueSpinEdit.TabIndex = 54;
            // 
            // HourValueSpinEdit
            // 
            this.HourValueSpinEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "HourValue", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.HourValueSpinEdit.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.HourValueSpinEdit.Location = new System.Drawing.Point(371, 315);
            this.HourValueSpinEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.HourValueSpinEdit.Name = "HourValueSpinEdit";
            this.HourValueSpinEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.HourValueSpinEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.HourValueSpinEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.HourValueSpinEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.HourValueSpinEdit.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.HourValueSpinEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.HourValueSpinEdit.Properties.MaskSettings.Set("mask", "F");
            this.HourValueSpinEdit.Size = new System.Drawing.Size(73, 20);
            this.HourValueSpinEdit.StyleController = this.dataLayoutControl1;
            this.HourValueSpinEdit.TabIndex = 55;
            // 
            // ExpensesAccountIdLookUpEdit
            // 
            this.ExpensesAccountIdLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "ExpensesAccountId", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ExpensesAccountIdLookUpEdit.Location = new System.Drawing.Point(912, 291);
            this.ExpensesAccountIdLookUpEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.ExpensesAccountIdLookUpEdit.Name = "ExpensesAccountIdLookUpEdit";
            this.ExpensesAccountIdLookUpEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.ExpensesAccountIdLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.ExpensesAccountIdLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.ExpensesAccountIdLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ExpensesAccountIdLookUpEdit.Properties.NullText = "";
            this.ExpensesAccountIdLookUpEdit.Size = new System.Drawing.Size(77, 20);
            this.ExpensesAccountIdLookUpEdit.StyleController = this.dataLayoutControl1;
            this.ExpensesAccountIdLookUpEdit.TabIndex = 56;
            // 
            // AccruedAccountIdLookUpEdit
            // 
            this.AccruedAccountIdLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "AccruedAccountId", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.AccruedAccountIdLookUpEdit.Location = new System.Drawing.Point(714, 315);
            this.AccruedAccountIdLookUpEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.AccruedAccountIdLookUpEdit.Name = "AccruedAccountIdLookUpEdit";
            this.AccruedAccountIdLookUpEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.AccruedAccountIdLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.AccruedAccountIdLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.AccruedAccountIdLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.AccruedAccountIdLookUpEdit.Properties.NullText = "";
            this.AccruedAccountIdLookUpEdit.Size = new System.Drawing.Size(275, 20);
            this.AccruedAccountIdLookUpEdit.StyleController = this.dataLayoutControl1;
            this.AccruedAccountIdLookUpEdit.TabIndex = 57;
            // 
            // PhotoPictureEdit
            // 
            this.PhotoPictureEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "Photo", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.PhotoPictureEdit.Location = new System.Drawing.Point(464, 76);
            this.PhotoPictureEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.PhotoPictureEdit.Name = "PhotoPictureEdit";
            this.PhotoPictureEdit.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Squeeze;
            this.PhotoPictureEdit.Size = new System.Drawing.Size(168, 119);
            this.PhotoPictureEdit.StyleController = this.dataLayoutControl1;
            this.PhotoPictureEdit.TabIndex = 58;
            // 
            // BirthCountryIdLookUpEdit
            // 
            this.BirthCountryIdLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "BirthPlaceId", true));
            this.BirthCountryIdLookUpEdit.Location = new System.Drawing.Point(242, 315);
            this.BirthCountryIdLookUpEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.BirthCountryIdLookUpEdit.Name = "BirthCountryIdLookUpEdit";
            this.BirthCountryIdLookUpEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.BirthCountryIdLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.BirthCountryIdLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.BirthCountryIdLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.BirthCountryIdLookUpEdit.Properties.NullText = "";
            this.BirthCountryIdLookUpEdit.Size = new System.Drawing.Size(165, 20);
            this.BirthCountryIdLookUpEdit.StyleController = this.dataLayoutControl1;
            this.BirthCountryIdLookUpEdit.TabIndex = 59;
            // 
            // NationalityIdLookUpEdit
            // 
            this.NationalityIdLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "NationalityId", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NationalityIdLookUpEdit.Location = new System.Drawing.Point(617, 339);
            this.NationalityIdLookUpEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.NationalityIdLookUpEdit.Name = "NationalityIdLookUpEdit";
            this.NationalityIdLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.NationalityIdLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.NationalityIdLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.NationalityIdLookUpEdit.Properties.DisplayMember = "Name";
            this.NationalityIdLookUpEdit.Properties.NullText = "";
            this.NationalityIdLookUpEdit.Properties.ValueMember = "ID";
            this.NationalityIdLookUpEdit.Size = new System.Drawing.Size(166, 20);
            this.NationalityIdLookUpEdit.StyleController = this.dataLayoutControl1;
            this.NationalityIdLookUpEdit.TabIndex = 60;
            // 
            // ReligionIdLookUpEdit
            // 
            this.ReligionIdLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "ReligionId", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ReligionIdLookUpEdit.Location = new System.Drawing.Point(242, 339);
            this.ReligionIdLookUpEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.ReligionIdLookUpEdit.Name = "ReligionIdLookUpEdit";
            this.ReligionIdLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.ReligionIdLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.ReligionIdLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ReligionIdLookUpEdit.Properties.DisplayMember = "Name";
            this.ReligionIdLookUpEdit.Properties.NullText = "";
            this.ReligionIdLookUpEdit.Properties.ValueMember = "ID";
            this.ReligionIdLookUpEdit.Size = new System.Drawing.Size(165, 20);
            this.ReligionIdLookUpEdit.StyleController = this.dataLayoutControl1;
            this.ReligionIdLookUpEdit.TabIndex = 61;
            // 
            // QualificationIdLookUpEdit
            // 
            this.QualificationIdLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.EmpBindingSource, "QualificationId", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.QualificationIdLookUpEdit.Location = new System.Drawing.Point(230, 92);
            this.QualificationIdLookUpEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.QualificationIdLookUpEdit.Name = "QualificationIdLookUpEdit";
            this.QualificationIdLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.QualificationIdLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.QualificationIdLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.QualificationIdLookUpEdit.Properties.DisplayMember = "Name";
            this.QualificationIdLookUpEdit.Properties.NullText = "";
            this.QualificationIdLookUpEdit.Properties.ValueMember = "ID";
            this.QualificationIdLookUpEdit.Size = new System.Drawing.Size(171, 20);
            this.QualificationIdLookUpEdit.StyleController = this.dataLayoutControl1;
            this.QualificationIdLookUpEdit.TabIndex = 62;
            // 
            // btnRemoveImg
            // 
            this.btnRemoveImg.ImageOptions.SvgImage = global::EasyStock.HR.Properties.Resources.delete;
            this.btnRemoveImg.Location = new System.Drawing.Point(417, 76);
            this.btnRemoveImg.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnRemoveImg.Name = "btnRemoveImg";
            this.btnRemoveImg.Size = new System.Drawing.Size(43, 36);
            this.btnRemoveImg.StyleController = this.dataLayoutControl1;
            this.btnRemoveImg.TabIndex = 63;
            this.btnRemoveImg.Click += new System.EventHandler(this.btnRemoveImg_Click);
            // 
            // layoutControlGroup2
            // 
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(735, 50);
            // 
            // ItemForJob
            // 
            this.ItemForJob.Control = this.JobTextEdit;
            this.ItemForJob.Location = new System.Drawing.Point(0, 674);
            this.ItemForJob.Name = "ItemForJob";
            this.ItemForJob.Size = new System.Drawing.Size(759, 26);
            this.ItemForJob.TextSize = new System.Drawing.Size(43, 16);
            // 
            // ItemForAbsenceRegulation
            // 
            this.ItemForAbsenceRegulation.Control = this.AbsenceRegulationTextEdit;
            this.ItemForAbsenceRegulation.Location = new System.Drawing.Point(0, 26);
            this.ItemForAbsenceRegulation.Name = "ItemForAbsenceRegulation";
            this.ItemForAbsenceRegulation.Size = new System.Drawing.Size(426, 26);
            this.ItemForAbsenceRegulation.TextSize = new System.Drawing.Size(126, 14);
            // 
            // ItemForDelayRegulation
            // 
            this.ItemForDelayRegulation.Control = this.DelayRegulationTextEdit;
            this.ItemForDelayRegulation.Location = new System.Drawing.Point(0, 78);
            this.ItemForDelayRegulation.Name = "ItemForDelayRegulation";
            this.ItemForDelayRegulation.Size = new System.Drawing.Size(426, 26);
            this.ItemForDelayRegulation.TextSize = new System.Drawing.Size(126, 14);
            // 
            // ItemForOverTimeRegulation
            // 
            this.ItemForOverTimeRegulation.Control = this.OverTimeRegulationTextEdit;
            this.ItemForOverTimeRegulation.Location = new System.Drawing.Point(0, 130);
            this.ItemForOverTimeRegulation.Name = "ItemForOverTimeRegulation";
            this.ItemForOverTimeRegulation.Size = new System.Drawing.Size(426, 26);
            this.ItemForOverTimeRegulation.TextSize = new System.Drawing.Size(126, 14);
            // 
            // ItemForSalaryRegulation
            // 
            this.ItemForSalaryRegulation.Control = this.SalaryRegulationTextEdit;
            this.ItemForSalaryRegulation.Location = new System.Drawing.Point(0, 182);
            this.ItemForSalaryRegulation.Name = "ItemForSalaryRegulation";
            this.ItemForSalaryRegulation.Size = new System.Drawing.Size(426, 26);
            this.ItemForSalaryRegulation.TextSize = new System.Drawing.Size(126, 14);
            // 
            // ItemForID
            // 
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(1036, 26);
            this.ItemForID.TextSize = new System.Drawing.Size(126, 14);
            // 
            // ItemForBirthPlace
            // 
            this.ItemForBirthPlace.Control = this.BirthPlaceTextEdit;
            this.ItemForBirthPlace.Location = new System.Drawing.Point(0, 26);
            this.ItemForBirthPlace.Name = "ItemForBirthPlace";
            this.ItemForBirthPlace.Size = new System.Drawing.Size(636, 26);
            this.ItemForBirthPlace.TextSize = new System.Drawing.Size(103, 14);
            // 
            // ItemForNationality
            // 
            this.ItemForNationality.Control = this.NationalityTextEdit;
            this.ItemForNationality.Location = new System.Drawing.Point(0, 26);
            this.ItemForNationality.Name = "ItemForNationality";
            this.ItemForNationality.Size = new System.Drawing.Size(251, 52);
            this.ItemForNationality.TextSize = new System.Drawing.Size(103, 14);
            // 
            // layoutControlGroup10
            // 
            this.layoutControlGroup10.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup10.Name = "layoutControlGroup10";
            this.layoutControlGroup10.Size = new System.Drawing.Size(985, 264);
            this.layoutControlGroup10.TextVisible = false;
            // 
            // tabbedControlGroup2
            // 
            this.tabbedControlGroup2.Location = new System.Drawing.Point(322, 168);
            this.tabbedControlGroup2.Name = "tabbedControlGroup2";
            this.tabbedControlGroup2.SelectedTabPage = this.layoutControlGroup13;
            this.tabbedControlGroup2.Size = new System.Drawing.Size(526, 92);
            this.tabbedControlGroup2.TabPages.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup13,
            this.layoutControlGroup14});
            // 
            // layoutControlGroup13
            // 
            this.layoutControlGroup13.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup13.Name = "layoutControlGroup13";
            this.layoutControlGroup13.Size = new System.Drawing.Size(502, 45);
            // 
            // layoutControlGroup14
            // 
            this.layoutControlGroup14.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup14.Name = "layoutControlGroup14";
            this.layoutControlGroup14.Size = new System.Drawing.Size(502, 45);
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(1025, 549);
            this.Root.TextVisible = false;
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.GroupStyle = DevExpress.Utils.GroupStyle.Card;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup8,
            this.tabbedControlGroup1,
            this.emptySpaceItem4});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(1005, 529);
            // 
            // layoutControlGroup8
            // 
            this.layoutControlGroup8.AppearanceGroup.BorderColor = System.Drawing.Color.DarkSlateBlue;
            this.layoutControlGroup8.AppearanceGroup.Options.UseBorderColor = true;
            this.layoutControlGroup8.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForDepartmentID,
            this.ItemForGroupID,
            this.ItemForJobID,
            this.ItemForCode,
            this.ItemForName,
            this.ItemForQualificationId,
            this.ItemForBranch,
            this.emptySpaceItem6,
            this.layoutControlItem5,
            this.layoutControlGroup9});
            this.layoutControlGroup8.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup8.Name = "layoutControlGroup8";
            this.layoutControlGroup8.Size = new System.Drawing.Size(648, 212);
            this.layoutControlGroup8.Text = "Informations personnelles";
            // 
            // ItemForDepartmentID
            // 
            this.ItemForDepartmentID.Control = this.DepartmentIDTextEdit;
            this.ItemForDepartmentID.Location = new System.Drawing.Point(0, 96);
            this.ItemForDepartmentID.Name = "ItemForDepartmentID";
            this.ItemForDepartmentID.Size = new System.Drawing.Size(381, 24);
            this.ItemForDepartmentID.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForGroupID
            // 
            this.ItemForGroupID.Control = this.GroupIDTextEdit;
            this.ItemForGroupID.Location = new System.Drawing.Point(0, 120);
            this.ItemForGroupID.Name = "ItemForGroupID";
            this.ItemForGroupID.Size = new System.Drawing.Size(381, 24);
            this.ItemForGroupID.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForJobID
            // 
            this.ItemForJobID.Control = this.JobIDTextEdit;
            this.ItemForJobID.Location = new System.Drawing.Point(0, 144);
            this.ItemForJobID.Name = "ItemForJobID";
            this.ItemForJobID.Size = new System.Drawing.Size(381, 24);
            this.ItemForJobID.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForCode
            // 
            this.ItemForCode.Control = this.CodeTextEdit;
            this.ItemForCode.Location = new System.Drawing.Point(0, 0);
            this.ItemForCode.MaxSize = new System.Drawing.Size(314, 24);
            this.ItemForCode.MinSize = new System.Drawing.Size(314, 24);
            this.ItemForCode.Name = "ItemForCode";
            this.ItemForCode.Size = new System.Drawing.Size(314, 24);
            this.ItemForCode.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForCode.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForName
            // 
            this.ItemForName.Control = this.NameTextEdit;
            this.ItemForName.Location = new System.Drawing.Point(0, 24);
            this.ItemForName.MaxSize = new System.Drawing.Size(381, 24);
            this.ItemForName.MinSize = new System.Drawing.Size(381, 24);
            this.ItemForName.Name = "ItemForName";
            this.ItemForName.Size = new System.Drawing.Size(381, 24);
            this.ItemForName.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForName.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForQualificationId
            // 
            this.ItemForQualificationId.Control = this.QualificationIdLookUpEdit;
            this.ItemForQualificationId.Location = new System.Drawing.Point(0, 48);
            this.ItemForQualificationId.Name = "ItemForQualificationId";
            this.ItemForQualificationId.Size = new System.Drawing.Size(381, 24);
            this.ItemForQualificationId.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForBranch
            // 
            this.ItemForBranch.Control = this.BranchTextEdit;
            this.ItemForBranch.Location = new System.Drawing.Point(0, 72);
            this.ItemForBranch.Name = "ItemForBranch";
            this.ItemForBranch.Size = new System.Drawing.Size(381, 24);
            this.ItemForBranch.TextSize = new System.Drawing.Size(202, 13);
            // 
            // emptySpaceItem6
            // 
            this.emptySpaceItem6.AllowHotTrack = false;
            this.emptySpaceItem6.Location = new System.Drawing.Point(359, 0);
            this.emptySpaceItem6.Name = "emptySpaceItem6";
            this.emptySpaceItem6.Size = new System.Drawing.Size(22, 24);
            this.emptySpaceItem6.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlItem5
            // 
            this.layoutControlItem5.Control = this.labelControl1;
            this.layoutControlItem5.Location = new System.Drawing.Point(314, 0);
            this.layoutControlItem5.Name = "layoutControlItem5";
            this.layoutControlItem5.Size = new System.Drawing.Size(45, 24);
            this.layoutControlItem5.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem5.TextVisible = false;
            // 
            // layoutControlGroup9
            // 
            this.layoutControlGroup9.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForPhoto,
            this.layoutControlItem1,
            this.layoutControlItem2});
            this.layoutControlGroup9.Location = new System.Drawing.Point(381, 0);
            this.layoutControlGroup9.Name = "layoutControlGroup9";
            this.layoutControlGroup9.Size = new System.Drawing.Size(243, 168);
            this.layoutControlGroup9.Text = "Photo";
            // 
            // ItemForPhoto
            // 
            this.ItemForPhoto.Control = this.PhotoPictureEdit;
            this.ItemForPhoto.Location = new System.Drawing.Point(47, 0);
            this.ItemForPhoto.MaxSize = new System.Drawing.Size(172, 123);
            this.ItemForPhoto.MinSize = new System.Drawing.Size(172, 123);
            this.ItemForPhoto.Name = "ItemForPhoto";
            this.ItemForPhoto.Size = new System.Drawing.Size(172, 124);
            this.ItemForPhoto.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForPhoto.StartNewLine = true;
            this.ItemForPhoto.TextSize = new System.Drawing.Size(0, 0);
            this.ItemForPhoto.TextVisible = false;
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.btnRemoveImg;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem1.MaxSize = new System.Drawing.Size(47, 40);
            this.layoutControlItem1.MinSize = new System.Drawing.Size(47, 40);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(47, 40);
            this.layoutControlItem1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            // 
            // layoutControlItem2
            // 
            this.layoutControlItem2.Control = this.btnAddImg;
            this.layoutControlItem2.Location = new System.Drawing.Point(0, 40);
            this.layoutControlItem2.MaxSize = new System.Drawing.Size(47, 40);
            this.layoutControlItem2.MinSize = new System.Drawing.Size(47, 40);
            this.layoutControlItem2.Name = "layoutControlItem2";
            this.layoutControlItem2.Size = new System.Drawing.Size(47, 84);
            this.layoutControlItem2.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem2.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem2.TextVisible = false;
            // 
            // tabbedControlGroup1
            // 
            this.tabbedControlGroup1.AppearanceGroup.BorderColor = System.Drawing.Color.Green;
            this.tabbedControlGroup1.AppearanceGroup.Options.UseBorderColor = true;
            this.tabbedControlGroup1.HeaderAutoFill = DevExpress.Utils.DefaultBoolean.True;
            this.tabbedControlGroup1.Location = new System.Drawing.Point(0, 212);
            this.tabbedControlGroup1.Name = "tabbedControlGroup1";
            this.tabbedControlGroup1.SelectedTabPage = this.layoutControlGroup3;
            this.tabbedControlGroup1.Size = new System.Drawing.Size(1005, 317);
            this.tabbedControlGroup1.TabPages.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup3,
            this.layoutControlGroup4,
            this.layoutControlGroup7});
            // 
            // layoutControlGroup7
            // 
            this.layoutControlGroup7.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup11,
            this.layoutControlGroup17,
            this.emptySpaceItem2,
            this.layoutControlGroup12});
            this.layoutControlGroup7.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup7.Name = "layoutControlGroup7";
            this.layoutControlGroup7.Size = new System.Drawing.Size(981, 270);
            this.layoutControlGroup7.Text = "Informations sur le salaire";
            // 
            // layoutControlGroup11
            // 
            this.layoutControlGroup11.AppearanceGroup.BorderColor = System.Drawing.Color.DarkSlateBlue;
            this.layoutControlGroup11.AppearanceGroup.Options.UseBorderColor = true;
            this.layoutControlGroup11.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem3});
            this.layoutControlGroup11.Location = new System.Drawing.Point(0, 92);
            this.layoutControlGroup11.Name = "layoutControlGroup11";
            this.layoutControlGroup11.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignModeGroup.CustomSize;
            this.layoutControlGroup11.OptionsPrint.AppearanceItemText.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.layoutControlGroup11.OptionsPrint.AppearanceItemText.Options.UseFont = true;
            this.layoutControlGroup11.Size = new System.Drawing.Size(490, 163);
            this.layoutControlGroup11.Text = "Droits";
            // 
            // layoutControlItem3
            // 
            this.layoutControlItem3.Control = this.grdBenefits;
            this.layoutControlItem3.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem3.Name = "layoutControlItem3";
            this.layoutControlItem3.Size = new System.Drawing.Size(466, 119);
            this.layoutControlItem3.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem3.TextVisible = false;
            // 
            // layoutControlGroup17
            // 
            this.layoutControlGroup17.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForAccruedAccountId,
            this.ItemForSalaryVariable,
            this.ItemForExpensesAccountId,
            this.ItemForPayPeriod,
            this.ItemForSalaryBasic,
            this.ItemForCalcIncomeTax,
            this.ItemForHourValue,
            this.ItemForDayValue});
            this.layoutControlGroup17.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup17.Name = "layoutControlGroup17";
            this.layoutControlGroup17.Size = new System.Drawing.Size(981, 92);
            this.layoutControlGroup17.Text = " ";
            // 
            // ItemForAccruedAccountId
            // 
            this.ItemForAccruedAccountId.Control = this.AccruedAccountIdLookUpEdit;
            this.ItemForAccruedAccountId.Location = new System.Drawing.Point(574, 24);
            this.ItemForAccruedAccountId.Name = "ItemForAccruedAccountId";
            this.ItemForAccruedAccountId.Size = new System.Drawing.Size(383, 24);
            this.ItemForAccruedAccountId.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize;
            this.ItemForAccruedAccountId.TextSize = new System.Drawing.Size(99, 13);
            this.ItemForAccruedAccountId.TextToControlDistance = 5;
            // 
            // ItemForSalaryVariable
            // 
            this.ItemForSalaryVariable.Control = this.SalaryVariableSpinEdit;
            this.ItemForSalaryVariable.Location = new System.Drawing.Point(574, 0);
            this.ItemForSalaryVariable.Name = "ItemForSalaryVariable";
            this.ItemForSalaryVariable.Size = new System.Drawing.Size(191, 24);
            this.ItemForSalaryVariable.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize;
            this.ItemForSalaryVariable.TextSize = new System.Drawing.Size(73, 13);
            this.ItemForSalaryVariable.TextToControlDistance = 5;
            // 
            // ItemForExpensesAccountId
            // 
            this.ItemForExpensesAccountId.Control = this.ExpensesAccountIdLookUpEdit;
            this.ItemForExpensesAccountId.Location = new System.Drawing.Point(765, 0);
            this.ItemForExpensesAccountId.Name = "ItemForExpensesAccountId";
            this.ItemForExpensesAccountId.Size = new System.Drawing.Size(192, 24);
            this.ItemForExpensesAccountId.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize;
            this.ItemForExpensesAccountId.TextSize = new System.Drawing.Size(106, 13);
            this.ItemForExpensesAccountId.TextToControlDistance = 5;
            // 
            // ItemForPayPeriod
            // 
            this.ItemForPayPeriod.Control = this.PayPeriodImageComboBoxEdit;
            this.ItemForPayPeriod.Location = new System.Drawing.Point(0, 0);
            this.ItemForPayPeriod.Name = "ItemForPayPeriod";
            this.ItemForPayPeriod.Size = new System.Drawing.Size(287, 24);
            this.ItemForPayPeriod.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForSalaryBasic
            // 
            this.ItemForSalaryBasic.Control = this.SalaryBasicSpinEdit;
            this.ItemForSalaryBasic.Location = new System.Drawing.Point(287, 0);
            this.ItemForSalaryBasic.Name = "ItemForSalaryBasic";
            this.ItemForSalaryBasic.Size = new System.Drawing.Size(287, 24);
            this.ItemForSalaryBasic.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize;
            this.ItemForSalaryBasic.TextSize = new System.Drawing.Size(73, 13);
            this.ItemForSalaryBasic.TextToControlDistance = 5;
            // 
            // ItemForCalcIncomeTax
            // 
            this.ItemForCalcIncomeTax.Control = this.CalcIncomeTaxCheckEdit;
            this.ItemForCalcIncomeTax.Location = new System.Drawing.Point(0, 24);
            this.ItemForCalcIncomeTax.Name = "ItemForCalcIncomeTax";
            this.ItemForCalcIncomeTax.Size = new System.Drawing.Size(250, 24);
            this.ItemForCalcIncomeTax.TextSize = new System.Drawing.Size(0, 0);
            this.ItemForCalcIncomeTax.TextVisible = false;
            // 
            // ItemForHourValue
            // 
            this.ItemForHourValue.Control = this.HourValueSpinEdit;
            this.ItemForHourValue.Location = new System.Drawing.Point(250, 24);
            this.ItemForHourValue.Name = "ItemForHourValue";
            this.ItemForHourValue.Size = new System.Drawing.Size(162, 24);
            this.ItemForHourValue.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize;
            this.ItemForHourValue.TextSize = new System.Drawing.Size(80, 13);
            this.ItemForHourValue.TextToControlDistance = 5;
            // 
            // ItemForDayValue
            // 
            this.ItemForDayValue.Control = this.DayValueSpinEdit;
            this.ItemForDayValue.Location = new System.Drawing.Point(412, 24);
            this.ItemForDayValue.Name = "ItemForDayValue";
            this.ItemForDayValue.Size = new System.Drawing.Size(162, 24);
            this.ItemForDayValue.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize;
            this.ItemForDayValue.TextSize = new System.Drawing.Size(71, 13);
            this.ItemForDayValue.TextToControlDistance = 5;
            // 
            // emptySpaceItem2
            // 
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(0, 255);
            this.emptySpaceItem2.MaxSize = new System.Drawing.Size(0, 15);
            this.emptySpaceItem2.MinSize = new System.Drawing.Size(10, 15);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(981, 15);
            this.emptySpaceItem2.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlGroup12
            // 
            this.layoutControlGroup12.AppearanceGroup.BorderColor = System.Drawing.Color.DarkSlateBlue;
            this.layoutControlGroup12.AppearanceGroup.Options.UseBorderColor = true;
            this.layoutControlGroup12.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem4});
            this.layoutControlGroup12.Location = new System.Drawing.Point(490, 92);
            this.layoutControlGroup12.Name = "layoutControlGroup12";
            this.layoutControlGroup12.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignModeGroup.CustomSize;
            this.layoutControlGroup12.OptionsPrint.AppearanceItemText.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.layoutControlGroup12.OptionsPrint.AppearanceItemText.Options.UseFont = true;
            this.layoutControlGroup12.Size = new System.Drawing.Size(491, 163);
            this.layoutControlGroup12.Text = "Déductions";
            // 
            // layoutControlItem4
            // 
            this.layoutControlItem4.Control = this.grdDeduction;
            this.layoutControlItem4.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem4.Name = "layoutControlItem4";
            this.layoutControlItem4.Size = new System.Drawing.Size(467, 119);
            this.layoutControlItem4.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem4.TextVisible = false;
            // 
            // layoutControlGroup3
            // 
            this.layoutControlGroup3.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup16,
            this.emptySpaceItem12,
            this.emptySpaceItem1});
            this.layoutControlGroup3.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup3.Name = "layoutControlGroup3";
            this.layoutControlGroup3.Size = new System.Drawing.Size(981, 270);
            this.layoutControlGroup3.Text = " Informations";
            // 
            // layoutControlGroup16
            // 
            this.layoutControlGroup16.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForGender,
            this.ItemForBirthDate,
            this.ItemForNationalityId,
            this.ItemForNationalID,
            this.ItemForInsuranceDate,
            this.ItemForMilitarilyStatus,
            this.ItemForAddress,
            this.ItemForPhone,
            this.ItemForAccountNo,
            this.ItemForBankName,
            this.ItemForEmail,
            this.ItemForDrivingLicense,
            this.ItemForInsuranceNo,
            this.ItemForEndDate,
            this.ItemForReligionId,
            this.ItemForBirthCountryId,
            this.ItemForMaritalStatus});
            this.layoutControlGroup16.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup16.Name = "layoutControlGroup16";
            this.layoutControlGroup16.Size = new System.Drawing.Size(775, 260);
            this.layoutControlGroup16.Text = " ";
            // 
            // ItemForGender
            // 
            this.ItemForGender.Control = this.GenderImageComboBoxEdit;
            this.ItemForGender.Location = new System.Drawing.Point(373, 0);
            this.ItemForGender.MaxSize = new System.Drawing.Size(378, 24);
            this.ItemForGender.MinSize = new System.Drawing.Size(378, 24);
            this.ItemForGender.Name = "ItemForGender";
            this.ItemForGender.Size = new System.Drawing.Size(378, 24);
            this.ItemForGender.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForGender.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForBirthDate
            // 
            this.ItemForBirthDate.Control = this.BirthDateDateEdit;
            this.ItemForBirthDate.Location = new System.Drawing.Point(375, 24);
            this.ItemForBirthDate.Name = "ItemForBirthDate";
            this.ItemForBirthDate.Size = new System.Drawing.Size(376, 24);
            this.ItemForBirthDate.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForNationalityId
            // 
            this.ItemForNationalityId.Control = this.NationalityIdLookUpEdit;
            this.ItemForNationalityId.Location = new System.Drawing.Point(375, 48);
            this.ItemForNationalityId.Name = "ItemForNationalityId";
            this.ItemForNationalityId.Size = new System.Drawing.Size(376, 24);
            this.ItemForNationalityId.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForNationalID
            // 
            this.ItemForNationalID.Control = this.NationalIDTextEdit;
            this.ItemForNationalID.Location = new System.Drawing.Point(375, 72);
            this.ItemForNationalID.Name = "ItemForNationalID";
            this.ItemForNationalID.Size = new System.Drawing.Size(376, 24);
            this.ItemForNationalID.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForInsuranceDate
            // 
            this.ItemForInsuranceDate.Control = this.InsuranceDateDateEdit;
            this.ItemForInsuranceDate.Location = new System.Drawing.Point(375, 96);
            this.ItemForInsuranceDate.Name = "ItemForInsuranceDate";
            this.ItemForInsuranceDate.Size = new System.Drawing.Size(376, 24);
            this.ItemForInsuranceDate.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForMilitarilyStatus
            // 
            this.ItemForMilitarilyStatus.Control = this.MilitarilyStatusImageComboBoxEdit;
            this.ItemForMilitarilyStatus.Location = new System.Drawing.Point(375, 120);
            this.ItemForMilitarilyStatus.Name = "ItemForMilitarilyStatus";
            this.ItemForMilitarilyStatus.Size = new System.Drawing.Size(376, 24);
            this.ItemForMilitarilyStatus.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForAddress
            // 
            this.ItemForAddress.Control = this.AddressTextEdit;
            this.ItemForAddress.Location = new System.Drawing.Point(0, 144);
            this.ItemForAddress.Name = "ItemForAddress";
            this.ItemForAddress.Size = new System.Drawing.Size(751, 24);
            this.ItemForAddress.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForPhone
            // 
            this.ItemForPhone.Control = this.PhoneTextEdit;
            this.ItemForPhone.Location = new System.Drawing.Point(375, 168);
            this.ItemForPhone.Name = "ItemForPhone";
            this.ItemForPhone.Size = new System.Drawing.Size(376, 24);
            this.ItemForPhone.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForAccountNo
            // 
            this.ItemForAccountNo.Control = this.AccountNoTextEdit;
            this.ItemForAccountNo.Location = new System.Drawing.Point(375, 192);
            this.ItemForAccountNo.Name = "ItemForAccountNo";
            this.ItemForAccountNo.Size = new System.Drawing.Size(376, 24);
            this.ItemForAccountNo.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForBankName
            // 
            this.ItemForBankName.Control = this.BankNameTextEdit;
            this.ItemForBankName.Location = new System.Drawing.Point(0, 192);
            this.ItemForBankName.Name = "ItemForBankName";
            this.ItemForBankName.Size = new System.Drawing.Size(375, 24);
            this.ItemForBankName.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForEmail
            // 
            this.ItemForEmail.Control = this.EmailTextEdit;
            this.ItemForEmail.Location = new System.Drawing.Point(0, 168);
            this.ItemForEmail.Name = "ItemForEmail";
            this.ItemForEmail.Size = new System.Drawing.Size(375, 24);
            this.ItemForEmail.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForDrivingLicense
            // 
            this.ItemForDrivingLicense.Control = this.DrivingLicenseTextEdit;
            this.ItemForDrivingLicense.Location = new System.Drawing.Point(0, 120);
            this.ItemForDrivingLicense.Name = "ItemForDrivingLicense";
            this.ItemForDrivingLicense.Size = new System.Drawing.Size(375, 24);
            this.ItemForDrivingLicense.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForInsuranceNo
            // 
            this.ItemForInsuranceNo.Control = this.InsuranceNoTextEdit;
            this.ItemForInsuranceNo.Location = new System.Drawing.Point(0, 96);
            this.ItemForInsuranceNo.Name = "ItemForInsuranceNo";
            this.ItemForInsuranceNo.Size = new System.Drawing.Size(375, 24);
            this.ItemForInsuranceNo.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForEndDate
            // 
            this.ItemForEndDate.Control = this.EndDateDateEdit;
            this.ItemForEndDate.Location = new System.Drawing.Point(0, 72);
            this.ItemForEndDate.Name = "ItemForEndDate";
            this.ItemForEndDate.Size = new System.Drawing.Size(375, 24);
            this.ItemForEndDate.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForReligionId
            // 
            this.ItemForReligionId.Control = this.ReligionIdLookUpEdit;
            this.ItemForReligionId.Location = new System.Drawing.Point(0, 48);
            this.ItemForReligionId.Name = "ItemForReligionId";
            this.ItemForReligionId.Size = new System.Drawing.Size(375, 24);
            this.ItemForReligionId.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForBirthCountryId
            // 
            this.ItemForBirthCountryId.Control = this.BirthCountryIdLookUpEdit;
            this.ItemForBirthCountryId.Location = new System.Drawing.Point(0, 24);
            this.ItemForBirthCountryId.Name = "ItemForBirthCountryId";
            this.ItemForBirthCountryId.Size = new System.Drawing.Size(375, 24);
            this.ItemForBirthCountryId.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForMaritalStatus
            // 
            this.ItemForMaritalStatus.Control = this.MaritalStatusImageComboBoxEdit;
            this.ItemForMaritalStatus.Location = new System.Drawing.Point(0, 0);
            this.ItemForMaritalStatus.MaxSize = new System.Drawing.Size(373, 24);
            this.ItemForMaritalStatus.MinSize = new System.Drawing.Size(373, 24);
            this.ItemForMaritalStatus.Name = "ItemForMaritalStatus";
            this.ItemForMaritalStatus.Size = new System.Drawing.Size(373, 24);
            this.ItemForMaritalStatus.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForMaritalStatus.TextSize = new System.Drawing.Size(202, 13);
            // 
            // emptySpaceItem12
            // 
            this.emptySpaceItem12.AllowHotTrack = false;
            this.emptySpaceItem12.Location = new System.Drawing.Point(0, 260);
            this.emptySpaceItem12.Name = "emptySpaceItem12";
            this.emptySpaceItem12.Size = new System.Drawing.Size(981, 10);
            this.emptySpaceItem12.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(775, 0);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(206, 260);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlGroup4
            // 
            this.layoutControlGroup4.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup5,
            this.emptySpaceItem11,
            this.layoutControlGroup15,
            this.emptySpaceItem3});
            this.layoutControlGroup4.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup4.Name = "layoutControlGroup4";
            this.layoutControlGroup4.Size = new System.Drawing.Size(981, 270);
            this.layoutControlGroup4.Text = "Présence et Absence";
            // 
            // layoutControlGroup5
            // 
            this.layoutControlGroup5.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForContractPeriod,
            this.ItemForContractType,
            this.ItemForContactEndDate,
            this.ItemForDateOfRecruitment});
            this.layoutControlGroup5.Location = new System.Drawing.Point(387, 0);
            this.layoutControlGroup5.Name = "layoutControlGroup5";
            this.layoutControlGroup5.Size = new System.Drawing.Size(390, 188);
            this.layoutControlGroup5.Text = "Contrat";
            // 
            // ItemForContractPeriod
            // 
            this.ItemForContractPeriod.Control = this.ContractPeriodTextEdit;
            this.ItemForContractPeriod.Location = new System.Drawing.Point(0, 24);
            this.ItemForContractPeriod.Name = "ItemForContractPeriod";
            this.ItemForContractPeriod.OptionsTableLayoutItem.ColumnIndex = 1;
            this.ItemForContractPeriod.Size = new System.Drawing.Size(366, 24);
            this.ItemForContractPeriod.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForContractType
            // 
            this.ItemForContractType.Control = this.ContractTypeImageComboBoxEdit;
            this.ItemForContractType.Location = new System.Drawing.Point(0, 0);
            this.ItemForContractType.MaxSize = new System.Drawing.Size(366, 24);
            this.ItemForContractType.MinSize = new System.Drawing.Size(366, 24);
            this.ItemForContractType.Name = "ItemForContractType";
            this.ItemForContractType.Size = new System.Drawing.Size(366, 24);
            this.ItemForContractType.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForContractType.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForContactEndDate
            // 
            this.ItemForContactEndDate.Control = this.ContactEndDateDateEdit;
            this.ItemForContactEndDate.Location = new System.Drawing.Point(0, 72);
            this.ItemForContactEndDate.Name = "ItemForContactEndDate";
            this.ItemForContactEndDate.OptionsTableLayoutItem.RowIndex = 1;
            this.ItemForContactEndDate.Size = new System.Drawing.Size(366, 72);
            this.ItemForContactEndDate.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForDateOfRecruitment
            // 
            this.ItemForDateOfRecruitment.Control = this.DateOfRecruitmentDateEdit;
            this.ItemForDateOfRecruitment.Location = new System.Drawing.Point(0, 48);
            this.ItemForDateOfRecruitment.Name = "ItemForDateOfRecruitment";
            this.ItemForDateOfRecruitment.OptionsTableLayoutItem.ColumnIndex = 1;
            this.ItemForDateOfRecruitment.OptionsTableLayoutItem.RowIndex = 1;
            this.ItemForDateOfRecruitment.Size = new System.Drawing.Size(366, 24);
            this.ItemForDateOfRecruitment.TextSize = new System.Drawing.Size(202, 13);
            // 
            // emptySpaceItem11
            // 
            this.emptySpaceItem11.AllowHotTrack = false;
            this.emptySpaceItem11.Location = new System.Drawing.Point(0, 188);
            this.emptySpaceItem11.Name = "emptySpaceItem11";
            this.emptySpaceItem11.Size = new System.Drawing.Size(777, 82);
            this.emptySpaceItem11.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlGroup15
            // 
            this.layoutControlGroup15.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForFingerprintCode,
            this.ItemForFristShiftId,
            this.ItemForSecondShiftId,
            this.ItemForAbsenceRegistionID,
            this.ItemForDelayRegulationID,
            this.ItemForOverTimeRegulationID});
            this.layoutControlGroup15.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup15.Name = "layoutControlGroup15";
            this.layoutControlGroup15.Size = new System.Drawing.Size(387, 188);
            this.layoutControlGroup15.Text = " ";
            // 
            // ItemForFingerprintCode
            // 
            this.ItemForFingerprintCode.Control = this.FingerprintCodeTextEdit;
            this.ItemForFingerprintCode.Location = new System.Drawing.Point(0, 0);
            this.ItemForFingerprintCode.MaxSize = new System.Drawing.Size(363, 24);
            this.ItemForFingerprintCode.MinSize = new System.Drawing.Size(363, 24);
            this.ItemForFingerprintCode.Name = "ItemForFingerprintCode";
            this.ItemForFingerprintCode.Size = new System.Drawing.Size(363, 24);
            this.ItemForFingerprintCode.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForFingerprintCode.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForFristShiftId
            // 
            this.ItemForFristShiftId.Control = this.FristShiftIdTextEdit;
            this.ItemForFristShiftId.Location = new System.Drawing.Point(0, 24);
            this.ItemForFristShiftId.Name = "ItemForFristShiftId";
            this.ItemForFristShiftId.Size = new System.Drawing.Size(363, 24);
            this.ItemForFristShiftId.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForSecondShiftId
            // 
            this.ItemForSecondShiftId.Control = this.SecondShiftIdTextEdit;
            this.ItemForSecondShiftId.Location = new System.Drawing.Point(0, 48);
            this.ItemForSecondShiftId.Name = "ItemForSecondShiftId";
            this.ItemForSecondShiftId.Size = new System.Drawing.Size(363, 24);
            this.ItemForSecondShiftId.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForAbsenceRegistionID
            // 
            this.ItemForAbsenceRegistionID.Control = this.AbsenceRegistionIDTextEdit;
            this.ItemForAbsenceRegistionID.Location = new System.Drawing.Point(0, 72);
            this.ItemForAbsenceRegistionID.Name = "ItemForAbsenceRegistionID";
            this.ItemForAbsenceRegistionID.Size = new System.Drawing.Size(363, 24);
            this.ItemForAbsenceRegistionID.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForDelayRegulationID
            // 
            this.ItemForDelayRegulationID.Control = this.DelayRegulationIDTextEdit;
            this.ItemForDelayRegulationID.Location = new System.Drawing.Point(0, 96);
            this.ItemForDelayRegulationID.Name = "ItemForDelayRegulationID";
            this.ItemForDelayRegulationID.Size = new System.Drawing.Size(363, 24);
            this.ItemForDelayRegulationID.TextSize = new System.Drawing.Size(202, 13);
            // 
            // ItemForOverTimeRegulationID
            // 
            this.ItemForOverTimeRegulationID.Control = this.OverTimeRegulationIDTextEdit;
            this.ItemForOverTimeRegulationID.Location = new System.Drawing.Point(0, 120);
            this.ItemForOverTimeRegulationID.Name = "ItemForOverTimeRegulationID";
            this.ItemForOverTimeRegulationID.Size = new System.Drawing.Size(363, 24);
            this.ItemForOverTimeRegulationID.TextSize = new System.Drawing.Size(202, 13);
            // 
            // emptySpaceItem3
            // 
            this.emptySpaceItem3.AllowHotTrack = false;
            this.emptySpaceItem3.Location = new System.Drawing.Point(777, 0);
            this.emptySpaceItem3.Name = "emptySpaceItem3";
            this.emptySpaceItem3.Size = new System.Drawing.Size(204, 270);
            this.emptySpaceItem3.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem4
            // 
            this.emptySpaceItem4.AllowHotTrack = false;
            this.emptySpaceItem4.Location = new System.Drawing.Point(648, 0);
            this.emptySpaceItem4.Name = "emptySpaceItem4";
            this.emptySpaceItem4.Size = new System.Drawing.Size(357, 212);
            this.emptySpaceItem4.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem5
            // 
            this.emptySpaceItem5.AllowHotTrack = false;
            this.emptySpaceItem5.Location = new System.Drawing.Point(206, 206);
            this.emptySpaceItem5.Name = "emptySpaceItem5";
            this.emptySpaceItem5.Size = new System.Drawing.Size(396, 10);
            this.emptySpaceItem5.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem7
            // 
            this.emptySpaceItem7.AllowHotTrack = false;
            this.emptySpaceItem7.Location = new System.Drawing.Point(0, 216);
            this.emptySpaceItem7.Name = "emptySpaceItem7";
            this.emptySpaceItem7.Size = new System.Drawing.Size(602, 26);
            this.emptySpaceItem7.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem8
            // 
            this.emptySpaceItem8.AllowHotTrack = false;
            this.emptySpaceItem8.Location = new System.Drawing.Point(2048, 206);
            this.emptySpaceItem8.Name = "emptySpaceItem8";
            this.emptySpaceItem8.Size = new System.Drawing.Size(34, 26);
            this.emptySpaceItem8.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem9
            // 
            this.emptySpaceItem9.AllowHotTrack = false;
            this.emptySpaceItem9.Location = new System.Drawing.Point(2048, 232);
            this.emptySpaceItem9.Name = "emptySpaceItem9";
            this.emptySpaceItem9.Size = new System.Drawing.Size(34, 26);
            this.emptySpaceItem9.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem10
            // 
            this.emptySpaceItem10.AllowHotTrack = false;
            this.emptySpaceItem10.Location = new System.Drawing.Point(0, 406);
            this.emptySpaceItem10.Name = "emptySpaceItem10";
            this.emptySpaceItem10.Size = new System.Drawing.Size(1175, 10);
            this.emptySpaceItem10.TextSize = new System.Drawing.Size(0, 0);
            // 
            // EmpStateTextEdit
            // 
            this.EmpStateTextEdit.Location = new System.Drawing.Point(608, 12);
            this.EmpStateTextEdit.Name = "EmpStateTextEdit";
            this.EmpStateTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.EmpStateTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.EmpStateTextEdit.Properties.ReadOnly = true;
            this.EmpStateTextEdit.Size = new System.Drawing.Size(66, 22);
            this.EmpStateTextEdit.TabIndex = 68;
            // 
            // EmpView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.AutoValidate = System.Windows.Forms.AutoValidate.EnableAllowFocusChange;
            this.ClientSize = new System.Drawing.Size(1042, 552);
            this.Controls.Add(this.dataLayoutControl1);
            this.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.Name = "EmpView";
            this.Text = "EmpView";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdDeduction)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.empSalaryDeductionBindingSource1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewDeduction)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoded)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.salaryExtensionBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdBenefits)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.empSalaryExtensionBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewBenefits)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoExp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemImageComboBox1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmpBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.CodeTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NameTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BirthPlaceTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.JobTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.AbsenceRegulationTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DelayRegulationTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.OverTimeRegulationTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.SalaryRegulationTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.GenderImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NationalIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.InsuranceNoTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.InsuranceDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.InsuranceDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DrivingLicenseTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EndDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EndDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BankNameTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.AccountNoTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BirthDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BirthDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.MaritalStatusImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.MilitarilyStatusImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.PhoneTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmailTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.AddressTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.FingerprintCodeTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContractTypeImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContractPeriodTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContactEndDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContactEndDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateOfRecruitmentDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateOfRecruitmentDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.GroupIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.JobIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BranchTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DepartmentIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NationalityTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.PayPeriodImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.CalcIncomeTaxCheckEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.FristShiftIdTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.SecondShiftIdTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.AbsenceRegistionIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DelayRegulationIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.OverTimeRegulationIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.SalaryBasicSpinEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.SalaryVariableSpinEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayValueSpinEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.HourValueSpinEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ExpensesAccountIdLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.AccruedAccountIdLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.PhotoPictureEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BirthCountryIdLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NationalityIdLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ReligionIdLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.QualificationIdLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForJob)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAbsenceRegulation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDelayRegulation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForOverTimeRegulation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSalaryRegulation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBirthPlace)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNationality)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabbedControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDepartmentID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForGroupID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForJobID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForQualificationId)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBranch)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForPhoto)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabbedControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAccruedAccountId)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSalaryVariable)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForExpensesAccountId)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForPayPeriod)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSalaryBasic)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCalcIncomeTax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForHourValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDayValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForGender)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBirthDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNationalityId)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNationalID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForInsuranceDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForMilitarilyStatus)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAddress)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForPhone)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAccountNo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBankName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDrivingLicense)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForInsuranceNo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEndDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForReligionId)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBirthCountryId)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForMaritalStatus)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForContractPeriod)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForContractType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForContactEndDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDateOfRecruitment)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFingerprintCode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFristShiftId)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSecondShiftId)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAbsenceRegistionID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDelayRegulationID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForOverTimeRegulationID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmpStateTextEdit.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x040002F5 RID: 757
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x040002F6 RID: 758
		private global::System.Windows.Forms.BindingSource EmpBindingSource;

		// Token: 0x040002F7 RID: 759
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup6;

		// Token: 0x040002F8 RID: 760
		private global::DevExpress.XtraTab.XtraTabPage xtraTabPage2;

		// Token: 0x040002F9 RID: 761
		private global::DevExpress.XtraTab.XtraTabPage xtraTabPage1;

		// Token: 0x040002FA RID: 762
		private global::DevExpress.XtraEditors.CheckEdit CalcIncomeTaxCheckEdit;

		// Token: 0x040002FB RID: 763
		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		// Token: 0x040002FC RID: 764
		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		// Token: 0x040002FD RID: 765
		private global::DevExpress.XtraEditors.TextEdit CodeTextEdit;

		// Token: 0x040002FE RID: 766
		private global::DevExpress.XtraEditors.TextEdit NameTextEdit;

		// Token: 0x040002FF RID: 767
		private global::DevExpress.XtraEditors.ImageComboBoxEdit GenderImageComboBoxEdit;

		// Token: 0x04000300 RID: 768
		private global::DevExpress.XtraEditors.TextEdit NationalIDTextEdit;

		// Token: 0x04000301 RID: 769
		private global::DevExpress.XtraEditors.TextEdit InsuranceNoTextEdit;

		// Token: 0x04000302 RID: 770
		private global::DevExpress.XtraEditors.DateEdit InsuranceDateDateEdit;

		// Token: 0x04000303 RID: 771
		private global::DevExpress.XtraEditors.TextEdit DrivingLicenseTextEdit;

		// Token: 0x04000304 RID: 772
		private global::DevExpress.XtraEditors.DateEdit EndDateDateEdit;

		// Token: 0x04000305 RID: 773
		private global::DevExpress.XtraEditors.TextEdit BankNameTextEdit;

		// Token: 0x04000306 RID: 774
		private global::DevExpress.XtraEditors.TextEdit AccountNoTextEdit;

		// Token: 0x04000307 RID: 775
		private global::DevExpress.XtraEditors.DateEdit BirthDateDateEdit;

		// Token: 0x04000308 RID: 776
		private global::DevExpress.XtraEditors.TextEdit BirthPlaceTextEdit;

		// Token: 0x04000309 RID: 777
		private global::DevExpress.XtraEditors.ImageComboBoxEdit MaritalStatusImageComboBoxEdit;

		// Token: 0x0400030A RID: 778
		private global::DevExpress.XtraEditors.ImageComboBoxEdit MilitarilyStatusImageComboBoxEdit;

		// Token: 0x0400030B RID: 779
		private global::DevExpress.XtraEditors.TextEdit PhoneTextEdit;

		// Token: 0x0400030C RID: 780
		private global::DevExpress.XtraEditors.TextEdit EmailTextEdit;

		// Token: 0x0400030D RID: 781
		private global::DevExpress.XtraEditors.TextEdit AddressTextEdit;

		// Token: 0x0400030E RID: 782
		private global::DevExpress.XtraEditors.TextEdit FingerprintCodeTextEdit;

		// Token: 0x0400030F RID: 783
		private global::DevExpress.XtraEditors.ImageComboBoxEdit ContractTypeImageComboBoxEdit;

		// Token: 0x04000310 RID: 784
		private global::DevExpress.XtraEditors.TextEdit ContractPeriodTextEdit;

		// Token: 0x04000311 RID: 785
		private global::DevExpress.XtraEditors.DateEdit ContactEndDateDateEdit;

		// Token: 0x04000312 RID: 786
		private global::DevExpress.XtraEditors.DateEdit DateOfRecruitmentDateEdit;

		// Token: 0x04000313 RID: 787
		private global::DevExpress.XtraEditors.TextEdit JobTextEdit;

		// Token: 0x04000314 RID: 788
		private global::DevExpress.XtraEditors.TextEdit AbsenceRegulationTextEdit;

		// Token: 0x04000315 RID: 789
		private global::DevExpress.XtraEditors.TextEdit DelayRegulationTextEdit;

		// Token: 0x04000316 RID: 790
		private global::DevExpress.XtraEditors.TextEdit OverTimeRegulationTextEdit;

		// Token: 0x04000317 RID: 791
		private global::DevExpress.XtraEditors.TextEdit SalaryRegulationTextEdit;

		// Token: 0x04000318 RID: 792
		private global::DevExpress.XtraEditors.LookUpEdit GroupIDTextEdit;

		// Token: 0x04000319 RID: 793
		private global::DevExpress.XtraEditors.LookUpEdit JobIDTextEdit;

		// Token: 0x0400031A RID: 794
		private global::DevExpress.XtraEditors.LookUpEdit BranchTextEdit;

		// Token: 0x0400031B RID: 795
		private global::DevExpress.XtraEditors.LookUpEdit DepartmentIDTextEdit;

		// Token: 0x0400031C RID: 796
		private global::DevExpress.XtraEditors.ImageComboBoxEdit PayPeriodImageComboBoxEdit;

		// Token: 0x0400031D RID: 797
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;

		// Token: 0x0400031E RID: 798
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForJob;

		// Token: 0x0400031F RID: 799
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForAbsenceRegulation;

		// Token: 0x04000320 RID: 800
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDelayRegulation;

		// Token: 0x04000321 RID: 801
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForOverTimeRegulation;

		// Token: 0x04000322 RID: 802
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForSalaryRegulation;

		// Token: 0x04000323 RID: 803
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		// Token: 0x04000324 RID: 804
		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		// Token: 0x04000325 RID: 805
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		// Token: 0x04000326 RID: 806
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDepartmentID;

		// Token: 0x04000327 RID: 807
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForGroupID;

		// Token: 0x04000328 RID: 808
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForJobID;

		// Token: 0x04000329 RID: 809
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForBirthPlace;

		// Token: 0x0400032A RID: 810
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForNationality;

		// Token: 0x0400032B RID: 811
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup4;

		// Token: 0x0400032C RID: 812
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForAbsenceRegistionID;

		// Token: 0x0400032D RID: 813
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDelayRegulationID;

		// Token: 0x0400032E RID: 814
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForOverTimeRegulationID;

		// Token: 0x0400032F RID: 815
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForFingerprintCode;

		// Token: 0x04000330 RID: 816
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForFristShiftId;

		// Token: 0x04000331 RID: 817
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForSecondShiftId;

		// Token: 0x04000332 RID: 818
		private global::DevExpress.XtraEditors.LookUpEdit FristShiftIdTextEdit;

		// Token: 0x04000333 RID: 819
		private global::DevExpress.XtraEditors.LookUpEdit SecondShiftIdTextEdit;

		// Token: 0x04000334 RID: 820
		private global::DevExpress.XtraEditors.LookUpEdit AbsenceRegistionIDTextEdit;

		// Token: 0x04000335 RID: 821
		private global::DevExpress.XtraEditors.LookUpEdit DelayRegulationIDTextEdit;

		// Token: 0x04000336 RID: 822
		private global::DevExpress.XtraEditors.LookUpEdit OverTimeRegulationIDTextEdit;

		// Token: 0x04000337 RID: 823
		private global::DevExpress.XtraEditors.SpinEdit SalaryBasicSpinEdit;

		// Token: 0x04000338 RID: 824
		private global::DevExpress.XtraEditors.SpinEdit SalaryVariableSpinEdit;

		// Token: 0x04000339 RID: 825
		private global::DevExpress.XtraEditors.SpinEdit DayValueSpinEdit;

		// Token: 0x0400033A RID: 826
		private global::DevExpress.XtraEditors.SpinEdit HourValueSpinEdit;

		// Token: 0x0400033B RID: 827
		private global::DevExpress.XtraEditors.LookUpEdit ExpensesAccountIdLookUpEdit;

		// Token: 0x0400033C RID: 828
		private global::DevExpress.XtraEditors.LookUpEdit AccruedAccountIdLookUpEdit;

		// Token: 0x0400033D RID: 829
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem4;

		// Token: 0x0400033E RID: 830
		private global::DevExpress.XtraEditors.LookUpEdit NationalityTextEdit;

		// Token: 0x0400033F RID: 831
		private global::DevExpress.XtraEditors.PictureEdit PhotoPictureEdit;

		// Token: 0x04000340 RID: 832
		private global::DevExpress.XtraEditors.LookUpEdit BirthCountryIdLookUpEdit;

		// Token: 0x04000341 RID: 833
		private global::DevExpress.XtraEditors.LookUpEdit NationalityIdLookUpEdit;

		// Token: 0x04000342 RID: 834
		private global::DevExpress.XtraEditors.LookUpEdit ReligionIdLookUpEdit;

		// Token: 0x04000343 RID: 835
		private global::DevExpress.XtraEditors.LookUpEdit QualificationIdLookUpEdit;

		// Token: 0x04000344 RID: 836
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForPhoto;

		// Token: 0x04000345 RID: 837
		private global::DevExpress.XtraEditors.SimpleButton btnAddImg;

		// Token: 0x04000346 RID: 838
		private global::DevExpress.XtraEditors.SimpleButton btnRemoveImg;

		// Token: 0x04000347 RID: 839
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;

		// Token: 0x04000348 RID: 840
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem2;

		// Token: 0x04000349 RID: 841
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem5;

		// Token: 0x0400034A RID: 842
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem7;

		// Token: 0x0400034B RID: 843
		private global::DevExpress.XtraEditors.TextEdit EmpStateTextEdit;

		// Token: 0x0400034C RID: 844
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem8;

		// Token: 0x0400034D RID: 845
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem9;

		// Token: 0x0400034E RID: 846
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem10;

		// Token: 0x0400034F RID: 847
		private global::DevExpress.XtraGrid.GridControl grdBenefits;

		// Token: 0x04000350 RID: 848
		private global::DevExpress.XtraGrid.Views.Grid.GridView viewBenefits;

		// Token: 0x04000351 RID: 849
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup8;

		// Token: 0x04000352 RID: 850
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup9;

		// Token: 0x04000353 RID: 851
		private global::DevExpress.XtraLayout.TabbedControlGroup tabbedControlGroup1;

		// Token: 0x04000354 RID: 852
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup10;

		// Token: 0x04000355 RID: 853
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup3;

		// Token: 0x04000356 RID: 854
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForBirthDate;

		// Token: 0x04000357 RID: 855
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForMaritalStatus;

		// Token: 0x04000358 RID: 856
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForNationalID;

		// Token: 0x04000359 RID: 857
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForGender;

		// Token: 0x0400035A RID: 858
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForEndDate;

		// Token: 0x0400035B RID: 859
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForInsuranceNo;

		// Token: 0x0400035C RID: 860
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForInsuranceDate;

		// Token: 0x0400035D RID: 861
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDrivingLicense;

		// Token: 0x0400035E RID: 862
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForAddress;

		// Token: 0x0400035F RID: 863
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForEmail;

		// Token: 0x04000360 RID: 864
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForMilitarilyStatus;

		// Token: 0x04000361 RID: 865
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForPhone;

		// Token: 0x04000362 RID: 866
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForBankName;

		// Token: 0x04000363 RID: 867
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForAccountNo;

		// Token: 0x04000364 RID: 868
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForBirthCountryId;

		// Token: 0x04000365 RID: 869
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForNationalityId;

		// Token: 0x04000366 RID: 870
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForReligionId;

		// Token: 0x04000367 RID: 871
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup7;

		// Token: 0x04000368 RID: 872
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForPayPeriod;

		// Token: 0x04000369 RID: 873
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForSalaryVariable;

		// Token: 0x0400036A RID: 874
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForSalaryBasic;

		// Token: 0x0400036B RID: 875
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForExpensesAccountId;

		// Token: 0x0400036C RID: 876
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForAccruedAccountId;

		// Token: 0x0400036D RID: 877
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForHourValue;

		// Token: 0x0400036E RID: 878
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDayValue;

		// Token: 0x0400036F RID: 879
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForCalcIncomeTax;

		// Token: 0x04000370 RID: 880
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem3;

		// Token: 0x04000371 RID: 881
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem3;

		// Token: 0x04000372 RID: 882
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;

		// Token: 0x04000373 RID: 883
		private global::DevExpress.XtraGrid.GridControl grdDeduction;

		// Token: 0x04000374 RID: 884
		private global::DevExpress.XtraGrid.Views.Grid.GridView viewDeduction;

		// Token: 0x04000375 RID: 885
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup11;

		// Token: 0x04000376 RID: 886
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup12;

		// Token: 0x04000377 RID: 887
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem4;

		// Token: 0x04000378 RID: 888
		private global::System.Windows.Forms.BindingSource empSalaryExtensionBindingSource;

		// Token: 0x04000379 RID: 889
		private global::DevExpress.XtraGrid.Columns.GridColumn colSalaryExtensionId;

		// Token: 0x0400037A RID: 890
		private global::DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repoExp;

		// Token: 0x0400037B RID: 891
		private global::System.Windows.Forms.BindingSource salaryExtensionBindingSource;

		// Token: 0x0400037C RID: 892
		private global::DevExpress.XtraGrid.Columns.GridColumn colValue;

		// Token: 0x0400037D RID: 893
		private global::DevExpress.XtraGrid.Columns.GridColumn colNotes;

		// Token: 0x0400037E RID: 894
		private global::DevExpress.XtraGrid.Columns.GridColumn colSalaryExtensionId1;

		// Token: 0x0400037F RID: 895
		private global::DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repoded;

		// Token: 0x04000380 RID: 896
		private global::DevExpress.XtraGrid.Columns.GridColumn colValue1;

		// Token: 0x04000381 RID: 897
		private global::DevExpress.XtraGrid.Columns.GridColumn colNotes1;

		// Token: 0x04000382 RID: 898
		private global::DevExpress.XtraLayout.TabbedControlGroup tabbedControlGroup2;

		// Token: 0x04000383 RID: 899
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup13;

		// Token: 0x04000384 RID: 900
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup14;

		// Token: 0x04000385 RID: 901
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForCode;

		// Token: 0x04000386 RID: 902
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForName;

		// Token: 0x04000387 RID: 903
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForQualificationId;

		// Token: 0x04000388 RID: 904
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForBranch;

		// Token: 0x04000389 RID: 905
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;

		// Token: 0x0400038A RID: 906
		private global::System.Windows.Forms.BindingSource empSalaryDeductionBindingSource1;

		// Token: 0x0400038B RID: 907
		private global::DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox repositoryItemImageComboBox1;

		// Token: 0x0400038C RID: 908
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup17;

		// Token: 0x0400038D RID: 909
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup16;

		// Token: 0x0400038E RID: 910
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup5;

		// Token: 0x0400038F RID: 911
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForContractPeriod;

		// Token: 0x04000390 RID: 912
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForContractType;

		// Token: 0x04000391 RID: 913
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForContactEndDate;

		// Token: 0x04000392 RID: 914
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDateOfRecruitment;

		// Token: 0x04000393 RID: 915
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup15;

		// Token: 0x04000394 RID: 916
		private global::DevExpress.XtraEditors.LabelControl labelControl1;

		// Token: 0x04000395 RID: 917
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem5;

		// Token: 0x04000396 RID: 918
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem6;

		// Token: 0x04000397 RID: 919
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem12;

		// Token: 0x04000398 RID: 920
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem11;
	}
}
