﻿using DevExpress.XtraEditors;
using EasyStock.Common;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class SalaryExtensionView : MasterView
    {
        public static SalaryExtensionView Instance
        {
            get
            {
                bool flag = SalaryExtensionView.instance == null || SalaryExtensionView.instance.IsDisposed;
                if (flag)
                {
                    SalaryExtensionView.instance = new SalaryExtensionView();
                }
                return SalaryExtensionView.instance;
            }
        }

        public SalaryExtension Extension
        {
            get
            {
                return this.salaryExtensionBindingSource.Current as SalaryExtension;
            }
            set
            {
                this.salaryExtensionBindingSource.DataSource = value;
            }
        }

        public SalaryExtensionView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            this.context = new HRDataContext();
            this.gridView1.AddEditButton(new EventHandler(this.RepositoryItemButtonEdit1_Click));
            this.gridView1.SetAlternatingColors();
            base.Shown += this.SalaryExtensionView_Shown;
        }

        private void SalaryExtensionView_Shown(object sender, EventArgs e)
        {
            bool flag = this.Extension == null;
            if (flag)
            {
                this.New();
            }
        }

        private void RepositoryItemButtonEdit1_Click(object sender, EventArgs e)
        {
            SalaryExtension row = this.gridView1.GetRow(this.gridView1.FocusedRowHandle) as SalaryExtension;
            bool flag = row != null;
            if (flag)
            {
                this.GoTo(row.ID);
            }
        }

        public void GoTo(int id)
        {
            SalaryExtension sourceDepr = this.context.SalaryExtensions.SingleOrDefault((SalaryExtension x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.Extension = sourceDepr;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable");
            }
        }

        public override void New()
        {
            this.Extension = new SalaryExtension();
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.context.SalaryExtensions.AddOrUpdate(new SalaryExtension[]
                {
                    this.Extension
                });
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.gridControl1.DataSource = this.context.SalaryExtensions.ToList<SalaryExtension>();
            base.RefreshData();
        }

        public override void Delete()
        {
        }

        private static SalaryExtensionView instance;

        private HRDataContext context;
    }
}
