﻿using EasyStock.Controller;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Détails des Revenus et Dépenses")]
    public class RevExpEntryDetail : BaseNotifyPropertyChangedModel
    {
        [Display(Name = "N°")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID
        {
            get
            {
                return this._Id;
            }
            set
            {
                base.SetProperty<int>(ref this._Id, value, "ID");
            }
        }

        [Display(Name = "")]
        public int EntryID
        {
            get
            {
                return this._entryID;
            }
            set
            {
                base.SetProperty<int>(ref this._entryID, value, "EntryID");
            }
        }

        [Display(Name = "")]
        public RevExpEntry Entry
        {
            get
            {
                return this._entry;
            }
            set
            {
                base.SetProperty<RevExpEntry>(ref this._entry, value, "Entry");
            }
        }

        [Display(Name = "Compte")]
        [Range(1, **********, ErrorMessage = "Le compte est requis")]
        public int AccountID
        {
            get
            {
                return this._accountID;
            }
            set
            {
                base.SetProperty<int>(ref this._accountID, value, "AccountID");
            }
        }

        [Display(Name = "Montant")]
        [Range(0.0001, **********.0, ErrorMessage = "La valeur est requise")]
        public double Amount
        {
            get
            {
                return this._amount;
            }
            set
            {
                base.SetProperty<double>(ref this._amount, value, "Amount");
            }
        }

        public Currency Currency
        {
            get
            {
                bool flag = this._currency == null || this._currency.ID != this.CurrancyID;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        this._currency = db.Currencies.SingleOrDefault((Currency x) => x.ID == this.CurrancyID);
                    }
                }
                return this._currency;
            }
        }

        [Display(Name = "Monnaie")]
        public int CurrancyID
        {
            get
            {
                return this._currancyID;
            }
            set
            {
                base.SetProperty<int>(ref this._currancyID, value, "CurrancyID");
            }
        }

        [Display(Name = "Taux de change")]
        public double CurrancyRate
        {
            get
            {
                return this._currancyRate;
            }
            set
            {
                base.SetProperty<double>(ref this._currancyRate, value, "CurrancyRate");
            }
        }

        [NotMapped]
        [Display(Name = "Montant local")]
        public double LocalAmount
        {
            get
            {
                return this.Amount * this.CurrancyRate;
            }
            set
            {
                this.Amount = value / this.CurrancyRate;
            }
        }

        [Display(Name = "Notes")]
        public string Notes
        {
            get
            {
                return this._notes;
            }
            set
            {
                base.SetProperty<string>(ref this._notes, value, "Notes");
            }
        }

        public CostCenter CostCenter { get; set; }

        [Display(Name = "Centre de coût")]
        public int? CostCenterID
        {
            get
            {
                return this.costCenterID;
            }
            set
            {
                base.SetProperty<int?>(ref this.costCenterID, value, "CostCenterID");
            }
        }

        private int _Id;

        private int _entryID;

        private RevExpEntry _entry;

        private int _accountID;

        private double _amount;

        [NotMapped]
        private Currency _currency;

        private int _currancyID;

        private double _currancyRate;

        private string _notes;

        private int? costCenterID;
    }
}
