﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class adCahTrnsferMdl : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(adCahTrnsferMdl));

        string IMigrationMetadata.Id => "202103171856515_adCahTrnsferMdl";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.CashTransfers", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                BranchID = c.Int(false),
                FromAccountID = c.Int(false),
                ToAccountID = c.Int(false),
                CurrencyID = c.Int(false),
                CurrencyRate = c.Double(false),
                Amount = c.Double(false),
                Date = c.DateTime(false),
                Notes = c.String()
            }).PrimaryKey(t => t.ID);
        }

        public override void Down()
        {
            DropTable("dbo.CashTransfers");
        }
    }
}
