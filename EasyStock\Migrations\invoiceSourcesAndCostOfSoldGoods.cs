﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class invoiceSourcesAndCostOfSoldGoods : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(invoiceSourcesAndCostOfSoldGoods));

        string IMigrationMetadata.Id => "202102091737039_invoiceSourcesAndCostOfSoldGoods";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.TransactionLinkModels", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                DistnationTransactionID = c.Int(false),
                SourceTransactionID = c.Int(false),
                Quantity = c.Double(false),
                ProductTransaction_ID = c.Int()
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.ProductTransactions", t => t.ProductTransaction_ID).ForeignKey("dbo.ProductTransactions", t => t.DistnationTransactionID)
                .ForeignKey("dbo.ProductTransactions", t => t.SourceTransactionID)
                .Index(t => t.DistnationTransactionID)
                .Index(t => t.SourceTransactionID)
                .Index(t => t.ProductTransaction_ID);
            AddColumn("dbo.Branches", "CostCalculationMethod", (ColumnBuilder c) => c.Int(false));
        }

        public override void Down()
        {
            DropForeignKey("dbo.TransactionLinkModels", "SourceTransactionID", "dbo.ProductTransactions");
            DropForeignKey("dbo.TransactionLinkModels", "DistnationTransactionID", "dbo.ProductTransactions");
            DropForeignKey("dbo.TransactionLinkModels", "ProductTransaction_ID", "dbo.ProductTransactions");
            DropIndex("dbo.TransactionLinkModels", new string[1] { "ProductTransaction_ID" });
            DropIndex("dbo.TransactionLinkModels", new string[1] { "SourceTransactionID" });
            DropIndex("dbo.TransactionLinkModels", new string[1] { "DistnationTransactionID" });
            DropColumn("dbo.Branches", "CostCalculationMethod");
            DropTable("dbo.TransactionLinkModels");
        }
    }
}
