﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    [DisplayColumn("Présence et Absence")]
    public class EmpAttendance : BaseNotifyPropertyChangedModel
    {
        private int id;


        private int empid;

        private DateTime day;

        private TimeSpan? attend1;

        private DateTime? attend1Rule;

        private TimeSpan? leave1;

        private DateTime? shift1LeaveRule;

        private TimeSpan? attend2;

        private DateTime? attend2Rule;

        private TimeSpan? leave2;

        private DateTime? shift2LeaveRule;

        private string notes;

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return id;
            }
            set
            {
                SetProperty(ref id, value, "ID");
            }
        }

        [Display(Name = "Employé")]
        [Range(1, int.MaxValue, ErrorMessage = "*")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int EmpID
        {
            get
            {
                return empid;
            }
            set
            {
                SetProperty(ref empid, value, "EmpID");
            }
        }

        [Display(Name = "Jour")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public DateTime Day
        {
            get
            {
                return day;
            }
            set
            {
                SetProperty(ref day, value, "Day");
            }
        }

        [Display(Name = "Présence Shift 1")]
        public TimeSpan? Shift1Attend
        {
            get
            {
                return attend1;
            }
            set
            {
                SetProperty(ref attend1, value, "Shift1Attend");
            }
        }

        [Display(Name = "Présence réelle Shift 1")]
        public DateTime? Shift1AttendRule
        {
            get
            {
                return attend1Rule;
            }
            set
            {
                SetProperty(ref attend1Rule, value, "Shift1AttendRule");
            }
        }

        [Display(Name = "Départ Shift 1")]
        public TimeSpan? Shift1Leave
        {
            get
            {
                return leave1;
            }
            set
            {
                SetProperty(ref leave1, value, "Shift1Leave");
            }
        }

        [Display(Name = "Départ réel Shift 1")]
        public DateTime? Shift1LeaveRule
        {
            get
            {
                return shift1LeaveRule;
            }
            set
            {
                SetProperty(ref shift1LeaveRule, value, "Shift1LeaveRule");
            }
        }

        [Display(Name = "Présence Shift 2")]
        public TimeSpan? Shift2Attend
        {
            get
            {
                return attend2;
            }
            set
            {
                SetProperty(ref attend2, value, "Shift2Attend");
            }
        }

        [Display(Name = "Présence réelle Shift 2")]
        public DateTime? Shift2AttendRule
        {
            get
            {
                return attend2Rule;
            }
            set
            {
                SetProperty(ref attend2Rule, value, "Shift2AttendRule");
            }
        }

        [Display(Name = "Départ Shift 2")]
        public TimeSpan? Shift2Leave
        {
            get
            {
                return leave2;
            }
            set
            {
                SetProperty(ref leave2, value, "Shift2Leave");
            }
        }

        [Display(Name = "Départ réel Shift 2")]
        public DateTime? Shift2LeaveRule
        {
            get
            {
                return shift2LeaveRule;
            }
            set
            {
                SetProperty(ref shift2LeaveRule, value, "Shift2LeaveRule");
            }
        }

        [Display(Name = "Notes")]
        public string Notes
        {
            get
            {
                return notes;
            }
            set
            {
                SetProperty(ref notes, value, "Notes");
            }
        }
    }
}
