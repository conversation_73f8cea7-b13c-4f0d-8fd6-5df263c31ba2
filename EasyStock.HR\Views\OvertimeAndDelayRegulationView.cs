﻿using DevExpress.XtraEditors;
using EasyStock.Common;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class OvertimeAndDelayRegulationView : MasterView
    {
        public static OvertimeAndDelayRegulationView Instance
        {
            get
            {
                bool flag = OvertimeAndDelayRegulationView.instance == null || OvertimeAndDelayRegulationView.instance.IsDisposed;
                if (flag)
                {
                    OvertimeAndDelayRegulationView.instance = new OvertimeAndDelayRegulationView();
                }
                return OvertimeAndDelayRegulationView.instance;
            }
        }

        public OvertimeAndDelayRegulation OverandDelay
        {
            get
            {
                return this.overtimeAndDelayRegulationBindingSource.Current as OvertimeAndDelayRegulation;
            }
            set
            {
                this.overtimeAndDelayRegulationBindingSource.DataSource = value;
            }
        }

        public OvertimeAndDelayRegulationView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            this.context = new HRDataContext();
            this.gridView1.AddEditButton(new EventHandler(this.RepositoryItemButtonEdit1_Click));
            this.gridView1.SetAlternatingColors();
            base.Shown += this.OvertimeAndDelayRegulationView_Shown;
        }

        private void OvertimeAndDelayRegulationView_Shown(object sender, EventArgs e)
        {
            bool flag = this.OverandDelay == null;
            if (flag)
            {
                this.New();
            }
        }

        private void RepositoryItemButtonEdit1_Click(object sender, EventArgs e)
        {
            OvertimeAndDelayRegulation row = this.gridView1.GetRow(this.gridView1.FocusedRowHandle) as OvertimeAndDelayRegulation;
            bool flag = row != null;
            if (flag)
            {
                this.GoTo(row.ID);
            }
        }

        public void GoTo(int id)
        {
            OvertimeAndDelayRegulation sourceDepr = this.context.OvertimeAndDelayRegulations.SingleOrDefault((OvertimeAndDelayRegulation x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.OverandDelay = sourceDepr;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable");
            }
        }

        public override void New()
        {
            this.OverandDelay = new OvertimeAndDelayRegulation();
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.context.OvertimeAndDelayRegulations.AddOrUpdate(new OvertimeAndDelayRegulation[]
                {
                    this.OverandDelay
                });
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.gridControl1.DataSource = this.context.OvertimeAndDelayRegulations.ToList<OvertimeAndDelayRegulation>();
            base.RefreshData();
        }

        public override void Delete()
        {
        }

        private static OvertimeAndDelayRegulationView instance;

        private HRDataContext context;
    }
}
