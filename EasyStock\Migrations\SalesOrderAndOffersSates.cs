﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class SalesOrderAndOffersSates : DbMigration, IMigrationMetadata
    {
        public override void Up()
        {
            base.AddColumn("dbo.SalesOrders", "State", (ColumnBuilder c) => c.Int(new bool?(false), false, new int?(0), null, null, null, null), null);
            base.AddColumn("dbo.SalesPriceOffers", "State", (ColumnBuilder c) => c.Int(new bool?(false), false, new int?(0), null, null, null, null), null);
        }

        public override void Down()
        {
            base.DropColumn("dbo.SalesPriceOffers", "State", null);
            base.DropColumn("dbo.SalesOrders", "State", null);
        }

        string IMigrationMetadata.Id
        {
            get
            {
                return "202108021756325_SalesOrderAndOffersSates";
            }
        }

        string IMigrationMetadata.Source
        {
            get
            {
                return null;
            }
        }

        string IMigrationMetadata.Target
        {
            get
            {
                return this.Resources.GetString("Target");
            }
        }

        private readonly ResourceManager Resources = new ResourceManager(typeof(SalesOrderAndOffersSates));
    }
}
