﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Data.Entity.Migrations.Model;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class StockCorrectionTables : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(StockCorrectionTables));

        string IMigrationMetadata.Id => "202105052250217_StockCorrectionTables";

        string IMigrationMetadata.Source => Resources.GetString("Source");

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.StockBalanceAfterCorrectionDetail", (ColumnBuilder c) => new
            {
                ID = c.Int(false),
                IsDone = c<PERSON>(false),
                StockBalanceCorrectionID = c.Int(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.ProductTransactions", t => t.ID).ForeignKey("dbo.StockBalanceCorrection", t => t.StockBalanceCorrectionID)
                .Index(t => t.ID)
                .Index(t => t.StockBalanceCorrectionID);
            CreateTable("dbo.StockBalanceBeforeCorrectionDetail", (ColumnBuilder c) => new
            {
                ID = c.Int(false),
                StockBalanceCorrectionID = c.Int(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.ProductTransactions", t => t.ID).ForeignKey("dbo.StockBalanceCorrection", t => t.StockBalanceCorrectionID)
                .Index(t => t.ID)
                .Index(t => t.StockBalanceCorrectionID);
            CreateTable("dbo.StockBalanceCorrection", delegate (ColumnBuilder c)
            {
                ColumnModel iD = c.Int(false);
                int? maxLength = 150;
                ColumnModel employeeName = c.String(null, maxLength);
                maxLength = 150;
                return new
                {
                    ID = iD,
                    EmployeeName = employeeName,
                    StoreKeeperName = c.String(null, maxLength),
                    TotalRealQuantity = c.Double(false),
                    TotalRealCost = c.Double(false),
                    TotalShortageQuantity = c.Double(false),
                    TotalShortageCost = c.Double(false),
                    TotalSurplusQuantity = c.Double(false),
                    TotalSurplusCost = c.Double(false),
                    TotalCorrectionQuantity = c.Double(false),
                    TotalCorrectionCost = c.Double(false)
                };
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Bills", t => t.ID).Index(t => t.ID);
        }

        public override void Down()
        {
            DropForeignKey("dbo.StockBalanceCorrection", "ID", "dbo.Bills");
            DropForeignKey("dbo.StockBalanceBeforeCorrectionDetail", "StockBalanceCorrectionID", "dbo.StockBalanceCorrection");
            DropForeignKey("dbo.StockBalanceBeforeCorrectionDetail", "ID", "dbo.ProductTransactions");
            DropForeignKey("dbo.StockBalanceAfterCorrectionDetail", "StockBalanceCorrectionID", "dbo.StockBalanceCorrection");
            DropForeignKey("dbo.StockBalanceAfterCorrectionDetail", "ID", "dbo.ProductTransactions");
            DropIndex("dbo.StockBalanceCorrection", new string[1] { "ID" });
            DropIndex("dbo.StockBalanceBeforeCorrectionDetail", new string[1] { "StockBalanceCorrectionID" });
            DropIndex("dbo.StockBalanceBeforeCorrectionDetail", new string[1] { "ID" });
            DropIndex("dbo.StockBalanceAfterCorrectionDetail", new string[1] { "StockBalanceCorrectionID" });
            DropIndex("dbo.StockBalanceAfterCorrectionDetail", new string[1] { "ID" });
            DropTable("dbo.StockBalanceCorrection");
            DropTable("dbo.StockBalanceBeforeCorrectionDetail");
            DropTable("dbo.StockBalanceAfterCorrectionDetail");
        }
    }
}
