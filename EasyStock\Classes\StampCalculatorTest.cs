using System;

namespace EasyStock.Classes
{
    /// <summary>
    /// كلاس لاختبار حساب الطابع الجبائي
    /// </summary>
    public static class StampCalculatorTest
    {
        /// <summary>
        /// اختبار حساب الطابع الجبائي لمبالغ مختلفة
        /// </summary>
        public static void TestStampCalculation()
        {
            // أمثلة للاختبار
            double[] testAmounts = { 200, 300, 500, 1000, 5000, 30000, 50000, 100000, 150000 };
            
            Console.WriteLine("=== اختبار حساب الطابع الجبائي حسب القانون الجزائري ===");
            Console.WriteLine();
            
            foreach (double amount in testAmounts)
            {
                double stampAmount = StampCalculator.CalculateStampAmount(amount);
                string description = StampCalculator.GetStampCalculationDescription(amount);
                bool requiresStamp = StampCalculator.RequiresStamp(amount);
                
                Console.WriteLine($"المبلغ: {amount:N2} دج");
                Console.WriteLine($"الطابع الجبائي: {stampAmount:F2} دج");
                Console.WriteLine($"يتطلب طابع: {(requiresStamp ? "نعم" : "لا")}");
                Console.WriteLine($"التفاصيل: {description}");
                Console.WriteLine(new string('-', 50));
            }
        }
        
        /// <summary>
        /// اختبار تفصيلي للشرائح المختلفة
        /// </summary>
        public static void TestDetailedCalculation()
        {
            Console.WriteLine("=== اختبار تفصيلي للشرائح ===");
            Console.WriteLine();
            
            // الشريحة الأولى: أقل من أو يساوي 300
            Console.WriteLine("الشريحة الأولى (≤ 300 دج):");
            TestRange(100, 300, 50);
            
            // الشريحة الثانية: من 300.01 إلى 30,000
            Console.WriteLine("\nالشريحة الثانية (300.01 - 30,000 دج):");
            TestRange(301, 30000, 5000);
            
            // الشريحة الثالثة: من 30,000.01 إلى 100,000
            Console.WriteLine("\nالشريحة الثالثة (30,000.01 - 100,000 دج):");
            TestRange(30001, 100000, 10000);
            
            // الشريحة الرابعة: أكثر من 100,000
            Console.WriteLine("\nالشريحة الرابعة (> 100,000 دج):");
            TestRange(100001, 200000, 25000);
        }
        
        private static void TestRange(double start, double end, double step)
        {
            for (double amount = start; amount <= end; amount += step)
            {
                double stampAmount = StampCalculator.CalculateStampAmount(amount);
                Console.WriteLine($"{amount:N0} دج → {stampAmount:F2} دج");
            }
        }
    }
}