namespace EasyStock.HR.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;

    public partial class TimeTables
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public TimeTables()
        {
            ShiftDays = new HashSet<ShiftDays>();
        }

        public int ID { get; set; }

        [Required]
        [StringLength(150)]
        public string Name { get; set; }

        public TimeSpan AttendanceTime { get; set; }

        public int AttendanceStart { get; set; }

        public int AttendanceEnd { get; set; }

        public int LeaveDay { get; set; }

        public TimeSpan LeaveTime { get; set; }

        public int LeaveStart { get; set; }

        public int LeaveEnd { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<ShiftDays> ShiftDays { get; set; }
    }
}
