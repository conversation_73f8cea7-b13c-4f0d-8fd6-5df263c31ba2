﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x02000492 RID: 1170
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class AddDueDatetoBillAndjournalDetails : DbMigration, IMigrationMetadata
	{
		// Token: 0x0600231E RID: 8990 RVA: 0x001F1518 File Offset: 0x001EF718
		public override void Up()
		{
			base.AddColumn("dbo.Bills", "DueDate", (ColumnBuilder c) => c.DateTime(new bool?(true), null, null, null, null, null, null), null);
			base.AddColumn("dbo.JournalDetails", "DueDate", (ColumnBuilder c) => c.DateTime(new bool?(true), null, null, null, null, null, null), null);
		}

		// Token: 0x0600231F RID: 8991 RVA: 0x0001238E File Offset: 0x0001058E
		public override void Down()
		{
			base.DropColumn("dbo.JournalDetails", "DueDate", null);
			base.DropColumn("dbo.Bills", "DueDate", null);
		}

		// Token: 0x17000B88 RID: 2952
		// (get) Token: 0x06002320 RID: 8992 RVA: 0x001F1588 File Offset: 0x001EF788
		string IMigrationMetadata.Id
		{
			get
			{
				return "202106021110376_AddDueDatetoBillAndjournalDetails";
			}
		}

		// Token: 0x17000B89 RID: 2953
		// (get) Token: 0x06002321 RID: 8993 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000B8A RID: 2954
		// (get) Token: 0x06002322 RID: 8994 RVA: 0x001F15A0 File Offset: 0x001EF7A0
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B0B RID: 11019
		private readonly ResourceManager Resources = new ResourceManager(typeof(AddDueDatetoBillAndjournalDetails));
	}
}
