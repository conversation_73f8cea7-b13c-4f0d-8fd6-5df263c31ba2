﻿using EasyStock.Common;
using EasyStock.Controller;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Commande de vente")]
    public class SalesOrder : BaseNotifyPropertyChangedModel, ISalesBill, IBill
    {
        public BindingList<SalesOrderDetail> Details { get; set; }

        public IEnumerable<IProductRowDetail> BaseDetails
        {
            get
            {
                return this.Details;
            }
        }

        [Display(Name = "N°")]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Code", GroupName = "Données de la facture")]
        public string Code
        {
            get
            {
                return this._code;
            }
            set
            {
                base.SetProperty<string>(ref this._code, value, "Code");
            }
        }

        [Display(Name = "Code source")]
        public int? SourceID
        {
            get
            {
                return this.sourceID;
            }
            set
            {
                base.SetProperty<int?>(ref this.sourceID, value, "SourceID");
            }
        }

        [Display(Name = "Type de source")]
        public BillSourceType SourceType
        {
            get
            {
                return this.sourceType;
            }
            set
            {
                base.SetProperty<BillSourceType>(ref this.sourceType, value, "SourceType");
            }
        }

        [Display(Name = "Succursale")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int BranchID
        {
            get
            {
                return this.branchID;
            }
            set
            {
                base.SetProperty<int>(ref this.branchID, value, "BranchID");
            }
        }

        [Display(Name = "Date", GroupName = "Données de la facture")]
        public DateTime Date
        {
            get
            {
                return this._date;
            }
            set
            {
                base.SetProperty<DateTime>(ref this._date, value, "Date");
            }
        }

        [Display(Name = "Notes", GroupName = "Données de la facture")]
        public string Notes
        {
            get
            {
                return this._notes;
            }
            set
            {
                base.SetProperty<string>(ref this._notes, value, "Notes");
            }
        }

        [Display(Name = "Date d'expiration")]
        public DateTime? ExpireDate
        {
            get
            {
                return this.expireDate;
            }
            set
            {
                base.SetProperty<DateTime?>(ref this.expireDate, value, "ExpireDate");
            }
        }

        [Display(Name = "Date de livraison")]
        public DateTime? DeliveryDate
        {
            get
            {
                return this.deliveryDate;
            }
            set
            {
                base.SetProperty<DateTime?>(ref this.deliveryDate, value, "DeliveryDate");
            }
        }

        [Display(Name = "État")]
        public BillState State { get; set; }

        public void GetNewCode()
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                Type sss = base.GetType();
                int maxLength = (from x in db.SalesOrders.AsNoTracking()
                                 select x.Code).Max((string x) => (int?)x.Length).GetValueOrDefault();
                bool flag = maxLength == 0;
                if (flag)
                {
                    this.Code = "1";
                }
                else
                {
                    string maxCode = (from x in db.SalesOrders.AsNoTracking()
                                      where x.Code.Length == maxLength
                                      orderby x.ID descending
                                      select x).FirstOrDefault<SalesOrder>().Code;
                    bool flag2 = maxCode != string.Empty;
                    if (flag2)
                    {
                        this.Code = maxCode.GetNextSequence();
                    }
                }
            }
        }

        [Display(Name = "Client", GroupName = "Données du client")]
        [Range(1, 2147483647, ErrorMessage = "Un client doit être sélectionné")]
        public int CustomerID
        {
            get
            {
                return this._customerID;
            }
            set
            {
                base.SetProperty<int>(ref this._customerID, value, "CustomerID");
            }
        }

        [Display(Name = "")]
        public Customer Customer
        {
            get
            {
                return this.customer;
            }
            set
            {
                base.SetProperty<Customer>(ref this.customer, value, "Customer");
            }
        }

        [Display(Name = "Remise", GroupName = "Valeurs")]
        public double Discount
        {
            get
            {
                return this._discount;
            }
            set
            {
                base.SetProperty<double>(ref this._discount, value, "Discount");
            }
        }

        [Display(Name = "Pourcentage de remise")]
        [Range(0.0, 0.999, ErrorMessage = "Le pourcentage de remise doit être compris entre 0 et 99 %")]
        public double DiscountPercentage
        {
            get
            {
                bool flag = this.Total == 0.0;
                double result;
                if (flag)
                {
                    result = 0.0;
                }
                else
                {
                    result = this.Discount / this.Total;
                }
                return result;
            }
        }

        [Display(Name = "Net", GroupName = "Valeurs")]
        public double Net
        {
            get
            {
                return this.Total + this.Tax + this.OtherExpenses - this.Discount;
            }
        }

        [Display(Name = "Autres frais", GroupName = "Valeurs")]
        public double OtherExpenses
        {
            get
            {
                return this._otherExpenses;
            }
            set
            {
                base.SetProperty<double>(ref this._otherExpenses, value, "OtherExpenses");
            }
        }

        [Display(Name = "Taxe", GroupName = "Valeurs")]
        public double Tax
        {
            get
            {
                return this._tax;
            }
            set
            {
                base.SetProperty<double>(ref this._tax, value, "Tax");
            }
        }

        [Display(Name = "Total", GroupName = "Valeurs")]
        public double Total
        {
            get
            {
                return this._total;
            }
            set
            {
                base.SetProperty<double>(ref this._total, value, "Total");
            }
        }

        private int id;

        private string _code;

        private int? sourceID;

        private BillSourceType sourceType;

        private int branchID;

        private string _notes;

        private DateTime _date;

        private DateTime? expireDate;

        private DateTime? deliveryDate;

        private int _customerID;

        private Customer customer;

        private double _discount;

        private double _otherExpenses;

        private double _tax;

        private double _total;
    }
}
