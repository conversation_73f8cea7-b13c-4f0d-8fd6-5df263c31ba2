﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [Table("PettyCashes")]
    [DisplayColumn("Caisse de petite monnaie")]
    public class PettyCash : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "N°")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Succursale")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        [Range(1, 2147483647, ErrorMessage = "*")]
        public int BranchID
        {
            get
            {
                return this.branch;
            }
            set
            {
                base.SetProperty<int>(ref this.branch, value, "BranchID");
            }
        }

        public Branch Branch { get; set; }

        [Display(Name = "Date")]
        public DateTime Date
        {
            get
            {
                return this.date;
            }
            set
            {
                base.SetProperty<DateTime>(ref this.date, value, "Date");
            }
        }

        [Display(Name = "Type de caisse")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public PettyCashType Type { get; set; }

        [Display(Name = "Titulaire de la caisse")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        [Range(1, 2147483647, ErrorMessage = "*")]
        public int HolderID
        {
            get
            {
                return this.holderID;
            }
            set
            {
                base.SetProperty<int>(ref this.holderID, value, "HolderID");
            }
        }

        public PettyCashHolder Holder { get; set; }

        [Display(Name = "Centre de coût")]
        public int? CostCenter
        {
            get
            {
                return this.costCenter;
            }
            set
            {
                base.SetProperty<int?>(ref this.costCenter, value, "CostCenter");
            }
        }

        [Display(Name = "Montant")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double Amount
        {
            get
            {
                return this.amount;
            }
            set
            {
                base.SetProperty<double>(ref this.amount, value, "Amount");
            }
        }

        [Display(Name = "Devise")]
        public int CurrencyID
        {
            get
            {
                return this.currency;
            }
            set
            {
                base.SetProperty<int>(ref this.currency, value, "CurrencyID");
            }
        }

        [Display(Name = "Clôturé")]
        public bool IsClosed
        {
            get
            {
                return this.isClosed;
            }
            set
            {
                base.SetProperty<bool>(ref this.isClosed, value, "IsClosed");
            }
        }

        [Display(Name = "Notes")]
        public string Notes
        {
            get
            {
                return this.notes;
            }
            set
            {
                base.SetProperty<string>(ref this.notes, value, "Notes");
            }
        }

        private int id;

        private int branch;

        private DateTime date;

        private int holderID;

        private int? costCenter;

        private double amount;

        private int currency;

        private bool isClosed;

        private string notes;

        public enum PettyCashType
        {
            [Display(Name = "Permanent")]
            Permanent,
            [Display(Name = "Temporaire")]
            Temporary
        }
    }
}
