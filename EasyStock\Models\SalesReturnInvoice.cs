﻿using EasyStock.Common;
using EasyStock.Controller;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Factures de retour de vente")]
    [Table("SalesReturnInvoices")]
    public class SalesReturnInvoice : Bill
    {
        [Display(Name = "État de la transaction")]
        public TransactionState TransactionState
        {
            get
            {
                return this.transactionState;
            }
            set
            {
                base.SetProperty<TransactionState>(ref this.transactionState, value, "TransactionState");
            }
        }

        [Display(Name = "Code source")]
        public int InvoiceID
        {
            get
            {
                return this.invoiceID;
            }
            set
            {
                base.SetProperty<int>(ref this.invoiceID, value, "InvoiceID");
            }
        }

        public SalesInvoice Invoice
        {
            get
            {
                bool flag = this.invoice == null || this.invoice.ID != this.InvoiceID;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        this.invoice = db.SalesInvoices.SingleOrDefault((SalesInvoice x) => x.ID == this.InvoiceID);
                    }
                }
                return this.invoice;
            }
        }

        public CostCenter CostCenter { get; set; }

        [Display(Name = "Centre de coût")]
        public int? CostCenterID
        {
            get
            {
                return this.costCenterID;
            }
            set
            {
                base.SetProperty<int?>(ref this.costCenterID, value, "CostCenterID");
            }
        }

        public override void GetNewCode()
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                Type sss = base.GetType();
                int maxLength = (from x in db.SalesReturnInvoices.AsNoTracking()
                                 select x.Code).Max((string x) => (int?)x.Length).GetValueOrDefault();
                bool flag = maxLength == 0;
                if (flag)
                {
                    base.Code = "1";
                }
                else
                {
                    string maxCode = (from x in db.SalesReturnInvoices.AsNoTracking()
                                      where x.Code.Length == maxLength
                                      orderby x.ID descending
                                      select x).FirstOrDefault<SalesReturnInvoice>().Code;
                    bool flag2 = maxCode != string.Empty;
                    if (flag2)
                    {
                        base.Code = maxCode.GetNextSequence();
                    }
                }
            }
        }

        [NotMapped]
        public Customer Customer
        {
            get
            {
                bool flag = this._customer == null || this._customer.ID != this.CustomerID;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        this._customer = db.Customers.Include((Customer x) => x.Account).SingleOrDefault((Customer x) => x.ID == this.CustomerID);
                    }
                }
                return this._customer;
            }
            set
            {
                base.SetProperty<Customer>(ref this._customer, value, "Customer");
            }
        }

        [Display(Name = "Client", GroupName = "Données client")]
        [Range(1, **********, ErrorMessage = "Vous devez sélectionner le client")]
        public int CustomerID
        {
            get
            {
                return this._customerID;
            }
            set
            {
                base.SetProperty<int>(ref this._customerID, value, "CustomerID");
            }
        }

        [NotMapped]
        [Display(Name = "Articles", GroupName = "Articles")]
        public BindingList<InvoiceDetail> Details { get; set; }

        [Display(Name = "Remise", GroupName = "Valeur")]
        public double Discount
        {
            get
            {
                return this._discount;
            }
            set
            {
                base.SetProperty<double>(ref this._discount, value, "Discount");
            }
        }

        [NotMapped]
        [Display(Name = "Pourcentage de remise")]
        [Range(0.0, 0.99, ErrorMessage = "Le pourcentage de remise doit être compris entre 0 et 99 %")]
        public double DiscountPercentage
        {
            get
            {
                bool flag = base.Total == 0.0;
                double result;
                if (flag)
                {
                    result = 0.0;
                }
                else
                {
                    result = this.Discount / base.Total;
                }
                return result;
            }
        }

        [NotMapped]
        [Display(Name = "Net", GroupName = "Valeur")]
        public double Net
        {
            get
            {
                return base.Total + this.Tax + this.OtherExpenses - this.Discount;
            }
        }

        [Display(Name = "Autres frais", GroupName = "Valeur")]
        public double OtherExpenses
        {
            get
            {
                return this._otherExpenses;
            }
            set
            {
                base.SetProperty<double>(ref this._otherExpenses, value, "OtherExpenses");
            }
        }

        [NotMapped]
        [Display(Name = "Payé", GroupName = "Règlement")]
        public double Paid
        {
            get
            {
                BindingList<PayDetail> payDetails = this.PayDetails;
                double? num;
                if (payDetails == null)
                {
                    num = null;
                }
                else
                {
                    num = payDetails.Sum((PayDetail x) => new double?(x.LocalAmount));
                }
                double? num2 = num;
                return num2.GetValueOrDefault();
            }
        }

        [NotMapped]
        [Display(Name = "Paiements", GroupName = "Règlement")]
        public BindingList<PayDetail> PayDetails { get; set; }

        [NotMapped]
        [Display(Name = "Restant", GroupName = "Règlement")]
        public double Remaining
        {
            get
            {
                return this.Net - this.Paid;
            }
        }

        [Display(Name = "Taxe", GroupName = "Valeur")]
        public double Tax
        {
            get
            {
                return this._tax;
            }
            set
            {
                base.SetProperty<double>(ref this._tax, value, "Tax");
            }
        }

        private TransactionState transactionState = TransactionState.Posted;

        private int invoiceID;

        [NotMapped]
        private SalesInvoice invoice;

        private int? costCenterID;

        private Customer _customer;

        private int _customerID;

        private double _discount;

        private double _otherExpenses;

        private double _tax;
    }
}
