﻿using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Mask;
using EasyStock.HR.BLL;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Windows.Forms;

namespace EasyStock.HR.Views.Work
{
    public partial class WorkLeaveView : MasterView
    {
        public static WorkLeaveView Instance
        {
            get
            {
                bool flag = WorkLeaveView.instance == null || WorkLeaveView.instance.IsDisposed;
                if (flag)
                {
                    WorkLeaveView.instance = new WorkLeaveView();
                }
                return WorkLeaveView.instance;
            }
        }

        public WorkLeaveReturn WorkLeave
        {
            get
            {
                return this.WorkbindingSource.Current as WorkLeaveReturn;
            }
            set
            {
                this.WorkbindingSource.DataSource = value;
            }
        }

        public WorkLeaveView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            base.Shown += this.WorkLeaveView_Shown;
            this.Text = "Enregistrement du formulaire de départ";
            this.BindingLookupControls();
            this.DateDateEdit.Properties.Mask.MaskType = MaskType.DateTime;
            this.DateDateEdit.Properties.Mask.EditMask = "dd/MM/yyyy";
            this.DateDateEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.btn_Refresh.Visibility = BarItemVisibility.Always;
        }

        private void WorkLeaveView_Shown(object sender, EventArgs e)
        {
            bool flag = this.WorkLeave == null;
            if (flag)
            {
                this.New();
            }
        }

        public void GoTo(int id)
        {
            WorkLeaveReturn _obj = WorkLeaveReturnBLL.Get(id, WorkLeaveReturnType.WorkLeave);
            bool flag = _obj != null;
            if (flag)
            {
                this.WorkLeave = _obj;
                this.BindEmpSource(false);
                this.EmployeeIdLookUpEdit.ReadOnly = true;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("L'enregistrement est introuvable. ");
            }
        }

        public override void New()
        {
            this.WorkLeave = new WorkLeaveReturn
            {
                Type = WorkLeaveReturnType.WorkLeave,
                Date = DateTime.Now
            };
            this.EmployeeIdLookUpEdit.ReadOnly = false;
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                int res = WorkLeaveReturnBLL.AddOrUpdate(this.WorkLeave);
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.New();
            base.RefreshData();
            this.BindEmpSource(true);
        }

        public override void Delete()
        {
            bool flag = this.WorkLeave == null || this.WorkLeave.ID == 0;
            if (!flag)
            {
                int res = WorkLeaveReturnBLL.Delete(this.WorkLeave.ID);
                bool flag2 = res > 0;
                if (flag2)
                {
                    base.Delete();
                    bool flag3 = XtraMessageBox.Show("Souhaitez-vous réintégrer l'employé ?", this.Text, MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation) == DialogResult.Yes;
                    if (flag3)
                    {
                        res = EmployeeBLL.UpdateEmpState(this.WorkLeave.EmployeeId, EmpState.stillWorking);
                    }
                }
            }
        }

        public void BindingLookupControls()
        {
            this.BindEmpSource(true);
            this.EmployeeIdLookUpEdit.Properties.DisplayMember = "Name";
            this.EmployeeIdLookUpEdit.Properties.ValueMember = "ID";
            this.EmployeeIdLookUpEdit.Properties.NullText = "";
            this.EmployeeIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("ID", "Code"));
            this.EmployeeIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "L'employé"));
        }

        public void BindEmpSource(bool flag)
        {
            if (flag)
            {
                this.EmployeeIdLookUpEdit.Properties.DataSource = EmployeeBLL.GetEmpByStatus(EmpState.stillWorking);
            }
            else
            {
                this.EmployeeIdLookUpEdit.Properties.DataSource = EmployeeBLL.GetEmpByStatus(EmpState.leftWork);
            }
        }

        private static WorkLeaveView instance;
    }
}
