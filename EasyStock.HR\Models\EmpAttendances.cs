namespace EasyStock.HR.Models
{
    using System;

    public partial class EmpAttendances
    {
        public int ID { get; set; }

        public int EmpID { get; set; }

        public DateTime Day { get; set; }

        public DateTime? Shift1Attend { get; set; }

        public DateTime? Shift1AttendRule { get; set; }

        public DateTime? Shift1Leave { get; set; }

        public DateTime? Shift1LeaveRule { get; set; }

        public DateTime? Shift2Attend { get; set; }

        public DateTime? Shift2AttendRule { get; set; }

        public DateTime? Shift2Leave { get; set; }

        public DateTime? Shift2LeaveRule { get; set; }

        public string Notes { get; set; }
    }
}
