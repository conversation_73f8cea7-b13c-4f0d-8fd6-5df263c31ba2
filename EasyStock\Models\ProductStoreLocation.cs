﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
	[DisplayName("Emplacements des articles")]
	public class ProductStoreLocation : BaseNotifyPropertyChangedModel
	{
		
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		[ReadOnly(true)]
		[Display(Name = "Code")]
		public int ID { get; set; }

		[Display(Name = "Article")]
		public int ProductID
		{
			get
			{
				return this.productID;
			}
			set
			{
				base.SetProperty<int>(ref this.productID, value, "ProductID");
			}
		}

		[Display(Name = "")]
		public Product Product
		{
			get
			{
				return this.product;
			}
			set
			{
				base.SetProperty<Product>(ref this.product, value, "Product");
			}
		}

		[Display(Name = "Dépôt")]
		public int StoreID
		{
			get
			{
				return this.branchID;
			}
			set
			{
				base.SetProperty<int>(ref this.branchID, value, "StoreID");
			}
		}

		[Display(Name = "")]
		public Store Store
		{
			get
			{
				return this.branch;
			}
			set
			{
				base.SetProperty<Store>(ref this.branch, value, "Store");
			}
		}

		[StringLength(50)]
		[Display(Name = "Emplacement")]
		public string Location { get; set; }

		private int productID;

		private Product product;

		private int branchID;

		private Store branch;
	}
}
