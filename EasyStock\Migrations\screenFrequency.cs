﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class screenFrequency : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(screenFrequency));

        string IMigrationMetadata.Id => "202104260004407_screenFrequency";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.FrequentlyUsedScreens", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                UserID = c.Int(false),
                Count = c.Int(false),
                DayOfWeek = c.Int(false),
                Hour = c.Int(false)
            }).PrimaryKey(t => t.ID);
        }

        public override void Down()
        {
            DropTable("dbo.FrequentlyUsedScreens");
        }
    }
}
