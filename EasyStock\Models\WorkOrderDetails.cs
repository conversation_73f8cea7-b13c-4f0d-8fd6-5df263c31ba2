﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [Table("WorkOrderDetails")]
    [DisplayColumn("Détails de l'ordre de travail")]
    public class WorkOrderDetails : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "N°")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Code de l'ordre")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int OrderID
        {
            get
            {
                return this.orderID;
            }
            set
            {
                base.SetProperty<int>(ref this.orderID, value, "OrderID");
            }
        }

        public WorkOrder Order { get; set; }

        [Display(Name = "Produit / Article")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int ProductID
        {
            get
            {
                return this.prodID;
            }
            set
            {
                base.SetProperty<int>(ref this.prodID, value, "ProductID");
            }
        }

        [Display(Name = "Liste des matières premières")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int BOMID
        {
            get
            {
                return this.bomID;
            }
            set
            {
                base.SetProperty<int>(ref this.bomID, value, "BOMID");
            }
        }

        [Display(Name = "Unité")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int UintID
        {
            get
            {
                return this.uintID;
            }
            set
            {
                base.SetProperty<int>(ref this.uintID, value, "UintID");
            }
        }

        [Display(Name = "Quantité demandée")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double TotalOrderQuantity
        {
            get
            {
                return this.Orderquantity;
            }
            set
            {
                base.SetProperty<double>(ref this.Orderquantity, value, "TotalOrderQuantity");
            }
        }

        [Display(Name = "Quantité réelle")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double TotalAcualQuantity
        {
            get
            {
                return this.Acualquantity;
            }
            set
            {
                base.SetProperty<double>(ref this.Acualquantity, value, "TotalAcualQuantity");
            }
        }

        [Display(Name = "Coût total par défaut")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double TotalDefaultCost
        {
            get
            {
                return this.defaultCost;
            }
            set
            {
                base.SetProperty<double>(ref this.defaultCost, value, "TotalDefaultCost");
            }
        }

        [Display(Name = "Coût total réel")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double TotalAcualCost
        {
            get
            {
                return this.AcualCost;
            }
            set
            {
                base.SetProperty<double>(ref this.AcualCost, value, "TotalAcualCost");
            }
        }

        private int id;

        private int orderID;

        private int prodID;

        private int bomID;

        private int uintID;

        private double Orderquantity;

        private double Acualquantity;

        private double defaultCost;

        private double AcualCost;
    }
}
