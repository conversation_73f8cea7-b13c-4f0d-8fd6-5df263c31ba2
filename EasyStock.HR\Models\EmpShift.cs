﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    [DisplayColumn("Indemnités de quart")]
    public class EmpShift : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Employé")]
        [Range(1, 2147483647, ErrorMessage = "*")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int EmpID
        {
            get
            {
                return this.empid;
            }
            set
            {
                base.SetProperty<int>(ref this.empid, value, "EmpID");
            }
        }

        [Display(Name = "Date de début")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public DateTime FromDate
        {
            get
            {
                return this.fromdate;
            }
            set
            {
                base.SetProperty<DateTime>(ref this.fromdate, value, "FromDate");
            }
        }

        [Display(Name = "Date de fin")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public DateTime ToDate
        {
            get
            {
                return this.todate;
            }
            set
            {
                base.SetProperty<DateTime>(ref this.todate, value, "ToDate");
            }
        }

        [Display(Name = "Type d'indemnité de quart")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public EShiftType ShiftType { get; set; }

        [Display(Name = "Durée")]
        public int Duration
        {
            get
            {
                return this.duration;
            }
            set
            {
                base.SetProperty<int>(ref this.duration, value, "Duration");
            }
        }

        [Display(Name = "Notes")]
        public string Notes
        {
            get
            {
                return this.notes;
            }
            set
            {
                base.SetProperty<string>(ref this.notes, value, "Notes");
            }
        }

        private int id;

        private int empid;

        private DateTime fromdate;

        private DateTime todate;

        private int duration;

        private string notes;
    }
}
