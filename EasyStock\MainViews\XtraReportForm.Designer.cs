﻿namespace EasyStock.MainViews
{
	// Token: 0x020004F5 RID: 1269
	public partial class XtraReportForm : global::EasyStock.MainViews.BaseReportForm
	{
		// Token: 0x06002554 RID: 9556 RVA: 0x002027AC File Offset: 0x002009AC
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06002555 RID: 9557 RVA: 0x002027E4 File Offset: 0x002009E4
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(XtraReportForm));
            this.documentViewer1 = new DevExpress.XtraPrinting.Preview.DocumentViewer();
            this.documentViewerBarManager1 = new DevExpress.XtraPrinting.Preview.DocumentViewerBarManager(this.components);
            this.previewBar1 = new DevExpress.XtraPrinting.Preview.PreviewBar();
            this.bbiDocumentMap = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiParameters = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiThumbnails = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiFind = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiHighlightEditingFields = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiCustomize = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiOpen = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiSave = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiPrint = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiPrintDirect = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiPageSetup = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiEditPageHF = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiScale = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiMagnifier = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiZoomOut = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiZoom = new DevExpress.XtraPrinting.Preview.ZoomBarEditItem();
            this.printPreviewRepositoryItemComboBox1 = new DevExpress.XtraPrinting.Preview.PrintPreviewRepositoryItemComboBox();
            this.bbiZoomIn = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiShowFirstPage = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiShowPrevPage = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiShowNextPage = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiShowLastPage = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiMultiplePages = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiFillBackground = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiWatermark = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.bbiExportFile = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.previewBar2 = new DevExpress.XtraPrinting.Preview.PreviewBar();
            this.printPreviewStaticItem1 = new DevExpress.XtraPrinting.Preview.PrintPreviewStaticItem();
            this.progressBarEditItem1 = new DevExpress.XtraPrinting.Preview.ProgressBarEditItem();
            this.repositoryItemProgressBar1 = new DevExpress.XtraEditors.Repository.RepositoryItemProgressBar();
            this.printPreviewBarItem1 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.printPreviewStaticItem2 = new DevExpress.XtraPrinting.Preview.PrintPreviewStaticItem();
            this.zoomTrackBarEditItem1 = new DevExpress.XtraPrinting.Preview.ZoomTrackBarEditItem();
            this.repositoryItemZoomTrackBar1 = new DevExpress.XtraEditors.Repository.RepositoryItemZoomTrackBar();
            this.previewBar3 = new DevExpress.XtraPrinting.Preview.PreviewBar();
            this.barButtonItem2 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem4 = new DevExpress.XtraBars.BarButtonItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.miPageLayout = new DevExpress.XtraPrinting.Preview.PrintPreviewSubItem();
            this.miPageLayoutFacing = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.miPageLayoutContinuous = new DevExpress.XtraPrinting.Preview.PrintPreviewBarItem();
            this.miToolbars = new DevExpress.XtraBars.BarToolbarsListItem();
            this.printPreviewBarCheckItem1 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem2 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem3 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem4 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem5 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem6 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem7 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem8 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem9 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem10 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem11 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem12 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem13 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem14 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem15 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem16 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem17 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem18 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.printPreviewBarCheckItem19 = new DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem();
            this.barButtonItem3 = new DevExpress.XtraBars.BarButtonItem();
            ((System.ComponentModel.ISupportInitialize)(this.documentViewerBarManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.printPreviewRepositoryItemComboBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemProgressBar1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemZoomTrackBar1)).BeginInit();
            this.SuspendLayout();
            // 
            // documentViewer1
            // 
            this.documentViewer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.documentViewer1.IsMetric = true;
            this.documentViewer1.Location = new System.Drawing.Point(0, 49);
            this.documentViewer1.Name = "documentViewer1";
            this.documentViewer1.Size = new System.Drawing.Size(1126, 605);
            this.documentViewer1.TabIndex = 0;
            // 
            // documentViewerBarManager1
            // 
            this.documentViewerBarManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.previewBar1,
            this.previewBar2,
            this.previewBar3});
            this.documentViewerBarManager1.DockControls.Add(this.barDockControlTop);
            this.documentViewerBarManager1.DockControls.Add(this.barDockControlBottom);
            this.documentViewerBarManager1.DockControls.Add(this.barDockControlLeft);
            this.documentViewerBarManager1.DockControls.Add(this.barDockControlRight);
            this.documentViewerBarManager1.DocumentViewer = this.documentViewer1;
            this.documentViewerBarManager1.Form = this;
            this.documentViewerBarManager1.ImageStream = ((DevExpress.Utils.ImageCollectionStreamer)(resources.GetObject("documentViewerBarManager1.ImageStream")));
            this.documentViewerBarManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.printPreviewStaticItem1,
            this.progressBarEditItem1,
            this.printPreviewBarItem1,
            this.printPreviewStaticItem2,
            this.zoomTrackBarEditItem1,
            this.bbiDocumentMap,
            this.bbiParameters,
            this.bbiThumbnails,
            this.bbiFind,
            this.bbiHighlightEditingFields,
            this.bbiCustomize,
            this.bbiOpen,
            this.bbiSave,
            this.bbiPrint,
            this.bbiPrintDirect,
            this.bbiPageSetup,
            this.bbiEditPageHF,
            this.bbiScale,
            this.bbiMagnifier,
            this.bbiZoomOut,
            this.bbiZoom,
            this.bbiZoomIn,
            this.bbiShowFirstPage,
            this.bbiShowPrevPage,
            this.bbiShowNextPage,
            this.bbiShowLastPage,
            this.bbiMultiplePages,
            this.bbiFillBackground,
            this.bbiWatermark,
            this.bbiExportFile,
            this.miPageLayout,
            this.miPageLayoutFacing,
            this.miPageLayoutContinuous,
            this.miToolbars,
            this.printPreviewBarCheckItem1,
            this.printPreviewBarCheckItem2,
            this.printPreviewBarCheckItem3,
            this.printPreviewBarCheckItem4,
            this.printPreviewBarCheckItem5,
            this.printPreviewBarCheckItem6,
            this.printPreviewBarCheckItem7,
            this.printPreviewBarCheckItem8,
            this.printPreviewBarCheckItem9,
            this.printPreviewBarCheckItem10,
            this.printPreviewBarCheckItem11,
            this.printPreviewBarCheckItem12,
            this.printPreviewBarCheckItem13,
            this.printPreviewBarCheckItem14,
            this.printPreviewBarCheckItem15,
            this.printPreviewBarCheckItem16,
            this.printPreviewBarCheckItem17,
            this.printPreviewBarCheckItem18,
            this.printPreviewBarCheckItem19,
            this.barButtonItem2,
            this.barButtonItem4});
            this.documentViewerBarManager1.MainMenu = this.previewBar3;
            this.documentViewerBarManager1.MaxItemId = 62;
            this.documentViewerBarManager1.PreviewBar = this.previewBar1;
            this.documentViewerBarManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemProgressBar1,
            this.repositoryItemZoomTrackBar1,
            this.printPreviewRepositoryItemComboBox1});
            this.documentViewerBarManager1.StatusBar = this.previewBar2;
            this.documentViewerBarManager1.TransparentEditorsMode = DevExpress.Utils.DefaultBoolean.True;
            // 
            // previewBar1
            // 
            this.previewBar1.BarName = "Toolbar";
            this.previewBar1.DockCol = 0;
            this.previewBar1.DockRow = 1;
            this.previewBar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.previewBar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiDocumentMap),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiParameters),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiThumbnails),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiFind),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiHighlightEditingFields),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiCustomize, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiOpen, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiPrint, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiPrintDirect),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiPageSetup),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiEditPageHF),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiScale),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiMagnifier),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiZoomOut, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiZoom),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiZoomIn),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiShowFirstPage, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiShowPrevPage),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiShowNextPage),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiShowLastPage),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiMultiplePages, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiFillBackground),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiWatermark),
            new DevExpress.XtraBars.LinkPersistInfo(this.bbiExportFile, true)});
            this.previewBar1.Text = "Toolbar";
            // 
            // bbiDocumentMap
            // 
            this.bbiDocumentMap.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.bbiDocumentMap.Caption = "Document Map";
            this.bbiDocumentMap.Command = DevExpress.XtraPrinting.PrintingSystemCommand.DocumentMap;
            this.bbiDocumentMap.Enabled = false;
            this.bbiDocumentMap.Hint = "Document Map";
            this.bbiDocumentMap.Id = 5;
            this.bbiDocumentMap.Name = "bbiDocumentMap";
            // 
            // bbiParameters
            // 
            this.bbiParameters.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.bbiParameters.Caption = "Parameters";
            this.bbiParameters.Command = DevExpress.XtraPrinting.PrintingSystemCommand.Parameters;
            this.bbiParameters.Enabled = false;
            this.bbiParameters.Hint = "Parameters";
            this.bbiParameters.Id = 6;
            this.bbiParameters.Name = "bbiParameters";
            // 
            // bbiThumbnails
            // 
            this.bbiThumbnails.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.bbiThumbnails.Caption = "Thumbnails";
            this.bbiThumbnails.Command = DevExpress.XtraPrinting.PrintingSystemCommand.Thumbnails;
            this.bbiThumbnails.Enabled = false;
            this.bbiThumbnails.Hint = "Thumbnails";
            this.bbiThumbnails.Id = 7;
            this.bbiThumbnails.Name = "bbiThumbnails";
            // 
            // bbiFind
            // 
            this.bbiFind.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.bbiFind.Caption = "Search";
            this.bbiFind.Command = DevExpress.XtraPrinting.PrintingSystemCommand.Find;
            this.bbiFind.Enabled = false;
            this.bbiFind.Hint = "Search";
            this.bbiFind.Id = 8;
            this.bbiFind.Name = "bbiFind";
            // 
            // bbiHighlightEditingFields
            // 
            this.bbiHighlightEditingFields.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.bbiHighlightEditingFields.Caption = "Editing Fields";
            this.bbiHighlightEditingFields.Command = DevExpress.XtraPrinting.PrintingSystemCommand.HighlightEditingFields;
            this.bbiHighlightEditingFields.Enabled = false;
            this.bbiHighlightEditingFields.Hint = "Highlight Editing Fields";
            this.bbiHighlightEditingFields.Id = 9;
            this.bbiHighlightEditingFields.Name = "bbiHighlightEditingFields";
            // 
            // bbiCustomize
            // 
            this.bbiCustomize.Caption = "Customize";
            this.bbiCustomize.Command = DevExpress.XtraPrinting.PrintingSystemCommand.Customize;
            this.bbiCustomize.Enabled = false;
            this.bbiCustomize.Hint = "Customize";
            this.bbiCustomize.Id = 10;
            this.bbiCustomize.Name = "bbiCustomize";
            // 
            // bbiOpen
            // 
            this.bbiOpen.Caption = "Open";
            this.bbiOpen.Command = DevExpress.XtraPrinting.PrintingSystemCommand.Open;
            this.bbiOpen.Enabled = false;
            this.bbiOpen.Hint = "Open a document";
            this.bbiOpen.Id = 11;
            this.bbiOpen.Name = "bbiOpen";
            // 
            // bbiSave
            // 
            this.bbiSave.Caption = "Save";
            this.bbiSave.Command = DevExpress.XtraPrinting.PrintingSystemCommand.Save;
            this.bbiSave.Enabled = false;
            this.bbiSave.Hint = "Save the document";
            this.bbiSave.Id = 12;
            this.bbiSave.Name = "bbiSave";
            // 
            // bbiPrint
            // 
            this.bbiPrint.Caption = "&Print...";
            this.bbiPrint.Command = DevExpress.XtraPrinting.PrintingSystemCommand.Print;
            this.bbiPrint.Enabled = false;
            this.bbiPrint.Hint = "Print";
            this.bbiPrint.Id = 13;
            this.bbiPrint.Name = "bbiPrint";
            // 
            // bbiPrintDirect
            // 
            this.bbiPrintDirect.Caption = "P&rint";
            this.bbiPrintDirect.Command = DevExpress.XtraPrinting.PrintingSystemCommand.PrintDirect;
            this.bbiPrintDirect.Enabled = false;
            this.bbiPrintDirect.Hint = "Quick Print";
            this.bbiPrintDirect.Id = 14;
            this.bbiPrintDirect.Name = "bbiPrintDirect";
            // 
            // bbiPageSetup
            // 
            this.bbiPageSetup.Caption = "Page Set&up...";
            this.bbiPageSetup.Command = DevExpress.XtraPrinting.PrintingSystemCommand.PageSetup;
            this.bbiPageSetup.Enabled = false;
            this.bbiPageSetup.Hint = "Page Setup";
            this.bbiPageSetup.Id = 15;
            this.bbiPageSetup.Name = "bbiPageSetup";
            // 
            // bbiEditPageHF
            // 
            this.bbiEditPageHF.Caption = "Header And Footer";
            this.bbiEditPageHF.Command = DevExpress.XtraPrinting.PrintingSystemCommand.EditPageHF;
            this.bbiEditPageHF.Enabled = false;
            this.bbiEditPageHF.Hint = "Header And Footer";
            this.bbiEditPageHF.Id = 16;
            this.bbiEditPageHF.Name = "bbiEditPageHF";
            // 
            // bbiScale
            // 
            this.bbiScale.ActAsDropDown = true;
            this.bbiScale.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.bbiScale.Caption = "Scale";
            this.bbiScale.Command = DevExpress.XtraPrinting.PrintingSystemCommand.Scale;
            this.bbiScale.Enabled = false;
            this.bbiScale.Hint = "Scale";
            this.bbiScale.Id = 17;
            this.bbiScale.Name = "bbiScale";
            // 
            // bbiMagnifier
            // 
            this.bbiMagnifier.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.bbiMagnifier.Caption = "Magnifier";
            this.bbiMagnifier.Command = DevExpress.XtraPrinting.PrintingSystemCommand.Magnifier;
            this.bbiMagnifier.Enabled = false;
            this.bbiMagnifier.Hint = "Magnifier";
            this.bbiMagnifier.Id = 19;
            this.bbiMagnifier.Name = "bbiMagnifier";
            // 
            // bbiZoomOut
            // 
            this.bbiZoomOut.Caption = "Zoom Out";
            this.bbiZoomOut.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ZoomOut;
            this.bbiZoomOut.Enabled = false;
            this.bbiZoomOut.Hint = "Zoom Out";
            this.bbiZoomOut.Id = 20;
            this.bbiZoomOut.Name = "bbiZoomOut";
            // 
            // bbiZoom
            // 
            this.bbiZoom.Caption = "Zoom";
            this.bbiZoom.Edit = this.printPreviewRepositoryItemComboBox1;
            this.bbiZoom.EditValue = "100%";
            this.bbiZoom.EditWidth = 70;
            this.bbiZoom.Enabled = false;
            this.bbiZoom.Hint = "Zoom";
            this.bbiZoom.Id = 21;
            this.bbiZoom.Name = "bbiZoom";
            // 
            // printPreviewRepositoryItemComboBox1
            // 
            this.printPreviewRepositoryItemComboBox1.AutoComplete = false;
            this.printPreviewRepositoryItemComboBox1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.printPreviewRepositoryItemComboBox1.DropDownRows = 11;
            this.printPreviewRepositoryItemComboBox1.Name = "printPreviewRepositoryItemComboBox1";
            // 
            // bbiZoomIn
            // 
            this.bbiZoomIn.Caption = "Zoom In";
            this.bbiZoomIn.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ZoomIn;
            this.bbiZoomIn.Enabled = false;
            this.bbiZoomIn.Hint = "Zoom In";
            this.bbiZoomIn.Id = 22;
            this.bbiZoomIn.Name = "bbiZoomIn";
            // 
            // bbiShowFirstPage
            // 
            this.bbiShowFirstPage.Caption = "First Page";
            this.bbiShowFirstPage.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ShowFirstPage;
            this.bbiShowFirstPage.Enabled = false;
            this.bbiShowFirstPage.Hint = "First Page";
            this.bbiShowFirstPage.Id = 23;
            this.bbiShowFirstPage.Name = "bbiShowFirstPage";
            // 
            // bbiShowPrevPage
            // 
            this.bbiShowPrevPage.Caption = "Previous Page";
            this.bbiShowPrevPage.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ShowPrevPage;
            this.bbiShowPrevPage.Enabled = false;
            this.bbiShowPrevPage.Hint = "Previous Page";
            this.bbiShowPrevPage.Id = 24;
            this.bbiShowPrevPage.Name = "bbiShowPrevPage";
            // 
            // bbiShowNextPage
            // 
            this.bbiShowNextPage.Caption = "Next Page";
            this.bbiShowNextPage.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ShowNextPage;
            this.bbiShowNextPage.Enabled = false;
            this.bbiShowNextPage.Hint = "Next Page";
            this.bbiShowNextPage.Id = 25;
            this.bbiShowNextPage.Name = "bbiShowNextPage";
            // 
            // bbiShowLastPage
            // 
            this.bbiShowLastPage.Caption = "Last Page";
            this.bbiShowLastPage.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ShowLastPage;
            this.bbiShowLastPage.Enabled = false;
            this.bbiShowLastPage.Hint = "Last Page";
            this.bbiShowLastPage.Id = 26;
            this.bbiShowLastPage.Name = "bbiShowLastPage";
            // 
            // bbiMultiplePages
            // 
            this.bbiMultiplePages.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.bbiMultiplePages.Caption = "Multiple Pages";
            this.bbiMultiplePages.Command = DevExpress.XtraPrinting.PrintingSystemCommand.MultiplePages;
            this.bbiMultiplePages.Enabled = false;
            this.bbiMultiplePages.Hint = "Multiple Pages";
            this.bbiMultiplePages.Id = 27;
            this.bbiMultiplePages.Name = "bbiMultiplePages";
            // 
            // bbiFillBackground
            // 
            this.bbiFillBackground.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.bbiFillBackground.Caption = "&Color...";
            this.bbiFillBackground.Command = DevExpress.XtraPrinting.PrintingSystemCommand.FillBackground;
            this.bbiFillBackground.Enabled = false;
            this.bbiFillBackground.Hint = "Background";
            this.bbiFillBackground.Id = 28;
            this.bbiFillBackground.Name = "bbiFillBackground";
            // 
            // bbiWatermark
            // 
            this.bbiWatermark.Caption = "&Watermark...";
            this.bbiWatermark.Command = DevExpress.XtraPrinting.PrintingSystemCommand.Watermark;
            this.bbiWatermark.Enabled = false;
            this.bbiWatermark.Hint = "Watermark";
            this.bbiWatermark.Id = 29;
            this.bbiWatermark.Name = "bbiWatermark";
            // 
            // bbiExportFile
            // 
            this.bbiExportFile.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.bbiExportFile.Caption = "Export Document...";
            this.bbiExportFile.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ExportFile;
            this.bbiExportFile.Enabled = false;
            this.bbiExportFile.Hint = "Export Document...";
            this.bbiExportFile.Id = 30;
            this.bbiExportFile.Name = "bbiExportFile";
            // 
            // previewBar2
            // 
            this.previewBar2.BarName = "Status Bar";
            this.previewBar2.CanDockStyle = DevExpress.XtraBars.BarCanDockStyle.Bottom;
            this.previewBar2.DockCol = 0;
            this.previewBar2.DockRow = 0;
            this.previewBar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Bottom;
            this.previewBar2.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.printPreviewStaticItem1),
            new DevExpress.XtraBars.LinkPersistInfo(this.progressBarEditItem1),
            new DevExpress.XtraBars.LinkPersistInfo(this.printPreviewBarItem1),
            new DevExpress.XtraBars.LinkPersistInfo(this.printPreviewStaticItem2, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.zoomTrackBarEditItem1)});
            this.previewBar2.OptionsBar.AllowQuickCustomization = false;
            this.previewBar2.OptionsBar.DrawDragBorder = false;
            this.previewBar2.OptionsBar.UseWholeRow = true;
            this.previewBar2.Text = "Status Bar";
            // 
            // printPreviewStaticItem1
            // 
            this.printPreviewStaticItem1.Border = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.printPreviewStaticItem1.Caption = "Nothing";
            this.printPreviewStaticItem1.Id = 0;
            this.printPreviewStaticItem1.LeftIndent = 1;
            this.printPreviewStaticItem1.Name = "printPreviewStaticItem1";
            this.printPreviewStaticItem1.RightIndent = 1;
            this.printPreviewStaticItem1.Type = "PageOfPages";
            // 
            // progressBarEditItem1
            // 
            this.progressBarEditItem1.Edit = this.repositoryItemProgressBar1;
            this.progressBarEditItem1.EditHeight = 12;
            this.progressBarEditItem1.EditWidth = 150;
            this.progressBarEditItem1.Id = 1;
            this.progressBarEditItem1.Name = "progressBarEditItem1";
            this.progressBarEditItem1.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            // 
            // repositoryItemProgressBar1
            // 
            this.repositoryItemProgressBar1.Name = "repositoryItemProgressBar1";
            // 
            // printPreviewBarItem1
            // 
            this.printPreviewBarItem1.Caption = "Stop";
            this.printPreviewBarItem1.Command = DevExpress.XtraPrinting.PrintingSystemCommand.StopPageBuilding;
            this.printPreviewBarItem1.Enabled = false;
            this.printPreviewBarItem1.Hint = "Stop";
            this.printPreviewBarItem1.Id = 2;
            this.printPreviewBarItem1.Name = "printPreviewBarItem1";
            this.printPreviewBarItem1.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            // 
            // printPreviewStaticItem2
            // 
            this.printPreviewStaticItem2.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.printPreviewStaticItem2.Border = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.printPreviewStaticItem2.Caption = "100%";
            this.printPreviewStaticItem2.Id = 3;
            this.printPreviewStaticItem2.Name = "printPreviewStaticItem2";
            this.printPreviewStaticItem2.TextAlignment = System.Drawing.StringAlignment.Far;
            this.printPreviewStaticItem2.Type = "ZoomFactor";
            // 
            // zoomTrackBarEditItem1
            // 
            this.zoomTrackBarEditItem1.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.zoomTrackBarEditItem1.Edit = this.repositoryItemZoomTrackBar1;
            this.zoomTrackBarEditItem1.EditValue = 90;
            this.zoomTrackBarEditItem1.EditWidth = 140;
            this.zoomTrackBarEditItem1.Enabled = false;
            this.zoomTrackBarEditItem1.Id = 4;
            this.zoomTrackBarEditItem1.Name = "zoomTrackBarEditItem1";
            this.zoomTrackBarEditItem1.Range = new int[] {
        10,
        500};
            // 
            // repositoryItemZoomTrackBar1
            // 
            this.repositoryItemZoomTrackBar1.Alignment = DevExpress.Utils.VertAlignment.Center;
            this.repositoryItemZoomTrackBar1.AllowFocused = false;
            this.repositoryItemZoomTrackBar1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.repositoryItemZoomTrackBar1.Maximum = 180;
            this.repositoryItemZoomTrackBar1.Name = "repositoryItemZoomTrackBar1";
            // 
            // previewBar3
            // 
            this.previewBar3.BarName = "Main Menu";
            this.previewBar3.DockCol = 0;
            this.previewBar3.DockRow = 0;
            this.previewBar3.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.previewBar3.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonItem2, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonItem4, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph)});
            this.previewBar3.OptionsBar.MultiLine = true;
            this.previewBar3.OptionsBar.UseWholeRow = true;
            this.previewBar3.Text = "Main Menu";
            // 
            // barButtonItem2
            // 
            this.barButtonItem2.Caption = "Rafraîchir";
            this.barButtonItem2.Id = 60;
            this.barButtonItem2.ImageOptions.SvgImage = global::EasyStock.Properties.Resources.refreshallpivottable;
            this.barButtonItem2.Name = "barButtonItem2";
            this.barButtonItem2.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem2_ItemClick);
            // 
            // barButtonItem4
            // 
            this.barButtonItem4.Caption = "Afficher les filtres";
            this.barButtonItem4.Id = 61;
            this.barButtonItem4.ImageOptions.SvgImage = global::EasyStock.Properties.Resources.filterquery;
            this.barButtonItem4.Name = "barButtonItem4";
            this.barButtonItem4.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem4_ItemClick);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.documentViewerBarManager1;
            this.barDockControlTop.Size = new System.Drawing.Size(1126, 49);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 654);
            this.barDockControlBottom.Manager = this.documentViewerBarManager1;
            this.barDockControlBottom.Size = new System.Drawing.Size(1126, 22);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 49);
            this.barDockControlLeft.Manager = this.documentViewerBarManager1;
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 605);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1126, 49);
            this.barDockControlRight.Manager = this.documentViewerBarManager1;
            this.barDockControlRight.Size = new System.Drawing.Size(0, 605);
            // 
            // miPageLayout
            // 
            this.miPageLayout.Caption = "&Page Layout";
            this.miPageLayout.Command = DevExpress.XtraPrinting.PrintingSystemCommand.PageLayout;
            this.miPageLayout.Id = 36;
            this.miPageLayout.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.miPageLayoutFacing),
            new DevExpress.XtraBars.LinkPersistInfo(this.miPageLayoutContinuous)});
            this.miPageLayout.Name = "miPageLayout";
            // 
            // miPageLayoutFacing
            // 
            this.miPageLayoutFacing.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.miPageLayoutFacing.Caption = "&Facing";
            this.miPageLayoutFacing.Command = DevExpress.XtraPrinting.PrintingSystemCommand.PageLayoutFacing;
            this.miPageLayoutFacing.Enabled = false;
            this.miPageLayoutFacing.GroupIndex = 100;
            this.miPageLayoutFacing.Id = 37;
            this.miPageLayoutFacing.Name = "miPageLayoutFacing";
            // 
            // miPageLayoutContinuous
            // 
            this.miPageLayoutContinuous.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.miPageLayoutContinuous.Caption = "&Continuous";
            this.miPageLayoutContinuous.Command = DevExpress.XtraPrinting.PrintingSystemCommand.PageLayoutContinuous;
            this.miPageLayoutContinuous.Enabled = false;
            this.miPageLayoutContinuous.GroupIndex = 100;
            this.miPageLayoutContinuous.Id = 38;
            this.miPageLayoutContinuous.Name = "miPageLayoutContinuous";
            // 
            // miToolbars
            // 
            this.miToolbars.Caption = "Bars";
            this.miToolbars.Id = 39;
            this.miToolbars.Name = "miToolbars";
            // 
            // printPreviewBarCheckItem1
            // 
            this.printPreviewBarCheckItem1.BindableChecked = true;
            this.printPreviewBarCheckItem1.Caption = "PDF File";
            this.printPreviewBarCheckItem1.Checked = true;
            this.printPreviewBarCheckItem1.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ExportPdf;
            this.printPreviewBarCheckItem1.Enabled = false;
            this.printPreviewBarCheckItem1.GroupIndex = 1;
            this.printPreviewBarCheckItem1.Hint = "PDF File";
            this.printPreviewBarCheckItem1.Id = 40;
            this.printPreviewBarCheckItem1.Name = "printPreviewBarCheckItem1";
            // 
            // printPreviewBarCheckItem2
            // 
            this.printPreviewBarCheckItem2.Caption = "HTML File";
            this.printPreviewBarCheckItem2.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ExportHtm;
            this.printPreviewBarCheckItem2.Enabled = false;
            this.printPreviewBarCheckItem2.GroupIndex = 1;
            this.printPreviewBarCheckItem2.Hint = "HTML File";
            this.printPreviewBarCheckItem2.Id = 41;
            this.printPreviewBarCheckItem2.Name = "printPreviewBarCheckItem2";
            // 
            // printPreviewBarCheckItem3
            // 
            this.printPreviewBarCheckItem3.Caption = "MHT File";
            this.printPreviewBarCheckItem3.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ExportMht;
            this.printPreviewBarCheckItem3.Enabled = false;
            this.printPreviewBarCheckItem3.GroupIndex = 1;
            this.printPreviewBarCheckItem3.Hint = "MHT File";
            this.printPreviewBarCheckItem3.Id = 42;
            this.printPreviewBarCheckItem3.Name = "printPreviewBarCheckItem3";
            // 
            // printPreviewBarCheckItem4
            // 
            this.printPreviewBarCheckItem4.Caption = "RTF File";
            this.printPreviewBarCheckItem4.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ExportRtf;
            this.printPreviewBarCheckItem4.Enabled = false;
            this.printPreviewBarCheckItem4.GroupIndex = 1;
            this.printPreviewBarCheckItem4.Hint = "RTF File";
            this.printPreviewBarCheckItem4.Id = 43;
            this.printPreviewBarCheckItem4.Name = "printPreviewBarCheckItem4";
            // 
            // printPreviewBarCheckItem5
            // 
            this.printPreviewBarCheckItem5.Caption = "DOCX File";
            this.printPreviewBarCheckItem5.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ExportDocx;
            this.printPreviewBarCheckItem5.Enabled = false;
            this.printPreviewBarCheckItem5.GroupIndex = 1;
            this.printPreviewBarCheckItem5.Hint = "DOCX File";
            this.printPreviewBarCheckItem5.Id = 44;
            this.printPreviewBarCheckItem5.Name = "printPreviewBarCheckItem5";
            // 
            // printPreviewBarCheckItem6
            // 
            this.printPreviewBarCheckItem6.Caption = "XLS File";
            this.printPreviewBarCheckItem6.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ExportXls;
            this.printPreviewBarCheckItem6.Enabled = false;
            this.printPreviewBarCheckItem6.GroupIndex = 1;
            this.printPreviewBarCheckItem6.Hint = "XLS File";
            this.printPreviewBarCheckItem6.Id = 45;
            this.printPreviewBarCheckItem6.Name = "printPreviewBarCheckItem6";
            // 
            // printPreviewBarCheckItem7
            // 
            this.printPreviewBarCheckItem7.Caption = "XLSX File";
            this.printPreviewBarCheckItem7.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ExportXlsx;
            this.printPreviewBarCheckItem7.Enabled = false;
            this.printPreviewBarCheckItem7.GroupIndex = 1;
            this.printPreviewBarCheckItem7.Hint = "XLSX File";
            this.printPreviewBarCheckItem7.Id = 46;
            this.printPreviewBarCheckItem7.Name = "printPreviewBarCheckItem7";
            // 
            // printPreviewBarCheckItem8
            // 
            this.printPreviewBarCheckItem8.Caption = "CSV File";
            this.printPreviewBarCheckItem8.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ExportCsv;
            this.printPreviewBarCheckItem8.Enabled = false;
            this.printPreviewBarCheckItem8.GroupIndex = 1;
            this.printPreviewBarCheckItem8.Hint = "CSV File";
            this.printPreviewBarCheckItem8.Id = 47;
            this.printPreviewBarCheckItem8.Name = "printPreviewBarCheckItem8";
            // 
            // printPreviewBarCheckItem9
            // 
            this.printPreviewBarCheckItem9.Caption = "Text File";
            this.printPreviewBarCheckItem9.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ExportTxt;
            this.printPreviewBarCheckItem9.Enabled = false;
            this.printPreviewBarCheckItem9.GroupIndex = 1;
            this.printPreviewBarCheckItem9.Hint = "Text File";
            this.printPreviewBarCheckItem9.Id = 48;
            this.printPreviewBarCheckItem9.Name = "printPreviewBarCheckItem9";
            // 
            // printPreviewBarCheckItem10
            // 
            this.printPreviewBarCheckItem10.Caption = "Image File";
            this.printPreviewBarCheckItem10.Command = DevExpress.XtraPrinting.PrintingSystemCommand.ExportGraphic;
            this.printPreviewBarCheckItem10.Enabled = false;
            this.printPreviewBarCheckItem10.GroupIndex = 1;
            this.printPreviewBarCheckItem10.Hint = "Image File";
            this.printPreviewBarCheckItem10.Id = 49;
            this.printPreviewBarCheckItem10.Name = "printPreviewBarCheckItem10";
            // 
            // printPreviewBarCheckItem11
            // 
            this.printPreviewBarCheckItem11.BindableChecked = true;
            this.printPreviewBarCheckItem11.Caption = "PDF File";
            this.printPreviewBarCheckItem11.Checked = true;
            this.printPreviewBarCheckItem11.Command = DevExpress.XtraPrinting.PrintingSystemCommand.SendPdf;
            this.printPreviewBarCheckItem11.Enabled = false;
            this.printPreviewBarCheckItem11.GroupIndex = 1;
            this.printPreviewBarCheckItem11.Hint = "PDF File";
            this.printPreviewBarCheckItem11.Id = 50;
            this.printPreviewBarCheckItem11.Name = "printPreviewBarCheckItem11";
            // 
            // printPreviewBarCheckItem12
            // 
            this.printPreviewBarCheckItem12.Caption = "MHT File";
            this.printPreviewBarCheckItem12.Command = DevExpress.XtraPrinting.PrintingSystemCommand.SendMht;
            this.printPreviewBarCheckItem12.Enabled = false;
            this.printPreviewBarCheckItem12.GroupIndex = 1;
            this.printPreviewBarCheckItem12.Hint = "MHT File";
            this.printPreviewBarCheckItem12.Id = 51;
            this.printPreviewBarCheckItem12.Name = "printPreviewBarCheckItem12";
            // 
            // printPreviewBarCheckItem13
            // 
            this.printPreviewBarCheckItem13.Caption = "RTF File";
            this.printPreviewBarCheckItem13.Command = DevExpress.XtraPrinting.PrintingSystemCommand.SendRtf;
            this.printPreviewBarCheckItem13.Enabled = false;
            this.printPreviewBarCheckItem13.GroupIndex = 1;
            this.printPreviewBarCheckItem13.Hint = "RTF File";
            this.printPreviewBarCheckItem13.Id = 52;
            this.printPreviewBarCheckItem13.Name = "printPreviewBarCheckItem13";
            // 
            // printPreviewBarCheckItem14
            // 
            this.printPreviewBarCheckItem14.Caption = "DOCX File";
            this.printPreviewBarCheckItem14.Command = DevExpress.XtraPrinting.PrintingSystemCommand.SendDocx;
            this.printPreviewBarCheckItem14.Enabled = false;
            this.printPreviewBarCheckItem14.GroupIndex = 1;
            this.printPreviewBarCheckItem14.Hint = "DOCX File";
            this.printPreviewBarCheckItem14.Id = 53;
            this.printPreviewBarCheckItem14.Name = "printPreviewBarCheckItem14";
            // 
            // printPreviewBarCheckItem15
            // 
            this.printPreviewBarCheckItem15.Caption = "XLS File";
            this.printPreviewBarCheckItem15.Command = DevExpress.XtraPrinting.PrintingSystemCommand.SendXls;
            this.printPreviewBarCheckItem15.Enabled = false;
            this.printPreviewBarCheckItem15.GroupIndex = 1;
            this.printPreviewBarCheckItem15.Hint = "XLS File";
            this.printPreviewBarCheckItem15.Id = 54;
            this.printPreviewBarCheckItem15.Name = "printPreviewBarCheckItem15";
            // 
            // printPreviewBarCheckItem16
            // 
            this.printPreviewBarCheckItem16.Caption = "XLSX File";
            this.printPreviewBarCheckItem16.Command = DevExpress.XtraPrinting.PrintingSystemCommand.SendXlsx;
            this.printPreviewBarCheckItem16.Enabled = false;
            this.printPreviewBarCheckItem16.GroupIndex = 1;
            this.printPreviewBarCheckItem16.Hint = "XLSX File";
            this.printPreviewBarCheckItem16.Id = 55;
            this.printPreviewBarCheckItem16.Name = "printPreviewBarCheckItem16";
            // 
            // printPreviewBarCheckItem17
            // 
            this.printPreviewBarCheckItem17.Caption = "CSV File";
            this.printPreviewBarCheckItem17.Command = DevExpress.XtraPrinting.PrintingSystemCommand.SendCsv;
            this.printPreviewBarCheckItem17.Enabled = false;
            this.printPreviewBarCheckItem17.GroupIndex = 1;
            this.printPreviewBarCheckItem17.Hint = "CSV File";
            this.printPreviewBarCheckItem17.Id = 56;
            this.printPreviewBarCheckItem17.Name = "printPreviewBarCheckItem17";
            // 
            // printPreviewBarCheckItem18
            // 
            this.printPreviewBarCheckItem18.Caption = "Text File";
            this.printPreviewBarCheckItem18.Command = DevExpress.XtraPrinting.PrintingSystemCommand.SendTxt;
            this.printPreviewBarCheckItem18.Enabled = false;
            this.printPreviewBarCheckItem18.GroupIndex = 1;
            this.printPreviewBarCheckItem18.Hint = "Text File";
            this.printPreviewBarCheckItem18.Id = 57;
            this.printPreviewBarCheckItem18.Name = "printPreviewBarCheckItem18";
            // 
            // printPreviewBarCheckItem19
            // 
            this.printPreviewBarCheckItem19.Caption = "Image File";
            this.printPreviewBarCheckItem19.Command = DevExpress.XtraPrinting.PrintingSystemCommand.SendGraphic;
            this.printPreviewBarCheckItem19.Enabled = false;
            this.printPreviewBarCheckItem19.GroupIndex = 1;
            this.printPreviewBarCheckItem19.Hint = "Image File";
            this.printPreviewBarCheckItem19.Id = 58;
            this.printPreviewBarCheckItem19.Name = "printPreviewBarCheckItem19";
            // 
            // barButtonItem3
            // 
            this.barButtonItem3.Caption = "Filtres";
            this.barButtonItem3.Id = 4;
            this.barButtonItem3.Name = "barButtonItem3";
            this.barButtonItem3.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // XtraReportForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1126, 676);
            this.Controls.Add(this.documentViewer1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "XtraReportForm";
            this.Text = "XtraReportForm";
            ((System.ComponentModel.ISupportInitialize)(this.documentViewerBarManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.printPreviewRepositoryItemComboBox1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemProgressBar1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemZoomTrackBar1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x04002C41 RID: 11329
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04002C42 RID: 11330
		private global::DevExpress.XtraPrinting.Preview.DocumentViewerBarManager documentViewerBarManager1;

		// Token: 0x04002C43 RID: 11331
		private global::DevExpress.XtraPrinting.Preview.PreviewBar previewBar1;

		// Token: 0x04002C44 RID: 11332
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiDocumentMap;

		// Token: 0x04002C45 RID: 11333
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiParameters;

		// Token: 0x04002C46 RID: 11334
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiThumbnails;

		// Token: 0x04002C47 RID: 11335
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiFind;

		// Token: 0x04002C48 RID: 11336
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiHighlightEditingFields;

		// Token: 0x04002C49 RID: 11337
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiCustomize;

		// Token: 0x04002C4A RID: 11338
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiOpen;

		// Token: 0x04002C4B RID: 11339
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiSave;

		// Token: 0x04002C4C RID: 11340
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiPrint;

		// Token: 0x04002C4D RID: 11341
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiPrintDirect;

		// Token: 0x04002C4E RID: 11342
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiPageSetup;

		// Token: 0x04002C4F RID: 11343
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiEditPageHF;

		// Token: 0x04002C50 RID: 11344
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiScale;

		// Token: 0x04002C51 RID: 11345
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiMagnifier;

		// Token: 0x04002C52 RID: 11346
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiZoomOut;

		// Token: 0x04002C53 RID: 11347
		private global::DevExpress.XtraPrinting.Preview.ZoomBarEditItem bbiZoom;

		// Token: 0x04002C54 RID: 11348
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewRepositoryItemComboBox printPreviewRepositoryItemComboBox1;

		// Token: 0x04002C55 RID: 11349
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiZoomIn;

		// Token: 0x04002C56 RID: 11350
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiShowFirstPage;

		// Token: 0x04002C57 RID: 11351
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiShowPrevPage;

		// Token: 0x04002C58 RID: 11352
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiShowNextPage;

		// Token: 0x04002C59 RID: 11353
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiShowLastPage;

		// Token: 0x04002C5A RID: 11354
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiMultiplePages;

		// Token: 0x04002C5B RID: 11355
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiFillBackground;

		// Token: 0x04002C5C RID: 11356
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiWatermark;

		// Token: 0x04002C5D RID: 11357
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem bbiExportFile;

		// Token: 0x04002C5E RID: 11358
		private global::DevExpress.XtraPrinting.Preview.PreviewBar previewBar2;

		// Token: 0x04002C5F RID: 11359
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewStaticItem printPreviewStaticItem1;

		// Token: 0x04002C60 RID: 11360
		private global::DevExpress.XtraPrinting.Preview.ProgressBarEditItem progressBarEditItem1;

		// Token: 0x04002C61 RID: 11361
		private global::DevExpress.XtraEditors.Repository.RepositoryItemProgressBar repositoryItemProgressBar1;

		// Token: 0x04002C62 RID: 11362
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem printPreviewBarItem1;

		// Token: 0x04002C63 RID: 11363
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewStaticItem printPreviewStaticItem2;

		// Token: 0x04002C64 RID: 11364
		private global::DevExpress.XtraPrinting.Preview.ZoomTrackBarEditItem zoomTrackBarEditItem1;

		// Token: 0x04002C65 RID: 11365
		private global::DevExpress.XtraEditors.Repository.RepositoryItemZoomTrackBar repositoryItemZoomTrackBar1;

		// Token: 0x04002C66 RID: 11366
		private global::DevExpress.XtraPrinting.Preview.PreviewBar previewBar3;

		// Token: 0x04002C67 RID: 11367
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewSubItem miPageLayout;

		// Token: 0x04002C68 RID: 11368
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem miPageLayoutFacing;

		// Token: 0x04002C69 RID: 11369
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarItem miPageLayoutContinuous;

		// Token: 0x04002C6A RID: 11370
		private global::DevExpress.XtraBars.BarToolbarsListItem miToolbars;

		// Token: 0x04002C6B RID: 11371
		private global::DevExpress.XtraBars.BarDockControl barDockControlTop;

		// Token: 0x04002C6C RID: 11372
		private global::DevExpress.XtraBars.BarDockControl barDockControlBottom;

		// Token: 0x04002C6D RID: 11373
		private global::DevExpress.XtraBars.BarDockControl barDockControlLeft;

		// Token: 0x04002C6E RID: 11374
		private global::DevExpress.XtraBars.BarDockControl barDockControlRight;

		// Token: 0x04002C6F RID: 11375
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem1;

		// Token: 0x04002C70 RID: 11376
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem2;

		// Token: 0x04002C71 RID: 11377
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem3;

		// Token: 0x04002C72 RID: 11378
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem4;

		// Token: 0x04002C73 RID: 11379
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem5;

		// Token: 0x04002C74 RID: 11380
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem6;

		// Token: 0x04002C75 RID: 11381
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem7;

		// Token: 0x04002C76 RID: 11382
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem8;

		// Token: 0x04002C77 RID: 11383
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem9;

		// Token: 0x04002C78 RID: 11384
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem10;

		// Token: 0x04002C79 RID: 11385
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem11;

		// Token: 0x04002C7A RID: 11386
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem12;

		// Token: 0x04002C7B RID: 11387
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem13;

		// Token: 0x04002C7C RID: 11388
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem14;

		// Token: 0x04002C7D RID: 11389
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem15;

		// Token: 0x04002C7E RID: 11390
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem16;

		// Token: 0x04002C7F RID: 11391
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem17;

		// Token: 0x04002C80 RID: 11392
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem18;

		// Token: 0x04002C81 RID: 11393
		private global::DevExpress.XtraPrinting.Preview.PrintPreviewBarCheckItem printPreviewBarCheckItem19;

		// Token: 0x04002C82 RID: 11394
		private global::DevExpress.XtraBars.BarButtonItem barButtonItem2;

		// Token: 0x04002C83 RID: 11395
		private global::DevExpress.XtraBars.BarButtonItem barButtonItem4;

		// Token: 0x04002C84 RID: 11396
		private global::DevExpress.XtraBars.BarButtonItem barButtonItem3;

		// Token: 0x04002C85 RID: 11397
		public global::DevExpress.XtraPrinting.Preview.DocumentViewer documentViewer1;
	}
}
