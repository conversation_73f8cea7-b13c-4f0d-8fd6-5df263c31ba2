﻿using EasyStock.Classes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace EasyStock.Models
{
    public class SalesPriceOfferDetail : BaseNotifyPropertyChangedModel, IProductRowDetail
    {
        [Display(Name = "")]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        public SalesPriceOffer SalesPriceOffer { get; set; }

        public int SalesPriceOfferID { get; set; }

        [NotMapped]
        public Product Product
        {
            get
            {
                bool flag = this._product == null || this._product.ID != this.ProductID;
                if (flag)
                {
                    this._product = CurrentSession.Products.SingleOrDefault((Product x) => x.ID == this.ProductID);
                }
                return this._product;
            }
        }

        [Display(Name = " Article")]
        public int ProductID { get; set; }

        [NotMapped]
        public ProductUnit Unit
        {
            get
            {
                bool flag = this._unit == null || this._unit.ID != this.UnitID;
                if (flag)
                {
                    this._unit = CurrentSession.ProductUnits.SingleOrDefault((ProductUnit x) => x.ID == this.UnitID);
                }
                return this._unit;
            }
        }

        [Display(Name = "Unité")]
        [Range(1, 2147483647, ErrorMessage = "Vous devez sélectionner l'unité")]
        public int UnitID { get; set; }

        public double Factor { get; set; }

        [Display(Name = "Couleur")]
        public int? ColorID { get; set; }

        [Display(Name = "Taille")]
        public int? SizeID { get; set; }

        [Display(Name = "Date d'expiration")]
        public DateTime? Expire { get; set; }

        [Display(Name = "Quantité")]
        [Range(0.0, 1.7976931348623157E+308, ErrorMessage = "La quantité doit être supérieure à 0")]
        public double Quantity
        {
            get
            {
                return this._quantity;
            }
            set
            {
                base.SetProperty<double>(ref this._quantity, value, "Quantity");
            }
        }

        public double RowQuantity
        {
            get
            {
                return this.Quantity * this.Factor;
            }
        }

        [Display(Name = "Prix")]
        public double Price
        {
            get
            {
                return this._price;
            }
            set
            {
                base.SetProperty<double>(ref this._price, value, "Price");
            }
        }

        [NotMapped]
        [Display(Name = "Total")]
        public double Total
        {
            get
            {
                return this.Price * this.Quantity;
            }
        }

        public ProductTransaction ShallowCopy()
        {
            return (ProductTransaction)base.MemberwiseClone();
        }

        [Display(Name = "Code Article")]
        public string ProductCode { get; set; }

        [Display(Name = "Remise")]
        public double Discount { get; set; }

        [Display(Name = "Pourcentage de remise")]
        [Range(0.0, 0.99, ErrorMessage = "Le pourcentage de remise doit être compris entre 0 et 99 %")]
        public double DiscountPercentage
        {
            get
            {
                return this.discountPercentage;
            }
            set
            {
                base.SetProperty<double>(ref this.discountPercentage, value, "DiscountPercentage");
            }
        }

        [NotMapped]
        [Display(Name = "Net")]
        public double Net
        {
            get
            {
                return this.Total + this.Tax - this.Discount;
            }
        }

        [Display(Name = "Taxe")]
        public double Tax { get; set; }

        [NotMapped]
        [Display(Name = "Pourcentage de taxe")]
        [Range(0.0, 0.99, ErrorMessage = "Le pourcentage de taxe doit être compris entre 0 et 99 %")]
        public double TaxPercentage { get; set; }


        private int id;

        private Product _product;

        private ProductUnit _unit;

        private double _quantity;

        private double _price;

        private double discountPercentage;
    }
}
