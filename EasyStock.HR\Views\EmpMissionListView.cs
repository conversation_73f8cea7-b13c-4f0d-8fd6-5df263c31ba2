﻿using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using EasyStock.Common;
using EasyStock.HR.BLL;
using EasyStock.HR.Class;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class EmpMissionListView : MasterView
    {
        public static EmpDelayListView Instance
        {
            get
            {
                bool flag = EmpMissionListView.instance == null || EmpMissionListView.instance.IsDisposed;
                if (flag)
                {
                    EmpMissionListView.instance = new EmpDelayListView();
                }
                return EmpMissionListView.instance;
            }
        }

        public EmpMissionListView()
        {
            this.InitializeComponent();
            this.context = new HRDataContext();
            this.InitializeComponent();
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.FocusRectStyle = DrawFocusRectStyle.RowFocus;
            this.gridView1.OptionsSelection.MultiSelect = true;
            this.gridView1.SetAlternatingColors();
            this.gridView1.DoubleClick += this.GridView1_DoubleClick;
            this.btn_Save.Visibility = BarItemVisibility.Never;
            this.btn_Print.Visibility = BarItemVisibility.Always;
            this.btn_Refresh.Visibility = BarItemVisibility.Always;
            this.btn_Print.Enabled = true;
        }

        private void GridView1_DoubleClick(object sender, EventArgs e)
        {
            DXMouseEventArgs ea = e as DXMouseEventArgs;
            GridView view = sender as GridView;
            GridHitInfo info = view.CalcHitInfo(ea.Location);
            bool flag = info.InRow || info.InRowCell;
            if (flag)
            {
                int id = Convert.ToInt32(view.GetFocusedRowCellValue("ID"));
                this.Edit(id);
            }
        }

        public virtual void Edit(int id)
        {
            Static.OpenForm(EmpMissionView.Instance);
            EmpMissionView.Instance.GoTo(id);
        }

        public override void New()
        {
            Static.OpenForm(EmpMissionView.Instance);
            EmpMissionView.Instance.btn_New.PerformClick();
        }

        public override void RefreshData()
        {
            this.gridControl1.DataSource = this.context.EmpMissions.ToList<EmpMission>();
            this.repoemp.DataSource = EmployeeBLL.GetAll();
            base.RefreshData();
        }

        public override void Print()
        {
            base.Print();
        }

        public override void Delete()
        {
            base.Delete();
        }

        private static EmpDelayListView instance;

        private HRDataContext context;
    }
}
