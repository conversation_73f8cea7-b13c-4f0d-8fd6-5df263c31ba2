﻿namespace EasyStock.HR.Views.Work
{
	// Token: 0x0200004C RID: 76
	public partial class WorkReturnView : global::EasyStock.HR.MainViews.MasterView
	{
		// Token: 0x060002CD RID: 717 RVA: 0x00035204 File Offset: 0x00033404
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060002CE RID: 718 RVA: 0x0003523C File Offset: 0x0003343C
		private void InitializeComponent()
		{
			this.components = new global::System.ComponentModel.Container();
			global::System.ComponentModel.ComponentResourceManager resources = new global::System.ComponentModel.ComponentResourceManager(typeof(global::EasyStock.HR.Views.Work.WorkReturnView));
			this.dataLayoutControl1 = new global::DevExpress.XtraDataLayout.DataLayoutControl();
			this.IDTextEdit = new global::DevExpress.XtraEditors.TextEdit();
			this.WorkbindingSource = new global::System.Windows.Forms.BindingSource(this.components);
			this.EmployeeIdLookUpEdit = new global::DevExpress.XtraEditors.LookUpEdit();
			this.DateDateEdit = new global::DevExpress.XtraEditors.DateEdit();
			this.RemarksLookUpEdit = new global::DevExpress.XtraEditors.LookUpEdit();
			this.Root = new global::DevExpress.XtraLayout.LayoutControlGroup();
			this.layoutControlGroup1 = new global::DevExpress.XtraLayout.LayoutControlGroup();
			this.ItemForID = new global::DevExpress.XtraLayout.LayoutControlItem();
			this.ItemForEmployeeId = new global::DevExpress.XtraLayout.LayoutControlItem();
			this.ItemForDate = new global::DevExpress.XtraLayout.LayoutControlItem();
			this.ItemForRemarks = new global::DevExpress.XtraLayout.LayoutControlItem();
			((global::System.ComponentModel.ISupportInitialize)this.dataLayoutControl1).BeginInit();
			this.dataLayoutControl1.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.IDTextEdit.Properties).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.WorkbindingSource).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.EmployeeIdLookUpEdit.Properties).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.DateDateEdit.Properties.CalendarTimeProperties).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.DateDateEdit.Properties).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.RemarksLookUpEdit.Properties).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.Root).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.layoutControlGroup1).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.ItemForID).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.ItemForEmployeeId).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.ItemForDate).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.ItemForRemarks).BeginInit();
			base.SuspendLayout();
			this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
			this.dataLayoutControl1.Controls.Add(this.EmployeeIdLookUpEdit);
			this.dataLayoutControl1.Controls.Add(this.DateDateEdit);
			this.dataLayoutControl1.Controls.Add(this.RemarksLookUpEdit);
			this.dataLayoutControl1.DataSource = this.WorkbindingSource;
			this.dataLayoutControl1.Dock = global::System.Windows.Forms.DockStyle.Fill;
			this.dataLayoutControl1.Location = new global::System.Drawing.Point(0, 30);
			this.dataLayoutControl1.Name = "dataLayoutControl1";
			this.dataLayoutControl1.Root = this.Root;
			this.dataLayoutControl1.Size = new global::System.Drawing.Size(800, 392);
			this.dataLayoutControl1.TabIndex = 4;
			this.dataLayoutControl1.Text = "dataLayoutControl1";
			this.IDTextEdit.DataBindings.Add(new global::System.Windows.Forms.Binding("EditValue", this.WorkbindingSource, "ID", true));
			this.IDTextEdit.Location = new global::System.Drawing.Point(12, 12);
			this.IDTextEdit.Name = "IDTextEdit";
			this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
			this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = global::DevExpress.Utils.HorzAlignment.Near;
			this.IDTextEdit.Properties.Mask.EditMask = "N0";
			this.IDTextEdit.Properties.Mask.MaskType = global::DevExpress.XtraEditors.Mask.MaskType.Numeric;
			this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
			this.IDTextEdit.Properties.ReadOnly = true;
			this.IDTextEdit.Size = new global::System.Drawing.Size(704, 22);
			this.IDTextEdit.StyleController = this.dataLayoutControl1;
			this.IDTextEdit.TabIndex = 4;
			this.WorkbindingSource.DataSource = typeof(global::EasyStock.HR.Models.WorkLeaveReturn);
			this.EmployeeIdLookUpEdit.DataBindings.Add(new global::System.Windows.Forms.Binding("EditValue", this.WorkbindingSource, "EmployeeId", true));
			this.EmployeeIdLookUpEdit.Location = new global::System.Drawing.Point(12, 38);
			this.EmployeeIdLookUpEdit.Name = "EmployeeIdLookUpEdit";
			this.EmployeeIdLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
			this.EmployeeIdLookUpEdit.Properties.Appearance.TextOptions.HAlignment = global::DevExpress.Utils.HorzAlignment.Near;
			this.EmployeeIdLookUpEdit.Properties.Buttons.AddRange(new global::DevExpress.XtraEditors.Controls.EditorButton[]
			{
				new global::DevExpress.XtraEditors.Controls.EditorButton(global::DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)
			});
			this.EmployeeIdLookUpEdit.Properties.NullText = "";
			this.EmployeeIdLookUpEdit.Size = new global::System.Drawing.Size(704, 22);
			this.EmployeeIdLookUpEdit.StyleController = this.dataLayoutControl1;
			this.EmployeeIdLookUpEdit.TabIndex = 5;
			this.DateDateEdit.DataBindings.Add(new global::System.Windows.Forms.Binding("EditValue", this.WorkbindingSource, "Date", true));
			this.DateDateEdit.EditValue = null;
			this.DateDateEdit.Location = new global::System.Drawing.Point(12, 64);
			this.DateDateEdit.Name = "DateDateEdit";
			this.DateDateEdit.Properties.Buttons.AddRange(new global::DevExpress.XtraEditors.Controls.EditorButton[]
			{
				new global::DevExpress.XtraEditors.Controls.EditorButton(global::DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)
			});
			this.DateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new global::DevExpress.XtraEditors.Controls.EditorButton[]
			{
				new global::DevExpress.XtraEditors.Controls.EditorButton(global::DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)
			});
			this.DateDateEdit.Size = new global::System.Drawing.Size(704, 22);
			this.DateDateEdit.StyleController = this.dataLayoutControl1;
			this.DateDateEdit.TabIndex = 6;
			this.RemarksLookUpEdit.DataBindings.Add(new global::System.Windows.Forms.Binding("EditValue", this.WorkbindingSource, "Remarks", true));
			this.RemarksLookUpEdit.Location = new global::System.Drawing.Point(12, 90);
			this.RemarksLookUpEdit.Name = "RemarksLookUpEdit";
			this.RemarksLookUpEdit.Properties.Buttons.AddRange(new global::DevExpress.XtraEditors.Controls.EditorButton[]
			{
				new global::DevExpress.XtraEditors.Controls.EditorButton(global::DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)
			});
			this.RemarksLookUpEdit.Properties.NullText = "";
			this.RemarksLookUpEdit.Size = new global::System.Drawing.Size(704, 22);
			this.RemarksLookUpEdit.StyleController = this.dataLayoutControl1;
			this.RemarksLookUpEdit.TabIndex = 7;
			this.Root.EnableIndentsWithoutBorders = global::DevExpress.Utils.DefaultBoolean.True;
			this.Root.GroupBordersVisible = false;
			this.Root.Items.AddRange(new global::DevExpress.XtraLayout.BaseLayoutItem[]
			{
				this.layoutControlGroup1
			});
			this.Root.Name = "Root";
			this.Root.Size = new global::System.Drawing.Size(800, 392);
			this.Root.TextVisible = false;
			this.layoutControlGroup1.AllowDrawBackground = false;
			this.layoutControlGroup1.GroupBordersVisible = false;
			this.layoutControlGroup1.Items.AddRange(new global::DevExpress.XtraLayout.BaseLayoutItem[]
			{
				this.ItemForID,
				this.ItemForEmployeeId,
				this.ItemForDate,
				this.ItemForRemarks
			});
			this.layoutControlGroup1.Location = new global::System.Drawing.Point(0, 0);
			this.layoutControlGroup1.Name = "autoGeneratedGroup0";
			this.layoutControlGroup1.Size = new global::System.Drawing.Size(780, 372);
			this.ItemForID.Control = this.IDTextEdit;
			this.ItemForID.Location = new global::System.Drawing.Point(0, 0);
			this.ItemForID.Name = "ItemForID";
			this.ItemForID.Size = new global::System.Drawing.Size(780, 26);
			this.ItemForID.TextSize = new global::System.Drawing.Size(60, 17);
			this.ItemForEmployeeId.Control = this.EmployeeIdLookUpEdit;
			this.ItemForEmployeeId.Location = new global::System.Drawing.Point(0, 26);
			this.ItemForEmployeeId.Name = "ItemForEmployeeId";
			this.ItemForEmployeeId.Size = new global::System.Drawing.Size(780, 26);
			this.ItemForEmployeeId.TextSize = new global::System.Drawing.Size(60, 17);
			this.ItemForDate.Control = this.DateDateEdit;
			this.ItemForDate.Location = new global::System.Drawing.Point(0, 52);
			this.ItemForDate.Name = "ItemForDate";
			this.ItemForDate.Size = new global::System.Drawing.Size(780, 26);
			this.ItemForDate.TextSize = new global::System.Drawing.Size(60, 17);
			this.ItemForRemarks.Control = this.RemarksLookUpEdit;
			this.ItemForRemarks.Location = new global::System.Drawing.Point(0, 78);
			this.ItemForRemarks.Name = "ItemForRemarks";
			this.ItemForRemarks.Size = new global::System.Drawing.Size(780, 294);
			this.ItemForRemarks.TextSize = new global::System.Drawing.Size(60, 17);
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(7f, 16f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			this.AutoValidate = global::System.Windows.Forms.AutoValidate.EnableAllowFocusChange;
			base.ClientSize = new global::System.Drawing.Size(800, 450);
			base.Controls.Add(this.dataLayoutControl1);
			base.Name = "WorkReturnView";
			this.Text = "WorkReturnView";
			base.Controls.SetChildIndex(this.dataLayoutControl1, 0);
			((global::System.ComponentModel.ISupportInitialize)this.dataLayoutControl1).EndInit();
			this.dataLayoutControl1.ResumeLayout(false);
			((global::System.ComponentModel.ISupportInitialize)this.IDTextEdit.Properties).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.WorkbindingSource).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.EmployeeIdLookUpEdit.Properties).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.DateDateEdit.Properties.CalendarTimeProperties).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.DateDateEdit.Properties).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.RemarksLookUpEdit.Properties).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.Root).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.layoutControlGroup1).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.ItemForID).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.ItemForEmployeeId).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.ItemForDate).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.ItemForRemarks).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000497 RID: 1175
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04000498 RID: 1176
		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		// Token: 0x04000499 RID: 1177
		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		// Token: 0x0400049A RID: 1178
		private global::System.Windows.Forms.BindingSource WorkbindingSource;

		// Token: 0x0400049B RID: 1179
		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		// Token: 0x0400049C RID: 1180
		private global::DevExpress.XtraEditors.LookUpEdit EmployeeIdLookUpEdit;

		// Token: 0x0400049D RID: 1181
		private global::DevExpress.XtraEditors.DateEdit DateDateEdit;

		// Token: 0x0400049E RID: 1182
		private global::DevExpress.XtraEditors.LookUpEdit RemarksLookUpEdit;

		// Token: 0x0400049F RID: 1183
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		// Token: 0x040004A0 RID: 1184
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		// Token: 0x040004A1 RID: 1185
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForEmployeeId;

		// Token: 0x040004A2 RID: 1186
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDate;

		// Token: 0x040004A3 RID: 1187
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForRemarks;
	}
}
