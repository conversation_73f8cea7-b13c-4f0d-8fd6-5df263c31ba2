﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x020004C1 RID: 1217
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class PettyCashCloseOutAmount : DbMigration, IMigrationMetadata
	{
		// Token: 0x0600241C RID: 9244 RVA: 0x00012A86 File Offset: 0x00010C86
		public override void Up()
		{
			base.AddColumn("dbo.PettyCashCloseOut", "Amount", (ColumnBuilder c) => c.Double(new bool?(false), null, null, null, null, null), null);
		}

		// Token: 0x0600241D RID: 9245 RVA: 0x00012ABA File Offset: 0x00010CBA
		public override void Down()
		{
			base.DropColumn("dbo.PettyCashCloseOut", "Amount", null);
		}

		// Token: 0x17000BD3 RID: 3027
		// (get) Token: 0x0600241E RID: 9246 RVA: 0x001F5A0C File Offset: 0x001F3C0C
		string IMigrationMetadata.Id
		{
			get
			{
				return "202109121442149_PettyCashCloseOutAmount";
			}
		}

		// Token: 0x17000BD4 RID: 3028
		// (get) Token: 0x0600241F RID: 9247 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000BD5 RID: 3029
		// (get) Token: 0x06002420 RID: 9248 RVA: 0x001F5A24 File Offset: 0x001F3C24
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B76 RID: 11126
		private readonly ResourceManager Resources = new ResourceManager(typeof(PettyCashCloseOutAmount));
	}
}
