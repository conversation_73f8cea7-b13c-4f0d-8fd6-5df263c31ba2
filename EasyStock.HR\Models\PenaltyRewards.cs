namespace EasyStock.HR.Models
{
    using System;
    using System.ComponentModel.DataAnnotations;

    public partial class PenaltyRewards
    {
        public int ID { get; set; }

        public DateTime Date { get; set; }

        public int calcType { get; set; }

        public int Type { get; set; }

        public int NoOfDays { get; set; }

        public double Amount { get; set; }

        [StringLength(150)]
        public string Remarks { get; set; }

        public int EmployeeId { get; set; }

        public virtual Employees Employees { get; set; }
    }
}
