﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    public class OvertimeAndDelayRegulationMinutesTable : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Numéro")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "De Minute")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int FromMinute
        {
            get
            {
                return this.fromMin;
            }
            set
            {
                base.SetProperty<int>(ref this.fromMin, value, "FromMinute");
            }
        }

        [Display(Name = "À Minute")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int ToMinute
        {
            get
            {
                return this.ToMin;
            }
            set
            {
                base.SetProperty<int>(ref this.ToMin, value, "ToMinute");
            }
        }

        [Display(Name = "Calculé")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int Calculate
        {
            get
            {
                return this.calculate;
            }
            set
            {
                base.SetProperty<int>(ref this.calculate, value, "Calculate");
            }
        }

        public OvertimeAndDelayRegulation OvertimeAndDelayRegulation { get; set; }

        public int OvertimeAndDelayRegulationID { get; set; }

        private int id;

        private int fromMin;

        private int ToMin;

        private int calculate;
    }
}
