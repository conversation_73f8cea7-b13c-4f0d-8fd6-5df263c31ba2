﻿namespace EasyStock.MainViews
{
	public partial class HomeScreen : global::DevExpress.XtraEditors.XtraForm
	{
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraBars.Docking2010.Views.Widget.WidgetDockingContainer widgetDockingContainer1 = new DevExpress.XtraBars.Docking2010.Views.Widget.WidgetDockingContainer();
            DevExpress.XtraBars.Docking2010.Views.Widget.WidgetDockingContainer widgetDockingContainer2 = new DevExpress.XtraBars.Docking2010.Views.Widget.WidgetDockingContainer();
            DevExpress.XtraBars.Docking2010.Views.Widget.WidgetDockingContainer widgetDockingContainer3 = new DevExpress.XtraBars.Docking2010.Views.Widget.WidgetDockingContainer();
            this.frequentlyUsedTileControlDocument = new DevExpress.XtraBars.Docking2010.Views.Widget.Document(this.components);
            this.chartsViewDocument = new DevExpress.XtraBars.Docking2010.Views.Widget.Document(this.components);
            this.documentManager1 = new DevExpress.XtraBars.Docking2010.DocumentManager(this.components);
            this.widgetView1 = new DevExpress.XtraBars.Docking2010.Views.Widget.WidgetView(this.components);
            this.columnDefinition1 = new DevExpress.XtraBars.Docking2010.Views.Widget.ColumnDefinition();
            this.columnDefinition2 = new DevExpress.XtraBars.Docking2010.Views.Widget.ColumnDefinition();
            this.columnDefinition3 = new DevExpress.XtraBars.Docking2010.Views.Widget.ColumnDefinition();
            this.rowDefinition1 = new DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition();
            this.rowDefinition2 = new DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition();
            this.rowDefinition3 = new DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition();
            this.stackGroup1 = new DevExpress.XtraBars.Docking2010.Views.Widget.StackGroup(this.components);
            this.stackGroup2 = new DevExpress.XtraBars.Docking2010.Views.Widget.StackGroup(this.components);
            this.stackGroup3 = new DevExpress.XtraBars.Docking2010.Views.Widget.StackGroup(this.components);
            ((System.ComponentModel.ISupportInitialize)(this.frequentlyUsedTileControlDocument)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartsViewDocument)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.documentManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.widgetView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnDefinition1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnDefinition2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnDefinition3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rowDefinition1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rowDefinition2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rowDefinition3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.stackGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.stackGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.stackGroup3)).BeginInit();
            this.SuspendLayout();
            this.frequentlyUsedTileControlDocument.Caption = "Utilisé récemment";
            this.frequentlyUsedTileControlDocument.ControlName = "FrequentlyUsedTileControl";
            this.frequentlyUsedTileControlDocument.ControlTypeName = "EasyStock.Controls.FrequentlyUsedTileControl";
            this.frequentlyUsedTileControlDocument.FreeLayoutWidth.UnitValue = 0.70392848896658644D;
            this.frequentlyUsedTileControlDocument.Height = 497;
            this.frequentlyUsedTileControlDocument.RowSpan = 3;
            this.frequentlyUsedTileControlDocument.Width = 221;
            this.chartsViewDocument.Caption = "Statistiques";
            this.chartsViewDocument.ColumnIndex = 1;
            this.chartsViewDocument.ControlName = "ChartsView";
            this.chartsViewDocument.ControlTypeName = "EasyStock.Controls.ChartsView";
            this.chartsViewDocument.FreeLayoutHeight.UnitValue = 1.5521472392638036D;
            this.chartsViewDocument.FreeLayoutWidth.UnitValue = 1.5255797077547251D;
            this.chartsViewDocument.Height = 334;
            this.chartsViewDocument.RowIndex = 1;
            this.chartsViewDocument.RowSpan = 2;
            this.chartsViewDocument.Width = 221;
            this.documentManager1.ContainerControl = this;
            this.documentManager1.View = this.widgetView1;
            this.documentManager1.ViewCollection.AddRange(new DevExpress.XtraBars.Docking2010.Views.BaseView[] {
            this.widgetView1});
            this.widgetView1.AllowResetLayout = DevExpress.Utils.DefaultBoolean.False;
            this.widgetView1.AllowResizeAnimation = DevExpress.Utils.DefaultBoolean.True;
            this.widgetView1.AllowStartupAnimation = DevExpress.Utils.DefaultBoolean.True;
            this.widgetView1.Columns.AddRange(new DevExpress.XtraBars.Docking2010.Views.Widget.ColumnDefinition[] {
            this.columnDefinition1,
            this.columnDefinition2,
            this.columnDefinition3});
            this.widgetView1.DocumentProperties.AllowClose = false;
            this.widgetView1.DocumentProperties.AllowDragging = false;
            this.widgetView1.DocumentProperties.AllowGlyphSkinning = true;
            this.widgetView1.DocumentProperties.ShowCloseButton = false;
            this.widgetView1.Documents.AddRange(new DevExpress.XtraBars.Docking2010.Views.BaseDocument[] {
            this.frequentlyUsedTileControlDocument,
            this.chartsViewDocument});
            this.widgetView1.DocumentSpacing = 5;
            this.widgetView1.FreeLayoutProperties.FreeLayoutItems.AddRange(new DevExpress.XtraBars.Docking2010.Views.Widget.Document[] {
            this.frequentlyUsedTileControlDocument,
            this.chartsViewDocument});
            this.widgetView1.LayoutMode = DevExpress.XtraBars.Docking2010.Views.Widget.LayoutMode.FreeLayout;
            widgetDockingContainer2.Element = this.frequentlyUsedTileControlDocument;
            widgetDockingContainer3.Element = this.chartsViewDocument;
            widgetDockingContainer1.Nodes.AddRange(new DevExpress.XtraBars.Docking2010.Views.Tabbed.DockingContainer[] {
            widgetDockingContainer2,
            widgetDockingContainer3});
            this.widgetView1.RootContainer.Nodes.AddRange(new DevExpress.XtraBars.Docking2010.Views.Tabbed.DockingContainer[] {
            widgetDockingContainer1});
            this.widgetView1.RootContainer.Orientation = System.Windows.Forms.Orientation.Vertical;
            this.widgetView1.Rows.AddRange(new DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition[] {
            this.rowDefinition1,
            this.rowDefinition2,
            this.rowDefinition3});
            this.widgetView1.StackGroupProperties.AllowDragging = false;
            this.widgetView1.StackGroups.AddRange(new DevExpress.XtraBars.Docking2010.Views.Widget.StackGroup[] {
            this.stackGroup1,
            this.stackGroup2,
            this.stackGroup3});
            this.widgetView1.Style = DevExpress.XtraBars.Docking2010.Views.DockingViewStyle.Light;
            this.stackGroup1.Items.AddRange(new DevExpress.XtraBars.Docking2010.Views.Widget.Document[] {
            this.frequentlyUsedTileControlDocument});
            this.stackGroup2.Items.AddRange(new DevExpress.XtraBars.Docking2010.Views.Widget.Document[] {
            this.chartsViewDocument});
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1004, 644);
            this.Name = "HomeScreen";
            this.Text = "Principal";
            ((System.ComponentModel.ISupportInitialize)(this.frequentlyUsedTileControlDocument)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartsViewDocument)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.documentManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.widgetView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnDefinition1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnDefinition2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnDefinition3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rowDefinition1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rowDefinition2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rowDefinition3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.stackGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.stackGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.stackGroup3)).EndInit();
            this.ResumeLayout(false);

		}

		private global::System.ComponentModel.IContainer components = null;

		private global::DevExpress.XtraBars.Docking2010.DocumentManager documentManager1;

		private global::DevExpress.XtraBars.Docking2010.Views.Widget.WidgetView widgetView1;

		private global::DevExpress.XtraBars.Docking2010.Views.Widget.Document frequentlyUsedTileControlDocument;

		private global::DevExpress.XtraBars.Docking2010.Views.Widget.Document chartsViewDocument;

		private global::DevExpress.XtraBars.Docking2010.Views.Widget.ColumnDefinition columnDefinition1;

		private global::DevExpress.XtraBars.Docking2010.Views.Widget.ColumnDefinition columnDefinition2;

		private global::DevExpress.XtraBars.Docking2010.Views.Widget.ColumnDefinition columnDefinition3;

		private global::DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition rowDefinition1;

		private global::DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition rowDefinition2;

		private global::DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition rowDefinition3;

		private global::DevExpress.XtraBars.Docking2010.Views.Widget.StackGroup stackGroup1;

		private global::DevExpress.XtraBars.Docking2010.Views.Widget.StackGroup stackGroup2;

		private global::DevExpress.XtraBars.Docking2010.Views.Widget.StackGroup stackGroup3;
	}
}
