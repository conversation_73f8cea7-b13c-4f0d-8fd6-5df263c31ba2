﻿using DevExpress.Utils;
using DevExpress.XtraCharts;
using EasyStock.Charts.Models;
using EasyStock.Controller;
using EasyStock.Models;
using System;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace EasyStock.Charts
{
    public class SalesPerformanceDailyChart : BaseChart
    {
        public override string Caption => "Performance des ventes quotidiennes";

        public override Color Color => ColorTranslator.FromHtml("#37474f");

        public SalesPerformanceDailyChart()
        {
            Series series1 = new Series();
            SplineAreaSeriesView splineAreaSeriesView1 = new SplineAreaSeriesView();
            series1.ArgumentDataMember = "Date";
            series1.ValueDataMembersSerializable = "TotalSales";
            series1.Name = "Series 1";
            series1.View = splineAreaSeriesView1;
            base.SeriesSerializable = new Series[1] { series1 };
            XYDiagram diagram = (XYDiagram)base.Diagram;
            base.AnimationStartMode = ChartAnimationMode.OnDataChanged;
            diagram.AxisY.Visibility = DefaultBoolean.True;
            diagram.AxisX.Visibility = DefaultBoolean.True;
            base.Legend.Visibility = DefaultBoolean.False;
        }

        public override async Task<object> QueryData()
        {
            ERPDataContext db = new ERPDataContext();
            object dataSource = null;
            await Task.Run(delegate
            {
                DateTime Lastyear = DateTime.Now.AddDays(-30.0);
                var source = (from i in db.SalesInvoices
                              join c in db.Customers on i.CustomerID equals c.ID
                              where i.Date > Lastyear
                              select new
                              {
                                  ID = i.ID,
                                  Customer = c.Name,
                                  Date = DbFunctions.TruncateTime(i.Date),
                                  Net = i.Total + i.Tax + i.OtherExpenses - i.Discount,
                                  Paid = db.PayDetails.Where(d => d.SourceID == i.ID && (int)d.SourceType == 4).Sum(x => (double?)(x.Amount * x.CurrancyRate) ?? 0.0),
                                  Remaining = i.Total + i.Tax + i.OtherExpenses - i.Discount - db.PayDetails.Where(d => d.SourceID == i.ID && (int)d.SourceType == 4).Sum(x => (double?)(x.Amount * x.CurrancyRate) ?? 0.0)
                              } into x
                              group x by new
                              {
                                  x.Date.Value.Day,
                                  x.Date.Value.Month,
                                  x.Date.Value.Year
                              } into x
                              select new
                              {
                                  Key = x.Key,
                                  TotalSales = x.Sum(c => c.Net)
                              }).ToList();
                dataSource = source.Select(x => new DailySalesSummery
                {
                    Date = new DateTime(x.Key.Year, x.Key.Month, x.Key.Day),
                    TotalSales = x.TotalSales
                }).ToList();
            });
            return dataSource;
        }
    }
}
