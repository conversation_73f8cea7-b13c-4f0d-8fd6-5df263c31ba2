﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x0200047A RID: 1146
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class rename_branchs_to_store : DbMigration, IMigrationMetadata
	{
		// Token: 0x06002295 RID: 8853 RVA: 0x001EFB40 File Offset: 0x001EDD40
		public override void Up()
		{
			base.RenameTable("dbo.Branches", "Stores", null);
			base.RenameColumn("dbo.ProductStoreLocations", "BranchID", "StoreID", null);
			base.RenameIndex("dbo.ProductStoreLocations", "IX_BranchID", "IX_StoreID", null);
			base.RenameColumn("dbo.BillingDetails", "BranchLink", "StoreLink", null);
			base.RenameColumn("dbo.Bills", "BranchID", "StoreID", null);
			base.RenameColumn("dbo.StockTransferBill", "ToBranchID", "ToStoreID", null);
			base.RenameColumn("dbo.Journals", "BranchID", "StoreID", null);
			base.RenameColumn("dbo.CashNotes", "BranchID", "StoreID", null);
			base.RenameColumn("dbo.CashTransfers", "BranchID", "StoreID", null);
			base.RenameColumn("dbo.ProductTransactions", "BranchID", "StoreID", null);
			base.RenameColumn("dbo.RevExpEntries", "BranchID", "StoreID", null);
			base.RenameColumn("dbo.UserSettingsProfiles", "DefaultBranch", "DefaultStore", null);
			base.RenameColumn("dbo.UserSettingsProfiles", "DefaultRawBranch", "DefaultRawStore", null);
		}

		// Token: 0x06002296 RID: 8854 RVA: 0x001EFC74 File Offset: 0x001EDE74
		public override void Down()
		{
			base.AddColumn("dbo.UserSettingsProfiles", "DefaultRawBranch", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
			base.AddColumn("dbo.UserSettingsProfiles", "DefaultBranch", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
			base.AddColumn("dbo.RevExpEntries", "BranchID", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
			base.AddColumn("dbo.ProductTransactions", "BranchID", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
			base.AddColumn("dbo.CashTransfers", "BranchID", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
			base.AddColumn("dbo.CashNotes", "BranchID", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
			base.AddColumn("dbo.Journals", "BranchID", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
			base.AddColumn("dbo.StockTransferBill", "ToBranchID", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
			base.AddColumn("dbo.Bills", "BranchID", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
			base.AddColumn("dbo.BillingDetails", "BranchLink", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
			base.DropColumn("dbo.UserSettingsProfiles", "DefaultRawStore", null);
			base.DropColumn("dbo.UserSettingsProfiles", "DefaultStore", null);
			base.DropColumn("dbo.RevExpEntries", "StoreID", null);
			base.DropColumn("dbo.ProductTransactions", "StoreID", null);
			base.DropColumn("dbo.CashTransfers", "StoreID", null);
			base.DropColumn("dbo.CashNotes", "StoreID", null);
			base.DropColumn("dbo.Journals", "StoreID", null);
			base.DropColumn("dbo.StockTransferBill", "ToStoreID", null);
			base.DropColumn("dbo.Bills", "StoreID", null);
			base.DropColumn("dbo.BillingDetails", "StoreLink", null);
			base.RenameIndex("dbo.ProductStoreLocations", "IX_StoreID", "IX_BranchID", null);
			base.RenameColumn("dbo.ProductStoreLocations", "StoreID", "BranchID", null);
			base.RenameTable("dbo.Stores", "Branches", null);
		}

		// Token: 0x17000B64 RID: 2916
		// (get) Token: 0x06002297 RID: 8855 RVA: 0x001EFF60 File Offset: 0x001EE160
		string IMigrationMetadata.Id
		{
			get
			{
				return "202104141939165_rename_branchs_to_store";
			}
		}

		// Token: 0x17000B65 RID: 2917
		// (get) Token: 0x06002298 RID: 8856 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000B66 RID: 2918
		// (get) Token: 0x06002299 RID: 8857 RVA: 0x001EFF78 File Offset: 0x001EE178
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002ACA RID: 10954
		private readonly ResourceManager Resources = new ResourceManager(typeof(rename_branchs_to_store));
	}
}
