﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class addUserLogTable : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(addUserLogTable));

        string IMigrationMetadata.Id => "202103221145517_addUserLogTable";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.ChangeTrackerItems", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                EntityName = c.String(),
                EntityDisplayName = c.String(),
                State = c.Int(false),
                Properties = c.String(),
                ChangedProperties = c.String(),
                UserLog_ID = c.Int()
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.UserLogs", t => t.UserLog_ID).Index(t => t.UserLog_ID);
            CreateTable("dbo.UserLogs", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                UserID = c.Int(false),
                ScreenName = c.String(),
                Screen = c.String(),
                Action = c.Int(false),
                EntityID = c.Int(false),
                EntityCode = c.String()
            }).PrimaryKey(t => t.ID);
        }

        public override void Down()
        {
            DropForeignKey("dbo.ChangeTrackerItems", "UserLog_ID", "dbo.UserLogs");
            DropIndex("dbo.ChangeTrackerItems", new string[1] { "UserLog_ID" });
            DropTable("dbo.UserLogs");
            DropTable("dbo.ChangeTrackerItems");
        }
    }
}
