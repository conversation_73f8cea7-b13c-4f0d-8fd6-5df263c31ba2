﻿using DevExpress.XtraReports.UI;
using EasyStock.Reports;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.IO;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Modèles d'impression")]
    public class ReportTemplate : BaseNotifyPropertyChangedModel
    {
        public ReportTemplate()
        {
        }

        public ReportTemplate(ReportTypes reportType)
        {
            this.Type = reportType;
            XtraReport report;
            switch (reportType)
            {
                case ReportTypes.SalesInvoice:
                    report = new SalesInvoiceReport();
                    break;
                case ReportTypes.SalesReturnInvoice:
                    report = new SalesReturnInvoiceReport();
                    break;
                case ReportTypes.PurchaseInvoice:
                    report = new PurchaseInvoiceReport();
                    break;
                case ReportTypes.PurchaseReturnInvoice:
                    report = new PurchaseReturnInvoiceReport();
                    break;
                case ReportTypes.RevExpEntry:
                    report = new RevExpEntryReport();
                    break;
                case ReportTypes.Journal:
                    report = new JournalReport();
                    break;
                case ReportTypes.GridReportP:
                    report = new GridReportP();
                    break;
                case ReportTypes.CashNoteReport:
                    report = new CashNoteReport();
                    break;
                case ReportTypes.ItemBarcodes:
                    report = new BarcodeReport();
                    break;
                case ReportTypes.CashTransfer:
                    report = new CashTransferReport();
                    break;
                default:
                    throw new NotImplementedException();
            }
            using (MemoryStream stream = new MemoryStream())
            {
                report.SaveLayout(stream);
                this.Template = stream.ToArray();
            }
            this.ReportName = report.GetType().Name;
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        [Display(Name = "Nom")]
        public string Name
        {
            get
            {
                return this.name;
            }
            set
            {
                base.SetProperty<string>(ref this.name, value, "Name");
            }
        }

        [Display(Name = "Type")]
        public ReportTypes Type
        {
            get
            {
                return this.type;
            }
            set
            {
                base.SetProperty<ReportTypes>(ref this.type, value, "Type");
            }
        }

        [Display(Name = "")]
        public byte[] Template { get; set; }

        [Display(Name = "Par défaut")]
        public bool IsDefault
        {
            get
            {
                return this.isDefault;
            }
            set
            {
                base.SetProperty<bool>(ref this.isDefault, value, "IsDefault");
            }
        }

        public string ReportName { get; set; }

        public MasterReport GetReport()
        {
            MasterReport report;
            switch (this.Type)
            {
                case ReportTypes.SalesInvoice:
                    report = new SalesInvoiceReport();
                    break;
                case ReportTypes.SalesReturnInvoice:
                    report = new SalesReturnInvoiceReport();
                    break;
                case ReportTypes.PurchaseInvoice:
                    report = new PurchaseInvoiceReport();
                    break;
                case ReportTypes.PurchaseReturnInvoice:
                    report = new PurchaseReturnInvoiceReport();
                    break;
                case ReportTypes.RevExpEntry:
                    report = new RevExpEntryReport();
                    break;
                case ReportTypes.Journal:
                    report = new JournalReport();
                    break;
                case ReportTypes.GridReportP:
                    report = new GridReportP();
                    break;
                case ReportTypes.CashNoteReport:
                    report = new CashNoteReport();
                    break;
                case ReportTypes.ItemBarcodes:
                    report = new BarcodeReport();
                    break;
                case ReportTypes.CashTransfer:
                    report = new CashTransferReport();
                    break;
                default:
                    throw new NotImplementedException();
            }
            using (MemoryStream stream = new MemoryStream())
            {
                stream.Write(this.Template.ToArray<byte>(), 0, this.Template.ToArray<byte>().Length);
                report = new MasterReport();
                report.LoadLayout(stream);
            }
            return report;
        }

        private string name;

        private ReportTypes type;

        private bool isDefault;
    }
}
