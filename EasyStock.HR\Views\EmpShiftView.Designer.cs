﻿namespace EasyStock.HR.Views
{
	public partial class EmpShiftView : global::EasyStock.HR.MainViews.MasterView
	{
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.empShiftBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.FromDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.ToDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.ShiftTypeImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.DurationTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.NotesTextEdit = new DevExpress.XtraEditors.MemoEdit();
            this.EmpIDTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForEmpID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForToDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForShiftType = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDuration = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForNotes = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForFromDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.empShiftBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.FromDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.FromDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ToDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ToDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ShiftTypeImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DurationTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NotesTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmpIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmpID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForToDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForShiftType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDuration)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNotes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFromDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            this.SuspendLayout();
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.FromDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.ToDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.ShiftTypeImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.DurationTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NotesTextEdit);
            this.dataLayoutControl1.Controls.Add(this.EmpIDTextEdit);
            this.dataLayoutControl1.DataSource = this.empShiftBindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 26);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(800, 400);
            this.dataLayoutControl1.TabIndex = 4;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empShiftBindingSource, "ID", true));
            this.IDTextEdit.Location = new System.Drawing.Point(153, 44);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.EditMask = "N0";
            this.IDTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(261, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 4;
            this.empShiftBindingSource.DataSource = typeof(EasyStock.HR.Models.EmpShift);
            this.FromDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empShiftBindingSource, "FromDate", true));
            this.FromDateDateEdit.EditValue = null;
            this.FromDateDateEdit.Location = new System.Drawing.Point(153, 92);
            this.FromDateDateEdit.Name = "FromDateDateEdit";
            this.FromDateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.FromDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.FromDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.FromDateDateEdit.Size = new System.Drawing.Size(261, 20);
            this.FromDateDateEdit.StyleController = this.dataLayoutControl1;
            this.FromDateDateEdit.TabIndex = 6;
            this.ToDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empShiftBindingSource, "ToDate", true));
            this.ToDateDateEdit.EditValue = null;
            this.ToDateDateEdit.Location = new System.Drawing.Point(153, 116);
            this.ToDateDateEdit.Name = "ToDateDateEdit";
            this.ToDateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.ToDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ToDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ToDateDateEdit.Size = new System.Drawing.Size(261, 20);
            this.ToDateDateEdit.StyleController = this.dataLayoutControl1;
            this.ToDateDateEdit.TabIndex = 7;
            this.ShiftTypeImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empShiftBindingSource, "ShiftType", true));
            this.ShiftTypeImageComboBoxEdit.Location = new System.Drawing.Point(153, 140);
            this.ShiftTypeImageComboBoxEdit.Name = "ShiftTypeImageComboBoxEdit";
            this.ShiftTypeImageComboBoxEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.ShiftTypeImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.ShiftTypeImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.ShiftTypeImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ShiftTypeImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Supplément pour les quarts de travail", EasyStock.HR.EShiftType.Additional, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Allocation pour les quarts de travail", EasyStock.HR.EShiftType.Dismiss, 1)});
            this.ShiftTypeImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.ShiftTypeImageComboBoxEdit.Size = new System.Drawing.Size(261, 20);
            this.ShiftTypeImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.ShiftTypeImageComboBoxEdit.TabIndex = 8;
            this.DurationTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empShiftBindingSource, "Duration", true));
            this.DurationTextEdit.Location = new System.Drawing.Point(153, 164);
            this.DurationTextEdit.Name = "DurationTextEdit";
            this.DurationTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DurationTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DurationTextEdit.Properties.Mask.EditMask = "N0";
            this.DurationTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.DurationTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.DurationTextEdit.Size = new System.Drawing.Size(261, 20);
            this.DurationTextEdit.StyleController = this.dataLayoutControl1;
            this.DurationTextEdit.TabIndex = 9;
            this.NotesTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empShiftBindingSource, "Notes", true));
            this.NotesTextEdit.Location = new System.Drawing.Point(153, 188);
            this.NotesTextEdit.Name = "NotesTextEdit";
            this.NotesTextEdit.Size = new System.Drawing.Size(261, 56);
            this.NotesTextEdit.StyleController = this.dataLayoutControl1;
            this.NotesTextEdit.TabIndex = 10;
            this.EmpIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empShiftBindingSource, "EmpID", true));
            this.EmpIDTextEdit.Location = new System.Drawing.Point(153, 68);
            this.EmpIDTextEdit.Name = "EmpIDTextEdit";
            this.EmpIDTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.EmpIDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.EmpIDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.EmpIDTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.EmpIDTextEdit.Properties.NullText = "";
            this.EmpIDTextEdit.Size = new System.Drawing.Size(261, 20);
            this.EmpIDTextEdit.StyleController = this.dataLayoutControl1;
            this.EmpIDTextEdit.TabIndex = 5;
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(800, 400);
            this.Root.TextVisible = false;
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup2,
            this.emptySpaceItem1,
            this.emptySpaceItem2});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(780, 380);
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(418, 0);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(362, 380);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForID,
            this.ItemForEmpID,
            this.ItemForToDate,
            this.ItemForShiftType,
            this.ItemForDuration,
            this.ItemForNotes,
            this.ItemForFromDate});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(418, 248);
            this.layoutControlGroup2.Text = "Informations";
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(394, 24);
            this.ItemForID.TextSize = new System.Drawing.Size(125, 13);
            this.ItemForEmpID.Control = this.EmpIDTextEdit;
            this.ItemForEmpID.Location = new System.Drawing.Point(0, 24);
            this.ItemForEmpID.Name = "ItemForEmpID";
            this.ItemForEmpID.Size = new System.Drawing.Size(394, 24);
            this.ItemForEmpID.TextSize = new System.Drawing.Size(125, 13);
            this.ItemForToDate.Control = this.ToDateDateEdit;
            this.ItemForToDate.Location = new System.Drawing.Point(0, 72);
            this.ItemForToDate.Name = "ItemForToDate";
            this.ItemForToDate.Size = new System.Drawing.Size(394, 24);
            this.ItemForToDate.TextSize = new System.Drawing.Size(125, 13);
            this.ItemForShiftType.Control = this.ShiftTypeImageComboBoxEdit;
            this.ItemForShiftType.Location = new System.Drawing.Point(0, 96);
            this.ItemForShiftType.Name = "ItemForShiftType";
            this.ItemForShiftType.Size = new System.Drawing.Size(394, 24);
            this.ItemForShiftType.TextSize = new System.Drawing.Size(125, 13);
            this.ItemForDuration.Control = this.DurationTextEdit;
            this.ItemForDuration.Location = new System.Drawing.Point(0, 120);
            this.ItemForDuration.Name = "ItemForDuration";
            this.ItemForDuration.Size = new System.Drawing.Size(394, 24);
            this.ItemForDuration.TextSize = new System.Drawing.Size(125, 13);
            this.ItemForNotes.Control = this.NotesTextEdit;
            this.ItemForNotes.Location = new System.Drawing.Point(0, 144);
            this.ItemForNotes.MaxSize = new System.Drawing.Size(394, 60);
            this.ItemForNotes.MinSize = new System.Drawing.Size(394, 60);
            this.ItemForNotes.Name = "ItemForNotes";
            this.ItemForNotes.Size = new System.Drawing.Size(394, 60);
            this.ItemForNotes.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForNotes.TextSize = new System.Drawing.Size(125, 13);
            this.ItemForFromDate.Control = this.FromDateDateEdit;
            this.ItemForFromDate.Location = new System.Drawing.Point(0, 48);
            this.ItemForFromDate.Name = "ItemForFromDate";
            this.ItemForFromDate.Size = new System.Drawing.Size(394, 24);
            this.ItemForFromDate.TextSize = new System.Drawing.Size(125, 13);
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 248);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(418, 132);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.dataLayoutControl1);
            this.Name = "EmpShiftView";
            this.Text = "Indemnités de poste";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.empShiftBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.FromDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.FromDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ToDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ToDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ShiftTypeImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DurationTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NotesTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmpIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmpID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForToDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForShiftType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDuration)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNotes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFromDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		private global::System.ComponentModel.IContainer components = null;

		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		private global::System.Windows.Forms.BindingSource empShiftBindingSource;

		private global::DevExpress.XtraEditors.DateEdit FromDateDateEdit;

		private global::DevExpress.XtraEditors.DateEdit ToDateDateEdit;

		private global::DevExpress.XtraEditors.ImageComboBoxEdit ShiftTypeImageComboBoxEdit;

		private global::DevExpress.XtraEditors.TextEdit DurationTextEdit;

		private global::DevExpress.XtraEditors.MemoEdit NotesTextEdit;

		private global::DevExpress.XtraEditors.LookUpEdit EmpIDTextEdit;

		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForEmpID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForToDate;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForFromDate;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForShiftType;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDuration;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForNotes;

		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;
	}
}
