﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x02000490 RID: 1168
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class AddUserIDToRevExpEntry : DbMigration, IMigrationMetadata
	{
		// Token: 0x06002315 RID: 8981 RVA: 0x0001231B File Offset: 0x0001051B
		public override void Up()
		{
			base.AddColumn("dbo.RevExpEntries", "UserID", (ColumnBuilder c) => c.Int(new bool?(false), false, new int?(1), null, null, null, null), null);
		}

		// Token: 0x06002316 RID: 8982 RVA: 0x0001234F File Offset: 0x0001054F
		public override void Down()
		{
			base.DropColumn("dbo.RevExpEntries", "UserID", null);
		}

		// Token: 0x17000B85 RID: 2949
		// (get) Token: 0x06002317 RID: 8983 RVA: 0x001F14DC File Offset: 0x001EF6DC
		string IMigrationMetadata.Id
		{
			get
			{
				return "202106011828322_AddUserIDToRevExpEntry";
			}
		}

		// Token: 0x17000B86 RID: 2950
		// (get) Token: 0x06002318 RID: 8984 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000B87 RID: 2951
		// (get) Token: 0x06002319 RID: 8985 RVA: 0x001F14F4 File Offset: 0x001EF6F4
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B08 RID: 11016
		private readonly ResourceManager Resources = new ResourceManager(typeof(AddUserIDToRevExpEntry));
	}
}
