﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    [DisplayColumn("Congés officiels")]
    public class OfficialVacation : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Nom")]
        [StringLength(150)]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public string Name
        {
            get
            {
                return this.name;
            }
            set
            {
                base.SetProperty<string>(ref this.name, value, "Name");
            }
        }

        [Display(Name = "Date de début")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public DateTime FromDate
        {
            get
            {
                return this.fromdate;
            }
            set
            {
                base.SetProperty<DateTime>(ref this.fromdate, value, "FromDate");
            }
        }

        [Display(Name = "Date de fin")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public DateTime ToDate
        {
            get
            {
                return this.todate;
            }
            set
            {
                base.SetProperty<DateTime>(ref this.todate, value, "ToDate");
            }
        }

        [Display(Name = "Notes")]
        public string Notes
        {
            get
            {
                return this.notes;
            }
            set
            {
                base.SetProperty<string>(ref this.notes, value, "Notes");
            }
        }

        private int id;

        private string name;

        private DateTime fromdate;

        private DateTime todate;

        private string notes;
    }
}
