﻿using CustomControls.Models;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Tile;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace EasyStock.Controls
{

    public class TwoLevelTileView : XtraUserControl
    {
        private ICollection<Product> _productsSource;

        private IContainer components = null;

        private GridControl gridControlCategorys;

        private TileView TileView_Category;

        private GridControl gridControlProducts;

        private TileView TileView_Product;

        private BindingSource categoryBindingSource;

        private BindingSource productBindingSource1;

        private TileViewColumn colID;

        private TileViewColumn colName;

        private TileViewColumn colPrice;

        private TileViewColumn colCategoryId;

        private TileViewColumn colname1;

        private SplitContainer splitContainer1;

        public Orientation CategoryOrientation
        {
            get
            {
                return TileView_Category.OptionsTiles.Orientation;
            }
            set
            {
                TileView_Category.OptionsTiles.Orientation = value;
            }
        }

        public int CategoryColumnCount
        {
            get
            {
                return TileView_Category.OptionsTiles.ColumnCount;
            }
            set
            {
                TileView_Category.OptionsTiles.ColumnCount = value;
            }
        }

        public int ProductColumnCount
        {
            get
            {
                return TileView_Product.OptionsTiles.ColumnCount;
            }
            set
            {
                TileView_Product.OptionsTiles.ColumnCount = value;
            }
        }

        public Orientation ProductOrientation
        {
            get
            {
                return TileView_Product.OptionsTiles.Orientation;
            }
            set
            {
                TileView_Product.OptionsTiles.Orientation = value;
            }
        }

        public Color ProductBackColor
        {
            get
            {
                return TileView_Product.Appearance.ItemNormal.BackColor;
            }
            set
            {
                TileView_Product.Appearance.ItemNormal.BackColor = value;
                TileView_Product.Appearance.ItemNormal.Options.UseBackColor = true;
            }
        }

        public Color ProductBackColorPressed
        {
            get
            {
                return TileView_Product.Appearance.ItemPressed.BackColor;
            }
            set
            {
                TileView_Product.Appearance.ItemPressed.BackColor = value;
            }
        }

        public Color CategoryBackColor
        {
            get
            {
                return TileView_Category.Appearance.ItemNormal.BackColor;
            }
            set
            {
                TileView_Category.Appearance.ItemNormal.BackColor = value;
            }
        }

        public Color CategoryBackColorPressed
        {
            get
            {
                return TileView_Category.Appearance.ItemPressed.BackColor;
            }
            set
            {
                TileView_Category.Appearance.ItemPressed.BackColor = value;
            }
        }

        public Size CategoryItemSize
        {
            get
            {
                return TileView_Category.OptionsTiles.ItemSize;
            }
            set
            {
                TileView_Category.OptionsTiles.ItemSize = value;
            }
        }

        public Size ProductItemSize
        {
            get
            {
                return TileView_Product.OptionsTiles.ItemSize;
            }
            set
            {
                TileView_Product.OptionsTiles.ItemSize = value;
            }
        }

        public int CategoryPanalHeight
        {
            get
            {
                return splitContainer1.SplitterDistance;
            }
            set
            {
                try
                {
                    splitContainer1.SplitterDistance = value;
                }
                catch
                {
                }
            }
        }

        public ICollection<Category> CategorySource
        {
            set
            {
                gridControlCategorys.DataSource = value;
                if (value.Count > 0)
                {
                    gridControlProducts.DataSource = _productsSource.GetProductsByCategory(value.First().id);
                }
                else
                {
                    gridControlProducts.DataSource = null;
                }
            }
        }

        public ICollection<Product> ProductsSource
        {
            set
            {
                _productsSource = value;
            }
        }

        public event EventHandler<Product> ProductClick;

        public event EventHandler<Category> CategoryClick;

        public TwoLevelTileView()
        {
            InitializeComponent();
            TileView_Product.Appearance.ItemNormal.Options.UseBackColor = true;
            TileView_Product.Appearance.ItemPressed.Options.UseBackColor = true;
            TileView_Category.Appearance.ItemNormal.Options.UseBackColor = true;
            TileView_Category.Appearance.ItemPressed.Options.UseBackColor = true;
            TileView_Category.ItemClick += TileView_Click;
            TileView_Product.ItemClick += TileView_Click;
            CategorySource = Services.GetDummyCategoryData();
            ProductsSource = Services.GetDummyProductData();
            gridControlProducts.DataSource = _productsSource;
        }

        private void TileView_Click(object sender, EventArgs e)
        {
            TileView view = sender as TileView;
            object row = view.GetFocusedRow();
            if (row != null && row is Category category)
            {
                gridControlProducts.DataSource = _productsSource.GetProductsByCategory(category.id);
                this.CategoryClick?.Invoke(sender, category);
            }
            if (row != null && row is Product product)
            {
                this.ProductClick?.Invoke(sender, product);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraEditors.TableLayout.TableColumnDefinition tableColumnDefinition4 = new DevExpress.XtraEditors.TableLayout.TableColumnDefinition();
            DevExpress.XtraEditors.TableLayout.TableRowDefinition tableRowDefinition4 = new DevExpress.XtraEditors.TableLayout.TableRowDefinition();
            DevExpress.XtraGrid.Views.Tile.TileViewItemElement tileViewItemElement4 = new DevExpress.XtraGrid.Views.Tile.TileViewItemElement();
            DevExpress.XtraEditors.TableLayout.TableColumnDefinition tableColumnDefinition1 = new DevExpress.XtraEditors.TableLayout.TableColumnDefinition();
            DevExpress.XtraEditors.TableLayout.TableColumnDefinition tableColumnDefinition5 = new DevExpress.XtraEditors.TableLayout.TableColumnDefinition();
            DevExpress.XtraEditors.TableLayout.TableRowDefinition tableRowDefinition1 = new DevExpress.XtraEditors.TableLayout.TableRowDefinition();
            DevExpress.XtraEditors.TableLayout.TableRowDefinition tableRowDefinition5 = new DevExpress.XtraEditors.TableLayout.TableRowDefinition();
            DevExpress.XtraEditors.TableLayout.TableSpan tableSpan2 = new DevExpress.XtraEditors.TableLayout.TableSpan();
            DevExpress.XtraGrid.Views.Tile.TileViewItemElement tileViewItemElement1 = new DevExpress.XtraGrid.Views.Tile.TileViewItemElement();
            DevExpress.XtraGrid.Views.Tile.TileViewItemElement tileViewItemElement5 = new DevExpress.XtraGrid.Views.Tile.TileViewItemElement();
            this.colname1 = new DevExpress.XtraGrid.Columns.TileViewColumn();
            this.colName = new DevExpress.XtraGrid.Columns.TileViewColumn();
            this.colPrice = new DevExpress.XtraGrid.Columns.TileViewColumn();
            this.gridControlCategorys = new DevExpress.XtraGrid.GridControl();
            this.categoryBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.TileView_Category = new DevExpress.XtraGrid.Views.Tile.TileView();
            this.gridControlProducts = new DevExpress.XtraGrid.GridControl();
            this.productBindingSource1 = new System.Windows.Forms.BindingSource(this.components);
            this.TileView_Product = new DevExpress.XtraGrid.Views.Tile.TileView();
            this.colID = new DevExpress.XtraGrid.Columns.TileViewColumn();
            this.colCategoryId = new DevExpress.XtraGrid.Columns.TileViewColumn();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCategorys)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.categoryBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.TileView_Category)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlProducts)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.productBindingSource1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.TileView_Product)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.SuspendLayout();
            // 
            // colname1
            // 
            this.colname1.FieldName = "name";
            this.colname1.Name = "colname1";
            this.colname1.Visible = true;
            this.colname1.VisibleIndex = 0;
            // 
            // colName
            // 
            this.colName.FieldName = "Name";
            this.colName.Name = "colName";
            this.colName.Visible = true;
            this.colName.VisibleIndex = 1;
            // 
            // colPrice
            // 
            this.colPrice.FieldName = "Price";
            this.colPrice.Name = "colPrice";
            this.colPrice.Visible = true;
            this.colPrice.VisibleIndex = 2;
            // 
            // gridControlCategorys
            // 
            this.gridControlCategorys.DataSource = this.categoryBindingSource;
            this.gridControlCategorys.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlCategorys.Location = new System.Drawing.Point(0, 0);
            this.gridControlCategorys.MainView = this.TileView_Category;
            this.gridControlCategorys.Name = "gridControlCategorys";
            this.gridControlCategorys.Size = new System.Drawing.Size(378, 161);
            this.gridControlCategorys.TabIndex = 0;
            this.gridControlCategorys.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.TileView_Category});
            // 
            // categoryBindingSource
            // 
            this.categoryBindingSource.DataSource = typeof(CustomControls.Models.Category);
            // 
            // TileView_Category
            // 
            this.TileView_Category.Appearance.ItemNormal.Options.UseBackColor = true;
            this.TileView_Category.Appearance.ItemPressed.BackColor = System.Drawing.Color.Maroon;
            this.TileView_Category.Appearance.ItemPressed.Options.UseBackColor = true;
            this.TileView_Category.Appearance.ItemSelected.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.TileView_Category.Appearance.ItemSelected.Options.UseBackColor = true;
            this.TileView_Category.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colname1});
            this.TileView_Category.GridControl = this.gridControlCategorys;
            this.TileView_Category.Name = "TileView_Category";
            this.TileView_Category.OptionsBehavior.AllowSmoothScrolling = true;
            this.TileView_Category.OptionsTiles.ScrollMode = DevExpress.XtraEditors.TileControlScrollMode.TouchScrollBar;
            this.TileView_Category.TileColumns.Add(tableColumnDefinition4);
            this.TileView_Category.TileRows.Add(tableRowDefinition4);
            tileViewItemElement4.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileViewItemElement4.Appearance.Normal.Options.UseFont = true;
            tileViewItemElement4.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 15.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileViewItemElement4.Appearance.Pressed.Options.UseFont = true;
            tileViewItemElement4.Column = this.colname1;
            tileViewItemElement4.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileViewItemElement4.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomInside;
            tileViewItemElement4.Text = "colname1";
            tileViewItemElement4.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            this.TileView_Category.TileTemplate.Add(tileViewItemElement4);
            // 
            // gridControlProducts
            // 
            this.gridControlProducts.DataSource = this.productBindingSource1;
            this.gridControlProducts.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlProducts.Location = new System.Drawing.Point(0, 0);
            this.gridControlProducts.MainView = this.TileView_Product;
            this.gridControlProducts.Name = "gridControlProducts";
            this.gridControlProducts.Size = new System.Drawing.Size(378, 228);
            this.gridControlProducts.TabIndex = 1;
            this.gridControlProducts.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.TileView_Product});
            // 
            // productBindingSource1
            // 
            this.productBindingSource1.DataSource = typeof(CustomControls.Models.Product);
            // 
            // TileView_Product
            // 
            this.TileView_Product.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colID,
            this.colName,
            this.colPrice,
            this.colCategoryId});
            this.TileView_Product.GridControl = this.gridControlProducts;
            this.TileView_Product.Name = "TileView_Product";
            this.TileView_Product.OptionsBehavior.AllowSmoothScrolling = true;
            this.TileView_Product.OptionsTiles.ScrollMode = DevExpress.XtraEditors.TileControlScrollMode.TouchScrollBar;
            this.TileView_Product.TileColumns.Add(tableColumnDefinition1);
            this.TileView_Product.TileColumns.Add(tableColumnDefinition5);
            tableRowDefinition1.Length.Value = 46D;
            tableRowDefinition5.Length.Value = 35D;
            this.TileView_Product.TileRows.Add(tableRowDefinition1);
            this.TileView_Product.TileRows.Add(tableRowDefinition5);
            tableSpan2.ColumnSpan = 2;
            this.TileView_Product.TileSpans.Add(tableSpan2);
            tileViewItemElement1.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 11.25F, System.Drawing.FontStyle.Bold);
            tileViewItemElement1.Appearance.Hovered.Options.UseFont = true;
            tileViewItemElement1.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 11.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileViewItemElement1.Appearance.Normal.Options.UseFont = true;
            tileViewItemElement1.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 11.25F, System.Drawing.FontStyle.Bold);
            tileViewItemElement1.Appearance.Pressed.Options.UseFont = true;
            tileViewItemElement1.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 11.25F, System.Drawing.FontStyle.Bold);
            tileViewItemElement1.Appearance.Selected.Options.UseFont = true;
            tileViewItemElement1.Column = this.colName;
            tileViewItemElement1.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileViewItemElement1.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomInside;
            tileViewItemElement1.Text = "colName";
            tileViewItemElement1.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileViewItemElement5.Column = this.colPrice;
            tileViewItemElement5.ColumnIndex = 1;
            tileViewItemElement5.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileViewItemElement5.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomInside;
            tileViewItemElement5.RowIndex = 1;
            tileViewItemElement5.Text = "colPrice";
            tileViewItemElement5.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            this.TileView_Product.TileTemplate.Add(tileViewItemElement1);
            this.TileView_Product.TileTemplate.Add(tileViewItemElement5);
            // 
            // colID
            // 
            this.colID.FieldName = "ID";
            this.colID.Name = "colID";
            this.colID.Visible = true;
            this.colID.VisibleIndex = 0;
            // 
            // colCategoryId
            // 
            this.colCategoryId.FieldName = "CategoryId";
            this.colCategoryId.Name = "colCategoryId";
            this.colCategoryId.Visible = true;
            this.colCategoryId.VisibleIndex = 3;
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.gridControlCategorys);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.gridControlProducts);
            this.splitContainer1.Size = new System.Drawing.Size(378, 404);
            this.splitContainer1.SplitterDistance = 161;
            this.splitContainer1.SplitterWidth = 15;
            this.splitContainer1.TabIndex = 2;
            // 
            // TwoLevelTileView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.splitContainer1);
            this.Name = "TwoLevelTileView";
            this.Size = new System.Drawing.Size(370, 404);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCategorys)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.categoryBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.TileView_Category)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlProducts)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.productBindingSource1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.TileView_Product)).EndInit();
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).EndInit();
            this.splitContainer1.ResumeLayout(false);
            this.ResumeLayout(false);

        }
    }
}
