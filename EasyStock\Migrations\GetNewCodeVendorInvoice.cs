﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x020004BB RID: 1211
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class GetNewCodeVendorInvoice : DbMigration, IMigrationMetadata
	{
		// Token: 0x060023FB RID: 9211 RVA: 0x000129A0 File Offset: 0x00010BA0
		public override void Up()
		{
			base.AlterColumn("dbo.VendorInvoices", "Code", (ColumnBuilder c) => c.String(new bool?(false), null, null, null, null, null, null, null, null), null);
		}

		// Token: 0x060023FC RID: 9212 RVA: 0x000129D4 File Offset: 0x00010BD4
		public override void Down()
		{
			base.AlterColumn("dbo.VendorInvoices", "Code", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
		}

		// Token: 0x17000BCA RID: 3018
		// (get) Token: 0x060023FD RID: 9213 RVA: 0x001F56D0 File Offset: 0x001F38D0
		string IMigrationMetadata.Id
		{
			get
			{
				return "202108191139234_GetNewCodeVendorInvoice";
			}
		}

		// Token: 0x17000BCB RID: 3019
		// (get) Token: 0x060023FE RID: 9214 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000BCC RID: 3020
		// (get) Token: 0x060023FF RID: 9215 RVA: 0x001F56E8 File Offset: 0x001F38E8
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B67 RID: 11111
		private readonly ResourceManager Resources = new ResourceManager(typeof(GetNewCodeVendorInvoice));
	}
}
