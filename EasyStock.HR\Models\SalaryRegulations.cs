namespace EasyStock.HR.Models
{
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;

    public partial class SalaryRegulations
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public SalaryRegulations()
        {
            SalaryRegulationExtensions = new HashSet<SalaryRegulationExtensions>();
        }

        public int ID { get; set; }

        [Required]
        [StringLength(150)]
        public string Name { get; set; }

        public int? CostCenter { get; set; }

        public int ExpensesAccount { get; set; }

        public int BenefitsAccount { get; set; }

        public double DayValue { get; set; }

        public double HourValue { get; set; }

        public int SalaryPeriod { get; set; }

        public int SalaryCalculation { get; set; }

        public double DefaultSalary { get; set; }

        public string Equation { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<SalaryRegulationExtensions> SalaryRegulationExtensions { get; set; }
    }
}
