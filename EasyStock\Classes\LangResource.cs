﻿namespace EasyStock.Classes
{
    public static class LangResource
    {
        public static string StatmentType => "Type de Déclaration";
        public static string Statment => "Déclaration";
        public static string ErrorCantBeEmpry => "Ce champ est requis";
        public static string MaxCredit => "Crédit maximum";
        public static string Quantity => "Quantité";
        public static string Price => "Prix";
        public static string TaxValue => "Valeur de la taxe";
        public static string DiscountValue => "Valeur de la remise";
        public static string Code => "Code";
        public static string BarCode => "Code-barres";
        public static string CashTransfer => "Transfert d'argent";
        public static string om => "De";
        public static string To => "À";
        public static string Number => "Numéro";
        public static string AskForSaving => "Il y a des modifications non enregistrées \n Voulez-vous enregistrer d'abord ?";
        public static string ConmDelete => "Voulez-vous supprimer ?";
        public static string CantDeleteDrawerHasTransactions => "Impossible de supprimer le tiroir, il y a des transactions financières liées dans le système";
        public static string CantDeleteBankUsedByPayCards => "Impossible de supprimer la banque, il y a des transactions financières liées dans le système";
        public static string All => "Tout";
        public static string Account => "Compte";
        public static string Vendor => "Fournisseur";
        public static string Customer => "Client";
        public static string CashNoteIn => "Reçu de paiement en espèces";
        public static string CashNoteOut => "Chèque de paiement en espèces";
        public static string ErrorValMustBeGreaterThan0 => "La valeur doit être supérieure à zéro";
        public static string Discount => "Remise";
        public static string CashPay => "Paiement en espèces";
        public static string BankTransfer => "Virement bancaire";
        public static string PayCards => "Carte de paiement";
        public static string OnAccount => "Sur compte";
        public static string DocumentNotFound => "Le document est introuvable !";
        public static string Debit => "Débiteur";
        public static string Credit => "Créditeur";
        public static string ErorrThisNameIsUsedBefore => "Ce nom est déjà utilisé";
        public static string Income => "Revenu";
        public static string OutCome => "Dépense";
        public static string Amount => "Montant";
        public static string RevenuEntry => "Enregistrement des revenus";
        public static string ExpenceEntry => "Enregistrement des dépenses";
        public static string AddRevenueAccount => "Ajouter un compte de revenus";
        public static string AddExpenceAccount => "Ajouter un compte de dépenses";
        public static string Finish => "Terminer";
        public static string ErrorMustInsertOneItemAtLeast => "Vous devez saisir au moins un élément";
        public static string Revenue => "Revenus";
        public static string Expense => "Dépenses";
        public static string Notes => "Notes";
        public static string FirstInFirstOut => "Premier entré, premier sorti";
        public static string LastInFirstOut => "Dernier entré, premier sorti";
        public static string WeightedAverage => "Moyenne pondérée";
        public static string CantDeleteStoreIsUsedInTheSystem => "Impossible de supprimer le stock, il est utilisé dans le système";
        public static string Sales => "Ventes";
        public static string SalesReturn => "Retour des ventes";
        public static string SalesDiscount => "Remise autorisée";
        public static string Purchases => "Achats";
        public static string PurchasesReturn => "Retour des achats";
        public static string PurchaseDiscount => "Remise obtenue";
        public static string CloseInventory => "Fin de période";
        public static string OpenInventory => "Début de période";
        public static string CostOfSoldGoodsAccount => "Coût des marchandises vendues";
        public static string TheInventory => "Inventaire";
        public static string CantDeletevendorIsUsedInTheSystem => "Impossible de supprimer le fournisseur, des transactions sont liées dans le système";
        public static string CantDeletevendorIsParentOfanotherVendor => "Impossible de supprimer le fournisseur, il est lié à un autre fournisseur";
        public static string AnoymasCustomer => "Client général";
        public static string AnoymasVendor => "Fournisseur général";
        public static string MainDrawer => "Tiroir principal";
        public static string Assets => "Actifs";
        public static string FixedAssets => "Actifs immobilisés";
        public static string CurrentAssets => "Actifs circulants";
        public static string Drawer => "Caisses";
        public static string Banks => "Banques";
        public static string Customers => "Clients";
        public static string NotesReceivable => "Effets à recevoir";
        public static string EmployeesDue => "Créances sur employés";
        public static string FinancialCustodies => "Cautions financières";
        public static string PermanentCustodies => "Cautions permanentes";
        public static string TemporaryCustdodies => "Cautions temporaires";
        public static string OtherAssets => "Autres actifs";
        public static string Liabilites_Owners_Equity => "Passifs et capitaux propres";
        public static string Creditor => "Créditeurs";
        public static string Vendors => "Fournisseurs";
        public static string Loans => "Emprunts";
        public static string Owner_Equity => "Capitaux propres";
        public static string Capital => "Capital";
        public static string CurrentAccount => "Compte courant";
        public static string DueSalerys => "Salaires dus";
        public static string DepreciationComplex => "Amortissement";
        public static string Expenses => "Dépenses";
        public static string EmployeeSalaries => "Salaires et charges sociales";
        public static string SalesTax => "Taxe sur les ventes";
        public static string GeneralExpenses => "Dépenses générales";
        public static string GeneralTaxes => "Taxes générales";
        public static string Revenues => "Revenus";
        public static string GeneralRevenues => "Revenus généraux";
        public static string Merchandising => "Commerce";
        public static string NotesPayableAccount => "Effets à payer";
        public static string ManufacturingExpAccount => "Dépenses de production";
        public static string SalesDeductTaxAccount => "Remise en espèces autorisée";
        public static string PurchaseAddTaxAccount => "Remise obtenue";
        public static string SalesAddTaxAccount => "Taxe sur les ventes";
        public static string PurchaseDeductTaxAccount => "Taxe sur les achats";
        public static string DepreciationAccount => "Amortissement";
        public static string RecieveNotesUnderCollectAccount => "Effets sous recouvrement";
        public static string ThisAccountDebitWith => "Ce compte est débiteur de";
        public static string ThisAccountCreditWith => "Ce compte est créditeur de";
        public static string Balance => "Solde";
        public static string CantDeleteNoPermission => "Pas de droits pour supprimer";
        public static string CantDeleteAccountHasChilds => "Impossible de supprimer le compte, des sous-comptes sont liés";
        public static string CantDeleteAccountHasAcctiveties => "Impossible de supprimer le compte, des écritures sont liées";
        public static string CashNoteIns => "Reçus de paiement";
        public static string CashNoteOuts => "Chèques de paiement";
        public static string CantDeletecustomerIsUsedInTheSystem => "Impossible de supprimer le client, son compte est utilisé dans le système";
        public static string CantDeletecustomerIsParentOfanotherCustomer => "Impossible de supprimer le client, son compte est lié à un autre client";
        public static string CantDeleteGroupHasItems => "Impossible de supprimer le groupe, il y a des éléments dedans";
        public static string CantDeleteGroupHasChild => "Impossible de supprimer le groupe, il y a des éléments dedans";
        public static string ID => "Numéro";
        public static string Name => "Nom";
        public static string Phone => "Téléphone";
        public static string City => "Ville";
        public static string Address => "Adresse";
        public static string ParentStoreID => "Dépôt principal";
        public static string Group => "Groupe";
        public static string TaxFileNumber => "Numéro de fichier fiscal";
        public static string Employee => "Employé";
        public static string Pay => "Paiement";
        public static string Commission => "Commission";
        public static string MarketingExpenses => "Dépenses marketing";
        public static string CommissionSalesman => "Commission des vendeurs";
        public static string Email => "Email";
        public static string Mobile => "Mobile";
        public static string SalesInvoice => "Facture de vente";
        public static string PurchaseInvoice => "Facture d'achat";
        public static string AddedTax => "Taxe ajoutée";
        public static string CostOfSoldGoods => "Coût des marchandises vendues";

        // Numbers
        public static string Zero => "Zero";
        public static string One => "Un";
        public static string Two => "Deux";
        public static string Three => "Trois";
        public static string Four => "Quatre";
        public static string Five => "Cinq";
        public static string Six => "Six";
        public static string Seven => "Sept";
        public static string Eight => "Huit";
        public static string Nine => "Neuf";
        public static string Ten => "Dix";
        public static string Eleven => "Onze";
        public static string Twelve => "Douze";
        public static string Thirteen => "Treize";
        public static string Fourteen => "Quatorze";
        public static string Fifteen => "Quinze";
        public static string Sixteen => "Seize";
        public static string Seventeen => "Dix-sept";
        public static string Eighteen => "Dix-huit";
        public static string Nineteen => "Dix-neuf";
        public static string Twenty => "Vingt";
        public static string Thirty => "Trente";
        public static string Forty => "Quarante";
        public static string Fifty => "Cinquante";
        public static string Sixty => "Soixante";
        public static string Seventy => "Soixante-dix";
        public static string Eighty => "Quatre-vingt";
        public static string Ninety => "Quatre-vingt-dix";
        public static string and => "et";
        public static string Thousand => "Mille";
        public static string Million => "Million";
        public static string Billion => "Milliard";
        public static string Trillion => "Billion";
        public static string Quadrillion => "Quadrillion";
        public static string Minus => "Moins";
        public static string Hundred => "Cent";

    }
}
