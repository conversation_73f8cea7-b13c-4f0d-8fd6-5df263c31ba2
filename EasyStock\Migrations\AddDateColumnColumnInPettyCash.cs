﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x020004B2 RID: 1202
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class AddDateColumnColumnInPettyCash : DbMigration, IMigrationMetadata
	{
		// Token: 0x060023CF RID: 9167 RVA: 0x000127C7 File Offset: 0x000109C7
		public override void Up()
		{
			base.AddColumn("dbo.PettyCashes", "Date", delegate(ColumnBuilder c)
			{
				bool? nullable = new bool?(false);
				DateTime? defaultValue = new DateTime?(new DateTime(1999, 1, 1));
				return c.DateTime(nullable, null, defaultValue, null, null, null, null);
			}, null);
		}

		// Token: 0x060023D0 RID: 9168 RVA: 0x000127FB File Offset: 0x000109FB
		public override void Down()
		{
			base.DropColumn("dbo.PettyCashes", "Date", null);
		}

		// Token: 0x17000BBB RID: 3003
		// (get) Token: 0x060023D1 RID: 9169 RVA: 0x001F4C0C File Offset: 0x001F2E0C
		string IMigrationMetadata.Id
		{
			get
			{
				return "202108151822132_AddDateColumnColumnInPettyCash";
			}
		}

		// Token: 0x17000BBC RID: 3004
		// (get) Token: 0x060023D2 RID: 9170 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000BBD RID: 3005
		// (get) Token: 0x060023D3 RID: 9171 RVA: 0x001F4C24 File Offset: 0x001F2E24
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B58 RID: 11096
		private readonly ResourceManager Resources = new ResourceManager(typeof(AddDateColumnColumnInPettyCash));
	}
}
