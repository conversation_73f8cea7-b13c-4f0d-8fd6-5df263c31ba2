﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Machines de paiement")]
    [Table("PayCards")]
    public class PayCard : BillingDetail
    {
        public override string DisplayName
        {
            get
            {
                string format = "{0} - {1} : {2}";
                object name = this.Name;
                object number = this.Number;
                Bank bank = this.Bank;
                return string.Format(format, name, number, (bank != null) ? bank.DisplayName : null);
            }
        }

        [Display(Name = "Banque affiliée")]
        public Bank Bank { get; set; }

        [Display(Name = "Code de la banque affiliée")]
        public int BankID { get; set; }

        [Display(Name = "Nom de la machine")]
        public override string Name
        {
            get
            {
                return base.Name;
            }
            set
            {
                base.Name = value;
            }
        }

        [Required]
        [StringLength(50)]
        [Display(Name = "Numéro de la machine")]
        public string Number { get; set; }

        [Display(Name = "Commission de la banque")]
        [Range(0.0, 0.99, ErrorMessage = "La commission doit être comprise entre 0 et 99 %")]
        public double CommissionRate { get; set; }

        [Display(Name = "Compte de commission de la banque")]
        public Account CommissionAccount { get; set; }

        [Display(Name = "Code du compte de commission de la banque")]
        public int CommissionAccountID { get; set; }

        public override Account Account
        {
            get
            {
                Bank bank = this.Bank;
                return (bank != null) ? bank.Account : null;
            }
            set
            {
            }
        }

        public override int AccountID
        {
            get
            {
                Bank bank = this.Bank;
                return (bank != null) ? bank.AccountID : 0;
            }
            set
            {
            }
        }
    }
}
