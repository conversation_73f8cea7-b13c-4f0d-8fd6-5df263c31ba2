﻿using DevExpress.XtraCharts;
using EasyStock.Charts.Models;
using System.Drawing;
using System.Threading.Tasks;

namespace EasyStock.Charts
{

    public abstract class BaseChart : ChartControl, IWidget
    {
        public abstract string Caption { get; }

        public abstract Color Color { get; }

        public BaseChart()
        {
            base.Height = 250;
            ReloadDataAsync();
        }

        public void ReloadDataAsync()
        {
            Task.Run(delegate
            {
                object result = QueryData().Result;
                DataSource = result;
            });
        }

        public abstract Task<object> QueryData();


    }
}
