﻿namespace EasyStock.HR.Views
{
	public partial class EmpVacationListView : global::EasyStock.HR.MainViews.MasterView
	{
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.empVacationBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colEmpID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repoemp = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.employeeBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.colVacationType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colFromDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colToDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDeductionType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDuration = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colNotes = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.empVacationBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoemp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.employeeBindingSource)).BeginInit();
            this.SuspendLayout();
            this.gridControl1.DataSource = this.empVacationBindingSource;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 26);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repoemp});
            this.gridControl1.Size = new System.Drawing.Size(800, 400);
            this.gridControl1.TabIndex = 4;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            this.empVacationBindingSource.DataSource = typeof(EasyStock.HR.Models.EmpVacation);
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colID,
            this.colEmpID,
            this.colVacationType,
            this.colFromDate,
            this.colToDate,
            this.colDeductionType,
            this.colDuration,
            this.colNotes});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.colID.FieldName = "ID";
            this.colID.Name = "colID";
            this.colID.OptionsColumn.ReadOnly = true;
            this.colID.Visible = true;
            this.colID.VisibleIndex = 0;
            this.colEmpID.ColumnEdit = this.repoemp;
            this.colEmpID.FieldName = "EmpID";
            this.colEmpID.Name = "colEmpID";
            this.colEmpID.Visible = true;
            this.colEmpID.VisibleIndex = 1;
            this.repoemp.AutoHeight = false;
            this.repoemp.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repoemp.DataSource = this.employeeBindingSource;
            this.repoemp.DisplayMember = "Name";
            this.repoemp.Name = "repoemp";
            this.repoemp.ValueMember = "ID";
            this.employeeBindingSource.DataSource = typeof(EasyStock.HR.Models.Employee);
            this.colVacationType.FieldName = "VacationType";
            this.colVacationType.Name = "colVacationType";
            this.colVacationType.Visible = true;
            this.colVacationType.VisibleIndex = 2;
            this.colFromDate.FieldName = "FromDate";
            this.colFromDate.Name = "colFromDate";
            this.colFromDate.Visible = true;
            this.colFromDate.VisibleIndex = 3;
            this.colToDate.FieldName = "ToDate";
            this.colToDate.Name = "colToDate";
            this.colToDate.Visible = true;
            this.colToDate.VisibleIndex = 4;
            this.colDeductionType.FieldName = "DeductionType";
            this.colDeductionType.Name = "colDeductionType";
            this.colDeductionType.Visible = true;
            this.colDeductionType.VisibleIndex = 5;
            this.colDuration.FieldName = "Duration";
            this.colDuration.Name = "colDuration";
            this.colDuration.Visible = true;
            this.colDuration.VisibleIndex = 6;
            this.colNotes.FieldName = "Notes";
            this.colNotes.Name = "colNotes";
            this.colNotes.Visible = true;
            this.colNotes.VisibleIndex = 7;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.gridControl1);
            this.Name = "EmpVacationListView";
            this.Text = "Congés";
            this.Controls.SetChildIndex(this.gridControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.empVacationBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoemp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.employeeBindingSource)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		private global::System.ComponentModel.IContainer components = null;

		private global::DevExpress.XtraGrid.GridControl gridControl1;

		private global::System.Windows.Forms.BindingSource empVacationBindingSource;

		private global::DevExpress.XtraGrid.Views.Grid.GridView gridView1;

		private global::DevExpress.XtraGrid.Columns.GridColumn colID;

		private global::DevExpress.XtraGrid.Columns.GridColumn colEmpID;

		private global::DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repoemp;

		private global::System.Windows.Forms.BindingSource employeeBindingSource;

		private global::DevExpress.XtraGrid.Columns.GridColumn colVacationType;

		private global::DevExpress.XtraGrid.Columns.GridColumn colFromDate;

		private global::DevExpress.XtraGrid.Columns.GridColumn colToDate;

		private global::DevExpress.XtraGrid.Columns.GridColumn colDeductionType;

		private global::DevExpress.XtraGrid.Columns.GridColumn colDuration;

		private global::DevExpress.XtraGrid.Columns.GridColumn colNotes;
	}
}
