﻿using DevExpress.XtraEditors;
using EasyStock.Common;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class TimeTableView : MasterView
    {
        public static TimeTableView Instance
        {
            get
            {
                bool flag = TimeTableView.instance == null || TimeTableView.instance.IsDisposed;
                if (flag)
                {
                    TimeTableView.instance = new TimeTableView();
                }
                return TimeTableView.instance;
            }
        }

        public TimeTable table
        {
            get
            {
                return this.timeTableBindingSource.Current as TimeTable;
            }
            set
            {
                this.timeTableBindingSource.DataSource = value;
            }
        }

        public TimeTableView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            this.context = new HRDataContext();
            this.gridView1.AddEditButton(new EventHandler(this.RepositoryItemButtonEdit1_Click));
            this.gridView1.SetAlternatingColors();
            base.Shown += this.TimeTableView_Shown;
        }

        private void TimeTableView_Shown(object sender, EventArgs e)
        {
            bool flag = this.table == null;
            if (flag)
            {
                this.New();
            }
        }

        private void RepositoryItemButtonEdit1_Click(object sender, EventArgs e)
        {
            TimeTable row = this.gridView1.GetRow(this.gridView1.FocusedRowHandle) as TimeTable;
            bool flag = row != null;
            if (flag)
            {
                this.GoTo(row.ID);
            }
        }

        public void GoTo(int id)
        {
            TimeTable sourceDepr = this.context.TimeTables.SingleOrDefault((TimeTable x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.table = sourceDepr;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable");
            }
        }

        public override void New()
        {
            this.table = new TimeTable();
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.context.TimeTables.AddOrUpdate(new TimeTable[]
                {
                    this.table
                });
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.gridControl1.DataSource = this.context.TimeTables.ToList<TimeTable>();
            base.RefreshData();
        }

        public override void Delete()
        {
        }

        private static TimeTableView instance;

        private HRDataContext context;

        private void LeaveTimeDateEdit_EditValueChanged(object sender, EventArgs e)
        {

        }
    }
}
