﻿namespace EasyStock.HR.Views
{
	public partial class EmpVacationView : global::EasyStock.HR.MainViews.MasterView
	{
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.tabbedControlGroup1 = new DevExpress.XtraLayout.TabbedControlGroup();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup3 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.empVacationBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.VacationTypeImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.FromDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.ToDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.DeductionTypeImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.DurationTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.EmpIDTextEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.NotesTextEdit = new DevExpress.XtraEditors.MemoEdit();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForEmpID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForVacationType = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForFromDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForToDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDeductionType = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDuration = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForNotes = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabbedControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.empVacationBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.VacationTypeImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.FromDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.FromDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ToDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ToDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DeductionTypeImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DurationTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmpIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NotesTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmpID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForVacationType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFromDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForToDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDeductionType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDuration)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNotes)).BeginInit();
            this.SuspendLayout();
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.VacationTypeImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.FromDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.ToDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.DeductionTypeImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.DurationTextEdit);
            this.dataLayoutControl1.Controls.Add(this.EmpIDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NotesTextEdit);
            this.dataLayoutControl1.DataSource = this.empVacationBindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.HiddenItems.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.tabbedControlGroup1});
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 26);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(800, 400);
            this.dataLayoutControl1.TabIndex = 4;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            this.tabbedControlGroup1.Location = new System.Drawing.Point(0, 237);
            this.tabbedControlGroup1.Name = "tabbedControlGroup1";
            this.tabbedControlGroup1.SelectedTabPage = this.layoutControlGroup2;
            this.tabbedControlGroup1.Size = new System.Drawing.Size(780, 147);
            this.tabbedControlGroup1.TabPages.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup2});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(756, 100);
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(800, 400);
            this.Root.TextVisible = false;
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup3,
            this.emptySpaceItem1,
            this.emptySpaceItem2});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(780, 380);
            this.layoutControlGroup3.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForID,
            this.ItemForEmpID,
            this.ItemForVacationType,
            this.ItemForFromDate,
            this.ItemForToDate,
            this.ItemForDeductionType,
            this.ItemForDuration,
            this.ItemForNotes});
            this.layoutControlGroup3.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup3.Name = "layoutControlGroup3";
            this.layoutControlGroup3.Size = new System.Drawing.Size(427, 276);
            this.layoutControlGroup3.Text = "Informations";
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(427, 0);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(353, 380);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 276);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(427, 104);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empVacationBindingSource, "ID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.IDTextEdit.Location = new System.Drawing.Point(138, 44);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.IDTextEdit.Properties.MaskSettings.Set("mask", "N0");
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(285, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 4;
            this.empVacationBindingSource.DataSource = typeof(EasyStock.HR.Models.EmpVacation);
            this.VacationTypeImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empVacationBindingSource, "VacationType", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.VacationTypeImageComboBoxEdit.Location = new System.Drawing.Point(138, 92);
            this.VacationTypeImageComboBoxEdit.Name = "VacationTypeImageComboBoxEdit";
            this.VacationTypeImageComboBoxEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.VacationTypeImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.VacationTypeImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.VacationTypeImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.VacationTypeImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Urgente", EasyStock.HR.EVacationType.Casual, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Ordinaire", EasyStock.HR.EVacationType.Normal, 1)});
            this.VacationTypeImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.VacationTypeImageComboBoxEdit.Size = new System.Drawing.Size(285, 20);
            this.VacationTypeImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.VacationTypeImageComboBoxEdit.TabIndex = 6;
            this.FromDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empVacationBindingSource, "FromDate", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.FromDateDateEdit.EditValue = null;
            this.FromDateDateEdit.Location = new System.Drawing.Point(138, 116);
            this.FromDateDateEdit.Name = "FromDateDateEdit";
            this.FromDateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.FromDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.FromDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.FromDateDateEdit.Size = new System.Drawing.Size(285, 20);
            this.FromDateDateEdit.StyleController = this.dataLayoutControl1;
            this.FromDateDateEdit.TabIndex = 7;
            this.ToDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empVacationBindingSource, "ToDate", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ToDateDateEdit.EditValue = null;
            this.ToDateDateEdit.Location = new System.Drawing.Point(138, 140);
            this.ToDateDateEdit.Name = "ToDateDateEdit";
            this.ToDateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.ToDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ToDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ToDateDateEdit.Size = new System.Drawing.Size(285, 20);
            this.ToDateDateEdit.StyleController = this.dataLayoutControl1;
            this.ToDateDateEdit.TabIndex = 8;
            this.DeductionTypeImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empVacationBindingSource, "DeductionType", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DeductionTypeImageComboBoxEdit.Location = new System.Drawing.Point(138, 164);
            this.DeductionTypeImageComboBoxEdit.Name = "DeductionTypeImageComboBoxEdit";
            this.DeductionTypeImageComboBoxEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.DeductionTypeImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DeductionTypeImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DeductionTypeImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DeductionTypeImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Jour complet", EasyStock.HR.EDeductionType.FullDay, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Demi-journée", EasyStock.HR.EDeductionType.HalfDay, 1)});
            this.DeductionTypeImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.DeductionTypeImageComboBoxEdit.Size = new System.Drawing.Size(285, 20);  
            this.DeductionTypeImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.DeductionTypeImageComboBoxEdit.TabIndex = 9;
            this.DurationTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empVacationBindingSource, "Duration", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DurationTextEdit.Location = new System.Drawing.Point(138, 188);
            this.DurationTextEdit.Name = "DurationTextEdit";
            this.DurationTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DurationTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DurationTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.DurationTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.DurationTextEdit.Properties.MaskSettings.Set("mask", "N0");
            this.DurationTextEdit.Size = new System.Drawing.Size(285, 20);
            this.DurationTextEdit.StyleController = this.dataLayoutControl1;
            this.DurationTextEdit.TabIndex = 10;
            this.EmpIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empVacationBindingSource, "EmpID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.EmpIDTextEdit.Location = new System.Drawing.Point(138, 68);
            this.EmpIDTextEdit.Name = "EmpIDTextEdit";
            this.EmpIDTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.EmpIDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.EmpIDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.EmpIDTextEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.EmpIDTextEdit.Properties.NullText = "";
            this.EmpIDTextEdit.Size = new System.Drawing.Size(285, 20);
            this.EmpIDTextEdit.StyleController = this.dataLayoutControl1;
            this.EmpIDTextEdit.TabIndex = 5;
            this.NotesTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.empVacationBindingSource, "Notes", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NotesTextEdit.Location = new System.Drawing.Point(138, 212);
            this.NotesTextEdit.Name = "NotesTextEdit";
            this.NotesTextEdit.Size = new System.Drawing.Size(285, 60);
            this.NotesTextEdit.StyleController = this.dataLayoutControl1;
            this.NotesTextEdit.TabIndex = 11;
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(403, 24);
            this.ItemForID.TextSize = new System.Drawing.Size(110, 13);
            this.ItemForEmpID.Control = this.EmpIDTextEdit;
            this.ItemForEmpID.Location = new System.Drawing.Point(0, 24);
            this.ItemForEmpID.Name = "ItemForEmpID";
            this.ItemForEmpID.Size = new System.Drawing.Size(403, 24);
            this.ItemForEmpID.TextSize = new System.Drawing.Size(110, 13);
            this.ItemForVacationType.Control = this.VacationTypeImageComboBoxEdit;
            this.ItemForVacationType.Location = new System.Drawing.Point(0, 48);
            this.ItemForVacationType.Name = "ItemForVacationType";
            this.ItemForVacationType.Size = new System.Drawing.Size(403, 24);
            this.ItemForVacationType.TextSize = new System.Drawing.Size(110, 13);
            this.ItemForFromDate.Control = this.FromDateDateEdit;
            this.ItemForFromDate.Location = new System.Drawing.Point(0, 72);
            this.ItemForFromDate.Name = "ItemForFromDate";
            this.ItemForFromDate.Size = new System.Drawing.Size(403, 24);
            this.ItemForFromDate.TextSize = new System.Drawing.Size(110, 13);
            this.ItemForToDate.Control = this.ToDateDateEdit;
            this.ItemForToDate.Location = new System.Drawing.Point(0, 96);
            this.ItemForToDate.Name = "ItemForToDate";
            this.ItemForToDate.Size = new System.Drawing.Size(403, 24);
            this.ItemForToDate.TextSize = new System.Drawing.Size(110, 13);
            this.ItemForDeductionType.Control = this.DeductionTypeImageComboBoxEdit;
            this.ItemForDeductionType.Location = new System.Drawing.Point(0, 120);
            this.ItemForDeductionType.Name = "ItemForDeductionType";
            this.ItemForDeductionType.Size = new System.Drawing.Size(403, 24);
            this.ItemForDeductionType.TextSize = new System.Drawing.Size(110, 13);
            this.ItemForDuration.Control = this.DurationTextEdit;
            this.ItemForDuration.Location = new System.Drawing.Point(0, 144);
            this.ItemForDuration.Name = "ItemForDuration";
            this.ItemForDuration.Size = new System.Drawing.Size(403, 24);
            this.ItemForDuration.TextSize = new System.Drawing.Size(110, 13);
            this.ItemForNotes.Control = this.NotesTextEdit;
            this.ItemForNotes.Location = new System.Drawing.Point(0, 168);
            this.ItemForNotes.MaxSize = new System.Drawing.Size(403, 64);
            this.ItemForNotes.MinSize = new System.Drawing.Size(403, 64);
            this.ItemForNotes.Name = "ItemForNotes";
            this.ItemForNotes.Size = new System.Drawing.Size(403, 64);
            this.ItemForNotes.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForNotes.TextSize = new System.Drawing.Size(110, 13);
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.dataLayoutControl1);
            this.Name = "EmpVacationView";
            this.Text = "Enregistrer un congé";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabbedControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.empVacationBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.VacationTypeImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.FromDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.FromDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ToDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ToDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DeductionTypeImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DurationTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmpIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NotesTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmpID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForVacationType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFromDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForToDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDeductionType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDuration)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNotes)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		private global::System.ComponentModel.IContainer components = null;

		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		private global::System.Windows.Forms.BindingSource empVacationBindingSource;

		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		private global::DevExpress.XtraEditors.ImageComboBoxEdit VacationTypeImageComboBoxEdit;

		private global::DevExpress.XtraEditors.DateEdit FromDateDateEdit;

		private global::DevExpress.XtraEditors.DateEdit ToDateDateEdit;

		private global::DevExpress.XtraEditors.ImageComboBoxEdit DeductionTypeImageComboBoxEdit;

		private global::DevExpress.XtraEditors.TextEdit DurationTextEdit;

		private global::DevExpress.XtraEditors.LookUpEdit EmpIDTextEdit;

		private global::DevExpress.XtraLayout.TabbedControlGroup tabbedControlGroup1;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup3;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForEmpID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForVacationType;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForFromDate;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForToDate;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDeductionType;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDuration;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForNotes;

		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;

		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;

		private global::DevExpress.XtraEditors.MemoEdit NotesTextEdit;
	}
}
