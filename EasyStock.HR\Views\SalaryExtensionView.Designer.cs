﻿namespace EasyStock.HR.Views
{
	// Token: 0x02000045 RID: 69
	public partial class SalaryExtensionView : global::EasyStock.HR.MainViews.MasterView
	{
		// Token: 0x06000260 RID: 608 RVA: 0x0002BE08 File Offset: 0x0002A008
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000261 RID: 609 RVA: 0x0002BE40 File Offset: 0x0002A040
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.salaryExtensionBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.NameTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.CalculationTypeImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.StatusImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.TypeImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.MainAccountLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem3 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup3 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForName = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForType = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForCalculationType = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForStatus = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForMainAccount = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.salaryExtensionBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NameTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.CalculationTypeImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.StatusImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.TypeImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.MainAccountLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCalculationType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForStatus)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForMainAccount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            this.SuspendLayout();
            // 
            // dataLayoutControl1
            // 
            this.dataLayoutControl1.Controls.Add(this.gridControl1);
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NameTextEdit);
            this.dataLayoutControl1.Controls.Add(this.CalculationTypeImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.StatusImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.TypeImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.MainAccountLookUpEdit);
            this.dataLayoutControl1.DataSource = this.salaryExtensionBindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 26);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(800, 400);
            this.dataLayoutControl1.TabIndex = 0;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            // 
            // gridControl1
            // 
            this.gridControl1.DataSource = this.salaryExtensionBindingSource;
            this.gridControl1.Location = new System.Drawing.Point(401, 44);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(332, 231);
            this.gridControl1.TabIndex = 12;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // salaryExtensionBindingSource
            // 
            this.salaryExtensionBindingSource.DataSource = typeof(EasyStock.HR.Models.SalaryExtension);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colName});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // colName
            // 
            this.colName.FieldName = "Name";
            this.colName.Name = "colName";
            this.colName.Visible = true;
            this.colName.VisibleIndex = 0;
            // 
            // IDTextEdit
            // 
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryExtensionBindingSource, "ID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.IDTextEdit.Location = new System.Drawing.Point(107, 44);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.IDTextEdit.Properties.MaskSettings.Set("mask", "N0");
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(266, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 4;
            // 
            // NameTextEdit
            // 
            this.NameTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryExtensionBindingSource, "Name", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NameTextEdit.Location = new System.Drawing.Point(107, 68);
            this.NameTextEdit.Name = "NameTextEdit";
            this.NameTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.NameTextEdit.Size = new System.Drawing.Size(266, 20);
            this.NameTextEdit.StyleController = this.dataLayoutControl1;
            this.NameTextEdit.TabIndex = 5;
            // 
            // CalculationTypeImageComboBoxEdit
            // 
            this.CalculationTypeImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryExtensionBindingSource, "CalculationType", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.CalculationTypeImageComboBoxEdit.Location = new System.Drawing.Point(107, 116);
            this.CalculationTypeImageComboBoxEdit.Name = "CalculationTypeImageComboBoxEdit";
            this.CalculationTypeImageComboBoxEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.CalculationTypeImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.CalculationTypeImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.CalculationTypeImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.CalculationTypeImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Montant fixe", EasyStock.HR.ECalculationType.FixedAmount, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Équation", EasyStock.HR.ECalculationType.Equation, 1)});
            this.CalculationTypeImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.CalculationTypeImageComboBoxEdit.Size = new System.Drawing.Size(266, 20);
            this.CalculationTypeImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.CalculationTypeImageComboBoxEdit.TabIndex = 7;
            // 
            // StatusImageComboBoxEdit
            // 
            this.StatusImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryExtensionBindingSource, "Status", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.StatusImageComboBoxEdit.Location = new System.Drawing.Point(107, 140);
            this.StatusImageComboBoxEdit.Name = "StatusImageComboBoxEdit";
            this.StatusImageComboBoxEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.StatusImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.StatusImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.StatusImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.StatusImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Inactif", EasyStock.HR.EStatus.Inactive, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Actif", EasyStock.HR.EStatus.Active, 1)});
            this.StatusImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.StatusImageComboBoxEdit.Size = new System.Drawing.Size(266, 20);
            this.StatusImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.StatusImageComboBoxEdit.TabIndex = 8;
            // 
            // TypeImageComboBoxEdit
            // 
            this.TypeImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryExtensionBindingSource, "Type", true));
            this.TypeImageComboBoxEdit.Location = new System.Drawing.Point(107, 92);
            this.TypeImageComboBoxEdit.Name = "TypeImageComboBoxEdit";
            this.TypeImageComboBoxEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.TypeImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.TypeImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.TypeImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.TypeImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Avantage", EasyStock.HR.ExtensionType.Benefit, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Déduction", EasyStock.HR.ExtensionType.Deduction, 1)});
            this.TypeImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.TypeImageComboBoxEdit.Size = new System.Drawing.Size(266, 20);
            this.TypeImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.TypeImageComboBoxEdit.TabIndex = 10;
            // 
            // MainAccountLookUpEdit
            // 
            this.MainAccountLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryExtensionBindingSource, "MainAccount", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.MainAccountLookUpEdit.Location = new System.Drawing.Point(107, 164);
            this.MainAccountLookUpEdit.Name = "MainAccountLookUpEdit";
            this.MainAccountLookUpEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.MainAccountLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.MainAccountLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.MainAccountLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.MainAccountLookUpEdit.Properties.NullText = "";
            this.MainAccountLookUpEdit.Size = new System.Drawing.Size(266, 20);
            this.MainAccountLookUpEdit.StyleController = this.dataLayoutControl1;
            this.MainAccountLookUpEdit.TabIndex = 11;
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(800, 400);
            this.Root.TextVisible = false;
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.emptySpaceItem2,
            this.emptySpaceItem3,
            this.layoutControlGroup3,
            this.emptySpaceItem1,
            this.layoutControlGroup2});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(780, 380);
            // 
            // emptySpaceItem2
            // 
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(0, 188);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(377, 91);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem3
            // 
            this.emptySpaceItem3.AllowHotTrack = false;
            this.emptySpaceItem3.Location = new System.Drawing.Point(0, 279);
            this.emptySpaceItem3.Name = "emptySpaceItem3";
            this.emptySpaceItem3.Size = new System.Drawing.Size(780, 101);
            this.emptySpaceItem3.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlGroup3
            // 
            this.layoutControlGroup3.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForID,
            this.ItemForName,
            this.ItemForType,
            this.ItemForCalculationType,
            this.ItemForStatus,
            this.ItemForMainAccount});
            this.layoutControlGroup3.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup3.Name = "layoutControlGroup3";
            this.layoutControlGroup3.Size = new System.Drawing.Size(377, 188);
            this.layoutControlGroup3.Text = "Informations";
            // 
            // ItemForID
            // 
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.MaxSize = new System.Drawing.Size(353, 24);
            this.ItemForID.MinSize = new System.Drawing.Size(353, 24);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(353, 24);
            this.ItemForID.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForID.TextSize = new System.Drawing.Size(79, 13);
            // 
            // ItemForName
            // 
            this.ItemForName.Control = this.NameTextEdit;
            this.ItemForName.Location = new System.Drawing.Point(0, 24);
            this.ItemForName.Name = "ItemForName";
            this.ItemForName.Size = new System.Drawing.Size(353, 24);
            this.ItemForName.TextSize = new System.Drawing.Size(79, 13);
            // 
            // ItemForType
            // 
            this.ItemForType.Control = this.TypeImageComboBoxEdit;
            this.ItemForType.Location = new System.Drawing.Point(0, 48);
            this.ItemForType.Name = "ItemForType";
            this.ItemForType.Size = new System.Drawing.Size(353, 24);
            this.ItemForType.TextSize = new System.Drawing.Size(79, 13);
            // 
            // ItemForCalculationType
            // 
            this.ItemForCalculationType.Control = this.CalculationTypeImageComboBoxEdit;
            this.ItemForCalculationType.CustomizationFormText = "Période de paie";
            this.ItemForCalculationType.Location = new System.Drawing.Point(0, 72);
            this.ItemForCalculationType.Name = "ItemForCalculationType";
            this.ItemForCalculationType.Size = new System.Drawing.Size(353, 24);
            this.ItemForCalculationType.Text = "Période de paie";
            this.ItemForCalculationType.TextSize = new System.Drawing.Size(79, 13);
            // 
            // ItemForStatus
            // 
            this.ItemForStatus.Control = this.StatusImageComboBoxEdit;
            this.ItemForStatus.Location = new System.Drawing.Point(0, 96);
            this.ItemForStatus.Name = "ItemForStatus";
            this.ItemForStatus.Size = new System.Drawing.Size(353, 24);
            this.ItemForStatus.TextSize = new System.Drawing.Size(79, 13);
            // 
            // ItemForMainAccount
            // 
            this.ItemForMainAccount.Control = this.MainAccountLookUpEdit;
            this.ItemForMainAccount.Location = new System.Drawing.Point(0, 120);
            this.ItemForMainAccount.Name = "ItemForMainAccount";
            this.ItemForMainAccount.Size = new System.Drawing.Size(353, 24);
            this.ItemForMainAccount.TextSize = new System.Drawing.Size(79, 13);
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(737, 0);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(43, 279);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlGroup2
            // 
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem1});
            this.layoutControlGroup2.Location = new System.Drawing.Point(377, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(360, 279);
            this.layoutControlGroup2.Text = " ";
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.gridControl1;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem1.MaxSize = new System.Drawing.Size(336, 235);
            this.layoutControlItem1.MinSize = new System.Drawing.Size(336, 235);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(336, 235);
            this.layoutControlItem1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            // 
            // SalaryExtensionView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.dataLayoutControl1);
            this.Name = "SalaryExtensionView";
            this.Text = "Les accessoires de salaire";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.salaryExtensionBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NameTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.CalculationTypeImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.StatusImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.TypeImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.MainAccountLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCalculationType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForStatus)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForMainAccount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x040003F7 RID: 1015
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x040003F8 RID: 1016
		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		// Token: 0x040003F9 RID: 1017
		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		// Token: 0x040003FA RID: 1018
		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		// Token: 0x040003FB RID: 1019
		private global::System.Windows.Forms.BindingSource salaryExtensionBindingSource;

		// Token: 0x040003FC RID: 1020
		private global::DevExpress.XtraEditors.TextEdit NameTextEdit;

		// Token: 0x040003FD RID: 1021
		private global::DevExpress.XtraEditors.ImageComboBoxEdit CalculationTypeImageComboBoxEdit;

		// Token: 0x040003FE RID: 1022
		private global::DevExpress.XtraEditors.ImageComboBoxEdit StatusImageComboBoxEdit;

		// Token: 0x040003FF RID: 1023
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		// Token: 0x04000400 RID: 1024
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		// Token: 0x04000401 RID: 1025
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForName;

		// Token: 0x04000402 RID: 1026
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForType;

		// Token: 0x04000403 RID: 1027
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForCalculationType;

		// Token: 0x04000404 RID: 1028
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForStatus;

		// Token: 0x04000405 RID: 1029
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForMainAccount;

		// Token: 0x04000406 RID: 1030
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;

		// Token: 0x04000407 RID: 1031
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;

		// Token: 0x04000408 RID: 1032
		private global::DevExpress.XtraEditors.ImageComboBoxEdit TypeImageComboBoxEdit;

		// Token: 0x04000409 RID: 1033
		private global::DevExpress.XtraEditors.LookUpEdit MainAccountLookUpEdit;

		// Token: 0x0400040A RID: 1034
		private global::DevExpress.XtraGrid.GridControl gridControl1;

		// Token: 0x0400040B RID: 1035
		private global::DevExpress.XtraGrid.Views.Grid.GridView gridView1;

		// Token: 0x0400040C RID: 1036
		private global::DevExpress.XtraGrid.Columns.GridColumn colName;

		// Token: 0x0400040D RID: 1037
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;

		// Token: 0x0400040E RID: 1038
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;

		// Token: 0x0400040F RID: 1039
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem3;

		// Token: 0x04000410 RID: 1040
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup3;
	}
}
