﻿using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using EasyStock.Charts.Models;
using System;
using System.Drawing;
using System.Threading.Tasks;

#nullable disable
namespace EasyStock.Charts
{
    public abstract class GridChart : GridControl, IWidget
    {
        internal GridView view;

        public GridChart()
        {
            this.Height = 250;
            this.view = new GridView();
            this.MainView = this.view;
            this.view.GridControl = this;
            this.view.OptionsBehavior.Editable = false;
            this.view.OptionsView.ShowGroupPanel = false;
            this.ReloadDataAsync();
        }

        public abstract string Caption { get; }

        public abstract Color Color { get; }

        public async void ReloadDataAsync()
        {
            object data;
            if (this.InvokeRequired)
            {
                this.BeginInvoke((Action)(() => this.ReloadDataAsync()));
                data = null;
            }
            else
            {
                data = await this.QueryData();
                this.DataSource = data;
                this.view.BestFitColumns();
            }
        }

        public abstract Task<object> QueryData();

        int IWidget.Height
        {
            get { return this.Height; }
            set { this.Height = value; }
        }
    }
}
