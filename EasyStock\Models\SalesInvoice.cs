﻿using EasyStock.Common;
using EasyStock.Controller;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Factures de vente")]
    [Table("SalesInvoices")]
    public class SalesInvoice : Bill
    {
        [Display(Name = "Code Source")]
        public int? SourceID
        {
            get
            {
                return this.sourceID;
            }
            set
            {
                base.SetProperty<int?>(ref this.sourceID, value, "SourceID");
            }
        }

        [Display(Name = "Type de source")]
        public BillSourceType SourceType
        {
            get
            {
                return this.sourceType;
            }
            set
            {
                base.SetProperty<BillSourceType>(ref this.sourceType, value, "SourceType");
            }
        }

        [Display(Name = "État de la transaction")]
        public TransactionState TransactionState
        {
            get
            {
                return this.transactionState;
            }
            set
            {
                base.SetProperty<TransactionState>(ref this.transactionState, value, "TransactionState");
            }
        }

        public CostCenter CostCenter { get; set; }

        [Display(Name = "Centre de coût")]
        public int? CostCenterID
        {
            get
            {
                return this.costCenterID;
            }
            set
            {
                base.SetProperty<int?>(ref this.costCenterID, value, "CostCenterID");
            }
        }

        public override void GetNewCode()
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                Type sss = base.GetType();
                int maxLength = (from x in db.SalesInvoices.AsNoTracking()
                                 select x.Code).Max((string x) => (int?)x.Length).GetValueOrDefault();
                bool flag = maxLength == 0;
                if (flag)
                {
                    base.Code = "1";
                }
                else
                {
                    string maxCode = (from x in db.SalesInvoices.AsNoTracking()
                                      where x.Code.Length == maxLength
                                      orderby x.ID descending
                                      select x).FirstOrDefault<SalesInvoice>().Code;
                    bool flag2 = maxCode != string.Empty;
                    if (flag2)
                    {
                        base.Code = maxCode.GetNextSequence();
                    }
                }
            }
        }

        [NotMapped]
        [Display(Name = "Client", GroupName = "Informations Client")]
        public Customer Customer
        {
            get
            {
                bool flag = this._customer == null || this._customer.ID != this.CustomerID;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        this._customer = db.Customers.Include((Customer x) => x.Account).SingleOrDefault((Customer x) => x.ID == this.CustomerID);
                    }
                }
                return this._customer;
            }
            set
            {
                base.SetProperty<Customer>(ref this._customer, value, "Customer");
            }
        }

        [Display(Name = "Client", GroupName = "Informations Client")]
        [Range(1, **********, ErrorMessage = "Vous devez sélectionner un client")]
        public int CustomerID
        {
            get
            {
                return this._customerID;
            }
            set
            {
                base.SetProperty<int>(ref this._customerID, value, "CustomerID");
            }
        }

        [NotMapped]
        [Display(Name = "Articles", GroupName = "Articles")]
        public BindingList<InvoiceDetail> Details { get; set; }

        [Display(Name = "Réduction", GroupName = "Valeur")]
        public double Discount
        {
            get
            {
                return this._discount;
            }
            set
            {
                base.SetProperty<double>(ref this._discount, value, "Discount");
            }
        }

        [NotMapped]
        [Display(Name = "Remise %")]
        [Range(0.0, 0.99, ErrorMessage = "Le pourcentage de réduction doit être compris entre 0 et 99 %")]
        public double DiscountPercentage
        {
            get
            {
                bool flag = base.Total == 0.0;
                double result;
                if (flag)
                {
                    result = 0.0;
                }
                else
                {
                    result = this.Discount / base.Total;
                }
                return result;
            }
        }

        [NotMapped]
        [Display(Name = "Total TTC", GroupName = "Valeur")]
        public double Net
        {
            get
            {
                return base.Total + this.Tax + this.OtherExpenses - this.Discount;
            }
        }

        [Display(Name = "Autres frais", GroupName = "Valeur")]
        public double OtherExpenses
        {
            get
            {
                return this._otherExpenses;
            }
            set
            {
                base.SetProperty<double>(ref this._otherExpenses, value, "OtherExpenses");
            }
        }

        [NotMapped]
        [Display(Name = "Payé", GroupName = "Réglement")]
        public double Paid
        {
            get
            {
                BindingList<PayDetail> payDetails = this.PayDetails;
                double? num;
                if (payDetails == null)
                {
                    num = null;
                }
                else
                {
                    num = payDetails.Sum((PayDetail x) => new double?(x.LocalAmount));
                }
                double? num2 = num;
                return num2.GetValueOrDefault();
            }
        }

        [NotMapped]
        [Display(Name = "Payé", GroupName = "Réglement")]
        public BindingList<PayDetail> PayDetails { get; set; }

        [NotMapped]
        [Display(Name = "Reste", GroupName = "Réglement")]
        public double Remaining
        {
            get
            {
                return this.Net - this.Paid;
            }
        }

        [Display(Name = "Taxe %", GroupName = "Valeur")]
        public double Tax
        {
            get
            {
                return this._tax;
            }
            set
            {
                base.SetProperty<double>(ref this._tax, value, "Tax");
            }
        }

        [Display(Name = "Numéro de commande de livraison")]
        public string DeliveryOrderNumber
        {
            get
            {
                return this.deliveryOrderNumber;
            }
            set
            {
                base.SetProperty<string>(ref this.deliveryOrderNumber, value, "DeliveryOrderNumber");
            }
        }

        [Display(Name = "Date de livraison")]
        public DateTime? PostDate
        {
            get
            {
                return this.postDate;
            }
            set
            {
                base.SetProperty<DateTime?>(ref this.postDate, value, "PostDate");
            }
        }

        [Display(Name = "Lieu de livraison")]
        public string DropOffLocation
        {
            get
            {
                return this.dropOffLocation;
            }
            set
            {
                base.SetProperty<string>(ref this.dropOffLocation, value, "DropOffLocation");
            }
        }

        [Display(Name = "Adresse de livraison")]
        public string DropOffAddress
        {
            get
            {
                return this.dropOffAddress;
            }
            set
            {
                base.SetProperty<string>(ref this.dropOffAddress, value, "DropOffAddress");
            }
        }

        [Display(Name = "N° de commande")]
        public string OrderNumber
        {
            get
            {
                return this.orderNumber;
            }
            set
            {
                base.SetProperty<string>(ref this.orderNumber, value, "OrderNumber");
            }
        }

        [Display(Name = "Type de livraison")]
        public string DropOffType
        {
            get
            {
                return this.dropOffType;
            }
            set
            {
                base.SetProperty<string>(ref this.dropOffType, value, "DropOffType");
            }
        }

        private Customer _customer;

        private int _customerID;

        private double _discount;

        private double _otherExpenses;

        private double _tax;

        private int? sourceID;

        private BillSourceType sourceType;

        private TransactionState transactionState = TransactionState.Posted;

        private int? costCenterID;

        private string deliveryOrderNumber;

        private DateTime? postDate;

        private string dropOffLocation;

        private string dropOffAddress;

        private string orderNumber;

        private string dropOffType;
    }
}
