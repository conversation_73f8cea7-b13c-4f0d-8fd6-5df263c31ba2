﻿namespace EasyStock.HR.Views.PenaltyRewardView
{
	// Token: 0x02000050 RID: 80
	public partial class RewardView : global::EasyStock.HR.MainViews.MasterView
	{
		// Token: 0x06000319 RID: 793 RVA: 0x0003A4F4 File Offset: 0x000386F4
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600031A RID: 794 RVA: 0x0003A52C File Offset: 0x0003872C
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.lblForNoRewardsValue = new DevExpress.XtraEditors.LabelControl();
            this.itemForNoRewards = new DevExpress.XtraEditors.LabelControl();
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.RewardbindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.EmployeeIdLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.DateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.calcTypeRadioGroup = new DevExpress.XtraEditors.RadioGroup();
            this.NoOfDaysSpinEdit = new DevExpress.XtraEditors.SpinEdit();
            this.AmountSpinEdit = new DevExpress.XtraEditors.SpinEdit();
            this.RemarksMemoEdit = new DevExpress.XtraEditors.MemoEdit();
            this.layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.emptySpaceItem4 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForRemarks = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForEmployeeId = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForcalcType = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForNoOfDays = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForAmount = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem3 = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.RewardbindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmployeeIdLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.calcTypeRadioGroup.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NoOfDaysSpinEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.AmountSpinEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.RemarksMemoEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForRemarks)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmployeeId)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForcalcType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNoOfDays)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAmount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).BeginInit();
            this.SuspendLayout();
            // 
            // dataLayoutControl1
            // 
            this.dataLayoutControl1.Controls.Add(this.lblForNoRewardsValue);
            this.dataLayoutControl1.Controls.Add(this.itemForNoRewards);
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.EmployeeIdLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.DateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.calcTypeRadioGroup);
            this.dataLayoutControl1.Controls.Add(this.NoOfDaysSpinEdit);
            this.dataLayoutControl1.Controls.Add(this.AmountSpinEdit);
            this.dataLayoutControl1.Controls.Add(this.RemarksMemoEdit);
            this.dataLayoutControl1.DataSource = this.RewardbindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.HiddenItems.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem2});
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 26);
            this.dataLayoutControl1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(686, 316);
            this.dataLayoutControl1.TabIndex = 4;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            // 
            // lblForNoRewardsValue
            // 
            this.lblForNoRewardsValue.Location = new System.Drawing.Point(353, 64);
            this.lblForNoRewardsValue.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.lblForNoRewardsValue.Name = "lblForNoRewardsValue";
            this.lblForNoRewardsValue.Size = new System.Drawing.Size(69, 13);
            this.lblForNoRewardsValue.StyleController = this.dataLayoutControl1;
            this.lblForNoRewardsValue.TabIndex = 12;
            // 
            // itemForNoRewards
            // 
            this.itemForNoRewards.Location = new System.Drawing.Point(276, 64);
            this.itemForNoRewards.Name = "itemForNoRewards";
            this.itemForNoRewards.Size = new System.Drawing.Size(73, 13);
            this.itemForNoRewards.StyleController = this.dataLayoutControl1;
            this.itemForNoRewards.TabIndex = 11;
            this.itemForNoRewards.Text = "N° de pénalités";
            // 
            // IDTextEdit
            // 
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.RewardbindingSource, "ID", true));
            this.IDTextEdit.Location = new System.Drawing.Point(104, 40);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.EditMask = "N0";
            this.IDTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(318, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 4;
            // 
            // RewardbindingSource
            // 
            this.RewardbindingSource.DataSource = typeof(EasyStock.HR.Models.PenaltyReward);
            // 
            // EmployeeIdLookUpEdit
            // 
            this.EmployeeIdLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.RewardbindingSource, "EmployeeId", true));
            this.EmployeeIdLookUpEdit.Location = new System.Drawing.Point(104, 64);
            this.EmployeeIdLookUpEdit.Name = "EmployeeIdLookUpEdit";
            this.EmployeeIdLookUpEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.EmployeeIdLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.EmployeeIdLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.EmployeeIdLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.EmployeeIdLookUpEdit.Properties.NullText = "";
            this.EmployeeIdLookUpEdit.Size = new System.Drawing.Size(168, 20);
            this.EmployeeIdLookUpEdit.StyleController = this.dataLayoutControl1;
            this.EmployeeIdLookUpEdit.TabIndex = 5;
            // 
            // DateDateEdit
            // 
            this.DateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.RewardbindingSource, "Date", true));
            this.DateDateEdit.EditValue = "";
            this.DateDateEdit.Location = new System.Drawing.Point(104, 88);
            this.DateDateEdit.Name = "DateDateEdit";
            this.DateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.DateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DateDateEdit.Properties.NullDate = "";
            this.DateDateEdit.Size = new System.Drawing.Size(318, 20);
            this.DateDateEdit.StyleController = this.dataLayoutControl1;
            this.DateDateEdit.TabIndex = 6;
            // 
            // calcTypeRadioGroup
            // 
            this.calcTypeRadioGroup.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.RewardbindingSource, "calcType", true));
            this.calcTypeRadioGroup.Location = new System.Drawing.Point(104, 112);
            this.calcTypeRadioGroup.Name = "calcTypeRadioGroup";
            this.calcTypeRadioGroup.Properties.Appearance.Options.UseTextOptions = true;
            this.calcTypeRadioGroup.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.calcTypeRadioGroup.Size = new System.Drawing.Size(318, 20);
            this.calcTypeRadioGroup.StyleController = this.dataLayoutControl1;
            this.calcTypeRadioGroup.TabIndex = 7;
            // 
            // NoOfDaysSpinEdit
            // 
            this.NoOfDaysSpinEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.RewardbindingSource, "NoOfDays", true));
            this.NoOfDaysSpinEdit.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.NoOfDaysSpinEdit.Location = new System.Drawing.Point(104, 136);
            this.NoOfDaysSpinEdit.Name = "NoOfDaysSpinEdit";
            this.NoOfDaysSpinEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.NoOfDaysSpinEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.NoOfDaysSpinEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.NoOfDaysSpinEdit.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.NoOfDaysSpinEdit.Properties.Mask.EditMask = "N0";
            this.NoOfDaysSpinEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.NoOfDaysSpinEdit.Size = new System.Drawing.Size(318, 20);
            this.NoOfDaysSpinEdit.StyleController = this.dataLayoutControl1;
            this.NoOfDaysSpinEdit.TabIndex = 8;
            // 
            // AmountSpinEdit
            // 
            this.AmountSpinEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.RewardbindingSource, "Amount", true));
            this.AmountSpinEdit.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.AmountSpinEdit.Location = new System.Drawing.Point(104, 160);
            this.AmountSpinEdit.Name = "AmountSpinEdit";
            this.AmountSpinEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.AmountSpinEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.AmountSpinEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.AmountSpinEdit.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.AmountSpinEdit.Properties.Mask.EditMask = "F";
            this.AmountSpinEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.AmountSpinEdit.Size = new System.Drawing.Size(318, 20);
            this.AmountSpinEdit.StyleController = this.dataLayoutControl1;
            this.AmountSpinEdit.TabIndex = 9;
            // 
            // RemarksMemoEdit
            // 
            this.RemarksMemoEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.RewardbindingSource, "Remarks", true));
            this.RemarksMemoEdit.Location = new System.Drawing.Point(104, 184);
            this.RemarksMemoEdit.Name = "RemarksMemoEdit";
            this.RemarksMemoEdit.Size = new System.Drawing.Size(318, 49);
            this.RemarksMemoEdit.StyleController = this.dataLayoutControl1;
            this.RemarksMemoEdit.TabIndex = 10;
            // 
            // layoutControlItem2
            // 
            this.layoutControlItem2.Location = new System.Drawing.Point(695, 26);
            this.layoutControlItem2.Name = "layoutControlItem2";
            this.layoutControlItem2.Size = new System.Drawing.Size(85, 346);
            this.layoutControlItem2.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem2.TextVisible = false;
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(686, 316);
            this.Root.TextVisible = false;
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup2,
            this.emptySpaceItem4});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(668, 300);
            // 
            // emptySpaceItem4
            // 
            this.emptySpaceItem4.AllowHotTrack = false;
            this.emptySpaceItem4.Location = new System.Drawing.Point(426, 0);
            this.emptySpaceItem4.Name = "emptySpaceItem4";
            this.emptySpaceItem4.Size = new System.Drawing.Size(242, 300);
            this.emptySpaceItem4.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlGroup2
            // 
            this.layoutControlGroup2.AppearanceGroup.BorderColor = System.Drawing.Color.RoyalBlue;
            this.layoutControlGroup2.AppearanceGroup.Options.UseBorderColor = true;
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForRemarks,
            this.ItemForEmployeeId,
            this.ItemForDate,
            this.ItemForcalcType,
            this.ItemForNoOfDays,
            this.ItemForAmount,
            this.ItemForID,
            this.layoutControlItem3,
            this.layoutControlItem1});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(426, 300);
            this.layoutControlGroup2.Text = "Informations";
            // 
            // ItemForRemarks
            // 
            this.ItemForRemarks.Control = this.RemarksMemoEdit;
            this.ItemForRemarks.Location = new System.Drawing.Point(0, 144);
            this.ItemForRemarks.MaxSize = new System.Drawing.Size(404, 53);
            this.ItemForRemarks.MinSize = new System.Drawing.Size(404, 53);
            this.ItemForRemarks.Name = "ItemForRemarks";
            this.ItemForRemarks.Size = new System.Drawing.Size(404, 116);
            this.ItemForRemarks.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForRemarks.StartNewLine = true;
            this.ItemForRemarks.TextSize = new System.Drawing.Size(79, 13);
            // 
            // ItemForEmployeeId
            // 
            this.ItemForEmployeeId.Control = this.EmployeeIdLookUpEdit;
            this.ItemForEmployeeId.Location = new System.Drawing.Point(0, 24);
            this.ItemForEmployeeId.Name = "ItemForEmployeeId";
            this.ItemForEmployeeId.Size = new System.Drawing.Size(254, 24);
            this.ItemForEmployeeId.TextSize = new System.Drawing.Size(79, 13);
            // 
            // ItemForDate
            // 
            this.ItemForDate.Control = this.DateDateEdit;
            this.ItemForDate.Location = new System.Drawing.Point(0, 48);
            this.ItemForDate.Name = "ItemForDate";
            this.ItemForDate.Size = new System.Drawing.Size(404, 24);
            this.ItemForDate.TextSize = new System.Drawing.Size(79, 13);
            // 
            // ItemForcalcType
            // 
            this.ItemForcalcType.Control = this.calcTypeRadioGroup;
            this.ItemForcalcType.Location = new System.Drawing.Point(0, 72);
            this.ItemForcalcType.MaxSize = new System.Drawing.Size(404, 24);
            this.ItemForcalcType.MinSize = new System.Drawing.Size(404, 24);
            this.ItemForcalcType.Name = "ItemForcalcType";
            this.ItemForcalcType.Size = new System.Drawing.Size(404, 24);
            this.ItemForcalcType.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForcalcType.StartNewLine = true;
            this.ItemForcalcType.TextSize = new System.Drawing.Size(79, 13);
            // 
            // ItemForNoOfDays
            // 
            this.ItemForNoOfDays.Control = this.NoOfDaysSpinEdit;
            this.ItemForNoOfDays.Location = new System.Drawing.Point(0, 96);
            this.ItemForNoOfDays.Name = "ItemForNoOfDays";
            this.ItemForNoOfDays.Size = new System.Drawing.Size(404, 24);
            this.ItemForNoOfDays.TextSize = new System.Drawing.Size(79, 13);
            // 
            // ItemForAmount
            // 
            this.ItemForAmount.Control = this.AmountSpinEdit;
            this.ItemForAmount.Location = new System.Drawing.Point(0, 120);
            this.ItemForAmount.Name = "ItemForAmount";
            this.ItemForAmount.Size = new System.Drawing.Size(404, 24);
            this.ItemForAmount.TextSize = new System.Drawing.Size(79, 13);
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.itemForNoRewards;
            this.layoutControlItem1.Location = new System.Drawing.Point(254, 24);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(77, 24);
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            // 
            // ItemForID
            // 
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(404, 24);
            this.ItemForID.TextSize = new System.Drawing.Size(79, 13);
            // 
            // layoutControlItem3
            // 
            this.layoutControlItem3.Control = this.lblForNoRewardsValue;
            this.layoutControlItem3.Location = new System.Drawing.Point(331, 24);
            this.layoutControlItem3.Name = "layoutControlItem3";
            this.layoutControlItem3.Size = new System.Drawing.Size(73, 24);
            this.layoutControlItem3.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem3.TextVisible = false;
            // 
            // RewardView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(686, 366);
            this.Controls.Add(this.dataLayoutControl1);
            this.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.Name = "RewardView";
            this.Text = "RewardView";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.RewardbindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmployeeIdLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.calcTypeRadioGroup.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NoOfDaysSpinEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.AmountSpinEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.RemarksMemoEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForRemarks)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmployeeId)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForcalcType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNoOfDays)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAmount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x040004E4 RID: 1252
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x040004E5 RID: 1253
		private global::System.Windows.Forms.BindingSource RewardbindingSource;

		// Token: 0x040004E6 RID: 1254
		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		// Token: 0x040004E7 RID: 1255
		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		// Token: 0x040004E8 RID: 1256
		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		// Token: 0x040004E9 RID: 1257
		private global::DevExpress.XtraEditors.LookUpEdit EmployeeIdLookUpEdit;

		// Token: 0x040004EA RID: 1258
		private global::DevExpress.XtraEditors.DateEdit DateDateEdit;

		// Token: 0x040004EB RID: 1259
		private global::DevExpress.XtraEditors.RadioGroup calcTypeRadioGroup;

		// Token: 0x040004EC RID: 1260
		private global::DevExpress.XtraEditors.SpinEdit NoOfDaysSpinEdit;

		// Token: 0x040004ED RID: 1261
		private global::DevExpress.XtraEditors.SpinEdit AmountSpinEdit;

		// Token: 0x040004EE RID: 1262
		private global::DevExpress.XtraEditors.MemoEdit RemarksMemoEdit;

		// Token: 0x040004EF RID: 1263
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		// Token: 0x040004F0 RID: 1264
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		// Token: 0x040004F1 RID: 1265
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForEmployeeId;

		// Token: 0x040004F2 RID: 1266
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDate;

		// Token: 0x040004F3 RID: 1267
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForcalcType;

		// Token: 0x040004F4 RID: 1268
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForNoOfDays;

		// Token: 0x040004F5 RID: 1269
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForAmount;

		// Token: 0x040004F6 RID: 1270
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForRemarks;

		// Token: 0x040004F7 RID: 1271
		private global::DevExpress.XtraEditors.LabelControl itemForNoRewards;

		// Token: 0x040004F8 RID: 1272
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem4;

		// Token: 0x040004F9 RID: 1273
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;

		// Token: 0x040004FA RID: 1274
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem2;

		// Token: 0x040004FB RID: 1275
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;

		// Token: 0x040004FC RID: 1276
		private global::DevExpress.XtraEditors.LabelControl lblForNoRewardsValue;

		// Token: 0x040004FD RID: 1277
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem3;
	}
}
