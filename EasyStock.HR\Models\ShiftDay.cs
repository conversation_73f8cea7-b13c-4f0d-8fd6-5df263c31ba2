﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    public class ShiftDay : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        public Shift Shift { get; set; }

        public int ShiftID { get; set; }

        [Display(Name = "Jour")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int Day
        {
            get
            {
                return this.day;
            }
            set
            {
                base.SetProperty<int>(ref this.day, value, "Day");
            }
        }

        [Display(Name = "Tableau horaire")]
        [Range(0, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int TimeTableID
        {
            get
            {
                return this.timeTableID;
            }
            set
            {
                base.SetProperty<int>(ref this.timeTableID, value, "TimeTableID");
            }
        }

        [Display(Name = "Tableau horaire")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public TimeTable TimeTable
        {
            get
            {
                return this.timeTable;
            }
            set
            {
                base.SetProperty<TimeTable>(ref this.timeTable, value, "TimeTable");
            }
        }

        private int id;

        private int day;

        private int timeTableID;

        private TimeTable timeTable;
    }
}