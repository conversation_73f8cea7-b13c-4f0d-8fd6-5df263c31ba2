﻿using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Mouvements d'articles en magasin")]
    public class ProductTransaction : BaseNotifyPropertyChangedModel
    {

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "N°")]
        [ReadOnly(true)]
        public int ID { get; set; }

        [Display(Name = "Histoire du mouvement")]
        public DateTime Date
        {
            get
            {
                return this._date;
            }
            set
            {
                base.SetProperty<DateTime>(ref this._date, value, "Date");
            }
        }

        [Display(Name = "Type de mouvement")]
        public TransactionType Type { get; set; }
        [Display(Name = "Numéro de carte")]
        public int BillID { get; set; }

        [NotMapped]
        public Product Product
        {
            get
            {
                bool flag = this._product == null || this._product.ID != this.ProductID;
                if (flag)
                {
                    this._product = CurrentSession.Products.SingleOrDefault((Product x) => x.ID == this.ProductID);
                }
                return this._product;
            }
        }
        [Display(Name = " Article")]
        public int ProductID { get; set; }
        [NotMapped]
        public ProductUnit Unit
        {
            get
            {
                bool flag = this._unit == null || this._unit.ID != this.UnitID;
                if (flag)
                {
                    this._unit = CurrentSession.ProductUnits.SingleOrDefault((ProductUnit x) => x.ID == this.UnitID);
                }
                return this._unit;
            }
        }


        [Display(Name = "Unité")]
        [Range(1, 2147483647, ErrorMessage = "Vous devez choisir l'unité")]
        public int UnitID { get; set; }

        public double Factor { get; set; }

        [Display(Name = "En série")]
        public string Serial { get; set; }


        [Display(Name = "Couleur")]
        public int? ColorID { get; set; }


        [Display(Name = "Taille")]
        public int? SizeID { get; set; }

        [Display(Name = "Date d'expiration")]
        public DateTime? Expire { get; set; }

        [Display(Name = "Qté")]
        [Range(0.0, 1.7976931348623157E+308, ErrorMessage = "La quantité doit être supérieure à 0")]
        public virtual double Quantity
        {
            get
            {
                return this._quantity;
            }
            set
            {
                base.SetProperty<double>(ref this._quantity, value, "Quantity");
            }
        }

        [Display(Name = "Type de mouvement")]
        public ProductTransactionType TransactionType { get; set; }

        public double RowQuantity
        {
            get
            {
                return this.Quantity * this.Factor;
            }
        }

        [NotMapped]
        public Store Store
        {
            get
            {
                bool flag = this._branch == null || this._branch.ID != this.StoreID;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        this._branch = db.Stores.SingleOrDefault((Store x) => x.ID == this.StoreID);
                    }
                }
                return this._branch;
            }
        }
        [Display(Name = "Le magasin", GroupName = "Données de facturation")]
        [Range(1, 2147483647, ErrorMessage = "Vous devez sélectionner le magasin\r\n")]
        public int StoreID { get; set; }
        [Display(Name = "Prix")]
        public double Price
        {
            get
            {
                return this._price;
            }
            set
            {
                base.SetProperty<double>(ref this._price, value, "Price");
            }
        }

        [Display(Name = "Le coût")]
        public double CostValue { get; set; }

        [NotMapped]
        [Display(Name = "Total")]
        public double Total
        {
            get
            {
                return this.Price * this.Quantity;
            }
        }
        [Display(Name = "Coût Total")]
        [NotMapped]
        public double TotalCostValue
        {
            get
            {
                return this.CostValue * this.Quantity;
            }
        }

        [Display(Name = "Code de suivi")]
        public int? ParentTransactionID { get; set; }

        public List<TransactionLinkModel> Sources { get; set; }

        public ProductTransaction ShallowCopy()
        {
            return (ProductTransaction)base.MemberwiseClone();
        }

        [Display(Name = "Article Code")]
        public string ProductCode { get; set; }

        [Display(Name = "Statut d'expulsion")]
        public TransactionState TransactionState
        {
            get
            {
                return this.transactionState;
            }
            set
            {
                base.SetProperty<TransactionState>(ref this.transactionState, value, "TransactionState");
            }
        }
        private Product _product;
        private ProductUnit _unit;
        private double _quantity;
        public Store _branch;
        private double _price;

        private DateTime _date;

        // Token: 0x04002863 RID: 10339
        private TransactionState transactionState = TransactionState.Posted;
    }
}
