﻿using DevExpress.Utils.Extensions;
using DevExpress.XtraEditors;
using DevExpress.XtraLayout;
using EasyStock.Classes;
using EasyStock.Controller;
using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;

namespace EasyStock.Controls
{
    public class ProductUnitsListControl : XtraUserControl
    {
        public ERPDataContext db;

        private BindingList<ProductUnit> _dataSource;

        private List<UnitOfMeasurement> _uoms;

        public Product product;

        private IContainer components = null;

        private FlowLayoutPanel flowLayoutPanel1;

        private LayoutControl layoutControl1;

        private SimpleButton simpleButton1;

        private LayoutControlGroup Root;

        private LayoutControlItem layoutControlItem1;

        private EmptySpaceItem emptySpaceItem1;

        private LayoutControlItem layoutControlItem2;

        public LayoutControlGroup GroupForAdd;

        public BindingList<ProductUnit> DataSource
        {
            get
            {
                return _dataSource;
            }
            set
            {
                _dataSource = value;
                flowLayoutPanel1.Controls.Clear();
                if (_dataSource == null)
                {
                    return;
                }
                _dataSource.ListChanged -= _dataSource_ListChanged;
                _dataSource.ListChanged += _dataSource_ListChanged;
                foreach (ProductUnit item in _dataSource)
                {
                    AddUnitControl(item);
                }
            }
        }

        public IList<ProductUnitControl> ProductUnitControls
        {
            get
            {
                List<ProductUnitControl> list = new List<ProductUnitControl>();
                foreach (Control item in flowLayoutPanel1.Controls)
                {
                    if (item is ProductUnitControl unitControl)
                    {
                        list.Add(unitControl);
                    }
                }
                return list;
            }
        }

        public List<UnitOfMeasurement> UOMs
        {
            get
            {
                return _uoms;
            }
            set
            {
                _uoms = value;
                ProductUnitControls.ForEach(delegate (ProductUnitControl x)
                {
                    x.UnitNameIDTextEdit.Properties.DataSource = _uoms;
                });
            }
        }

        private void AddUnitControl(ProductUnit unit)
        {
            ProductUnitControl unitControl = new ProductUnitControl(this, unit);
            flowLayoutPanel1.Controls.Add(unitControl);
        }

        private void SortControls()
        {
        }

        private void _dataSource_ListChanged(object sender, ListChangedEventArgs e)
        {
            if (e.ListChangedType == ListChangedType.ItemAdded)
            {
                ProductUnit unit = DataSource[e.NewIndex];
                AddUnitControl(unit);
                return;
            }
            if (e.ListChangedType == ListChangedType.ItemDeleted)
            {
                foreach (Control item in flowLayoutPanel1.Controls)
                {
                    ProductUnitControl unitControl = item as ProductUnitControl;
                    if (unitControl == null)
                    {
                    }
                }
                return;
            }
            if (e.ListChangedType == ListChangedType.ItemChanged && e.PropertyDescriptor?.Name == "Factor")
            {
                SortControls();
            }
        }

        public ProductUnitsListControl()
        {
            InitializeComponent();
            flowLayoutPanel1.AutoSize = true;
        }

        private void ProductUnitsListControl_SizeChanged(object sender, EventArgs e)
        {
            foreach (object item in flowLayoutPanel1.Controls)
            {
                if (item is Control control)
                {
                    control.Width = base.Width - 50;
                }
            }
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            ProductUnit unit = new ProductUnit();
            unit.Product = product;
            unit.Barcodes = new BindingList<ProductUnitBarcode>();
            ProductUnitBarcode unitBarcode = new ProductUnitBarcode
            {
                Unit = unit
            };
            DataSource.Add(unit);
            unitBarcode.GetNewUnitBarcode();
            unit.Barcodes.Add(unitBarcode);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            this.layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.GroupForAdd = new DevExpress.XtraLayout.LayoutControlGroup();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)this.layoutControl1).BeginInit();
            this.layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)this.Root).BeginInit();
            ((System.ComponentModel.ISupportInitialize)this.layoutControlItem1).BeginInit();
            ((System.ComponentModel.ISupportInitialize)this.GroupForAdd).BeginInit();
            ((System.ComponentModel.ISupportInitialize)this.emptySpaceItem1).BeginInit();
            ((System.ComponentModel.ISupportInitialize)this.layoutControlItem2).BeginInit();
            base.SuspendLayout();
            this.flowLayoutPanel1.AutoScroll = true;
            this.flowLayoutPanel1.Location = new System.Drawing.Point(2, 2);
            this.flowLayoutPanel1.Name = "flowLayoutPanel1";
            this.flowLayoutPanel1.Size = new System.Drawing.Size(729, 311);
            this.flowLayoutPanel1.TabIndex = 0;
            this.layoutControl1.Controls.Add(this.simpleButton1);
            this.layoutControl1.Controls.Add(this.flowLayoutPanel1);
            this.layoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl1.Location = new System.Drawing.Point(0, 0);
            this.layoutControl1.Name = "layoutControl1";
            this.layoutControl1.Root = this.Root;
            this.layoutControl1.Size = new System.Drawing.Size(733, 361);
            this.layoutControl1.TabIndex = 1;
            this.layoutControl1.Text = "layoutControl1";
            this.simpleButton1.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.simpleButton1.ImageOptions.SvgImage = EasyStock.Properties.Resources.actions_add;
            this.simpleButton1.Location = new System.Drawing.Point(678, 320);
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Size = new System.Drawing.Size(50, 36);
            this.simpleButton1.StyleController = this.layoutControl1;
            this.simpleButton1.TabIndex = 4;
            this.simpleButton1.Click += new System.EventHandler(simpleButton1_Click);
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[2] { this.layoutControlItem1, this.GroupForAdd });
            this.Root.Name = "Root";
            this.Root.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.Root.Size = new System.Drawing.Size(733, 361);
            this.Root.TextVisible = false;
            this.layoutControlItem1.Control = this.flowLayoutPanel1;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(733, 315);
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            this.GroupForAdd.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[2] { this.emptySpaceItem1, this.layoutControlItem2 });
            this.GroupForAdd.Location = new System.Drawing.Point(0, 315);
            this.GroupForAdd.Name = "GroupForAdd";
            this.GroupForAdd.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.GroupForAdd.Size = new System.Drawing.Size(733, 46);
            this.GroupForAdd.TextVisible = false;
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 0);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(673, 40);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem2.Control = this.simpleButton1;
            this.layoutControlItem2.Location = new System.Drawing.Point(673, 0);
            this.layoutControlItem2.Name = "layoutControlItem2";
            this.layoutControlItem2.Size = new System.Drawing.Size(54, 40);
            this.layoutControlItem2.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem2.TextVisible = false;
            base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
            base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            base.Controls.Add(this.layoutControl1);
            base.Name = "ProductUnitsListControl";
            base.Size = new System.Drawing.Size(733, 361);
            ((System.ComponentModel.ISupportInitialize)this.layoutControl1).EndInit();
            this.layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)this.Root).EndInit();
            ((System.ComponentModel.ISupportInitialize)this.layoutControlItem1).EndInit();
            ((System.ComponentModel.ISupportInitialize)this.GroupForAdd).EndInit();
            ((System.ComponentModel.ISupportInitialize)this.emptySpaceItem1).EndInit();
            ((System.ComponentModel.ISupportInitialize)this.layoutControlItem2).EndInit();
            base.ResumeLayout(false);
        }
    }
}
