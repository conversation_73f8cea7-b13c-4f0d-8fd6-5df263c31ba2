﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    [DisplayColumn("Réglementation des absences")]
    public class AbsenceRegulation : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Nom")]
        [StringLength(150)]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public string Name
        {
            get
            {
                return this.name;
            }
            set
            {
                base.SetProperty<string>(ref this.name, value, "Name");
            }
        }

        [Display(Name = "Déduction par jour avec autorisation (jours)")]
        public double DayPermissionDayDeduction
        {
            get
            {
                return this.daypermissionDayDeduction;
            }
            set
            {
                base.SetProperty<double>(ref this.daypermissionDayDeduction, value, "DayPermissionDayDeduction");
            }
        }

        [Display(Name = "Montant fixe avec autorisation")]
        public double DayPermissionFixedAmount
        {
            get
            {
                return this.daypermissionFixedAmount;
            }
            set
            {
                base.SetProperty<double>(ref this.daypermissionFixedAmount, value, "DayPermissionFixedAmount");
            }
        }

        [Display(Name = "Déduction par jour sans autorisation (jours)")]
        public double NonDayPermissionDayDeduction
        {
            get
            {
                return this.nondaypermissionDayDeduction;
            }
            set
            {
                base.SetProperty<double>(ref this.nondaypermissionDayDeduction, value, "NonDayPermissionDayDeduction");
            }
        }

        [Display(Name = "Montant fixe sans autorisation")]
        public double NonDayPermissionFixedAmount
        {
            get
            {
                return this.nondaypermissionFixedAmount;
            }
            set
            {
                base.SetProperty<double>(ref this.nondaypermissionFixedAmount, value, "NonDayPermissionFixedAmount");
            }
        }

        private int id;

        private string name;

        private double daypermissionDayDeduction;

        private double daypermissionFixedAmount;

        private double nondaypermissionDayDeduction;

        private double nondaypermissionFixedAmount;
    }
}
