﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Data.Entity.Migrations.Model;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class productLocationInStore : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(productLocationInStore));

        string IMigrationMetadata.Id => "202012311209598_productLocationInStore";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.ProductStoreLocations", delegate (ColumnBuilder c)
            {
                ColumnModel iD = c.Int(false, identity: true);
                ColumnModel productID = c.Int(false);
                ColumnModel branchID = c.Int(false);
                int? maxLength = 50;
                return new
                {
                    ID = iD,
                    ProductID = productID,
                    BranchID = branchID,
                    Location = c.String(null, maxLength)
                };
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Branches", t => t.BranchID).ForeignKey("dbo.Products", t => t.ProductID)
                .Index(t => t.ProductID)
                .Index(t => t.BranchID);
        }

        public override void Down()
        {
            DropForeignKey("dbo.ProductStoreLocations", "ProductID", "dbo.Products");
            DropForeignKey("dbo.ProductStoreLocations", "BranchID", "dbo.Branches");
            DropIndex("dbo.ProductStoreLocations", new string[1] { "BranchID" });
            DropIndex("dbo.ProductStoreLocations", new string[1] { "ProductID" });
            DropTable("dbo.ProductStoreLocations");
        }
    }
}
