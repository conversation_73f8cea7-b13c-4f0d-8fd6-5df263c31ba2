﻿using DevExpress.Data;
using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraLayout;
using EasyStock.Classes;
using EasyStock.Controller;
using EasyStock.Models;
using System;
using System.ComponentModel;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace EasyStock.Controls
{
    // Token: 0x020004FC RID: 1276
    public class AccountBalance : XtraUserControl
    {
        public AccountBalance()
        {
            InitializeComponent();
        }

        private void Credit_EditValueChanged(object sender, EventArgs e)
        {
            SpinEdit edit = sender as SpinEdit;
            if (credit.Value > 0m)
            {
                debit.EditValue = 0;
            }
            else if (debit.Value > 0m)
            {
                credit.EditValue = 0;
            }
            if (edit.Value == 0m)
            {
                edit.Properties.Appearance.BackColor = Color.LightGray;
            }
            else
            {
                edit.Properties.Appearance.BackColor = Color.LightYellow;
            }
        }

        public void GetBalance(int accountID)
        {
            using ERPDataContext db = new ERPDataContext();
            Journal journal = db.Journals.Include((Journal x) => x.Details).SingleOrDefault((Journal x) => (int)x.ProcessType == 0 && x.ProcessID == accountID);
            if (journal != null)
            {
                JournalDetail capital = journal.Details.First((JournalDetail x) => x.AccountID != accountID);
                lookUpEdit1.EditValue = capital.AccountID;
                debit.EditValue = capital.Credit;
                credit.EditValue = capital.Debit;
                dateEdit1.EditValue = journal.Date;
            }
        }

        public void SetBalance(int accountID)
        {
            using ERPDataContext db = new ERPDataContext();
            Journal journal = db.Journals.Include((Journal x) => x.Details).SingleOrDefault((Journal x) => (int)x.ProcessType == 0 && x.ProcessID == accountID);
            Account account = db.Accounts.SingleOrDefault((Account x) => x.ID == accountID);
            if (lookUpEdit1.EditValue is int capitalID && capitalID != 0)
            {
                if (journal == null && debit.Value + credit.Value > 0m)
                {
                    string statment = "Solde Initial du Compte " + account.Name;
                    journal = new Journal
                    {
                        ProcessID = account.ID,
                        ProcessType = SystemProcess.AccountOpenBalance,
                        Note = statment,
                        BranchID = CurrentSession.DefaultBranch.ID,
                        Details = new BindingList<JournalDetail>
                    {
                        new JournalDetail
                        {
                            AccountID = accountID,
                            CurrencyRate = 1.0,
                            CurrencyID = 1,
                            Journal = journal,
                            Statement = statment
                        },
                        new JournalDetail
                        {
                            CurrencyRate = 1.0,
                            CurrencyID = 1,
                            Journal = journal,
                            Statement = statment
                        }
                    }
                    };
                    journal.GetNewCode();
                    db.Journals.Add(journal);
                }
                if (journal != null)
                {
                    if (debit.Value == 0m && credit.Value == 0m)
                    {
                        db.JournalDetails.RemoveRange(journal.Details);
                        db.Journals.Remove(journal);
                        db.SaveChanges();
                        return;
                    }
                    JournalDetail capital = journal.Details.First((JournalDetail x) => x.AccountID != accountID);
                    JournalDetail accountJornal = journal.Details.First((JournalDetail x) => x.AccountID == accountID);
                    double num2 = (accountJornal.Debit = Convert.ToDouble(debit.Value));
                    capital.Credit = num2;
                    num2 = (accountJornal.Credit = Convert.ToDouble(credit.Value));
                    capital.Debit = num2;
                    capital.AccountID = capitalID;
                    journal.Date = dateEdit1.DateTime;
                }
            }
            db.SaveChanges();
        }

        public void Initialze()
        {
            dateEdit1.EditValue = DateTime.Now;
            lookUpEdit1.Properties.DataSource = CurrentSession.EndNodeAccounts;
            lookUpEdit1.EditValue = CurrentSession.SystemSettings.CapitalAccount;
            credit.EditValueChanged += Credit_EditValueChanged;
            debit.EditValueChanged += Credit_EditValueChanged;
        }

        private void AccountBalance_Load(object sender, EventArgs e)
        {
        }
        protected override void Dispose(bool disposing)
        {
            bool flag = disposing && this.components != null;
            if (flag)
            {
                this.components.Dispose();
            }
            base.Dispose(disposing);
        }

        // Token: 0x06002581 RID: 9601 RVA: 0x00205C20 File Offset: 0x00203E20
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            this.debit = new DevExpress.XtraEditors.SpinEdit();
            this.credit = new DevExpress.XtraEditors.SpinEdit();
            this.dateEdit1 = new DevExpress.XtraEditors.DateEdit();
            this.lookUpEdit1 = new DevExpress.XtraEditors.LookUpEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem4 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlGroup3 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem3 = new DevExpress.XtraLayout.LayoutControlItem();
            this.accountBindingSource = new System.Windows.Forms.BindingSource(this.components);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).BeginInit();
            this.layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.debit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.credit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEdit1.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lookUpEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.accountBindingSource)).BeginInit();
            this.SuspendLayout();
            // 
            // layoutControl1
            // 
            this.layoutControl1.AutoScroll = false;
            this.layoutControl1.Controls.Add(this.debit);
            this.layoutControl1.Controls.Add(this.credit);
            this.layoutControl1.Controls.Add(this.dateEdit1);
            this.layoutControl1.Controls.Add(this.lookUpEdit1);
            this.layoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl1.Location = new System.Drawing.Point(0, 0);
            this.layoutControl1.Name = "layoutControl1";
            this.layoutControl1.OptionsView.AlwaysScrollActiveControlIntoView = false;
            this.layoutControl1.Root = this.Root;
            this.layoutControl1.Size = new System.Drawing.Size(250, 145);
            this.layoutControl1.TabIndex = 0;
            this.layoutControl1.Text = "layoutControl1";
            // 
            // debit
            // 
            this.debit.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.debit.Location = new System.Drawing.Point(128, 95);
            this.debit.Name = "debit";
            this.debit.Properties.Appearance.BackColor = System.Drawing.Color.LightGray;
            this.debit.Properties.Appearance.Options.UseBackColor = true;
            this.debit.Size = new System.Drawing.Size(110, 20);
            this.debit.StyleController = this.layoutControl1;
            this.debit.TabIndex = 7;
            // 
            // credit
            // 
            this.credit.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.credit.Location = new System.Drawing.Point(12, 95);
            this.credit.Name = "credit";
            this.credit.Properties.Appearance.BackColor = System.Drawing.Color.LightGray;
            this.credit.Properties.Appearance.Options.UseBackColor = true;
            this.credit.Size = new System.Drawing.Size(110, 20);
            this.credit.StyleController = this.layoutControl1;
            this.credit.TabIndex = 6;
            // 
            // dateEdit1
            // 
            this.dateEdit1.EditValue = null;
            this.dateEdit1.Location = new System.Drawing.Point(128, 51);
            this.dateEdit1.Name = "dateEdit1";
            this.dateEdit1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.dateEdit1.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.dateEdit1.Size = new System.Drawing.Size(112, 20);
            this.dateEdit1.StyleController = this.layoutControl1;
            this.dateEdit1.TabIndex = 5;
            // 
            // lookUpEdit1
            // 
            this.lookUpEdit1.Location = new System.Drawing.Point(128, 29);
            this.lookUpEdit1.Name = "lookUpEdit1";
            this.lookUpEdit1.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lookUpEdit1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.lookUpEdit1.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ID", "Code", 38, DevExpress.Utils.FormatType.Numeric, "", true, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.Default),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("Name", "Nom", 39, DevExpress.Utils.FormatType.None, "", true, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.Default)});
            this.lookUpEdit1.Properties.DataSource = this.accountBindingSource;
            this.lookUpEdit1.Properties.DisplayMember = "Name";
            this.lookUpEdit1.Properties.NullText = "";
            this.lookUpEdit1.Properties.ValueMember = "ID";
            this.lookUpEdit1.Size = new System.Drawing.Size(112, 20);
            this.lookUpEdit1.StyleController = this.layoutControl1;
            this.lookUpEdit1.TabIndex = 4;
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.Root.Size = new System.Drawing.Size(250, 145);
            this.Root.TextVisible = false;
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.CustomizationFormText = "Solde d\'ouverture";
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem1,
            this.layoutControlItem2,
            this.layoutControlGroup2,
            this.layoutControlGroup3});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "layoutControlGroup1";
            this.layoutControlGroup1.Size = new System.Drawing.Size(250, 145);
            this.layoutControlGroup1.Text = "Solde d\'ouverture";
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.lookUpEdit1;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(232, 22);
            this.layoutControlItem1.Text = "Compte complémentaire";
            this.layoutControlItem1.TextSize = new System.Drawing.Size(115, 13);
            // 
            // layoutControlItem2
            // 
            this.layoutControlItem2.Control = this.dateEdit1;
            this.layoutControlItem2.Location = new System.Drawing.Point(0, 22);
            this.layoutControlItem2.Name = "layoutControlItem2";
            this.layoutControlItem2.Size = new System.Drawing.Size(232, 22);
            this.layoutControlItem2.Text = "Date";
            this.layoutControlItem2.TextSize = new System.Drawing.Size(115, 13);
            // 
            // layoutControlGroup2
            // 
            this.layoutControlGroup2.AppearanceGroup.Options.UseTextOptions = true;
            this.layoutControlGroup2.AppearanceGroup.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.layoutControlGroup2.AppearanceGroup.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem4});
            this.layoutControlGroup2.Location = new System.Drawing.Point(116, 44);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.layoutControlGroup2.Size = new System.Drawing.Size(116, 65);
            this.layoutControlGroup2.Text = "Débiteur";
            // 
            // layoutControlItem4
            // 
            this.layoutControlItem4.Control = this.debit;
            this.layoutControlItem4.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem4.Name = "layoutControlItem4";
            this.layoutControlItem4.Size = new System.Drawing.Size(112, 41);
            this.layoutControlItem4.Text = "Débiteur";
            this.layoutControlItem4.TextLocation = DevExpress.Utils.Locations.Top;
            this.layoutControlItem4.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem4.TextVisible = false;
            // 
            // layoutControlGroup3
            // 
            this.layoutControlGroup3.AppearanceGroup.Options.UseTextOptions = true;
            this.layoutControlGroup3.AppearanceGroup.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.layoutControlGroup3.AppearanceGroup.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.layoutControlGroup3.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem3});
            this.layoutControlGroup3.Location = new System.Drawing.Point(0, 44);
            this.layoutControlGroup3.Name = "layoutControlGroup3";
            this.layoutControlGroup3.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.layoutControlGroup3.Size = new System.Drawing.Size(116, 65);
            this.layoutControlGroup3.Text = "Créditeur";
            // 
            // layoutControlItem3
            // 
            this.layoutControlItem3.Control = this.credit;
            this.layoutControlItem3.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem3.Name = "layoutControlItem3";
            this.layoutControlItem3.Size = new System.Drawing.Size(112, 41);
            this.layoutControlItem3.Text = "Créditeur";
            this.layoutControlItem3.TextLocation = DevExpress.Utils.Locations.Top;
            this.layoutControlItem3.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem3.TextVisible = false;
            // 
            // accountBindingSource
            // 
            this.accountBindingSource.DataSource = typeof(EasyStock.Models.Account);
            // 
            // AccountBalance
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.layoutControl1);
            this.MaximumSize = new System.Drawing.Size(0, 145);
            this.MinimumSize = new System.Drawing.Size(250, 145);
            this.Name = "AccountBalance";
            this.Size = new System.Drawing.Size(250, 145);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).EndInit();
            this.layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.debit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.credit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEdit1.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lookUpEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.accountBindingSource)).EndInit();
            this.ResumeLayout(false);

        }

        // Token: 0x04002C9E RID: 11422
        private IContainer components = null;

        // Token: 0x04002C9F RID: 11423
        private LayoutControl layoutControl1;

        // Token: 0x04002CA0 RID: 11424
        private LayoutControlGroup Root;

        // Token: 0x04002CA1 RID: 11425
        private SpinEdit debit;

        // Token: 0x04002CA2 RID: 11426
        private SpinEdit credit;

        // Token: 0x04002CA3 RID: 11427
        private DateEdit dateEdit1;

        // Token: 0x04002CA4 RID: 11428
        private LookUpEdit lookUpEdit1;

        // Token: 0x04002CA5 RID: 11429
        private LayoutControlGroup layoutControlGroup1;

        // Token: 0x04002CA6 RID: 11430
        private LayoutControlItem layoutControlItem1;

        // Token: 0x04002CA7 RID: 11431
        private LayoutControlItem layoutControlItem2;

        // Token: 0x04002CA8 RID: 11432
        private LayoutControlGroup layoutControlGroup2;

        // Token: 0x04002CA9 RID: 11433
        private LayoutControlItem layoutControlItem4;

        // Token: 0x04002CAA RID: 11434
        private LayoutControlGroup layoutControlGroup3;

        // Token: 0x04002CAB RID: 11435
        private LayoutControlItem layoutControlItem3;

        // Token: 0x04002CAC RID: 11436
        private BindingSource accountBindingSource;
    }
}
