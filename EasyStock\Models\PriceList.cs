﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.Models
{
	public class PriceList : BaseNotifyPropertyChangedModel
	{
		public int ID { get; set; }

		[Display(Name = "")]
		public string Name
		{
			get
			{
				return this.name;
			}
			set
			{
				base.SetProperty<string>(ref this.name, value, "Name");
			}
		}

		public int MyProperty { get; set; }

		private string name;
	}
}
