﻿namespace EasyStock.HR.Views
{
	// Token: 0x0200002D RID: 45
	public partial class AbcenceRegulationView : global::EasyStock.HR.MainViews.MasterView
	{
		// Token: 0x06000113 RID: 275 RVA: 0x000066E4 File Offset: 0x000048E4
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000114 RID: 276 RVA: 0x0000671C File Offset: 0x0000491C
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.absenceRegulationBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.NameTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.DayPermissionDayDeductionTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.DayPermissionFixedAmountTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.NonDayPermissionDayDeductionTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.NonDayPermissionFixedAmountTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForName = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDayPermissionDayDeduction = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDayPermissionFixedAmount = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForNonDayPermissionDayDeduction = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForNonDayPermissionFixedAmount = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup3 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.absenceRegulationBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NameTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayPermissionDayDeductionTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayPermissionFixedAmountTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NonDayPermissionDayDeductionTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NonDayPermissionFixedAmountTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDayPermissionDayDeduction)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDayPermissionFixedAmount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNonDayPermissionDayDeduction)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNonDayPermissionFixedAmount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            this.SuspendLayout();
            // 
            // dataLayoutControl1
            // 
            this.dataLayoutControl1.Controls.Add(this.gridControl1);
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NameTextEdit);
            this.dataLayoutControl1.Controls.Add(this.DayPermissionDayDeductionTextEdit);
            this.dataLayoutControl1.Controls.Add(this.DayPermissionFixedAmountTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NonDayPermissionDayDeductionTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NonDayPermissionFixedAmountTextEdit);
            this.dataLayoutControl1.DataSource = this.absenceRegulationBindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 26);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(869, 505);
            this.dataLayoutControl1.TabIndex = 0;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            // 
            // gridControl1
            // 
            this.gridControl1.DataSource = this.absenceRegulationBindingSource;
            this.gridControl1.Location = new System.Drawing.Point(447, 44);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(398, 437);
            this.gridControl1.TabIndex = 10;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // absenceRegulationBindingSource
            // 
            this.absenceRegulationBindingSource.DataSource = typeof(EasyStock.HR.Models.AbsenceRegulation);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colName});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // colName
            // 
            this.colName.FieldName = "Name";
            this.colName.Name = "colName";
            this.colName.OptionsColumn.AllowEdit = false;
            this.colName.Visible = true;
            this.colName.VisibleIndex = 0;
            this.colName.Width = 63;
            // 
            // IDTextEdit
            // 
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.absenceRegulationBindingSource, "ID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.IDTextEdit.Location = new System.Drawing.Point(248, 44);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.EditMask = "N0";
            this.IDTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(171, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 4;
            // 
            // NameTextEdit
            // 
            this.NameTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.absenceRegulationBindingSource, "Name", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NameTextEdit.Location = new System.Drawing.Point(248, 68);
            this.NameTextEdit.Name = "NameTextEdit";
            this.NameTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.NameTextEdit.Size = new System.Drawing.Size(171, 20);
            this.NameTextEdit.StyleController = this.dataLayoutControl1;
            this.NameTextEdit.TabIndex = 5;
            // 
            // DayPermissionDayDeductionTextEdit
            // 
            this.DayPermissionDayDeductionTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.absenceRegulationBindingSource, "DayPermissionDayDeduction", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DayPermissionDayDeductionTextEdit.Location = new System.Drawing.Point(248, 92);
            this.DayPermissionDayDeductionTextEdit.Name = "DayPermissionDayDeductionTextEdit";
            this.DayPermissionDayDeductionTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DayPermissionDayDeductionTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DayPermissionDayDeductionTextEdit.Properties.Mask.EditMask = "F";
            this.DayPermissionDayDeductionTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.DayPermissionDayDeductionTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.DayPermissionDayDeductionTextEdit.Size = new System.Drawing.Size(171, 20);
            this.DayPermissionDayDeductionTextEdit.StyleController = this.dataLayoutControl1;
            this.DayPermissionDayDeductionTextEdit.TabIndex = 6;
            // 
            // DayPermissionFixedAmountTextEdit
            // 
            this.DayPermissionFixedAmountTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.absenceRegulationBindingSource, "DayPermissionFixedAmount", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DayPermissionFixedAmountTextEdit.Location = new System.Drawing.Point(248, 116);
            this.DayPermissionFixedAmountTextEdit.Name = "DayPermissionFixedAmountTextEdit";
            this.DayPermissionFixedAmountTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DayPermissionFixedAmountTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DayPermissionFixedAmountTextEdit.Properties.Mask.EditMask = "F";
            this.DayPermissionFixedAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.DayPermissionFixedAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.DayPermissionFixedAmountTextEdit.Size = new System.Drawing.Size(171, 20);
            this.DayPermissionFixedAmountTextEdit.StyleController = this.dataLayoutControl1;
            this.DayPermissionFixedAmountTextEdit.TabIndex = 7;
            // 
            // NonDayPermissionDayDeductionTextEdit
            // 
            this.NonDayPermissionDayDeductionTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.absenceRegulationBindingSource, "NonDayPermissionDayDeduction", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NonDayPermissionDayDeductionTextEdit.Location = new System.Drawing.Point(248, 140);
            this.NonDayPermissionDayDeductionTextEdit.Name = "NonDayPermissionDayDeductionTextEdit";
            this.NonDayPermissionDayDeductionTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.NonDayPermissionDayDeductionTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.NonDayPermissionDayDeductionTextEdit.Properties.Mask.EditMask = "F";
            this.NonDayPermissionDayDeductionTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.NonDayPermissionDayDeductionTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.NonDayPermissionDayDeductionTextEdit.Size = new System.Drawing.Size(171, 20);
            this.NonDayPermissionDayDeductionTextEdit.StyleController = this.dataLayoutControl1;
            this.NonDayPermissionDayDeductionTextEdit.TabIndex = 8;
            // 
            // NonDayPermissionFixedAmountTextEdit
            // 
            this.NonDayPermissionFixedAmountTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.absenceRegulationBindingSource, "NonDayPermissionFixedAmount", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NonDayPermissionFixedAmountTextEdit.Location = new System.Drawing.Point(248, 164);
            this.NonDayPermissionFixedAmountTextEdit.Name = "NonDayPermissionFixedAmountTextEdit";
            this.NonDayPermissionFixedAmountTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.NonDayPermissionFixedAmountTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.NonDayPermissionFixedAmountTextEdit.Properties.Mask.EditMask = "F";
            this.NonDayPermissionFixedAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.NonDayPermissionFixedAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.NonDayPermissionFixedAmountTextEdit.Size = new System.Drawing.Size(171, 20);
            this.NonDayPermissionFixedAmountTextEdit.StyleController = this.dataLayoutControl1;
            this.NonDayPermissionFixedAmountTextEdit.TabIndex = 9;
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(869, 505);
            this.Root.TextVisible = false;
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.emptySpaceItem2,
            this.layoutControlGroup2,
            this.emptySpaceItem1,
            this.layoutControlGroup3});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(849, 485);
            // 
            // emptySpaceItem2
            // 
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(10, 188);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(413, 297);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlGroup2
            // 
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForID,
            this.ItemForName,
            this.ItemForDayPermissionDayDeduction,
            this.ItemForDayPermissionFixedAmount,
            this.ItemForNonDayPermissionDayDeduction,
            this.ItemForNonDayPermissionFixedAmount});
            this.layoutControlGroup2.Location = new System.Drawing.Point(10, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(413, 188);
            this.layoutControlGroup2.Text = "Informations";
            // 
            // ItemForID
            // 
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(389, 24);
            this.ItemForID.TextSize = new System.Drawing.Size(210, 13);
            // 
            // ItemForName
            // 
            this.ItemForName.Control = this.NameTextEdit;
            this.ItemForName.Location = new System.Drawing.Point(0, 24);
            this.ItemForName.Name = "ItemForName";
            this.ItemForName.Size = new System.Drawing.Size(389, 24);
            this.ItemForName.TextSize = new System.Drawing.Size(210, 13);
            // 
            // ItemForDayPermissionDayDeduction
            // 
            this.ItemForDayPermissionDayDeduction.Control = this.DayPermissionDayDeductionTextEdit;
            this.ItemForDayPermissionDayDeduction.Location = new System.Drawing.Point(0, 48);
            this.ItemForDayPermissionDayDeduction.Name = "ItemForDayPermissionDayDeduction";
            this.ItemForDayPermissionDayDeduction.Size = new System.Drawing.Size(389, 24);
            this.ItemForDayPermissionDayDeduction.TextSize = new System.Drawing.Size(210, 13);
            // 
            // ItemForDayPermissionFixedAmount
            // 
            this.ItemForDayPermissionFixedAmount.Control = this.DayPermissionFixedAmountTextEdit;
            this.ItemForDayPermissionFixedAmount.Location = new System.Drawing.Point(0, 72);
            this.ItemForDayPermissionFixedAmount.Name = "ItemForDayPermissionFixedAmount";
            this.ItemForDayPermissionFixedAmount.Size = new System.Drawing.Size(389, 24);
            this.ItemForDayPermissionFixedAmount.TextSize = new System.Drawing.Size(210, 13);
            // 
            // ItemForNonDayPermissionDayDeduction
            // 
            this.ItemForNonDayPermissionDayDeduction.Control = this.NonDayPermissionDayDeductionTextEdit;
            this.ItemForNonDayPermissionDayDeduction.Location = new System.Drawing.Point(0, 96);
            this.ItemForNonDayPermissionDayDeduction.Name = "ItemForNonDayPermissionDayDeduction";
            this.ItemForNonDayPermissionDayDeduction.Size = new System.Drawing.Size(389, 24);
            this.ItemForNonDayPermissionDayDeduction.TextSize = new System.Drawing.Size(210, 13);
            // 
            // ItemForNonDayPermissionFixedAmount
            // 
            this.ItemForNonDayPermissionFixedAmount.Control = this.NonDayPermissionFixedAmountTextEdit;
            this.ItemForNonDayPermissionFixedAmount.Location = new System.Drawing.Point(0, 120);
            this.ItemForNonDayPermissionFixedAmount.MaxSize = new System.Drawing.Size(389, 24);
            this.ItemForNonDayPermissionFixedAmount.MinSize = new System.Drawing.Size(389, 24);
            this.ItemForNonDayPermissionFixedAmount.Name = "ItemForNonDayPermissionFixedAmount";
            this.ItemForNonDayPermissionFixedAmount.Size = new System.Drawing.Size(389, 24);
            this.ItemForNonDayPermissionFixedAmount.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForNonDayPermissionFixedAmount.TextSize = new System.Drawing.Size(210, 13);
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 0);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(10, 485);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlGroup3
            // 
            this.layoutControlGroup3.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem1});
            this.layoutControlGroup3.Location = new System.Drawing.Point(423, 0);
            this.layoutControlGroup3.Name = "layoutControlGroup3";
            this.layoutControlGroup3.Size = new System.Drawing.Size(426, 485);
            this.layoutControlGroup3.Text = " ";
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.gridControl1;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem1.MaxSize = new System.Drawing.Size(402, 0);
            this.layoutControlItem1.MinSize = new System.Drawing.Size(402, 24);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(402, 441);
            this.layoutControlItem1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            // 
            // AbcenceRegulationView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(869, 555);
            this.Controls.Add(this.dataLayoutControl1);
            this.Name = "AbcenceRegulationView";
            this.Text = "Liste des congés";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.absenceRegulationBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NameTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayPermissionDayDeductionTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayPermissionFixedAmountTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NonDayPermissionDayDeductionTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NonDayPermissionFixedAmountTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDayPermissionDayDeduction)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDayPermissionFixedAmount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNonDayPermissionDayDeduction)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNonDayPermissionFixedAmount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x04000111 RID: 273
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04000112 RID: 274
		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		// Token: 0x04000113 RID: 275
		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		// Token: 0x04000114 RID: 276
		private global::System.Windows.Forms.BindingSource absenceRegulationBindingSource;

		// Token: 0x04000115 RID: 277
		private global::DevExpress.XtraEditors.TextEdit NameTextEdit;

		// Token: 0x04000116 RID: 278
		private global::DevExpress.XtraEditors.TextEdit DayPermissionDayDeductionTextEdit;

		// Token: 0x04000117 RID: 279
		private global::DevExpress.XtraEditors.TextEdit DayPermissionFixedAmountTextEdit;

		// Token: 0x04000118 RID: 280
		private global::DevExpress.XtraEditors.TextEdit NonDayPermissionDayDeductionTextEdit;

		// Token: 0x04000119 RID: 281
		private global::DevExpress.XtraEditors.TextEdit NonDayPermissionFixedAmountTextEdit;

		// Token: 0x0400011A RID: 282
		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		// Token: 0x0400011B RID: 283
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		// Token: 0x0400011C RID: 284
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		// Token: 0x0400011D RID: 285
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForName;

		// Token: 0x0400011E RID: 286
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDayPermissionDayDeduction;

		// Token: 0x0400011F RID: 287
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDayPermissionFixedAmount;

		// Token: 0x04000120 RID: 288
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForNonDayPermissionDayDeduction;

		// Token: 0x04000121 RID: 289
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForNonDayPermissionFixedAmount;

		// Token: 0x04000122 RID: 290
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;

		// Token: 0x04000123 RID: 291
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;

		// Token: 0x04000124 RID: 292
		private global::DevExpress.XtraGrid.GridControl gridControl1;

		// Token: 0x04000125 RID: 293
		private global::DevExpress.XtraGrid.Views.Grid.GridView gridView1;

		// Token: 0x04000126 RID: 294
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;

		// Token: 0x04000127 RID: 295
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup3;

		// Token: 0x04000128 RID: 296
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;

		// Token: 0x04000129 RID: 297
		private global::DevExpress.XtraGrid.Columns.GridColumn colName;
	}
}
