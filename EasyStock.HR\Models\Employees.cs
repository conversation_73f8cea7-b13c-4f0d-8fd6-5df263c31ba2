namespace EasyStock.HR.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;

    public partial class Employees
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Employees()
        {
            EmpSalaryExtensions = new HashSet<EmpSalaryExtensions>();
            EmpSalaryExtensions1 = new HashSet<EmpSalaryExtensions>();
            EmpSalaryExtensions2 = new HashSet<EmpSalaryExtensions>();
            PenaltyRewards = new HashSet<PenaltyRewards>();
            WorkLeaveReturns = new HashSet<WorkLeaveReturns>();
        }

        public int ID { get; set; }

        [Required]
        [StringLength(50)]
        public string Code { get; set; }

        [Required]
        [StringLength(150)]
        public string Name { get; set; }

        public int Gender { get; set; }

        public int? Branch { get; set; }

        public int? DepartmentID { get; set; }

        public int? GroupID { get; set; }

        [StringLength(50)]
        public string NationalID { get; set; }

        [StringLength(50)]
        public string InsuranceNo { get; set; }

        public DateTime? InsuranceDate { get; set; }

        [StringLength(50)]
        public string DrivingLicense { get; set; }

        public DateTime? EndDate { get; set; }

        public string BankName { get; set; }

        public string AccountNo { get; set; }

        public DateTime? BirthDate { get; set; }

        public int MaritalStatus { get; set; }

        public int MilitarilyStatus { get; set; }

        [StringLength(20)]
        public string Phone { get; set; }

        [StringLength(50)]
        public string Email { get; set; }

        [StringLength(150)]
        public string Address { get; set; }

        public int FingerprintCode { get; set; }

        public int ContractType { get; set; }

        public string ContractPeriod { get; set; }

        public DateTime? ContactEndDate { get; set; }

        public DateTime? DateOfRecruitment { get; set; }

        public int? JobID { get; set; }

        public int? AbsenceRegistionID { get; set; }

        public int? DelayRegulationID { get; set; }

        public int? OverTimeRegulationID { get; set; }

        public int? AbsenceRegulation_ID { get; set; }

        public int? FristShiftId { get; set; }

        public int? SecondShiftId { get; set; }

        public int PayPeriod { get; set; }

        public double SalaryBasic { get; set; }

        public double SalaryVariable { get; set; }

        public double DayValue { get; set; }

        public double HourValue { get; set; }

        public bool CalcIncomeTax { get; set; }

        public int? FirstShift_ID { get; set; }

        public int? ExpensesAccountId { get; set; }

        public int? AccruedAccountId { get; set; }

        public int? NationalityId { get; set; }

        public int? ReligionId { get; set; }

        public int? QualificationId { get; set; }

        public int EmpState { get; set; }

        public int? BirthPlaceId { get; set; }

        public byte[] Photo { get; set; }

        public virtual AbsenceRegulations AbsenceRegulations { get; set; }

        public virtual Departments Departments { get; set; }

        public virtual Groups Groups { get; set; }

        public virtual Jobs Jobs { get; set; }

        public virtual OvertimeAndDelayRegulations OvertimeAndDelayRegulations { get; set; }

        public virtual OvertimeAndDelayRegulations OvertimeAndDelayRegulations1 { get; set; }

        public virtual References References { get; set; }

        public virtual References References1 { get; set; }

        public virtual References References2 { get; set; }

        public virtual References References3 { get; set; }

        public virtual Shifts Shifts { get; set; }

        public virtual Shifts Shifts1 { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<EmpSalaryExtensions> EmpSalaryExtensions { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<EmpSalaryExtensions> EmpSalaryExtensions1 { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<EmpSalaryExtensions> EmpSalaryExtensions2 { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PenaltyRewards> PenaltyRewards { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<WorkLeaveReturns> WorkLeaveReturns { get; set; }
    }
}
