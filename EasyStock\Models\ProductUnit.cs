﻿using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Unité")]
    public class ProductUnit : BaseNotifyPropertyChangedModel
    {
        private int id;

        private Product product;

        private int productID;

        private UnitOfMeasurement unit;

        private int _unitNameID;

        private double buyPrice;

        private double sellPrice;

        private double sellDiscount;

        private double factor;

        private bool _defualtBuy;

        private bool _defualtSell;
        [Display(Name = "Numéros de code-barres")]
        public ICollection<ProductUnitBarcode> Barcodes { get; set; }

        public ProductUnit()
        {
            Barcodes = new BindingList<ProductUnitBarcode>();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [ReadOnly(true)]
        [Display(Name = "Code")]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Article")]
        public Product Product
        {
            get
            {
                return this.product;
            }
            set
            {
                base.SetProperty<Product>(ref this.product, value, "Product");
            }
        }

        [Display(Name = "Article Code")]
        public int ProductID
        {
            get
            {
                return this.productID;
            }
            set
            {
                base.SetProperty<int>(ref this.productID, value, "ProductID");
            }
        }

        [Display(Name = "Nom de l'unité")]
        public UnitOfMeasurement UnitName
        {
            get
            {
                return this.unit;
            }
            set
            {
                base.SetProperty<UnitOfMeasurement>(ref this.unit, value, "UnitName");
            }
        }

        [Display(Name = "Nom de l'unité")]
        [Range(1, 2147483647, ErrorMessage = "Vous devez choisir le nom de l'unité")]
        public int UnitNameID
        {
            get
            {
                return this._unitNameID;
            }
            set
            {
                base.SetProperty<int>(ref this._unitNameID, value, "UnitNameID");
            }
        }
        [Display(Name = "Achat Prix")]
        public double BuyPrice
        {
            get
            {
                return this.buyPrice;
            }
            set
            {
                base.SetProperty<double>(ref this.buyPrice, value, "BuyPrice");
            }
        }

        [Display(Name = "Vente Prix")]
        public double SellPrice
        {
            get
            {
                return this.sellPrice;
            }
            set
            {
                base.SetProperty<double>(ref this.sellPrice, value, "SellPrice");
            }
        }

        [Display(Name = "Remise de vente")]
        public double SellDiscount
        {
            get
            {
                return this.sellDiscount;
            }
            set
            {
                base.SetProperty<double>(ref this.sellDiscount, value, "SellDiscount");
            }
        }


        [Display(Name = "Coefficient unitaire")]
        [Range(1.0, 1.7976931348623157E+308, ErrorMessage = "La valeur du paramètre doit être supérieure à un")]
        public double Factor
        {
            get
            {
                return this.factor;
            }
            set
            {
                base.SetProperty<double>(ref this.factor, value, "Factor");
            }
        }

        [Display(Name = "Achat par défaut")]
        public bool DefualtBuy
        {
            get
            {
                return this._defualtBuy;
            }
            set
            {
                base.SetProperty<bool>(ref this._defualtBuy, value, "DefualtBuy");
            }
        }

        [Display(Name = "Vente par défaut")]
        public bool DefualtSell
        {
            get
            {
                return this._defualtSell;
            }
            set
            {
                base.SetProperty<bool>(ref this._defualtSell, value, "DefualtSell");
            }
        }

    }
}
