﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x02000494 RID: 1172
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class addusertocashnotes : DbMigration, IMigrationMetadata
	{
		// Token: 0x06002328 RID: 9000 RVA: 0x000123DF File Offset: 0x000105DF
		public override void Up()
		{
			base.AddColumn("dbo.CashNotes", "UserID", (ColumnBuilder c) => c.Int(new bool?(false), false, new int?(1), null, null, null, null), null);
		}

		// Token: 0x06002329 RID: 9001 RVA: 0x00006A8E File Offset: 0x00004C8E
		public override void Down()
		{
		}

		// Token: 0x17000B8B RID: 2955
		// (get) Token: 0x0600232A RID: 9002 RVA: 0x001F15F4 File Offset: 0x001EF7F4
		string IMigrationMetadata.Id
		{
			get
			{
				return "202106071940527_addusertocashnotes";
			}
		}

		// Token: 0x17000B8C RID: 2956
		// (get) Token: 0x0600232B RID: 9003 RVA: 0x001F160C File Offset: 0x001EF80C
		string IMigrationMetadata.Source
		{
			get
			{
				return this.Resources.GetString("Source");
			}
		}

		// Token: 0x17000B8D RID: 2957
		// (get) Token: 0x0600232C RID: 9004 RVA: 0x001F1630 File Offset: 0x001EF830
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B0F RID: 11023
		private readonly ResourceManager Resources = new ResourceManager(typeof(addusertocashnotes));
	}
}
