﻿namespace EasyStock.HR.Views
{
	// Token: 0x02000046 RID: 70
	public partial class SalaryRegulationView : global::EasyStock.HR.MainViews.MasterView
	{
		// Token: 0x0600026D RID: 621 RVA: 0x0002D55C File Offset: 0x0002B75C
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600026E RID: 622 RVA: 0x0002D594 File Offset: 0x0002B794
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.salaryRegulationBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.NameTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.CostCenterTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.ExpensesAccountTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.BenefitsAccountTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.DayValueTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.HourValueTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.SalaryPeriodImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.SalaryCalculationImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.DefaultSalaryTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.EquationTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForName = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForCostCenter = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForExpensesAccount = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForBenefitsAccount = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDayValue = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForHourValue = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForSalaryPeriod = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForSalaryCalculation = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDefaultSalary = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForEquation = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup3 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.salaryRegulationBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NameTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.CostCenterTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ExpensesAccountTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BenefitsAccountTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayValueTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.HourValueTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.SalaryPeriodImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.SalaryCalculationImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DefaultSalaryTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EquationTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCostCenter)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForExpensesAccount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBenefitsAccount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDayValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForHourValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSalaryPeriod)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSalaryCalculation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDefaultSalary)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEquation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            this.SuspendLayout();
            // 
            // dataLayoutControl1
            // 
            this.dataLayoutControl1.Controls.Add(this.gridControl1);
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NameTextEdit);
            this.dataLayoutControl1.Controls.Add(this.CostCenterTextEdit);
            this.dataLayoutControl1.Controls.Add(this.ExpensesAccountTextEdit);
            this.dataLayoutControl1.Controls.Add(this.BenefitsAccountTextEdit);
            this.dataLayoutControl1.Controls.Add(this.DayValueTextEdit);
            this.dataLayoutControl1.Controls.Add(this.HourValueTextEdit);
            this.dataLayoutControl1.Controls.Add(this.SalaryPeriodImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.SalaryCalculationImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.DefaultSalaryTextEdit);
            this.dataLayoutControl1.Controls.Add(this.EquationTextEdit);
            this.dataLayoutControl1.DataSource = this.salaryRegulationBindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 26);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(800, 400);
            this.dataLayoutControl1.TabIndex = 0;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            // 
            // gridControl1
            // 
            this.gridControl1.DataSource = this.salaryRegulationBindingSource;
            this.gridControl1.Location = new System.Drawing.Point(441, 44);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(335, 260);
            this.gridControl1.TabIndex = 15;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // salaryRegulationBindingSource
            // 
            this.salaryRegulationBindingSource.DataSource = typeof(EasyStock.HR.Models.SalaryRegulation);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colName});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // colName
            // 
            this.colName.FieldName = "Name";
            this.colName.Name = "colName";
            this.colName.Visible = true;
            this.colName.VisibleIndex = 0;
            // 
            // IDTextEdit
            // 
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryRegulationBindingSource, "ID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.IDTextEdit.Location = new System.Drawing.Point(214, 44);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.EditMask = "N0";
            this.IDTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(199, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 4;
            // 
            // NameTextEdit
            // 
            this.NameTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryRegulationBindingSource, "Name", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NameTextEdit.Location = new System.Drawing.Point(214, 68);
            this.NameTextEdit.Name = "NameTextEdit";
            this.NameTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.NameTextEdit.Size = new System.Drawing.Size(199, 20);
            this.NameTextEdit.StyleController = this.dataLayoutControl1;
            this.NameTextEdit.TabIndex = 5;
            // 
            // CostCenterTextEdit
            // 
            this.CostCenterTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryRegulationBindingSource, "CostCenter", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.CostCenterTextEdit.Location = new System.Drawing.Point(214, 92);
            this.CostCenterTextEdit.Name = "CostCenterTextEdit";
            this.CostCenterTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.CostCenterTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.CostCenterTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.CostCenterTextEdit.Properties.Mask.EditMask = "N0";
            this.CostCenterTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.CostCenterTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.CostCenterTextEdit.Size = new System.Drawing.Size(199, 20);
            this.CostCenterTextEdit.StyleController = this.dataLayoutControl1;
            this.CostCenterTextEdit.TabIndex = 6;
            // 
            // ExpensesAccountTextEdit
            // 
            this.ExpensesAccountTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryRegulationBindingSource, "ExpensesAccount", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ExpensesAccountTextEdit.Location = new System.Drawing.Point(214, 116);
            this.ExpensesAccountTextEdit.Name = "ExpensesAccountTextEdit";
            this.ExpensesAccountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.ExpensesAccountTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.ExpensesAccountTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.ExpensesAccountTextEdit.Properties.Mask.EditMask = "N0";
            this.ExpensesAccountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.ExpensesAccountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.ExpensesAccountTextEdit.Size = new System.Drawing.Size(199, 20);
            this.ExpensesAccountTextEdit.StyleController = this.dataLayoutControl1;
            this.ExpensesAccountTextEdit.TabIndex = 7;
            // 
            // BenefitsAccountTextEdit
            // 
            this.BenefitsAccountTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryRegulationBindingSource, "BenefitsAccount", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.BenefitsAccountTextEdit.Location = new System.Drawing.Point(214, 140);
            this.BenefitsAccountTextEdit.Name = "BenefitsAccountTextEdit";
            this.BenefitsAccountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.BenefitsAccountTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.BenefitsAccountTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.BenefitsAccountTextEdit.Properties.Mask.EditMask = "N0";
            this.BenefitsAccountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.BenefitsAccountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.BenefitsAccountTextEdit.Size = new System.Drawing.Size(199, 20);
            this.BenefitsAccountTextEdit.StyleController = this.dataLayoutControl1;
            this.BenefitsAccountTextEdit.TabIndex = 8;
            // 
            // DayValueTextEdit
            // 
            this.DayValueTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryRegulationBindingSource, "DayValue", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DayValueTextEdit.Location = new System.Drawing.Point(214, 164);
            this.DayValueTextEdit.Name = "DayValueTextEdit";
            this.DayValueTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.DayValueTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DayValueTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DayValueTextEdit.Properties.Mask.EditMask = "F";
            this.DayValueTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.DayValueTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.DayValueTextEdit.Size = new System.Drawing.Size(199, 20);
            this.DayValueTextEdit.StyleController = this.dataLayoutControl1;
            this.DayValueTextEdit.TabIndex = 9;
            // 
            // HourValueTextEdit
            // 
            this.HourValueTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryRegulationBindingSource, "HourValue", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.HourValueTextEdit.Location = new System.Drawing.Point(214, 188);
            this.HourValueTextEdit.Name = "HourValueTextEdit";
            this.HourValueTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.HourValueTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.HourValueTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.HourValueTextEdit.Properties.Mask.EditMask = "F";
            this.HourValueTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.HourValueTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.HourValueTextEdit.Size = new System.Drawing.Size(199, 20);
            this.HourValueTextEdit.StyleController = this.dataLayoutControl1;
            this.HourValueTextEdit.TabIndex = 10;
            // 
            // SalaryPeriodImageComboBoxEdit
            // 
            this.SalaryPeriodImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryRegulationBindingSource, "SalaryPeriod", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.SalaryPeriodImageComboBoxEdit.Location = new System.Drawing.Point(214, 212);
            this.SalaryPeriodImageComboBoxEdit.Name = "SalaryPeriodImageComboBoxEdit";
            this.SalaryPeriodImageComboBoxEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.SalaryPeriodImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.SalaryPeriodImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.SalaryPeriodImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.SalaryPeriodImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Semaine", EasyStock.HR.ESalaryPeriod.Week, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Deux semaines", EasyStock.HR.ESalaryPeriod.TwoWeek, 1),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Mois", EasyStock.HR.ESalaryPeriod.Month, 2),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("À la pièce", EasyStock.HR.ESalaryPeriod.Piece, 3)});
            this.SalaryPeriodImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.SalaryPeriodImageComboBoxEdit.Size = new System.Drawing.Size(199, 20);
            this.SalaryPeriodImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.SalaryPeriodImageComboBoxEdit.TabIndex = 11;
            // 
            // SalaryCalculationImageComboBoxEdit
            // 
            this.SalaryCalculationImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryRegulationBindingSource, "SalaryCalculation", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.SalaryCalculationImageComboBoxEdit.Location = new System.Drawing.Point(214, 236);
            this.SalaryCalculationImageComboBoxEdit.Name = "SalaryCalculationImageComboBoxEdit";
            this.SalaryCalculationImageComboBoxEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.SalaryCalculationImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.SalaryCalculationImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.SalaryCalculationImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.SalaryCalculationImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Montant fixe", EasyStock.HR.ESalaryCalculation.FixedAmount, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Équation", EasyStock.HR.ESalaryCalculation.Equation, 1)});
            this.SalaryCalculationImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.SalaryCalculationImageComboBoxEdit.Size = new System.Drawing.Size(199, 20);
            this.SalaryCalculationImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.SalaryCalculationImageComboBoxEdit.TabIndex = 12;
            // 
            // DefaultSalaryTextEdit
            // 
            this.DefaultSalaryTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryRegulationBindingSource, "DefaultSalary", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DefaultSalaryTextEdit.Location = new System.Drawing.Point(214, 260);
            this.DefaultSalaryTextEdit.Name = "DefaultSalaryTextEdit";
            this.DefaultSalaryTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DefaultSalaryTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DefaultSalaryTextEdit.Properties.Mask.EditMask = "F";
            this.DefaultSalaryTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.DefaultSalaryTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.DefaultSalaryTextEdit.Size = new System.Drawing.Size(199, 20);
            this.DefaultSalaryTextEdit.StyleController = this.dataLayoutControl1;
            this.DefaultSalaryTextEdit.TabIndex = 13;
            // 
            // EquationTextEdit
            // 
            this.EquationTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.salaryRegulationBindingSource, "Equation", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.EquationTextEdit.Location = new System.Drawing.Point(214, 284);
            this.EquationTextEdit.Name = "EquationTextEdit";
            this.EquationTextEdit.Size = new System.Drawing.Size(199, 20);
            this.EquationTextEdit.StyleController = this.dataLayoutControl1;
            this.EquationTextEdit.TabIndex = 14;
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(800, 400);
            this.Root.TextVisible = false;
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup2,
            this.emptySpaceItem1,
            this.emptySpaceItem2,
            this.layoutControlGroup3});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(780, 380);
            // 
            // layoutControlGroup2
            // 
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForID,
            this.ItemForName,
            this.ItemForCostCenter,
            this.ItemForExpensesAccount,
            this.ItemForBenefitsAccount,
            this.ItemForDayValue,
            this.ItemForHourValue,
            this.ItemForSalaryPeriod,
            this.ItemForSalaryCalculation,
            this.ItemForDefaultSalary,
            this.ItemForEquation});
            this.layoutControlGroup2.Location = new System.Drawing.Point(10, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(407, 308);
            this.layoutControlGroup2.Text = "Informations";
            // 
            // ItemForID
            // 
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.MaxSize = new System.Drawing.Size(383, 24);
            this.ItemForID.MinSize = new System.Drawing.Size(383, 24);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(383, 24);
            this.ItemForID.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForID.TextSize = new System.Drawing.Size(176, 13);
            // 
            // ItemForName
            // 
            this.ItemForName.Control = this.NameTextEdit;
            this.ItemForName.Location = new System.Drawing.Point(0, 24);
            this.ItemForName.Name = "ItemForName";
            this.ItemForName.Size = new System.Drawing.Size(383, 24);
            this.ItemForName.TextSize = new System.Drawing.Size(176, 13);
            // 
            // ItemForCostCenter
            // 
            this.ItemForCostCenter.Control = this.CostCenterTextEdit;
            this.ItemForCostCenter.Location = new System.Drawing.Point(0, 48);
            this.ItemForCostCenter.Name = "ItemForCostCenter";
            this.ItemForCostCenter.Size = new System.Drawing.Size(383, 24);
            this.ItemForCostCenter.TextSize = new System.Drawing.Size(176, 13);
            // 
            // ItemForExpensesAccount
            // 
            this.ItemForExpensesAccount.Control = this.ExpensesAccountTextEdit;
            this.ItemForExpensesAccount.Location = new System.Drawing.Point(0, 72);
            this.ItemForExpensesAccount.Name = "ItemForExpensesAccount";
            this.ItemForExpensesAccount.Size = new System.Drawing.Size(383, 24);
            this.ItemForExpensesAccount.TextSize = new System.Drawing.Size(176, 13);
            // 
            // ItemForBenefitsAccount
            // 
            this.ItemForBenefitsAccount.Control = this.BenefitsAccountTextEdit;
            this.ItemForBenefitsAccount.Location = new System.Drawing.Point(0, 96);
            this.ItemForBenefitsAccount.Name = "ItemForBenefitsAccount";
            this.ItemForBenefitsAccount.Size = new System.Drawing.Size(383, 24);
            this.ItemForBenefitsAccount.TextSize = new System.Drawing.Size(176, 13);
            // 
            // ItemForDayValue
            // 
            this.ItemForDayValue.Control = this.DayValueTextEdit;
            this.ItemForDayValue.Location = new System.Drawing.Point(0, 120);
            this.ItemForDayValue.Name = "ItemForDayValue";
            this.ItemForDayValue.Size = new System.Drawing.Size(383, 24);
            this.ItemForDayValue.TextSize = new System.Drawing.Size(176, 13);
            // 
            // ItemForHourValue
            // 
            this.ItemForHourValue.Control = this.HourValueTextEdit;
            this.ItemForHourValue.Location = new System.Drawing.Point(0, 144);
            this.ItemForHourValue.Name = "ItemForHourValue";
            this.ItemForHourValue.Size = new System.Drawing.Size(383, 24);
            this.ItemForHourValue.TextSize = new System.Drawing.Size(176, 13);
            // 
            // ItemForSalaryPeriod
            // 
            this.ItemForSalaryPeriod.Control = this.SalaryPeriodImageComboBoxEdit;
            this.ItemForSalaryPeriod.Location = new System.Drawing.Point(0, 168);
            this.ItemForSalaryPeriod.Name = "ItemForSalaryPeriod";
            this.ItemForSalaryPeriod.Size = new System.Drawing.Size(383, 24);
            this.ItemForSalaryPeriod.TextSize = new System.Drawing.Size(176, 13);
            // 
            // ItemForSalaryCalculation
            // 
            this.ItemForSalaryCalculation.Control = this.SalaryCalculationImageComboBoxEdit;
            this.ItemForSalaryCalculation.Location = new System.Drawing.Point(0, 192);
            this.ItemForSalaryCalculation.Name = "ItemForSalaryCalculation";
            this.ItemForSalaryCalculation.Size = new System.Drawing.Size(383, 24);
            this.ItemForSalaryCalculation.TextSize = new System.Drawing.Size(176, 13);
            // 
            // ItemForDefaultSalary
            // 
            this.ItemForDefaultSalary.Control = this.DefaultSalaryTextEdit;
            this.ItemForDefaultSalary.Location = new System.Drawing.Point(0, 216);
            this.ItemForDefaultSalary.Name = "ItemForDefaultSalary";
            this.ItemForDefaultSalary.Size = new System.Drawing.Size(383, 24);
            this.ItemForDefaultSalary.TextSize = new System.Drawing.Size(176, 13);
            // 
            // ItemForEquation
            // 
            this.ItemForEquation.Control = this.EquationTextEdit;
            this.ItemForEquation.Location = new System.Drawing.Point(0, 240);
            this.ItemForEquation.Name = "ItemForEquation";
            this.ItemForEquation.Size = new System.Drawing.Size(383, 24);
            this.ItemForEquation.TextSize = new System.Drawing.Size(176, 13);
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 0);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(10, 308);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem2
            // 
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(0, 308);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(780, 72);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlGroup3
            // 
            this.layoutControlGroup3.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem1});
            this.layoutControlGroup3.Location = new System.Drawing.Point(417, 0);
            this.layoutControlGroup3.Name = "layoutControlGroup3";
            this.layoutControlGroup3.Size = new System.Drawing.Size(363, 308);
            this.layoutControlGroup3.Text = " ";
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.gridControl1;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem1.MaxSize = new System.Drawing.Size(339, 264);
            this.layoutControlItem1.MinSize = new System.Drawing.Size(339, 264);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(339, 264);
            this.layoutControlItem1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            // 
            // SalaryRegulationView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.dataLayoutControl1);
            this.Name = "SalaryRegulationView";
            this.Text = "La liste des salaires";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.salaryRegulationBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NameTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.CostCenterTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ExpensesAccountTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BenefitsAccountTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DayValueTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.HourValueTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.SalaryPeriodImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.SalaryCalculationImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DefaultSalaryTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EquationTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCostCenter)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForExpensesAccount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBenefitsAccount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDayValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForHourValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSalaryPeriod)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSalaryCalculation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDefaultSalary)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEquation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x04000413 RID: 1043
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04000414 RID: 1044
		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		// Token: 0x04000415 RID: 1045
		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		// Token: 0x04000416 RID: 1046
		private global::System.Windows.Forms.BindingSource salaryRegulationBindingSource;

		// Token: 0x04000417 RID: 1047
		private global::DevExpress.XtraEditors.TextEdit NameTextEdit;

		// Token: 0x04000418 RID: 1048
		private global::DevExpress.XtraEditors.TextEdit CostCenterTextEdit;

		// Token: 0x04000419 RID: 1049
		private global::DevExpress.XtraEditors.TextEdit ExpensesAccountTextEdit;

		// Token: 0x0400041A RID: 1050
		private global::DevExpress.XtraEditors.TextEdit BenefitsAccountTextEdit;

		// Token: 0x0400041B RID: 1051
		private global::DevExpress.XtraEditors.TextEdit DayValueTextEdit;

		// Token: 0x0400041C RID: 1052
		private global::DevExpress.XtraEditors.TextEdit HourValueTextEdit;

		// Token: 0x0400041D RID: 1053
		private global::DevExpress.XtraEditors.ImageComboBoxEdit SalaryPeriodImageComboBoxEdit;

		// Token: 0x0400041E RID: 1054
		private global::DevExpress.XtraEditors.ImageComboBoxEdit SalaryCalculationImageComboBoxEdit;

		// Token: 0x0400041F RID: 1055
		private global::DevExpress.XtraEditors.TextEdit DefaultSalaryTextEdit;

		// Token: 0x04000420 RID: 1056
		private global::DevExpress.XtraEditors.TextEdit EquationTextEdit;

		// Token: 0x04000421 RID: 1057
		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		// Token: 0x04000422 RID: 1058
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		// Token: 0x04000423 RID: 1059
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		// Token: 0x04000424 RID: 1060
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForName;

		// Token: 0x04000425 RID: 1061
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForCostCenter;

		// Token: 0x04000426 RID: 1062
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForExpensesAccount;

		// Token: 0x04000427 RID: 1063
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForBenefitsAccount;

		// Token: 0x04000428 RID: 1064
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDayValue;

		// Token: 0x04000429 RID: 1065
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForHourValue;

		// Token: 0x0400042A RID: 1066
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForSalaryPeriod;

		// Token: 0x0400042B RID: 1067
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForSalaryCalculation;

		// Token: 0x0400042C RID: 1068
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDefaultSalary;

		// Token: 0x0400042D RID: 1069
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForEquation;

		// Token: 0x0400042E RID: 1070
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;

		// Token: 0x0400042F RID: 1071
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;

		// Token: 0x04000430 RID: 1072
		private global::DevExpress.XtraGrid.GridControl gridControl1;

		// Token: 0x04000431 RID: 1073
		private global::DevExpress.XtraGrid.Views.Grid.GridView gridView1;

		// Token: 0x04000432 RID: 1074
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;

		// Token: 0x04000433 RID: 1075
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup3;

		// Token: 0x04000434 RID: 1076
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;

		// Token: 0x04000435 RID: 1077
		private global::DevExpress.XtraGrid.Columns.GridColumn colName;
	}
}
