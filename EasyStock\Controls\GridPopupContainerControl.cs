﻿using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using System;
using System.Collections.Generic;
using System.Windows.Forms;

namespace EasyStock.Controls
{

    public class GridPopupContainerControl : PopupContainerEdit
    {
        private PopupContainerControl popupContainerControl;

        private GridControl gridControl;

        public GridView gridView;

        private string DisplayText;

        private string valueMember = "ID";

        private string displayMember;

        public new IList<int> EditValue
        {
            get
            {
                return base.EditValue as List<int>;
            }
            set
            {
                base.EditValue = value;
            }
        }

        public string ValueMember
        {
            get
            {
                return valueMember;
            }
            set
            {
                valueMember = value;
            }
        }

        public string DisplayMember
        {
            get
            {
                return displayMember;
            }
            set
            {
                displayMember = value;
            }
        }

        public bool MultiSelect
        {
            get
            {
                return gridView.OptionsSelection.MultiSelect;
            }
            set
            {
                gridView.OptionsSelection.MultiSelect = value;
            }
        }

        public object DataSource
        {
            get
            {
                return gridControl.DataSource;
            }
            set
            {
                gridControl.DataSource = value;
                gridView.BestFitColumns();
            }
        }

        public GridPopupContainerControl()
        {
            popupContainerControl = new PopupContainerControl();
            gridControl = new GridControl();
            gridView = new GridView();
            gridView.OptionsBehavior.Editable = false;
            gridView.OptionsFind.AlwaysVisible = true;
            gridView.OptionsFind.FindNullPrompt = "Saisissez le texte de la recherche";
            gridView.OptionsFind.FindPanelLocation = GridFindPanelLocation.Panel;
            gridView.OptionsFind.SearchInPreview = true;
            gridView.OptionsSelection.MultiSelectMode = GridMultiSelectMode.CheckBoxRowSelect;
            gridView.OptionsView.ShowGroupPanel = false;
            gridView.OptionsBehavior.Editable = false;
            gridView.OptionsSelection.EnableAppearanceFocusedCell = false;
            gridView.OptionsSelection.EnableAppearanceHideSelection = false;
            MultiSelect = true;
            gridView.OptionsSelection.MultiSelectMode = GridMultiSelectMode.CheckBoxRowSelect;
            gridView.OptionsView.ShowGroupPanel = false;
            gridView.FocusRectStyle = DrawFocusRectStyle.RowFocus;
            DisplayMember = "Name";
            ValueMember = "ID";
            gridControl.MainView = gridView;
            gridControl.Dock = DockStyle.Fill;
            gridControl.ViewCollection.AddRange(new BaseView[1] { gridView });
            gridView.GridControl = gridControl;
            popupContainerControl.Controls.Add(gridControl);
            base.Properties.PopupControl = popupContainerControl;
            base.QueryResultValue += GridPopupContainerControl_QueryResultValue;
            base.CustomDisplayText += GridPopupContainerControl_CustomDisplayText;
            base.Properties.Buttons.Clear();
            base.Properties.Buttons.AddRange(new EditorButton[1]
            {
            new EditorButton(ButtonPredefines.DropDown)
            });
            base.ButtonClick += delegate (object sender, ButtonPressedEventArgs e)
            {
                if (!(e.Button.GetType() != typeof(EditorButton)) && e.Button.Kind == ButtonPredefines.Clear)
                {
                    ((BaseEdit)sender).EditValue = null;
                }
            };
            popupContainerControl.Width = base.Width;
            popupContainerControl.Height = 250;
            gridView.Click += GridView_Click;
            gridView.KeyDown += GridView1_KeyDown;
            gridView.OptionsView.ShowAutoFilterRow = true;
            gridView.OptionsDetail.EnableMasterViewMode = false;
            base.KeyPress += GridPopupContainerControl_KeyPress;
        }

        private void GridPopupContainerControl_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!IsPopupOpen)
            {
                ShowPopup();
            }
            gridView.FindFilterText += e.KeyChar;
            gridView.ShowFindPanel();
        }

        private void GridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Return)
            {
                ClosePopup();
            }
            else if (e.KeyCode == Keys.Space)
            {
                gridView.SelectRow(gridView.FocusedRowHandle);
            }
            else if (e.KeyCode == Keys.Back && gridView.FindFilterText.Length <= 0)
            {
            }
        }

        private void GridView_Click(object sender, EventArgs e)
        {
            DXMouseEventArgs ea = e as DXMouseEventArgs;
            GridView view = sender as GridView;
            GridHitInfo info = view.CalcHitInfo(ea.Location);
            if (info.InRow || info.InRowCell)
            {
                if (!MultiSelect)
                {
                    ClosePopup();
                }
                else
                {
                    view.SelectRow(info.RowHandle);
                }
            }
        }

        private void GridPopupContainerControl_CustomDisplayText(object sender, CustomDisplayTextEventArgs e)
        {
            e.DisplayText = ((string.IsNullOrEmpty(DisplayText) && MultiSelect) ? "Tout" : DisplayText?.ToString());
        }

        private void GridPopupContainerControl_QueryResultValue(object sender, QueryResultValueEventArgs e)
        {
            int[] selectedRows = gridView.GetSelectedRows();
            List<int> values = new List<int>();
            List<string> names = new List<string>();
            int[] array = selectedRows;
            foreach (int rowHandle in array)
            {
                if (gridView.GetRowCellValue(rowHandle, ValueMember) is int id)
                {
                    values.Add(id);
                }
                if (gridView.GetRowCellValue(rowHandle, DisplayMember) is string name)
                {
                    names.Add(name);
                }
            }
            e.Value = values;
            DisplayText = string.Join(", ", names);
        }
    }
}
