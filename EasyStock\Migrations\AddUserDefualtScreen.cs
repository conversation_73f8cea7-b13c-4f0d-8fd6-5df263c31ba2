﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x02000484 RID: 1156
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class AddUserDefualtScreen : DbMigration, IMigrationMetadata
	{
		// Token: 0x060022DD RID: 8925 RVA: 0x000120EB File Offset: 0x000102EB
		public override void Up()
		{
			base.AddColumn("dbo.UserSettingsProfiles", "DefaultScreen", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
		}

		// Token: 0x060022DE RID: 8926 RVA: 0x0001211F File Offset: 0x0001031F
		public override void Down()
		{
			base.DropColumn("dbo.UserSettingsProfiles", "DefaultScreen", null);
		}

		// Token: 0x17000B73 RID: 2931
		// (get) Token: 0x060022DF RID: 8927 RVA: 0x001F0AC8 File Offset: 0x001EECC8
		string IMigrationMetadata.Id
		{
			get
			{
				return "202104212302126_AddUserDefualtScreen";
			}
		}

		// Token: 0x17000B74 RID: 2932
		// (get) Token: 0x060022E0 RID: 8928 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000B75 RID: 2933
		// (get) Token: 0x060022E1 RID: 8929 RVA: 0x001F0AE0 File Offset: 0x001EECE0
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002AF4 RID: 10996
		private readonly ResourceManager Resources = new ResourceManager(typeof(AddUserDefualtScreen));
	}
}
