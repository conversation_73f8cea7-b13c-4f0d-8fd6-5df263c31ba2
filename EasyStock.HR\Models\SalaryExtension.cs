﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    [DisplayColumn("Extensions de salaire")]
    public class SalaryExtension : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Nom")]
        [StringLength(150)]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public string Name
        {
            get
            {
                return this.name;
            }
            set
            {
                base.SetProperty<string>(ref this.name, value, "Name");
            }
        }

        [Display(Name = "Type")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public ExtensionType Type { get; set; }

        [Display(Name = "Méthode de calcul")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public ECalculationType CalculationType { get; set; }

        [Display(Name = "Statut")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public EStatus Status { get; set; }

        [Display(Name = "Compte principal")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int MainAccount
        {
            get
            {
                return this.mainAccount;
            }
            set
            {
                base.SetProperty<int>(ref this.mainAccount, value, "MainAccount");
            }
        }

        private int id;

        private string name;

        private int mainAccount;
    }
}
