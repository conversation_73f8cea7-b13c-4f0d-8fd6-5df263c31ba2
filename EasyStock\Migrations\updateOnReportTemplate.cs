﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x020004CF RID: 1231
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class updateOnReportTemplate : DbMigration, IMigrationMetadata
	{
		// Token: 0x06002490 RID: 9360 RVA: 0x00012C31 File Offset: 0x00010E31
		public override void Up()
		{
			base.AddColumn("dbo.ReportTemplates", "ReportName", (ColumnBuilder c) => c.String(null, null, null, null, null, null, null, null, null), null);
		}

		// Token: 0x06002491 RID: 9361 RVA: 0x00012C65 File Offset: 0x00010E65
		public override void Down()
		{
			base.DropColumn("dbo.ReportTemplates", "ReportName", null);
		}

		// Token: 0x17000BEB RID: 3051
		// (get) Token: 0x06002492 RID: 9362 RVA: 0x001FD064 File Offset: 0x001FB264
		string IMigrationMetadata.Id
		{
			get
			{
				return "202102141522143_updateOnReportTemplate";
			}
		}

		// Token: 0x17000BEC RID: 3052
		// (get) Token: 0x06002493 RID: 9363 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000BED RID: 3053
		// (get) Token: 0x06002494 RID: 9364 RVA: 0x001FD07C File Offset: 0x001FB27C
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002BBC RID: 11196
		private readonly ResourceManager Resources = new ResourceManager(typeof(updateOnReportTemplate));
	}
}
