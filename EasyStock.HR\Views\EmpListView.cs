﻿using DevExpress.Utils;
using DevExpress.Utils.Menu;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using EasyStock.Common;
using EasyStock.HR.BLL;
using EasyStock.HR.Class;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class EmpListView : MasterView
    {
        public static EmpListView Instance
        {
            get
            {
                bool flag = EmpListView.instance == null || EmpListView.instance.IsDisposed;
                if (flag)
                {
                    EmpListView.instance = new EmpListView();
                }
                return EmpListView.instance;
            }
        }

        public EmpListView()
        {
            this.InitializeComponent();
            this.btn_Save.Visibility = BarItemVisibility.Never;
            this.btn_Print.Visibility = BarItemVisibility.Always;
            this.btn_Refresh.Visibility = BarItemVisibility.Always;
            this.btn_Print.Enabled = true;
            this.Text = "Les employés";
            this.InitializeGrid();
        }

        private void InitializeGrid()
        {
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.FocusRectStyle = DrawFocusRectStyle.RowFocus;
            this.gridView1.OptionsSelection.MultiSelect = true;
            this.gridView1.SetAlternatingColors();
            this.gridView1.DoubleClick += this.GridView1_DoubleClick;
            this.gridView1.PopupMenuShowing += this.GridView1_PopupMenuShowing;
            this.gridView1.CustomColumnDisplayText += this.GridView1_CustomColumnDisplayText;
            this.gridView1.OptionsBehavior.AutoPopulateColumns = false;
            this.gridView1.Columns.AddRange(new GridColumn[]
            {
                new GridColumn
                {
                    FieldName = "ID",
                    Visible = false
                },
                new GridColumn
                {
                    FieldName = "Code",
                    Visible = true
                },
                new GridColumn
                {
                    FieldName = "Name",
                    Visible = true
                },
                new GridColumn
                {
                    FieldName = "PayPeriod",
                    Visible = true
                },
                new GridColumn
                {
                    FieldName = "Department",
                    Visible = true
                },
                new GridColumn
                {
                    FieldName = "Job",
                    Visible = true
                },
                new GridColumn
                {
                    FieldName = "Group",
                    Visible = true
                },
                new GridColumn
                {
                    FieldName = "Gender",
                    Visible = true
                },
                new GridColumn
                {
                    FieldName = "EmpState",
                    Visible = true
                }
            });
        }

        private void GridView1_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            bool flag = e.Column.FieldName == "Department";
            if (flag)
            {
                Department d = e.Value as Department;
                e.DisplayText = ((d != null) ? d.Name : null);
            }
            bool flag2 = e.Column.FieldName == "Group";
            if (flag2)
            {
                Group g = e.Value as Group;
                e.DisplayText = ((g != null) ? g.Name : null);
            }
            bool flag3 = e.Column.FieldName == "Job";
            if (flag3)
            {
                Job i = e.Value as Job;
                e.DisplayText = ((i != null) ? i.Name : null);
            }
        }

        private void GridView1_PopupMenuShowing(object sender, PopupMenuShowingEventArgs e)
        {
            bool flag = e.HitInfo.InRow || e.HitInfo.InRowCell;
            if (flag)
            {
                DXMenuItem dxButtonPrint = new DXMenuItem
                {
                    Caption = "Imprimer"
                };
                dxButtonPrint.Click += this.DxButtonPrint_Click;
                e.Menu.Items.Add(dxButtonPrint);
                DXMenuItem dxButtonDelete = new DXMenuItem
                {
                    Caption = "Supprimer"
                };
                dxButtonDelete.Click += this.DxButtonDelete_Click;
                e.Menu.Items.Add(dxButtonDelete);
                DXMenuItem dxButtonEdit = new DXMenuItem
                {
                    Caption = "Modifier"
                };
                dxButtonEdit.Click += this.DxButtonEdit_Click;
                e.Menu.Items.Add(dxButtonEdit);
            }
        }

        private void DxButtonDelete_Click(object sender, EventArgs e)
        {
            this.btn_Delete.PerformClick();
        }

        private void DxButtonPrint_Click(object sender, EventArgs e)
        {
            this.btn_Print.PerformClick();
        }

        private void DxButtonEdit_Click(object sender, EventArgs e)
        {
            List<int> ids = this.GetSelectedIds();
            int id = (ids.Count > 0) ? ids.FirstOrDefault<int>() : 0;
            this.Edit(id);
        }

        private List<int> GetSelectedIds()
        {
            int[] handles = this.gridView1.GetSelectedRows();
            List<int> ids = new List<int>();
            foreach (int handel in handles)
            {
                ids.Add(Convert.ToInt32(this.gridView1.GetRowCellValue(handel, "ID")));
            }
            bool flag = ids.Count == 0;
            if (flag)
            {
                XtraMessageBox.Show("Veuillez sélectionner au moins un employé");
            }
            return ids;
        }

        private void GridView1_DoubleClick(object sender, EventArgs e)
        {
            DXMouseEventArgs ea = e as DXMouseEventArgs;
            GridView view = sender as GridView;
            GridHitInfo info = view.CalcHitInfo(ea.Location);
            bool flag = info.InRow || info.InRowCell;
            if (flag)
            {
                int id = Convert.ToInt32(view.GetFocusedRowCellValue("ID"));
                this.Edit(id);
            }
        }

        public virtual void Edit(int id)
        {
            Static.OpenForm(EmpView.Instance);
            EmpView.Instance.GoTo(id);
        }

        public override void New()
        {
            Static.OpenForm(EmpView.Instance);
            EmpView.Instance.btn_New.PerformClick();
        }

        public override void RefreshData()
        {
            this.gridControl1.DataSource = EmployeeBLL.GetAll();
            base.RefreshData();
        }

        public override void Print()
        {
            List<int> ids = this.GetSelectedIds();
            base.Print();
        }

        public override void Delete()
        {
            List<int> ids = this.GetSelectedIds();
            bool flag = ids.Count == 0;
            if (!flag)
            {
                int res = EmployeeBLL.Delete(ids);
                base.Delete();
            }
        }

        private static EmpListView instance;
    }
}
