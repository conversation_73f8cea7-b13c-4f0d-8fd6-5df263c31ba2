﻿using DevExpress.XtraBars;
using System;

namespace EasyStock.MainViews
{
    public partial class XtraReportForm : BaseReportForm
    {
        private XtraReportForm()
        {
            InitializeComponent();
            documentViewer1.DocumentChanged += DocumentViewer1_DocumentChanged;
        }

        private void DocumentViewer1_DocumentChanged(object sender, EventArgs e)
        {
        }

        public XtraReportForm(ReportFilter[] _filters) : base(_filters)
        {
            InitializeComponent();
        }

        private void barButtonItem2_ItemClick(object sender, ItemClickEventArgs e)
        {
            RefreshDataSource();
        }

        private void barButtonItem4_ItemClick(object sender, ItemClickEventArgs e)
        {
            base.FiltersForm.ShowDialog();
        }
    }
}
