﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Data.Entity.Migrations.Model;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class workorder : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(workorder));

        string IMigrationMetadata.Id => "202106281207307_workorder";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.ActualCosts", delegate (ColumnBuilder c)
            {
                ColumnModel iD3 = c.Int(false, identity: true);
                ColumnModel orderID2 = c.Int(false);
                ColumnModel accountID2 = c.Int(false);
                ColumnModel cost2 = c.Double(false);
                int? maxLength3 = 250;
                return new
                {
                    ID = iD3,
                    OrderID = orderID2,
                    AccountID = accountID2,
                    Cost = cost2,
                    Notes = c.String(null, maxLength3)
                };
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.WorkOrders", t => t.OrderID).Index(t => t.OrderID);
            CreateTable("dbo.WorkOrders", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Code = c.Int(false),
                BranchID = c.Int(false),
                StoreID = c.Int(false),
                StartDate = c.DateTime(false),
                EndDate = c.DateTime(),
                CostCenter = c.Int(false),
                Notes = c.String(),
                TotalOrderQuantity = c.Double(false),
                TotalAcualQuantity = c.Double(false),
                TotalDefaultCost = c.Double(false),
                TotalAcualCost = c.Double(false),
                UnitCost = c.Double(false)
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.DefaultCosts", delegate (ColumnBuilder c)
            {
                ColumnModel iD2 = c.Int(false, identity: true);
                ColumnModel orderID = c.Int(false);
                ColumnModel accountID = c.Int(false);
                ColumnModel cost = c.Double(false);
                int? maxLength2 = 250;
                return new
                {
                    ID = iD2,
                    OrderID = orderID,
                    AccountID = accountID,
                    Cost = cost,
                    Notes = c.String(null, maxLength2)
                };
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.WorkOrders", t => t.OrderID).Index(t => t.OrderID);
            CreateTable("dbo.DefaultMaterialConsumptions", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                OrderID = c.Int(false),
                ProductID = c.Int(false),
                UintID = c.Int(false),
                Quantity = c.Double(false),
                UnitCost = c.Double(false),
                TotalCost = c.Double(false),
                CurrentBalance = c.Double(false),
                Shtorage = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.WorkOrders", t => t.OrderID).Index(t => t.OrderID);
            CreateTable("dbo.WorkOrderDetails", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                OrderID = c.Int(false),
                ProductID = c.Int(false),
                BOMID = c.Int(false),
                UintID = c.Int(false),
                TotalOrderQuantity = c.Double(false),
                TotalAcualQuantity = c.Double(false),
                TotalDefaultCost = c.Double(false),
                TotalAcualCost = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.WorkOrders", t => t.OrderID).Index(t => t.OrderID);
            CreateTable("dbo.BillOfMaterials", delegate (ColumnBuilder c)
            {
                ColumnModel iD = c.Int(false, identity: true);
                ColumnModel name = c.String(false, 150);
                ColumnModel productID = c.Int(false);
                ColumnModel uintID = c.Int(false);
                ColumnModel quantity = c.Double(false);
                int? maxLength = 250;
                return new
                {
                    ID = iD,
                    Name = name,
                    ProductID = productID,
                    UintID = uintID,
                    Quantity = quantity,
                    Notes = c.String(null, maxLength)
                };
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.BOMDetails", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                BOMID = c.Int(false),
                ProductID = c.Int(false),
                UintID = c.Int(false),
                Quantity = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.BillOfMaterials", t => t.BOMID).Index(t => t.BOMID);
            CreateTable("dbo.BOMCost", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                BOMID = c.Int(false),
                AccountID = c.Int(false),
                Cost = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.BillOfMaterials", t => t.BOMID).Index(t => t.BOMID);
            CreateTable("dbo.AcualMaterialConsumptions", (ColumnBuilder c) => new
            {
                ID = c.Int(false),
                OrderID = c.Int(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.ProductTransactions", t => t.ID).ForeignKey("dbo.WorkOrders", t => t.OrderID)
                .Index(t => t.ID)
                .Index(t => t.OrderID);
        }

        public override void Down()
        {
            DropForeignKey("dbo.AcualMaterialConsumptions", "OrderID", "dbo.WorkOrders");
            DropForeignKey("dbo.AcualMaterialConsumptions", "ID", "dbo.ProductTransactions");
            DropForeignKey("dbo.BOMCost", "BOMID", "dbo.BillOfMaterials");
            DropForeignKey("dbo.BOMDetails", "BOMID", "dbo.BillOfMaterials");
            DropForeignKey("dbo.WorkOrderDetails", "OrderID", "dbo.WorkOrders");
            DropForeignKey("dbo.DefaultMaterialConsumptions", "OrderID", "dbo.WorkOrders");
            DropForeignKey("dbo.DefaultCosts", "OrderID", "dbo.WorkOrders");
            DropForeignKey("dbo.ActualCosts", "OrderID", "dbo.WorkOrders");
            DropIndex("dbo.AcualMaterialConsumptions", new string[1] { "OrderID" });
            DropIndex("dbo.AcualMaterialConsumptions", new string[1] { "ID" });
            DropIndex("dbo.BOMCost", new string[1] { "BOMID" });
            DropIndex("dbo.BOMDetails", new string[1] { "BOMID" });
            DropIndex("dbo.WorkOrderDetails", new string[1] { "OrderID" });
            DropIndex("dbo.DefaultMaterialConsumptions", new string[1] { "OrderID" });
            DropIndex("dbo.DefaultCosts", new string[1] { "OrderID" });
            DropIndex("dbo.ActualCosts", new string[1] { "OrderID" });
            DropTable("dbo.AcualMaterialConsumptions");
            DropTable("dbo.BOMCost");
            DropTable("dbo.BOMDetails");
            DropTable("dbo.BillOfMaterials");
            DropTable("dbo.WorkOrderDetails");
            DropTable("dbo.DefaultMaterialConsumptions");
            DropTable("dbo.DefaultCosts");
            DropTable("dbo.WorkOrders");
            DropTable("dbo.ActualCosts");
        }
    }
}

