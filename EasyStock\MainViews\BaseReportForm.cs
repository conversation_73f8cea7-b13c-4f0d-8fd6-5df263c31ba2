﻿using DevExpress.XtraEditors;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.ReportViews;
using System;
using System.ComponentModel;
using System.Linq;
using System.Windows.Forms;

namespace EasyStock.MainViews
{
    public partial class BaseReportForm : XtraForm
    {
        public ERPDataContext db = new ERPDataContext();

        public bool IsFilterInitialized;

        private IContainer components = null;

        public FilterForm FiltersForm { get; set; }

        public ReportFilter[] Filters { get; set; }

        protected override FormShowMode ShowMode => FormShowMode.AfterInitialization;

        protected BaseReportForm()
        {
            InitializeComponent();
        }

        public BaseReportForm(ReportFilter[] _filters)
        {
            InitializeComponent();
            Filters = _filters;
            FiltersForm = new FilterForm(this);
            base.Load += ReportForm_Load;
            base.TextChanged += ReportForm_TextChanged;
        }

        public virtual void ReportForm_Load(object sender, EventArgs e)
        {
            if (!IsFilterInitialized)
            {
                FiltersForm.ShowDialog();
                if (FiltersForm.IsCanceld)
                {
                    BeginInvoke(new MethodInvoker(base.Close));
                    return;
                }
                IsFilterInitialized = true;
            }
            this.RestoreGridLayoutIfFound(CurrentSession.CurrentUser.ID.ToString());
        }

        public virtual void ReportForm_TextChanged(object sender, EventArgs e)
        {
            FiltersForm.ReportName.Text = Text;
        }

        public virtual IQueryable GetQuery()
        {
            throw new NotImplementedException();
        }

        internal virtual bool ValidateFilters()
        {
            return true;
        }

        public virtual void RefreshDataSource()
        {
            throw new NotImplementedException();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Text = "BaseReportForm";
        }


    }
}
