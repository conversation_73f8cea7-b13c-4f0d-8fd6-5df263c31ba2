﻿using EasyStock.Classes;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Dépôt")]
    public class Store : BaseNotifyPropertyChangedModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID { get; set; }

        [Display(Name = "Nom")]
        [Required(ErrorMessageResourceName = "ErrorCantBeEmpry", ErrorMessageResourceType = typeof(LangResource))]
        [StringLength(250)]
        public string Name { get; set; }

        [Display(Name = "")]
        public Branch Branch
        {
            get
            {
                return this.branch;
            }
            set
            {
                base.SetProperty<Branch>(ref this.branch, value, "Branch");
            }
        }

        [Display(Name = "Succursale")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est requis")]
        public int BranchID
        {
            get
            {
                return this.branchID;
            }
            set
            {
                base.SetProperty<int>(ref this.branchID, value, "BranchID");
            }
        }

        [Display(Name = "Méthode de calcul des coûts des stocks")]
        public CostCalculationMethod CostCalculationMethod { get; set; }

        [Display(Name = "Téléphone")]
        [StringLength(50)]
        public string Phone { get; set; }

        [Display(Name = "Ville")]
        [StringLength(150)]
        public string City { get; set; }

        [Display(Name = "Adresse")]
        [StringLength(250)]
        public string Address { get; set; }

        [DefaultValue(true)]
        [Display(Name = "Générer automatiquement les comptes")]
        public bool AutoGenrateAccounts { get; set; }

        [Display(Name = "Compte d'achat", GroupName = "Comptes")]
        public int PurchaseAccountID { get; set; }

        [Display(Name = "Retour d'achat", GroupName = "Comptes")]
        public int PurchaseReturnAccountID { get; set; }

        [Display(Name = "Compte de vente", GroupName = "Comptes")]
        public int SellAccountID { get; set; }

        [Display(Name = "Retour de vente", GroupName = "Comptes")]
        public int SellReturnAccountID { get; set; }

        [Display(Name = "Stock initial", GroupName = "Comptes")]
        public int OpenInventoryAccountID { get; set; }

        [Display(Name = "Stock final", GroupName = "Comptes")]
        public int CloseInventoryAccountID { get; set; }

        [Display(Name = "Remise d'achat", GroupName = "Comptes")]
        public int PurchaseDiscountAccountID { get; set; }

        [Display(Name = "Remise autorisée", GroupName = "Comptes")]
        public int SalesDiscountAccountID { get; set; }

        [Display(Name = "Compte d'achat", GroupName = "Comptes")]
        [RequiredIf("AutoGenrateAccounts", false, false, ErrorMessageResourceName = "ErrorCantBeEmpry", ErrorMessageResourceType = typeof(LangResource))]
        public Account PurchaseAccount { get; set; }

        [Display(Name = "Retour d'achat", GroupName = "Comptes")]
        [RequiredIf("AutoGenrateAccounts", false, false, ErrorMessageResourceName = "ErrorCantBeEmpry", ErrorMessageResourceType = typeof(LangResource))]
        public Account PurchaseReturnAccount { get; set; }

        [Display(Name = "Compte de vente", GroupName = "Comptes")]
        [RequiredIf("AutoGenrateAccounts", false, false, ErrorMessageResourceName = "ErrorCantBeEmpry", ErrorMessageResourceType = typeof(LangResource))]
        public Account SellAccount { get; set; }

        [Display(Name = "Retour de vente", GroupName = "Comptes")]
        [RequiredIf("AutoGenrateAccounts", false, false, ErrorMessageResourceName = "ErrorCantBeEmpry", ErrorMessageResourceType = typeof(LangResource))]
        public Account SellReturnAccount { get; set; }

        [Display(Name = "Stock initial", GroupName = "Comptes")]
        [RequiredIf("AutoGenrateAccounts", false, false, ErrorMessageResourceName = "ErrorCantBeEmpry", ErrorMessageResourceType = typeof(LangResource))]
        public Account OpenInventoryAccount { get; set; }

        [Display(Name = "Stock final", GroupName = "Comptes")]
        [RequiredIf("AutoGenrateAccounts", false, false, ErrorMessageResourceName = "ErrorCantBeEmpry", ErrorMessageResourceType = typeof(LangResource))]
        public Account CloseInventoryAccount { get; set; }

        [Display(Name = "Remise d'achat", GroupName = "Comptes")]
        [RequiredIf("AutoGenrateAccounts", false, false, ErrorMessageResourceName = "ErrorCantBeEmpry", ErrorMessageResourceType = typeof(LangResource))]
        public Account PurchaseDiscountAccount { get; set; }

        [Display(Name = "Remise autorisée", GroupName = "Comptes")]
        [RequiredIf("AutoGenrateAccounts", false, false, ErrorMessageResourceName = "ErrorCantBeEmpry", ErrorMessageResourceType = typeof(LangResource))]
        public Account SalesDiscountAccount { get; set; }

        private Branch branch;
        private int branchID;
    }
}
