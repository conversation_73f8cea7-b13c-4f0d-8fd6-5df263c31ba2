﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class add_Branch_ToDrawerPeriod : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(add_Branch_ToDrawerPeriod));

        string IMigrationMetadata.Id => "202104182225261_add_Branch_ToDrawerPeriod";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            AddColumn("dbo.DrawerPeriods", "BranchID", (ColumnBuilder c) => c.Int(false, identity: false, 1));
            CreateIndex("dbo.DrawerPeriods", "BranchID");
            AddForeignKey("dbo.DrawerPeriods", "BranchID", "dbo.Branches", "ID");
        }

        public override void Down()
        {
            DropForeignKey("dbo.DrawerPeriods", "BranchID", "dbo.Branches");
            DropIndex("dbo.DrawerPeriods", new string[1] { "BranchID" });
            DropColumn("dbo.DrawerPeriods", "BranchID");
        }
    }
}
