﻿<XtraSerializer version="1.0" application="View">
  <property name="#LayoutVersion" />
  <property name="#LayoutScaleFactor">@1,Width=1@1,Height=1</property>
  <property name="OptionsView" isnull="true" iskey="true">
    <property name="NewItemRowPosition">Top</property>
    <property name="AllowCellMerge">true</property>
    <property name="ShowFooter">true</property>
    <property name="ShowGroupPanel">false</property>
    <property name="EnableAppearanceOddRow">true</property>
    <property name="EnableAppearanceEvenRow">true</property>
  </property>
  <property name="ActiveFilterEnabled">true</property>
  <property name="Columns" iskey="true" value="20">
    <property name="Item1" isnull="true" iskey="true">
      <property name="Name">colProductID</property>
      <property name="VisibleIndex">1</property>
      <property name="Visible">true</property>
      <property name="Width">217</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item2" isnull="true" iskey="true">
      <property name="Name">colUnitID</property>
      <property name="VisibleIndex">2</property>
      <property name="Visible">true</property>
      <property name="Width">72</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item3" isnull="true" iskey="true">
      <property name="Name">colQuantity</property>
      <property name="VisibleIndex">3</property>
      <property name="Visible">true</property>
      <property name="Width">55</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item4" isnull="true" iskey="true">
      <property name="Name">colPrice</property>
      <property name="VisibleIndex">4</property>
      <property name="Visible">true</property>
      <property name="Width">55</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item5" isnull="true" iskey="true">
      <property name="Name">colTotal</property>
      <property name="Width">42</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item6" isnull="true" iskey="true">
      <property name="Name">colTax</property>
      <property name="Width">38</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item7" isnull="true" iskey="true">
      <property name="Name">colNet</property>
      <property name="VisibleIndex">6</property>
      <property name="Visible">true</property>
      <property name="Width">58</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item8" isnull="true" iskey="true">
      <property name="Name">colDiscount</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item9" isnull="true" iskey="true">
      <property name="Name">colDiscountPercentage</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item10" isnull="true" iskey="true">
      <property name="Name">colTaxPercentage</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item11" isnull="true" iskey="true">
      <property name="Name">colExpire</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item12" isnull="true" iskey="true">
      <property name="Name">colCostValue</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item13" isnull="true" iskey="true">
      <property name="Name">colGroupID</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item14" isnull="true" iskey="true">
      <property name="Name">colSerial</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item15" isnull="true" iskey="true">
      <property name="Name">colSizeID</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item16" isnull="true" iskey="true">
      <property name="Name">colColorID</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item17" isnull="true" iskey="true">
      <property name="Name">clmCode</property>
      <property name="VisibleIndex">0</property>
      <property name="Visible">true</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item18" isnull="true" iskey="true">
      <property name="Name">clmIndex</property>
      <property name="Width">30</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">30</property>
      <property name="Summary" iskey="true" value="1">
        <property name="Item1" isnull="true" iskey="true">
          <property name="Tag" isnull="true" />
          <property name="FieldName">Index</property>
          <property name="SummaryType">Count</property>
          <property name="Mode">Mixed</property>
          <property name="DisplayFormat" />
        </property>
      </property>
    </property>
    <property name="Item19" isnull="true" iskey="true">
      <property name="Name">ColDelete</property>
      <property name="VisibleIndex">7</property>
      <property name="Visible">true</property>
      <property name="Width">25</property>
      <property name="MinWidth">25</property>
      <property name="MaxWidth">25</property>
    </property>
    <property name="Item20" isnull="true" iskey="true">
      <property name="Name">colBalance</property>
      <property name="VisibleIndex">5</property>
      <property name="Visible">true</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">100</property>
    </property>
  </property>
  <property name="GroupSummary" iskey="true" value="0" />
  <property name="ActiveFilterString" />
  <property name="GroupSummarySortInfoState" />
  <property name="FindFilterText" />
  <property name="FindPanelVisible">false</property>
</XtraSerializer>