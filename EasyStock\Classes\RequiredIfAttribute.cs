﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Reflection;

namespace EasyStock.Classes
{
	// Token: 0x0200054A RID: 1354
	[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
	public sealed class RequiredIfAttribute : ValidationAttribute
	{
		// Token: 0x17000CA7 RID: 3239
		// (get) Token: 0x060027B1 RID: 10161 RVA: 0x000142A8 File Offset: 0x000124A8
		// (set) Token: 0x060027B2 RID: 10162 RVA: 0x000142B0 File Offset: 0x000124B0
		public string OtherProperty { get; private set; }

		// Token: 0x17000CA8 RID: 3240
		// (get) Token: 0x060027B3 RID: 10163 RVA: 0x000142B9 File Offset: 0x000124B9
		// (set) Token: 0x060027B4 RID: 10164 RVA: 0x000142C1 File Offset: 0x000124C1
		public string OtherPropertyDisplayName { get; set; }

		// Token: 0x17000CA9 RID: 3241
		// (get) Token: 0x060027B5 RID: 10165 RVA: 0x000142CA File Offset: 0x000124CA
		// (set) Token: 0x060027B6 RID: 10166 RVA: 0x000142D2 File Offset: 0x000124D2
		public object OtherPropertyValue { get; private set; }

		// Token: 0x17000CAA RID: 3242
		// (get) Token: 0x060027B7 RID: 10167 RVA: 0x000142DB File Offset: 0x000124DB
		// (set) Token: 0x060027B8 RID: 10168 RVA: 0x000142E3 File Offset: 0x000124E3
		public bool IsInverted { get; set; }

		// Token: 0x17000CAB RID: 3243
		// (get) Token: 0x060027B9 RID: 10169 RVA: 0x0014C77C File Offset: 0x0014A97C
		public override bool RequiresValidationContext
		{
			get
			{
				return true;
			}
		}

		// Token: 0x060027BA RID: 10170 RVA: 0x000142EC File Offset: 0x000124EC
		public RequiredIfAttribute(string otherProperty, object otherPropertyValue, bool isInverted = false) : base("'{0}' is required because '{1}' has a value {3}'{2}'.")
		{
			this.OtherProperty = otherProperty;
			this.OtherPropertyValue = otherPropertyValue;
			this.IsInverted = isInverted;
		}

		// Token: 0x060027BB RID: 10171 RVA: 0x00217FB0 File Offset: 0x002161B0
		public override string FormatErrorMessage(string name)
		{
			return string.Format(CultureInfo.CurrentCulture, base.ErrorMessageString, new object[]
			{
				name,
				this.OtherPropertyDisplayName ?? this.OtherProperty,
				this.OtherPropertyValue,
				this.IsInverted ? "other than " : "of "
			});
		}

		// Token: 0x060027BC RID: 10172 RVA: 0x00218010 File Offset: 0x00216210
		protected override ValidationResult IsValid(object value, ValidationContext validationContext)
		{
			bool flag = validationContext == null;
			if (flag)
			{
				throw new ArgumentNullException("validationContext");
			}
			PropertyInfo otherProperty = validationContext.ObjectType.GetProperty(this.OtherProperty);
			bool flag2 = otherProperty == null;
			ValidationResult result;
			if (flag2)
			{
				result = new ValidationResult(string.Format(CultureInfo.CurrentCulture, "Could not find a property named '{0}'.", this.OtherProperty));
			}
			else
			{
				object otherValue = otherProperty.GetValue(validationContext.ObjectInstance);
				bool flag3 = (!this.IsInverted && object.Equals(otherValue, this.OtherPropertyValue)) || (this.IsInverted && !object.Equals(otherValue, this.OtherPropertyValue));
				if (flag3)
				{
					bool flag4 = value == null;
					if (flag4)
					{
						return new ValidationResult(this.FormatErrorMessage(validationContext.DisplayName));
					}
					string val = value as string;
					bool flag5 = val != null && val.Trim().Length == 0;
					if (flag5)
					{
						return new ValidationResult(this.FormatErrorMessage(validationContext.DisplayName));
					}
				}
				result = ValidationResult.Success;
			}
			return result;
		}
	}
}
