namespace EasyStock.HR.Models
{
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;

    public partial class OvertimeAndDelayRegulations
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public OvertimeAndDelayRegulations()
        {
            Employees = new HashSet<Employees>();
            Employees1 = new HashSet<Employees>();
            OvertimeAndDelayRegulationMinutesTables = new HashSet<OvertimeAndDelayRegulationMinutesTables>();
        }

        public int ID { get; set; }

        [Required]
        [StringLength(150)]
        public string Name { get; set; }

        public int Type { get; set; }

        public string Notes { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Employees> Employees { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Employees> Employees1 { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<OvertimeAndDelayRegulationMinutesTables> OvertimeAndDelayRegulationMinutesTables { get; set; }
    }
}
