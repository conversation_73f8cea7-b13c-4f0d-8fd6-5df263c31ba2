﻿using EasyStock.Common;
using EasyStock.Controller;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Écritures comptables")]
    public class Journal : BaseNotifyPropertyChangedModel
    {
        public void GetNewCode()
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                Type sss = base.GetType();
                int maxLength = (from x in db.Journals.AsNoTracking()
                                 select x.Code).Max((string x) => (int?)x.Length).GetValueOrDefault();
                bool flag = maxLength == 0;
                if (flag)
                {
                    this.Code = "1";
                }
                else
                {
                    string maxCode = (from x in db.Journals.AsNoTracking()
                                      where x.Code.Length == maxLength
                                      orderby x.ID descending
                                      select x).FirstOrDefault<Journal>().Code;
                    bool flag2 = maxCode != string.Empty;
                    if (flag2)
                    {
                        this.Code = maxCode.GetNextSequence();
                    }
                }
            }
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        public int ID { get; set; }

        [StringLength(50)]
        [Display(Name = "N°")]
        public string Code { get; set; }

        [Display(Name = "Date", GroupName = "Données")]
        public DateTime Date { get; set; }

        [Display(Name = "Notes", GroupName = "Données")]
        public string Note { get; set; }

        [Display(Name = "Utilisateur")]
        public int UserID { get; set; }

        [Display(Name = "Succursale")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int BranchID
        {
            get
            {
                return this.branchID;
            }
            set
            {
                base.SetProperty<int>(ref this.branchID, value, "BranchID");
            }
        }

        [Required]
        [Display(Name = "Source du Journal", GroupName = "Données")]
        public SystemProcess ProcessType { get; set; }

        [Display(Name = "Code Source", GroupName = "Données")]
        public int ProcessID { get; set; }

        [Display(Name = "Détails", GroupName = "Détails")]
        public BindingList<JournalDetail> Details { get; set; }

        private int branchID;
    }
}
