﻿using EasyStock.Controller;
using EasyStock.Models;
using System;
using System.ComponentModel.DataAnnotations;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;

namespace EasyStock.Charts
{

    public class StagentProductsCharts : GridChart
    {
        public class StagentProductsChartsModel
        {
            [Display(Name = "Article")]
            public string ProductName { get; set; }

            [Display(Name = "Solde")]
            public double? Balance { get; set; }

            [Display(Name = "Date de la dernière facture")]
            public DateTime LastInvoice { get; set; }

            [Display(Name = "Jours de stagnation")]
            public int? StagenPeriod { get; set; }
        }

        public override string Caption => "Les articles en stock stagnant";

        public override Color Color => ColorTranslator.FromHtml("#424242");

        public override async Task<object> QueryData()
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                var now = DateTime.Now;
                var defaultDate = new DateTime(2000, 1, 1, 0, 0, 0);

                var initialData = await (from product in db.Products
                                         let lastInvoice = (from x in db.ProductTransactions
                                                            where (int)x.TransactionState == 2
                                                            && x.ProductID == product.ID
                                                            && (int)x.Type == 4
                                                            select (DateTime?)x.Date).Max() ?? defaultDate
                                         select new
                                         {
                                             ProductName = product.Name,
                                             LastInvoice = lastInvoice,
                                             Balance = (from x in db.ProductTransactions
                                                        where (int)x.TransactionState == 2
                                                        && x.ProductID == product.ID
                                                        group x by x.TransactionType into g
                                                        select g.Key == 0
                                                            ? (double?)g.Sum(t => t.Quantity * t.Factor)
                                                            : (double?)-g.Sum(t => t.Quantity * t.Factor)
                                                       ).Sum() ?? 0.00
                                         }).ToListAsync();

                var result = initialData
                             .Where(x => (now - x.LastInvoice).TotalDays > 30)
                             .Select(x => new
                             {
                                 ProductName = x.ProductName,
                                 Balance = x.Balance,
                                 LastInvoice = x.LastInvoice,
                                 StagenPeriod = (now - x.LastInvoice).TotalDays
                             })
                             .OrderByDescending(x => x.StagenPeriod)
                             .ThenBy(x => x.Balance)
                             .ToList();

                return result;
            }
        }
    }
}
