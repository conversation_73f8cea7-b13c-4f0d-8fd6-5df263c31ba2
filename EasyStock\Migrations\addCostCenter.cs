﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Data.Entity.Migrations.Model;
using System.Resources;

namespace EasyStock.Migrations
{
    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class addCostCenter : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(addCostCenter));

        string IMigrationMetadata.Id => "202104071257199_addCostCenter";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.CostCenters", delegate (ColumnBuilder c)
            {
                ColumnModel iD = c.Int(false, identity: true);
                ColumnModel code = c.String();
                int? maxLength = 150;
                return new
                {
                    ID = iD,
                    Code = code,
                    Name = c.String(null, maxLength),
                    ParentID = c.Int(),
                    Notes = c.String()
                };
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.CostCenters", t => t.ParentID).Index(t => t.ParentID);
            AddColumn("dbo.Accounts", "CostCenterID", (ColumnBuilder c) => c.Int());
            AddColumn("dbo.Accounts", "CostCenterRestriction", (ColumnBuilder c) => c.Int(false, identity: false, 1));
            AddColumn("dbo.PurchaseInvoices", "CostCenterID", (ColumnBuilder c) => c.Int());
            AddColumn("dbo.PurchaseReturnInvoices", "CostCenterID", (ColumnBuilder c) => c.Int());
            AddColumn("dbo.SalesInvoices", "CostCenterID", (ColumnBuilder c) => c.Int());
            AddColumn("dbo.SalesReturnInvoices", "CostCenterID", (ColumnBuilder c) => c.Int());
            AddColumn("dbo.JournalDetails", "CostCenterID", (ColumnBuilder c) => c.Int());
            AddColumn("dbo.CashNotes", "CostCenterID", (ColumnBuilder c) => c.Int());
            AddColumn("dbo.RevExpEntryDetails", "CostCenterID", (ColumnBuilder c) => c.Int());
            CreateIndex("dbo.Accounts", "CostCenterID");
            CreateIndex("dbo.JournalDetails", "CostCenterID");
            CreateIndex("dbo.CashNotes", "CostCenterID");
            CreateIndex("dbo.RevExpEntryDetails", "CostCenterID");
            CreateIndex("dbo.PurchaseInvoices", "CostCenterID");
            CreateIndex("dbo.PurchaseReturnInvoices", "CostCenterID");
            CreateIndex("dbo.SalesInvoices", "CostCenterID");
            CreateIndex("dbo.SalesReturnInvoices", "CostCenterID");
            AddForeignKey("dbo.Accounts", "CostCenterID", "dbo.CostCenters", "ID");
            AddForeignKey("dbo.JournalDetails", "CostCenterID", "dbo.CostCenters", "ID");
            AddForeignKey("dbo.CashNotes", "CostCenterID", "dbo.CostCenters", "ID");
            AddForeignKey("dbo.RevExpEntryDetails", "CostCenterID", "dbo.CostCenters", "ID");
            AddForeignKey("dbo.PurchaseInvoices", "CostCenterID", "dbo.CostCenters", "ID");
            AddForeignKey("dbo.PurchaseReturnInvoices", "CostCenterID", "dbo.CostCenters", "ID");
            AddForeignKey("dbo.SalesInvoices", "CostCenterID", "dbo.CostCenters", "ID");
            AddForeignKey("dbo.SalesReturnInvoices", "CostCenterID", "dbo.CostCenters", "ID");
        }

        public override void Down()
        {
            DropForeignKey("dbo.SalesReturnInvoices", "CostCenterID", "dbo.CostCenters");
            DropForeignKey("dbo.SalesInvoices", "CostCenterID", "dbo.CostCenters");
            DropForeignKey("dbo.PurchaseReturnInvoices", "CostCenterID", "dbo.CostCenters");
            DropForeignKey("dbo.PurchaseInvoices", "CostCenterID", "dbo.CostCenters");
            DropForeignKey("dbo.RevExpEntryDetails", "CostCenterID", "dbo.CostCenters");
            DropForeignKey("dbo.CashNotes", "CostCenterID", "dbo.CostCenters");
            DropForeignKey("dbo.JournalDetails", "CostCenterID", "dbo.CostCenters");
            DropForeignKey("dbo.Accounts", "CostCenterID", "dbo.CostCenters");
            DropForeignKey("dbo.CostCenters", "ParentID", "dbo.CostCenters");
            DropIndex("dbo.SalesReturnInvoices", new string[1] { "CostCenterID" });
            DropIndex("dbo.SalesInvoices", new string[1] { "CostCenterID" });
            DropIndex("dbo.PurchaseReturnInvoices", new string[1] { "CostCenterID" });
            DropIndex("dbo.PurchaseInvoices", new string[1] { "CostCenterID" });
            DropIndex("dbo.RevExpEntryDetails", new string[1] { "CostCenterID" });
            DropIndex("dbo.CashNotes", new string[1] { "CostCenterID" });
            DropIndex("dbo.JournalDetails", new string[1] { "CostCenterID" });
            DropIndex("dbo.CostCenters", new string[1] { "ParentID" });
            DropIndex("dbo.Accounts", new string[1] { "CostCenterID" });
            DropColumn("dbo.RevExpEntryDetails", "CostCenterID");
            DropColumn("dbo.CashNotes", "CostCenterID");
            DropColumn("dbo.JournalDetails", "CostCenterID");
            DropColumn("dbo.SalesReturnInvoices", "CostCenterID");
            DropColumn("dbo.SalesInvoices", "CostCenterID");
            DropColumn("dbo.PurchaseReturnInvoices", "CostCenterID");
            DropColumn("dbo.PurchaseInvoices", "CostCenterID");
            DropColumn("dbo.Accounts", "CostCenterRestriction");
            DropColumn("dbo.Accounts", "CostCenterID");
            DropTable("dbo.CostCenters");
        }
    }
}
