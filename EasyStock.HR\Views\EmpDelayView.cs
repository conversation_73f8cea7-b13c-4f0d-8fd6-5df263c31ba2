﻿using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using EasyStock.Common;
using EasyStock.HR.BLL;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class EmpDelayView : MasterView
    {
        public static EmpDelayView Instance
        {
            get
            {
                bool flag = EmpDelayView.instance == null || EmpDelayView.instance.IsDisposed;
                if (flag)
                {
                    EmpDelayView.instance = new EmpDelayView();
                }
                return EmpDelayView.instance;
            }
        }

        public EmpOvertimeDelay delay
        {
            get
            {
                return this.empOvertimeDelayBindingSource.Current as EmpOvertimeDelay;
            }
            set
            {
                this.empOvertimeDelayBindingSource.DataSource = value;
            }
        }

        public EmpDelayView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            this.context = new HRDataContext();
            this.EmpIDTextEdit.EditValueChanged += this.EmpIDTextEdit_EditValueChanged;
            base.Shown += this.EmpOvertimeDelayView_Shown;
        }

        private void EmpIDTextEdit_EditValueChanged(object sender, EventArgs e)
        {
            this.EpmReg();
        }

        private void EmpOvertimeDelayView_Shown(object sender, EventArgs e)
        {
            bool flag = this.delay == null;
            if (flag)
            {
                this.New();
            }
        }

        private void EpmReg()
        {
            Employee emp = EmployeeBLL.Get(this.delay.EmpID);
            bool flag = emp != null;
            if (flag)
            {
                this.delay.RegulationID = emp.DelayRegulationID.Value;
            }
        }

        public override void New()
        {
            this.delay = new EmpOvertimeDelay
            {
                Type = EOvertimeDelay.Delay,
                DayDate = this.context.GetServerTime()
            };
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.EpmReg();
                this.context.EmpOvertimeDelays.AddOrUpdate(new EmpOvertimeDelay[]
                {
                    this.delay
                });
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.EmpIDTextEdit.Properties.DataSource = EmployeeBLL.GetActive();
            this.EmpIDTextEdit.Properties.DisplayMember = "Name";
            this.EmpIDTextEdit.Properties.ValueMember = "ID";
            this.EmpIDTextEdit.Properties.NullText = "";
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("ID", "Code"));
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Nom"));
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("PayPeriod", "Période de paiement"));
            this.RegulationIDTextEdit.Properties.DataSource = OvertimeAndDelayRegulationBLL.GetAll(EType.Delay);
            this.RegulationIDTextEdit.Properties.DisplayMember = "Name";
            this.RegulationIDTextEdit.Properties.ValueMember = "ID";
            this.RegulationIDTextEdit.Properties.NullText = "";
            base.RefreshData();
        }

        public void GoTo(int id)
        {
            EmpOvertimeDelay sourceDepr = this.context.EmpOvertimeDelays.SingleOrDefault((EmpOvertimeDelay x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.delay = sourceDepr;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable");
            }
        }

        public override void Delete()
        {
        }

        private static EmpDelayView instance;

        private HRDataContext context;
    }
}
