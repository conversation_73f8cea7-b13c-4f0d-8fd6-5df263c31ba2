﻿using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Revenus et Dépenses")]
    public class RevExpEntry : BaseNotifyPropertyChangedModel
    {
        [Display(Name = "N°")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID
        {
            get
            {
                return this._Id;
            }
            set
            {
                base.SetProperty<int>(ref this._Id, value, "ID");
            }
        }

        [Display(Name = "Code")]
        public string Code
        {
            get
            {
                return this._code;
            }
            set
            {
                base.SetProperty<string>(ref this._code, value, "Code");
            }
        }

        [Display(Name = "Succursale")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int BranchID
        {
            get
            {
                return this._branchID;
            }
            set
            {
                base.SetProperty<int>(ref this._branchID, value, "BranchID");
            }
        }

        [Display(Name = "Utilisateur")]
        public int UserID { get; set; }

        [Display(Name = "Méthode de paiement")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public CashNotePaySourceType MethodType
        {
            get
            {
                return this._methodType;
            }
            set
            {
                base.SetProperty<CashNotePaySourceType>(ref this._methodType, value, "MethodType");
            }
        }

        [Display(Name = "Compte de paiement")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int MethodID
        {
            get
            {
                return this._methodID;
            }
            set
            {
                base.SetProperty<int>(ref this._methodID, value, "MethodID");
            }
        }

        [Display(Name = "Date")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public DateTime DateTime
        {
            get
            {
                return this._DateTime;
            }
            set
            {
                base.SetProperty<DateTime>(ref this._DateTime, value, "DateTime");
            }
        }

        [Display(Name = "Total")]
        [Range(0.0001, 1.7976931348623157E+308, ErrorMessage = "La valeur doit être supérieure à zéro")]
        [ReadOnly(true)]
        public double Total
        {
            get
            {
                return this._total;
            }
            set
            {
                base.SetProperty<double>(ref this._total, value, "Total");
                this.OnPropertyChanged("DiscountValue");
                this.OnPropertyChanged("TaxValue");
                this.OnPropertyChanged("TotalAfterTax");
            }
        }

        [Display(Name = "Soumis à la taxe")]
        public bool Taxable
        {
            get
            {
                return this.taxable;
            }
            set
            {
                base.SetProperty<bool>(ref this.taxable, value, "Taxable");
            }
        }

        [Display(Name = "Taux de taxe")]
        public double TaxPersentage
        {
            get
            {
                return this.taxPersentage;
            }
            set
            {
                base.SetProperty<double>(ref this.taxPersentage, value, "TaxPersentage");
                this.OnPropertyChanged("DiscountValue");
                this.OnPropertyChanged("TaxValue");
                this.OnPropertyChanged("TotalAfterTax");
            }
        }

        [Display(Name = "Taux de remise")]
        public double DiscountPersentage
        {
            get
            {
                return this.discountPercentage;
            }
            set
            {
                base.SetProperty<double>(ref this.discountPercentage, value, "DiscountPersentage");
                this.OnPropertyChanged("DiscountValue");
                this.OnPropertyChanged("TaxValue");
                this.OnPropertyChanged("TotalAfterTax");
            }
        }

        [Display(Name = "N° de facture")]
        [RequiredIf("Taxable", true, false, ErrorMessage = "Ce champ est obligatoire")]
        public string InvoiceCode
        {
            get
            {
                return this.invoiceCode;
            }
            set
            {
                base.SetProperty<string>(ref this.invoiceCode, value, "InvoiceCode");
            }
        }

        [Display(Name = "Partie impliquée")]
        [RequiredIf("Taxable", true, false, ErrorMessage = "Ce champ est obligatoire")]
        public int? PersonalID
        {
            get
            {
                return this.personalID;
            }
            set
            {
                base.SetProperty<int?>(ref this.personalID, value, "PersonalID");
            }
        }

        [Display(Name = "")]
        public Personal Personal
        {
            get
            {
                return this.personal;
            }
            set
            {
                base.SetProperty<Personal>(ref this.personal, value, "Personal");
            }
        }

        [NotMapped]
        [Display(Name = "Montant de taxe")]
        public double TaxValue
        {
            get
            {
                bool flag = this.Taxable;
                double result;
                if (flag)
                {
                    bool calculateTaxAfterDiscount = CurrentSession.SystemSettings.CalculateTaxAfterDiscount;
                    if (calculateTaxAfterDiscount)
                    {
                        result = (this.Total - this.DiscountValue) * this.TaxPersentage;
                    }
                    else
                    {
                        result = this.Total * this.TaxPersentage;
                    }
                }
                else
                {
                    result = 0.0;
                }
                return result;
            }
        }

        [NotMapped]
        [Display(Name = "Montant de remise")]
        public double DiscountValue
        {
            get
            {
                bool flag = this.Taxable;
                double result;
                if (flag)
                {
                    result = this.Total * this.DiscountPersentage;
                }
                else
                {
                    result = 0.0;
                }
                return result;
            }
            set
            {
                bool flag = this.Taxable;
                if (flag)
                {
                    double discountPesentage = value / this.Total;
                    this.DiscountPersentage = discountPesentage;
                }
            }
        }

        [NotMapped]
        [Display(Name = "Net après taxe")]
        [ReadOnly(true)]
        public double TotalAfterTax
        {
            get
            {
                return this.Total - this.DiscountValue + this.TaxValue;
            }
        }

        [Display(Name = "Notes")]
        public string Notes
        {
            get
            {
                return this._notes;
            }
            set
            {
                base.SetProperty<string>(ref this._notes, value, "Notes");
            }
        }

        [Display(Name = "Entrées")]
        public BindingList<RevExpEntryDetail> Details
        {
            get
            {
                return this._details;
            }
            set
            {
                base.SetProperty<BindingList<RevExpEntryDetail>>(ref this._details, value, "Details");
            }
        }

        [Display(Name = "Type")]
        public RevExpEntryType EntryType
        {
            get
            {
                return this._entryType;
            }
            set
            {
                base.SetProperty<RevExpEntryType>(ref this._entryType, value, "EntryType");
            }
        }

        public Journal Journal { get; set; }

        public int JournalID { get; set; }

        public void GetNewCode()
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                Type sss = base.GetType();
                int maxLength = (from x in db.RevExpEntries.AsNoTracking()
                                 where (int)x.EntryType == (int)this.EntryType
                                 select x.Code).Max((string x) => (int?)x.Length).GetValueOrDefault();
                bool flag = maxLength == 0;
                if (flag)
                {
                    this.Code = "1";
                }
                else
                {
                    string maxCode = (from x in db.RevExpEntries.AsNoTracking()
                                      where (int)x.EntryType == (int)this.EntryType
                                      where x.Code.Length == maxLength
                                      orderby x.ID descending
                                      select x).FirstOrDefault<RevExpEntry>().Code;
                    bool flag2 = maxCode != string.Empty;
                    if (flag2)
                    {
                        this.Code = maxCode.GetNextSequence();
                    }
                }
            }
        }

        private int _Id;

        private string _code;

        private int _branchID;

        private CashNotePaySourceType _methodType;

        private int _methodID;

        private DateTime _DateTime;

        private double _total;

        private bool taxable;

        private double taxPersentage;

        private double discountPercentage;

        private string invoiceCode;

        private int? personalID;

        private Personal personal;

        private string _notes;

        private BindingList<RevExpEntryDetail> _details;

        private RevExpEntryType _entryType;
    }
}
