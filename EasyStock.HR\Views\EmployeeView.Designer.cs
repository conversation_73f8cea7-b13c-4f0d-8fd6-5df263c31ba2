﻿namespace EasyStock.HR.Views
{
	public partial class EmployeeView : global::EasyStock.HR.MainViews.MasterView
	{
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.DepartmentTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.employeeBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.GroupTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.JobTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.AbsenceRegulationTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.DelayRegulationTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.OverTimeRegulationTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.SalaryRegulationTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.CodeTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.NameTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.GenderImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.BranchTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.NationalIDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.InsuranceNoTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.InsuranceDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.DrivingLicenseTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.EndDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.BankNameTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.AccountNoTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.BirthDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.BirthPlaceTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.NationalityTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.MaritalStatusImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.MilitarilyStatusImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.PhoneTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.EmailTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.AddressTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.FingerprintCodeTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.ContractTypeImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.ContractPeriodTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.ContactEndDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.DateOfRecruitmentDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.AutoAlternateShiftCheckEdit = new DevExpress.XtraEditors.CheckEdit();
            this.FristShiftDateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.ShiftAlternatingPeriodTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.DepartmentIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.GroupIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.JobIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.AbsenceRegistionIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.DelayRegulationIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.SalaryRegulationIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.OverTimeRegulationIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.ItemForDepartment = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForGroup = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForJob = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForAbsenceRegulation = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDelayRegulation = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForSalaryRegulation = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForOverTimeRegulation = new DevExpress.XtraLayout.LayoutControlItem();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.tabbedControlGroup1 = new DevExpress.XtraLayout.TabbedControlGroup();
            this.layoutControlGroup6 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForAbsenceRegistionID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDelayRegulationID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForOverTimeRegulationID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForSalaryRegulationID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForAutoAlternateShift = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForShiftAlternatingPeriod = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForFristShiftDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem3 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForName = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForGender = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForBranch = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDepartmentID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForGroupID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForCode = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem4 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup3 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForNationalID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForInsuranceNo = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForInsuranceDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDrivingLicense = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForEndDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForBankName = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForAccountNo = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem5 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup4 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForContractType = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForContractPeriod = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForContactEndDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDateOfRecruitment = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForJobID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForFingerprintCode = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem6 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup5 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForBirthDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForBirthPlace = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForNationality = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForMaritalStatus = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForMilitarilyStatus = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForPhone = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForEmail = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForAddress = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem7 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.DepartmentTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.employeeBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.GroupTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.JobTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.AbsenceRegulationTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DelayRegulationTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.OverTimeRegulationTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.SalaryRegulationTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.CodeTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NameTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.GenderImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BranchTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NationalIDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.InsuranceNoTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.InsuranceDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.InsuranceDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DrivingLicenseTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EndDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EndDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BankNameTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.AccountNoTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BirthDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BirthDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BirthPlaceTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NationalityTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.MaritalStatusImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.MilitarilyStatusImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.PhoneTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmailTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.AddressTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.FingerprintCodeTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContractTypeImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContractPeriodTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContactEndDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContactEndDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateOfRecruitmentDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateOfRecruitmentDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.AutoAlternateShiftCheckEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.FristShiftDateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.FristShiftDateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ShiftAlternatingPeriodTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DepartmentIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.GroupIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.JobIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.AbsenceRegistionIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DelayRegulationIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.SalaryRegulationIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.OverTimeRegulationIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDepartment)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForGroup)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForJob)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAbsenceRegulation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDelayRegulation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSalaryRegulation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForOverTimeRegulation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabbedControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAbsenceRegistionID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDelayRegulationID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForOverTimeRegulationID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSalaryRegulationID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAutoAlternateShift)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForShiftAlternatingPeriod)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFristShiftDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForGender)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBranch)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDepartmentID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForGroupID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNationalID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForInsuranceNo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForInsuranceDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDrivingLicense)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEndDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBankName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAccountNo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForContractType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForContractPeriod)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForContactEndDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDateOfRecruitment)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForJobID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFingerprintCode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBirthDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBirthPlace)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNationality)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForMaritalStatus)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForMilitarilyStatus)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForPhone)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAddress)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            this.SuspendLayout();
            this.dataLayoutControl1.Controls.Add(this.DepartmentTextEdit);
            this.dataLayoutControl1.Controls.Add(this.GroupTextEdit);
            this.dataLayoutControl1.Controls.Add(this.JobTextEdit);
            this.dataLayoutControl1.Controls.Add(this.AbsenceRegulationTextEdit);
            this.dataLayoutControl1.Controls.Add(this.DelayRegulationTextEdit);
            this.dataLayoutControl1.Controls.Add(this.OverTimeRegulationTextEdit);
            this.dataLayoutControl1.Controls.Add(this.SalaryRegulationTextEdit);
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.CodeTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NameTextEdit);
            this.dataLayoutControl1.Controls.Add(this.GenderImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.BranchTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NationalIDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.InsuranceNoTextEdit);
            this.dataLayoutControl1.Controls.Add(this.InsuranceDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.DrivingLicenseTextEdit);
            this.dataLayoutControl1.Controls.Add(this.EndDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.BankNameTextEdit);
            this.dataLayoutControl1.Controls.Add(this.AccountNoTextEdit);
            this.dataLayoutControl1.Controls.Add(this.BirthDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.BirthPlaceTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NationalityTextEdit);
            this.dataLayoutControl1.Controls.Add(this.MaritalStatusImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.MilitarilyStatusImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.PhoneTextEdit);
            this.dataLayoutControl1.Controls.Add(this.EmailTextEdit);
            this.dataLayoutControl1.Controls.Add(this.AddressTextEdit);
            this.dataLayoutControl1.Controls.Add(this.FingerprintCodeTextEdit);
            this.dataLayoutControl1.Controls.Add(this.ContractTypeImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.ContractPeriodTextEdit);
            this.dataLayoutControl1.Controls.Add(this.ContactEndDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.DateOfRecruitmentDateEdit);
            this.dataLayoutControl1.Controls.Add(this.AutoAlternateShiftCheckEdit);
            this.dataLayoutControl1.Controls.Add(this.FristShiftDateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.ShiftAlternatingPeriodTextEdit);
            this.dataLayoutControl1.Controls.Add(this.DepartmentIDLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.GroupIDLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.JobIDLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.AbsenceRegistionIDLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.DelayRegulationIDLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.SalaryRegulationIDLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.OverTimeRegulationIDLookUpEdit);
            this.dataLayoutControl1.DataSource = this.employeeBindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.HiddenItems.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForDepartment,
            this.ItemForGroup,
            this.ItemForJob,
            this.ItemForAbsenceRegulation,
            this.ItemForDelayRegulation,
            this.ItemForSalaryRegulation,
            this.ItemForOverTimeRegulation});
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 26);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(835, 404);
            this.dataLayoutControl1.TabIndex = 0;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            this.DepartmentTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "Department", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DepartmentTextEdit.Location = new System.Drawing.Point(394, 167);
            this.DepartmentTextEdit.Name = "DepartmentTextEdit";
            this.DepartmentTextEdit.Size = new System.Drawing.Size(290, 20);
            this.DepartmentTextEdit.StyleController = this.dataLayoutControl1;
            this.DepartmentTextEdit.TabIndex = 9;
            this.employeeBindingSource.DataSource = typeof(EasyStock.HR.Models.Employee);
            this.GroupTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "Group", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.GroupTextEdit.Location = new System.Drawing.Point(394, 215);
            this.GroupTextEdit.Name = "GroupTextEdit";
            this.GroupTextEdit.Size = new System.Drawing.Size(290, 20);
            this.GroupTextEdit.StyleController = this.dataLayoutControl1;
            this.GroupTextEdit.TabIndex = 11;
            this.JobTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "Job", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.JobTextEdit.Location = new System.Drawing.Point(394, 143);
            this.JobTextEdit.Name = "JobTextEdit";
            this.JobTextEdit.Size = new System.Drawing.Size(290, 20);
            this.JobTextEdit.StyleController = this.dataLayoutControl1;
            this.JobTextEdit.TabIndex = 33;
            this.AbsenceRegulationTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "AbsenceRegulation", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.AbsenceRegulationTextEdit.Location = new System.Drawing.Point(394, 47);
            this.AbsenceRegulationTextEdit.Name = "AbsenceRegulationTextEdit";
            this.AbsenceRegulationTextEdit.Size = new System.Drawing.Size(290, 20);
            this.AbsenceRegulationTextEdit.StyleController = this.dataLayoutControl1;
            this.AbsenceRegulationTextEdit.TabIndex = 35;
            this.DelayRegulationTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "DelayRegulation", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DelayRegulationTextEdit.Location = new System.Drawing.Point(394, 95);
            this.DelayRegulationTextEdit.Name = "DelayRegulationTextEdit";
            this.DelayRegulationTextEdit.Size = new System.Drawing.Size(290, 20);
            this.DelayRegulationTextEdit.StyleController = this.dataLayoutControl1;
            this.DelayRegulationTextEdit.TabIndex = 37;
            this.OverTimeRegulationTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "OverTimeRegulation", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.OverTimeRegulationTextEdit.Location = new System.Drawing.Point(394, 95);
            this.OverTimeRegulationTextEdit.Name = "OverTimeRegulationTextEdit";
            this.OverTimeRegulationTextEdit.Size = new System.Drawing.Size(290, 20);
            this.OverTimeRegulationTextEdit.StyleController = this.dataLayoutControl1;
            this.OverTimeRegulationTextEdit.TabIndex = 39;
            this.SalaryRegulationTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "SalaryRegulation", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.SalaryRegulationTextEdit.Location = new System.Drawing.Point(394, 191);
            this.SalaryRegulationTextEdit.Name = "SalaryRegulationTextEdit";
            this.SalaryRegulationTextEdit.Size = new System.Drawing.Size(290, 20);
            this.SalaryRegulationTextEdit.StyleController = this.dataLayoutControl1;
            this.SalaryRegulationTextEdit.TabIndex = 41;
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "ID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.IDTextEdit.Location = new System.Drawing.Point(230, 47);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.IDTextEdit.Properties.MaskSettings.Set("mask", "N0");
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(142, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 4;
            this.CodeTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "Code", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.CodeTextEdit.Location = new System.Drawing.Point(230, 71);
            this.CodeTextEdit.Name = "CodeTextEdit";
            this.CodeTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.CodeTextEdit.Size = new System.Drawing.Size(142, 20);
            this.CodeTextEdit.StyleController = this.dataLayoutControl1;
            this.CodeTextEdit.TabIndex = 5;
            this.NameTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "Name", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NameTextEdit.Location = new System.Drawing.Point(230, 95);
            this.NameTextEdit.Name = "NameTextEdit";
            this.NameTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.NameTextEdit.Size = new System.Drawing.Size(142, 20);
            this.NameTextEdit.StyleController = this.dataLayoutControl1;
            this.NameTextEdit.TabIndex = 6;
            this.GenderImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "Gender", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.GenderImageComboBoxEdit.Location = new System.Drawing.Point(230, 119);
            this.GenderImageComboBoxEdit.Name = "GenderImageComboBoxEdit";
            this.GenderImageComboBoxEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.GenderImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.GenderImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.GenderImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.GenderImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Homme", EasyStock.HR.GenderType.Male, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Femme", EasyStock.HR.GenderType.Female, 1)});
            this.GenderImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.GenderImageComboBoxEdit.Size = new System.Drawing.Size(142, 20);
            this.GenderImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.GenderImageComboBoxEdit.TabIndex = 7;
            this.BranchTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "Branch", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.BranchTextEdit.Location = new System.Drawing.Point(230, 143);
            this.BranchTextEdit.Name = "BranchTextEdit";
            this.BranchTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.BranchTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.BranchTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.BranchTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.BranchTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.BranchTextEdit.Properties.MaskSettings.Set("mask", "N0");
            this.BranchTextEdit.Size = new System.Drawing.Size(142, 20);
            this.BranchTextEdit.StyleController = this.dataLayoutControl1;
            this.BranchTextEdit.TabIndex = 8;
            this.NationalIDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "NationalID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NationalIDTextEdit.Location = new System.Drawing.Point(230, 47);
            this.NationalIDTextEdit.Name = "NationalIDTextEdit";
            this.NationalIDTextEdit.Size = new System.Drawing.Size(142, 20);
            this.NationalIDTextEdit.StyleController = this.dataLayoutControl1;
            this.NationalIDTextEdit.TabIndex = 13;
            this.InsuranceNoTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "InsuranceNo", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.InsuranceNoTextEdit.Location = new System.Drawing.Point(230, 71);
            this.InsuranceNoTextEdit.Name = "InsuranceNoTextEdit";
            this.InsuranceNoTextEdit.Size = new System.Drawing.Size(142, 20);
            this.InsuranceNoTextEdit.StyleController = this.dataLayoutControl1;
            this.InsuranceNoTextEdit.TabIndex = 14;
            this.InsuranceDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "InsuranceDate", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.InsuranceDateDateEdit.EditValue = null;
            this.InsuranceDateDateEdit.Location = new System.Drawing.Point(230, 95);
            this.InsuranceDateDateEdit.Name = "InsuranceDateDateEdit";
            this.InsuranceDateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.InsuranceDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.InsuranceDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.InsuranceDateDateEdit.Size = new System.Drawing.Size(142, 20);
            this.InsuranceDateDateEdit.StyleController = this.dataLayoutControl1;
            this.InsuranceDateDateEdit.TabIndex = 15;
            this.DrivingLicenseTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "DrivingLicense", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DrivingLicenseTextEdit.Location = new System.Drawing.Point(230, 119);
            this.DrivingLicenseTextEdit.Name = "DrivingLicenseTextEdit";
            this.DrivingLicenseTextEdit.Size = new System.Drawing.Size(142, 20);
            this.DrivingLicenseTextEdit.StyleController = this.dataLayoutControl1;
            this.DrivingLicenseTextEdit.TabIndex = 16;
            this.EndDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "EndDate", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.EndDateDateEdit.EditValue = null;
            this.EndDateDateEdit.Location = new System.Drawing.Point(230, 143);
            this.EndDateDateEdit.Name = "EndDateDateEdit";
            this.EndDateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.EndDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.EndDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.EndDateDateEdit.Size = new System.Drawing.Size(142, 20);
            this.EndDateDateEdit.StyleController = this.dataLayoutControl1;
            this.EndDateDateEdit.TabIndex = 17;
            this.BankNameTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "BankName", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.BankNameTextEdit.Location = new System.Drawing.Point(230, 167);
            this.BankNameTextEdit.Name = "BankNameTextEdit";
            this.BankNameTextEdit.Size = new System.Drawing.Size(142, 20);
            this.BankNameTextEdit.StyleController = this.dataLayoutControl1;
            this.BankNameTextEdit.TabIndex = 18;
            this.AccountNoTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "AccountNo", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.AccountNoTextEdit.Location = new System.Drawing.Point(230, 191);
            this.AccountNoTextEdit.Name = "AccountNoTextEdit";
            this.AccountNoTextEdit.Size = new System.Drawing.Size(142, 20);
            this.AccountNoTextEdit.StyleController = this.dataLayoutControl1;
            this.AccountNoTextEdit.TabIndex = 19;
            this.BirthDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "BirthDate", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.BirthDateDateEdit.EditValue = null;
            this.BirthDateDateEdit.Location = new System.Drawing.Point(230, 47);
            this.BirthDateDateEdit.Name = "BirthDateDateEdit";
            this.BirthDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.BirthDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.BirthDateDateEdit.Size = new System.Drawing.Size(142, 20);
            this.BirthDateDateEdit.StyleController = this.dataLayoutControl1;
            this.BirthDateDateEdit.TabIndex = 20;
            this.BirthPlaceTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "BirthPlace", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.BirthPlaceTextEdit.Location = new System.Drawing.Point(230, 71);
            this.BirthPlaceTextEdit.Name = "BirthPlaceTextEdit";
            this.BirthPlaceTextEdit.Size = new System.Drawing.Size(142, 20);
            this.BirthPlaceTextEdit.StyleController = this.dataLayoutControl1;
            this.BirthPlaceTextEdit.TabIndex = 21;
            this.NationalityTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "Nationality", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NationalityTextEdit.Location = new System.Drawing.Point(230, 95);
            this.NationalityTextEdit.Name = "NationalityTextEdit";
            this.NationalityTextEdit.Size = new System.Drawing.Size(142, 20);
            this.NationalityTextEdit.StyleController = this.dataLayoutControl1;
            this.NationalityTextEdit.TabIndex = 22;
            this.MaritalStatusImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "MaritalStatus", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.MaritalStatusImageComboBoxEdit.Location = new System.Drawing.Point(230, 119);
            this.MaritalStatusImageComboBoxEdit.Name = "MaritalStatusImageComboBoxEdit";
            this.MaritalStatusImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.MaritalStatusImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.MaritalStatusImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.MaritalStatusImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Célibataire", EasyStock.HR.MaritalStatus.Single, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Marié", EasyStock.HR.MaritalStatus.Married, 1)});
            this.MaritalStatusImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.MaritalStatusImageComboBoxEdit.Size = new System.Drawing.Size(142, 20);
            this.MaritalStatusImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.MaritalStatusImageComboBoxEdit.TabIndex = 23;
            this.MilitarilyStatusImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "MilitarilyStatus", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.MilitarilyStatusImageComboBoxEdit.Location = new System.Drawing.Point(230, 143);
            this.MilitarilyStatusImageComboBoxEdit.Name = "MilitarilyStatusImageComboBoxEdit";
            this.MilitarilyStatusImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.MilitarilyStatusImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.MilitarilyStatusImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.MilitarilyStatusImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Exemption", EasyStock.HR.MilitarilyStatus.Exemption, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Reporté", EasyStock.HR.MilitarilyStatus.Delayed, 1),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Accompli", EasyStock.HR.MilitarilyStatus.Completion, 2)});
            this.MilitarilyStatusImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.MilitarilyStatusImageComboBoxEdit.Size = new System.Drawing.Size(142, 20);
            this.MilitarilyStatusImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.MilitarilyStatusImageComboBoxEdit.TabIndex = 24;
            this.PhoneTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "Phone", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.PhoneTextEdit.Location = new System.Drawing.Point(230, 167);
            this.PhoneTextEdit.Name = "PhoneTextEdit";
            this.PhoneTextEdit.Size = new System.Drawing.Size(142, 20);
            this.PhoneTextEdit.StyleController = this.dataLayoutControl1;
            this.PhoneTextEdit.TabIndex = 25;
            this.EmailTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "Email", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.EmailTextEdit.Location = new System.Drawing.Point(230, 191);
            this.EmailTextEdit.Name = "EmailTextEdit";
            this.EmailTextEdit.Size = new System.Drawing.Size(142, 20);
            this.EmailTextEdit.StyleController = this.dataLayoutControl1;
            this.EmailTextEdit.TabIndex = 26;
            this.AddressTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "Address", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.AddressTextEdit.Location = new System.Drawing.Point(230, 215);
            this.AddressTextEdit.Name = "AddressTextEdit";
            this.AddressTextEdit.Size = new System.Drawing.Size(142, 20);
            this.AddressTextEdit.StyleController = this.dataLayoutControl1;
            this.AddressTextEdit.TabIndex = 27;
            this.FingerprintCodeTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "FingerprintCode", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.FingerprintCodeTextEdit.Location = new System.Drawing.Point(230, 167);
            this.FingerprintCodeTextEdit.Name = "FingerprintCodeTextEdit";
            this.FingerprintCodeTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.FingerprintCodeTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.FingerprintCodeTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.FingerprintCodeTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.FingerprintCodeTextEdit.Properties.MaskSettings.Set("mask", "N0");
            this.FingerprintCodeTextEdit.Size = new System.Drawing.Size(142, 20);
            this.FingerprintCodeTextEdit.StyleController = this.dataLayoutControl1;
            this.FingerprintCodeTextEdit.TabIndex = 28;
            this.ContractTypeImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "ContractType", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ContractTypeImageComboBoxEdit.Location = new System.Drawing.Point(230, 47);
            this.ContractTypeImageComboBoxEdit.Name = "ContractTypeImageComboBoxEdit";
            this.ContractTypeImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.ContractTypeImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.ContractTypeImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ContractTypeImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Temporaire", EasyStock.HR.ContractTypes.Temporary, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Permanent", EasyStock.HR.ContractTypes.Peremenant, 1)});
            this.ContractTypeImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.ContractTypeImageComboBoxEdit.Size = new System.Drawing.Size(142, 20);
            this.ContractTypeImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.ContractTypeImageComboBoxEdit.TabIndex = 29;
            this.ContractPeriodTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "ContractPeriod", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ContractPeriodTextEdit.Location = new System.Drawing.Point(230, 71);
            this.ContractPeriodTextEdit.Name = "ContractPeriodTextEdit";
            this.ContractPeriodTextEdit.Size = new System.Drawing.Size(142, 20);
            this.ContractPeriodTextEdit.StyleController = this.dataLayoutControl1;
            this.ContractPeriodTextEdit.TabIndex = 30;
            this.ContactEndDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "ContactEndDate", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ContactEndDateDateEdit.EditValue = null;
            this.ContactEndDateDateEdit.Location = new System.Drawing.Point(230, 95);
            this.ContactEndDateDateEdit.Name = "ContactEndDateDateEdit";
            this.ContactEndDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ContactEndDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ContactEndDateDateEdit.Size = new System.Drawing.Size(142, 20);
            this.ContactEndDateDateEdit.StyleController = this.dataLayoutControl1;
            this.ContactEndDateDateEdit.TabIndex = 31;
            this.DateOfRecruitmentDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "DateOfRecruitment", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DateOfRecruitmentDateEdit.EditValue = null;
            this.DateOfRecruitmentDateEdit.Location = new System.Drawing.Point(230, 119);
            this.DateOfRecruitmentDateEdit.Name = "DateOfRecruitmentDateEdit";
            this.DateOfRecruitmentDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DateOfRecruitmentDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DateOfRecruitmentDateEdit.Size = new System.Drawing.Size(142, 20);
            this.DateOfRecruitmentDateEdit.StyleController = this.dataLayoutControl1;
            this.DateOfRecruitmentDateEdit.TabIndex = 32;
            this.AutoAlternateShiftCheckEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "AutoAlternateShift", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.AutoAlternateShiftCheckEdit.Location = new System.Drawing.Point(24, 143);
            this.AutoAlternateShiftCheckEdit.Name = "AutoAlternateShiftCheckEdit";
            this.AutoAlternateShiftCheckEdit.Properties.Caption = "Changement automatique des équipes";
            this.AutoAlternateShiftCheckEdit.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Default;
            this.AutoAlternateShiftCheckEdit.Size = new System.Drawing.Size(348, 19);
            this.AutoAlternateShiftCheckEdit.StyleController = this.dataLayoutControl1;
            this.AutoAlternateShiftCheckEdit.TabIndex = 43;
            this.FristShiftDateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "FristShiftDate", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.FristShiftDateDateEdit.EditValue = null;
            this.FristShiftDateDateEdit.Location = new System.Drawing.Point(230, 166);
            this.FristShiftDateDateEdit.Name = "FristShiftDateDateEdit";
            this.FristShiftDateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.FristShiftDateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.FristShiftDateDateEdit.Size = new System.Drawing.Size(142, 20);
            this.FristShiftDateDateEdit.StyleController = this.dataLayoutControl1;
            this.FristShiftDateDateEdit.TabIndex = 44;
            this.ShiftAlternatingPeriodTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "ShiftAlternatingPeriod", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ShiftAlternatingPeriodTextEdit.Location = new System.Drawing.Point(230, 190);
            this.ShiftAlternatingPeriodTextEdit.Name = "ShiftAlternatingPeriodTextEdit";
            this.ShiftAlternatingPeriodTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.ShiftAlternatingPeriodTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.ShiftAlternatingPeriodTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.ShiftAlternatingPeriodTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.ShiftAlternatingPeriodTextEdit.Properties.MaskSettings.Set("mask", "N0");
            this.ShiftAlternatingPeriodTextEdit.Size = new System.Drawing.Size(142, 20);
            this.ShiftAlternatingPeriodTextEdit.StyleController = this.dataLayoutControl1;
            this.ShiftAlternatingPeriodTextEdit.TabIndex = 45;
            this.DepartmentIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "DepartmentID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DepartmentIDLookUpEdit.Location = new System.Drawing.Point(230, 167);
            this.DepartmentIDLookUpEdit.Name = "DepartmentIDLookUpEdit";
            this.DepartmentIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DepartmentIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DepartmentIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DepartmentIDLookUpEdit.Properties.NullText = "";
            this.DepartmentIDLookUpEdit.Size = new System.Drawing.Size(142, 20);
            this.DepartmentIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.DepartmentIDLookUpEdit.TabIndex = 46;
            this.GroupIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "GroupID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.GroupIDLookUpEdit.Location = new System.Drawing.Point(230, 191);
            this.GroupIDLookUpEdit.Name = "GroupIDLookUpEdit";
            this.GroupIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.GroupIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.GroupIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.GroupIDLookUpEdit.Properties.NullText = "";
            this.GroupIDLookUpEdit.Size = new System.Drawing.Size(142, 20);
            this.GroupIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.GroupIDLookUpEdit.TabIndex = 47;
            this.JobIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "JobID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.JobIDLookUpEdit.Location = new System.Drawing.Point(230, 143);
            this.JobIDLookUpEdit.Name = "JobIDLookUpEdit";
            this.JobIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.JobIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.JobIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.JobIDLookUpEdit.Properties.NullText = "";
            this.JobIDLookUpEdit.Size = new System.Drawing.Size(142, 20);
            this.JobIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.JobIDLookUpEdit.TabIndex = 48;
            this.AbsenceRegistionIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "AbsenceRegistionID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.AbsenceRegistionIDLookUpEdit.Location = new System.Drawing.Point(230, 47);
            this.AbsenceRegistionIDLookUpEdit.Name = "AbsenceRegistionIDLookUpEdit";
            this.AbsenceRegistionIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.AbsenceRegistionIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.AbsenceRegistionIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.AbsenceRegistionIDLookUpEdit.Properties.NullText = "";
            this.AbsenceRegistionIDLookUpEdit.Size = new System.Drawing.Size(142, 20);
            this.AbsenceRegistionIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.AbsenceRegistionIDLookUpEdit.TabIndex = 49;
            this.DelayRegulationIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "DelayRegulationID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DelayRegulationIDLookUpEdit.Location = new System.Drawing.Point(230, 71);
            this.DelayRegulationIDLookUpEdit.Name = "DelayRegulationIDLookUpEdit";
            this.DelayRegulationIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DelayRegulationIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DelayRegulationIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DelayRegulationIDLookUpEdit.Properties.NullText = "";
            this.DelayRegulationIDLookUpEdit.Size = new System.Drawing.Size(142, 20);
            this.DelayRegulationIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.DelayRegulationIDLookUpEdit.TabIndex = 50;
            this.SalaryRegulationIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "SalaryRegulationID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.SalaryRegulationIDLookUpEdit.Location = new System.Drawing.Point(230, 119);
            this.SalaryRegulationIDLookUpEdit.Name = "SalaryRegulationIDLookUpEdit";
            this.SalaryRegulationIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.SalaryRegulationIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.SalaryRegulationIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.SalaryRegulationIDLookUpEdit.Properties.NullText = "";
            this.SalaryRegulationIDLookUpEdit.Size = new System.Drawing.Size(142, 20);
            this.SalaryRegulationIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.SalaryRegulationIDLookUpEdit.TabIndex = 51;
            this.OverTimeRegulationIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.employeeBindingSource, "OverTimeRegulationID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.OverTimeRegulationIDLookUpEdit.Location = new System.Drawing.Point(230, 95);
            this.OverTimeRegulationIDLookUpEdit.Name = "OverTimeRegulationIDLookUpEdit";
            this.OverTimeRegulationIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.OverTimeRegulationIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.OverTimeRegulationIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.OverTimeRegulationIDLookUpEdit.Properties.NullText = "";
            this.OverTimeRegulationIDLookUpEdit.Size = new System.Drawing.Size(142, 20);
            this.OverTimeRegulationIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.OverTimeRegulationIDLookUpEdit.TabIndex = 52;
            this.ItemForDepartment.Control = this.DepartmentTextEdit;
            this.ItemForDepartment.Location = new System.Drawing.Point(0, 120);
            this.ItemForDepartment.Name = "ItemForDepartment";
            this.ItemForDepartment.Size = new System.Drawing.Size(421, 24);
            this.ItemForDepartment.Text = "Nom du département";
            this.ItemForDepartment.TextSize = new System.Drawing.Size(115, 13);
            this.ItemForGroup.Control = this.GroupTextEdit;
            this.ItemForGroup.Location = new System.Drawing.Point(0, 168);
            this.ItemForGroup.Name = "ItemForGroup";
            this.ItemForGroup.Size = new System.Drawing.Size(421, 24);
            this.ItemForGroup.Text = "Nom du groupe";
            this.ItemForGroup.TextSize = new System.Drawing.Size(115, 13);
            this.ItemForJob.Control = this.JobTextEdit;
            this.ItemForJob.Location = new System.Drawing.Point(0, 96);
            this.ItemForJob.Name = "ItemForJob";
            this.ItemForJob.Size = new System.Drawing.Size(421, 24);
            this.ItemForJob.TextSize = new System.Drawing.Size(115, 13);
            this.ItemForAbsenceRegulation.Control = this.AbsenceRegulationTextEdit;
            this.ItemForAbsenceRegulation.Location = new System.Drawing.Point(0, 0);
            this.ItemForAbsenceRegulation.MinSize = new System.Drawing.Size(181, 24);
            this.ItemForAbsenceRegulation.Name = "ItemForAbsenceRegulation";
            this.ItemForAbsenceRegulation.Size = new System.Drawing.Size(421, 24);
            this.ItemForAbsenceRegulation.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForAbsenceRegulation.TextSize = new System.Drawing.Size(115, 13);
            this.ItemForDelayRegulation.Control = this.DelayRegulationTextEdit;
            this.ItemForDelayRegulation.Location = new System.Drawing.Point(0, 48);
            this.ItemForDelayRegulation.Name = "ItemForDelayRegulation";
            this.ItemForDelayRegulation.Size = new System.Drawing.Size(421, 24);
            this.ItemForDelayRegulation.TextSize = new System.Drawing.Size(115, 13);
            this.ItemForSalaryRegulation.Control = this.SalaryRegulationTextEdit;
            this.ItemForSalaryRegulation.Location = new System.Drawing.Point(0, 144);
            this.ItemForSalaryRegulation.Name = "ItemForSalaryRegulation";
            this.ItemForSalaryRegulation.Size = new System.Drawing.Size(421, 24);
            this.ItemForSalaryRegulation.TextSize = new System.Drawing.Size(115, 13);
            this.ItemForOverTimeRegulation.Control = this.OverTimeRegulationTextEdit;
            this.ItemForOverTimeRegulation.Location = new System.Drawing.Point(0, 48);
            this.ItemForOverTimeRegulation.Name = "ItemForOverTimeRegulation";
            this.ItemForOverTimeRegulation.Size = new System.Drawing.Size(421, 24);
            this.ItemForOverTimeRegulation.TextSize = new System.Drawing.Size(115, 13);
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(835, 404);
            this.Root.TextVisible = false;
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.GroupStyle = DevExpress.Utils.GroupStyle.Card;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.emptySpaceItem2,
            this.tabbedControlGroup1,
            this.emptySpaceItem1});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(815, 384);
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(0, 249);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(376, 135);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            this.tabbedControlGroup1.CustomizationFormText = "tabbedControlGroup1";
            this.tabbedControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.tabbedControlGroup1.Name = "tabbedControlGroup1";
            this.tabbedControlGroup1.SelectedTabPage = this.layoutControlGroup2;
            this.tabbedControlGroup1.Size = new System.Drawing.Size(376, 249);
            this.tabbedControlGroup1.TabPages.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup2,
            this.layoutControlGroup3,
            this.layoutControlGroup4,
            this.layoutControlGroup5,
            this.layoutControlGroup6});
            this.layoutControlGroup6.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForAbsenceRegistionID,
            this.ItemForDelayRegulationID,
            this.ItemForOverTimeRegulationID,
            this.ItemForSalaryRegulationID,
            this.ItemForAutoAlternateShift,
            this.ItemForShiftAlternatingPeriod,
            this.ItemForFristShiftDate,
            this.emptySpaceItem3});
            this.layoutControlGroup6.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup6.Name = "layoutControlGroup6";
            this.layoutControlGroup6.Size = new System.Drawing.Size(352, 202);
            this.layoutControlGroup6.Text = "Règlements";
            this.ItemForAbsenceRegistionID.Control = this.AbsenceRegistionIDLookUpEdit;
            this.ItemForAbsenceRegistionID.Location = new System.Drawing.Point(0, 0);
            this.ItemForAbsenceRegistionID.Name = "ItemForAbsenceRegistionID";
            this.ItemForAbsenceRegistionID.Size = new System.Drawing.Size(352, 24);
            this.ItemForAbsenceRegistionID.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForDelayRegulationID.Control = this.DelayRegulationIDLookUpEdit;
            this.ItemForDelayRegulationID.Location = new System.Drawing.Point(0, 24);
            this.ItemForDelayRegulationID.Name = "ItemForDelayRegulationID";
            this.ItemForDelayRegulationID.Size = new System.Drawing.Size(352, 24);
            this.ItemForDelayRegulationID.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForOverTimeRegulationID.Control = this.OverTimeRegulationIDLookUpEdit;
            this.ItemForOverTimeRegulationID.Location = new System.Drawing.Point(0, 48);
            this.ItemForOverTimeRegulationID.Name = "ItemForOverTimeRegulationID";
            this.ItemForOverTimeRegulationID.Size = new System.Drawing.Size(352, 24);
            this.ItemForOverTimeRegulationID.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForSalaryRegulationID.Control = this.SalaryRegulationIDLookUpEdit;
            this.ItemForSalaryRegulationID.Location = new System.Drawing.Point(0, 72);
            this.ItemForSalaryRegulationID.Name = "ItemForSalaryRegulationID";
            this.ItemForSalaryRegulationID.Size = new System.Drawing.Size(352, 24);
            this.ItemForSalaryRegulationID.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForAutoAlternateShift.Control = this.AutoAlternateShiftCheckEdit;
            this.ItemForAutoAlternateShift.Location = new System.Drawing.Point(0, 96);
            this.ItemForAutoAlternateShift.Name = "ItemForAutoAlternateShift";
            this.ItemForAutoAlternateShift.Size = new System.Drawing.Size(352, 23);
            this.ItemForAutoAlternateShift.TextSize = new System.Drawing.Size(0, 0);
            this.ItemForAutoAlternateShift.TextVisible = false;
            this.ItemForShiftAlternatingPeriod.Control = this.ShiftAlternatingPeriodTextEdit;
            this.ItemForShiftAlternatingPeriod.Location = new System.Drawing.Point(0, 143);
            this.ItemForShiftAlternatingPeriod.Name = "ItemForShiftAlternatingPeriod";
            this.ItemForShiftAlternatingPeriod.Size = new System.Drawing.Size(352, 24);
            this.ItemForShiftAlternatingPeriod.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForFristShiftDate.Control = this.FristShiftDateDateEdit;
            this.ItemForFristShiftDate.Location = new System.Drawing.Point(0, 119);
            this.ItemForFristShiftDate.Name = "ItemForFristShiftDate";
            this.ItemForFristShiftDate.Size = new System.Drawing.Size(352, 24);
            this.ItemForFristShiftDate.TextSize = new System.Drawing.Size(202, 13);
            this.emptySpaceItem3.AllowHotTrack = false;
            this.emptySpaceItem3.Location = new System.Drawing.Point(0, 167);
            this.emptySpaceItem3.Name = "emptySpaceItem3";
            this.emptySpaceItem3.Size = new System.Drawing.Size(352, 35);
            this.emptySpaceItem3.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForID,
            this.ItemForName,
            this.ItemForGender,
            this.ItemForBranch,
            this.ItemForDepartmentID,
            this.ItemForGroupID,
            this.ItemForCode,
            this.emptySpaceItem4});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(352, 202);
            this.layoutControlGroup2.Text = "Général";
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.MaxSize = new System.Drawing.Size(352, 24);
            this.ItemForID.MinSize = new System.Drawing.Size(352, 24);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(352, 24);
            this.ItemForID.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForID.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForName.Control = this.NameTextEdit;
            this.ItemForName.Location = new System.Drawing.Point(0, 48);
            this.ItemForName.Name = "ItemForName";
            this.ItemForName.Size = new System.Drawing.Size(352, 24);
            this.ItemForName.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForGender.Control = this.GenderImageComboBoxEdit;
            this.ItemForGender.Location = new System.Drawing.Point(0, 72);
            this.ItemForGender.Name = "ItemForGender";
            this.ItemForGender.Size = new System.Drawing.Size(352, 24);
            this.ItemForGender.Text = "Genre";
            this.ItemForGender.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForBranch.Control = this.BranchTextEdit;
            this.ItemForBranch.Location = new System.Drawing.Point(0, 96);
            this.ItemForBranch.Name = "ItemForBranch";
            this.ItemForBranch.Size = new System.Drawing.Size(352, 24);
            this.ItemForBranch.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForDepartmentID.Control = this.DepartmentIDLookUpEdit;
            this.ItemForDepartmentID.Location = new System.Drawing.Point(0, 120);
            this.ItemForDepartmentID.Name = "ItemForDepartmentID";
            this.ItemForDepartmentID.Size = new System.Drawing.Size(352, 24);
            this.ItemForDepartmentID.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForGroupID.Control = this.GroupIDLookUpEdit;
            this.ItemForGroupID.Location = new System.Drawing.Point(0, 144);
            this.ItemForGroupID.Name = "ItemForGroupID";
            this.ItemForGroupID.Size = new System.Drawing.Size(352, 24);
            this.ItemForGroupID.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForCode.Control = this.CodeTextEdit;
            this.ItemForCode.Location = new System.Drawing.Point(0, 24);
            this.ItemForCode.Name = "ItemForCode";
            this.ItemForCode.Size = new System.Drawing.Size(352, 24);
            this.ItemForCode.TextSize = new System.Drawing.Size(202, 13);
            this.emptySpaceItem4.AllowHotTrack = false;
            this.emptySpaceItem4.Location = new System.Drawing.Point(0, 168);
            this.emptySpaceItem4.Name = "emptySpaceItem4";
            this.emptySpaceItem4.Size = new System.Drawing.Size(352, 34);
            this.emptySpaceItem4.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlGroup3.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForNationalID,
            this.ItemForInsuranceNo,
            this.ItemForInsuranceDate,
            this.ItemForDrivingLicense,
            this.ItemForEndDate,
            this.ItemForBankName,
            this.ItemForAccountNo,
            this.emptySpaceItem5});
            this.layoutControlGroup3.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup3.Name = "layoutControlGroup3";
            this.layoutControlGroup3.Size = new System.Drawing.Size(352, 202);
            this.layoutControlGroup3.Text = "Comptabilité";
            this.ItemForNationalID.Control = this.NationalIDTextEdit;
            this.ItemForNationalID.Location = new System.Drawing.Point(0, 0);
            this.ItemForNationalID.Name = "ItemForNationalID";
            this.ItemForNationalID.Size = new System.Drawing.Size(352, 24);
            this.ItemForNationalID.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForInsuranceNo.Control = this.InsuranceNoTextEdit;
            this.ItemForInsuranceNo.Location = new System.Drawing.Point(0, 24);
            this.ItemForInsuranceNo.Name = "ItemForInsuranceNo";
            this.ItemForInsuranceNo.Size = new System.Drawing.Size(352, 24);
            this.ItemForInsuranceNo.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForInsuranceDate.Control = this.InsuranceDateDateEdit;
            this.ItemForInsuranceDate.Location = new System.Drawing.Point(0, 48);
            this.ItemForInsuranceDate.Name = "ItemForInsuranceDate";
            this.ItemForInsuranceDate.Size = new System.Drawing.Size(352, 24);
            this.ItemForInsuranceDate.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForDrivingLicense.Control = this.DrivingLicenseTextEdit;
            this.ItemForDrivingLicense.Location = new System.Drawing.Point(0, 72);
            this.ItemForDrivingLicense.Name = "ItemForDrivingLicense";
            this.ItemForDrivingLicense.Size = new System.Drawing.Size(352, 24);
            this.ItemForDrivingLicense.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForEndDate.Control = this.EndDateDateEdit;
            this.ItemForEndDate.Location = new System.Drawing.Point(0, 96);
            this.ItemForEndDate.Name = "ItemForEndDate";
            this.ItemForEndDate.Size = new System.Drawing.Size(352, 24);
            this.ItemForEndDate.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForBankName.Control = this.BankNameTextEdit;
            this.ItemForBankName.Location = new System.Drawing.Point(0, 120);
            this.ItemForBankName.Name = "ItemForBankName";
            this.ItemForBankName.Size = new System.Drawing.Size(352, 24);
            this.ItemForBankName.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForAccountNo.Control = this.AccountNoTextEdit;
            this.ItemForAccountNo.Location = new System.Drawing.Point(0, 144);
            this.ItemForAccountNo.Name = "ItemForAccountNo";
            this.ItemForAccountNo.Size = new System.Drawing.Size(352, 24);
            this.ItemForAccountNo.TextSize = new System.Drawing.Size(202, 13);
            this.emptySpaceItem5.AllowHotTrack = false;
            this.emptySpaceItem5.Location = new System.Drawing.Point(0, 168);
            this.emptySpaceItem5.Name = "emptySpaceItem5";
            this.emptySpaceItem5.Size = new System.Drawing.Size(352, 34);
            this.emptySpaceItem5.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlGroup4.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForContractType,
            this.ItemForContractPeriod,
            this.ItemForContactEndDate,
            this.ItemForDateOfRecruitment,
            this.ItemForJobID,
            this.ItemForFingerprintCode,
            this.emptySpaceItem6});
            this.layoutControlGroup4.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup4.Name = "layoutControlGroup4";
            this.layoutControlGroup4.Size = new System.Drawing.Size(352, 202);
            this.layoutControlGroup4.Text = "Contrat";
            this.ItemForContractType.Control = this.ContractTypeImageComboBoxEdit;
            this.ItemForContractType.Location = new System.Drawing.Point(0, 0);
            this.ItemForContractType.Name = "ItemForContractType";
            this.ItemForContractType.Size = new System.Drawing.Size(352, 24);
            this.ItemForContractType.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForContractPeriod.Control = this.ContractPeriodTextEdit;
            this.ItemForContractPeriod.Location = new System.Drawing.Point(0, 24);
            this.ItemForContractPeriod.Name = "ItemForContractPeriod";
            this.ItemForContractPeriod.Size = new System.Drawing.Size(352, 24);
            this.ItemForContractPeriod.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForContactEndDate.Control = this.ContactEndDateDateEdit;
            this.ItemForContactEndDate.Location = new System.Drawing.Point(0, 48);
            this.ItemForContactEndDate.Name = "ItemForContactEndDate";
            this.ItemForContactEndDate.Size = new System.Drawing.Size(352, 24);
            this.ItemForContactEndDate.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForDateOfRecruitment.Control = this.DateOfRecruitmentDateEdit;
            this.ItemForDateOfRecruitment.Location = new System.Drawing.Point(0, 72);
            this.ItemForDateOfRecruitment.Name = "ItemForDateOfRecruitment";
            this.ItemForDateOfRecruitment.Size = new System.Drawing.Size(352, 24);
            this.ItemForDateOfRecruitment.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForJobID.Control = this.JobIDLookUpEdit;
            this.ItemForJobID.Location = new System.Drawing.Point(0, 96);
            this.ItemForJobID.Name = "ItemForJobID";
            this.ItemForJobID.Size = new System.Drawing.Size(352, 24);
            this.ItemForJobID.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForFingerprintCode.Control = this.FingerprintCodeTextEdit;
            this.ItemForFingerprintCode.Location = new System.Drawing.Point(0, 120);
            this.ItemForFingerprintCode.Name = "ItemForFingerprintCode";
            this.ItemForFingerprintCode.Size = new System.Drawing.Size(352, 24);
            this.ItemForFingerprintCode.TextSize = new System.Drawing.Size(202, 13);
            this.emptySpaceItem6.AllowHotTrack = false;
            this.emptySpaceItem6.Location = new System.Drawing.Point(0, 144);
            this.emptySpaceItem6.Name = "emptySpaceItem6";
            this.emptySpaceItem6.Size = new System.Drawing.Size(352, 58);
            this.emptySpaceItem6.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlGroup5.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForBirthDate,
            this.ItemForBirthPlace,
            this.ItemForNationality,
            this.ItemForMaritalStatus,
            this.ItemForMilitarilyStatus,
            this.ItemForPhone,
            this.ItemForEmail,
            this.ItemForAddress,
            this.emptySpaceItem7});
            this.layoutControlGroup5.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup5.Name = "layoutControlGroup5";
            this.layoutControlGroup5.Size = new System.Drawing.Size(352, 202);
            this.layoutControlGroup5.Text = "Personnelle";
            this.ItemForBirthDate.Control = this.BirthDateDateEdit;
            this.ItemForBirthDate.Location = new System.Drawing.Point(0, 0);
            this.ItemForBirthDate.MinSize = new System.Drawing.Size(181, 24);
            this.ItemForBirthDate.Name = "ItemForBirthDate";
            this.ItemForBirthDate.Size = new System.Drawing.Size(352, 24);
            this.ItemForBirthDate.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForBirthDate.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForBirthPlace.Control = this.BirthPlaceTextEdit;
            this.ItemForBirthPlace.Location = new System.Drawing.Point(0, 24);
            this.ItemForBirthPlace.MinSize = new System.Drawing.Size(181, 24);
            this.ItemForBirthPlace.Name = "ItemForBirthPlace";
            this.ItemForBirthPlace.Size = new System.Drawing.Size(352, 24);
            this.ItemForBirthPlace.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForBirthPlace.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForNationality.Control = this.NationalityTextEdit;
            this.ItemForNationality.Location = new System.Drawing.Point(0, 48);
            this.ItemForNationality.Name = "ItemForNationality";
            this.ItemForNationality.Size = new System.Drawing.Size(352, 24);
            this.ItemForNationality.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForMaritalStatus.Control = this.MaritalStatusImageComboBoxEdit;
            this.ItemForMaritalStatus.Location = new System.Drawing.Point(0, 72);
            this.ItemForMaritalStatus.Name = "ItemForMaritalStatus";
            this.ItemForMaritalStatus.Size = new System.Drawing.Size(352, 24);
            this.ItemForMaritalStatus.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForMilitarilyStatus.Control = this.MilitarilyStatusImageComboBoxEdit;
            this.ItemForMilitarilyStatus.Location = new System.Drawing.Point(0, 96);
            this.ItemForMilitarilyStatus.Name = "ItemForMilitarilyStatus";
            this.ItemForMilitarilyStatus.Size = new System.Drawing.Size(352, 24);
            this.ItemForMilitarilyStatus.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForPhone.Control = this.PhoneTextEdit;
            this.ItemForPhone.Location = new System.Drawing.Point(0, 120);
            this.ItemForPhone.Name = "ItemForPhone";
            this.ItemForPhone.Size = new System.Drawing.Size(352, 24);
            this.ItemForPhone.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForEmail.Control = this.EmailTextEdit;
            this.ItemForEmail.Location = new System.Drawing.Point(0, 144);
            this.ItemForEmail.Name = "ItemForEmail";
            this.ItemForEmail.Size = new System.Drawing.Size(352, 24);
            this.ItemForEmail.TextSize = new System.Drawing.Size(202, 13);
            this.ItemForAddress.Control = this.AddressTextEdit;
            this.ItemForAddress.Location = new System.Drawing.Point(0, 168);
            this.ItemForAddress.Name = "ItemForAddress";
            this.ItemForAddress.Size = new System.Drawing.Size(352, 24);
            this.ItemForAddress.TextSize = new System.Drawing.Size(202, 13);
            this.emptySpaceItem7.AllowHotTrack = false;
            this.emptySpaceItem7.Location = new System.Drawing.Point(0, 192);
            this.emptySpaceItem7.Name = "emptySpaceItem7";
            this.emptySpaceItem7.Size = new System.Drawing.Size(352, 10);
            this.emptySpaceItem7.TextSize = new System.Drawing.Size(0, 0);
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(376, 0);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(439, 384);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(835, 454);
            this.Controls.Add(this.dataLayoutControl1);
            this.Name = "EmployeeView";
            this.Text = "Données de l\'employé";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.DepartmentTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.employeeBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.GroupTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.JobTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.AbsenceRegulationTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DelayRegulationTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.OverTimeRegulationTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.SalaryRegulationTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.CodeTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NameTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.GenderImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BranchTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NationalIDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.InsuranceNoTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.InsuranceDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.InsuranceDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DrivingLicenseTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EndDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EndDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BankNameTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.AccountNoTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BirthDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BirthDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BirthPlaceTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NationalityTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.MaritalStatusImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.MilitarilyStatusImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.PhoneTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmailTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.AddressTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.FingerprintCodeTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContractTypeImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContractPeriodTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContactEndDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ContactEndDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateOfRecruitmentDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateOfRecruitmentDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.AutoAlternateShiftCheckEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.FristShiftDateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.FristShiftDateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ShiftAlternatingPeriodTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DepartmentIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.GroupIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.JobIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.AbsenceRegistionIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DelayRegulationIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.SalaryRegulationIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.OverTimeRegulationIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDepartment)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForGroup)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForJob)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAbsenceRegulation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDelayRegulation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSalaryRegulation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForOverTimeRegulation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabbedControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAbsenceRegistionID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDelayRegulationID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForOverTimeRegulationID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForSalaryRegulationID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAutoAlternateShift)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForShiftAlternatingPeriod)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFristShiftDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForGender)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBranch)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDepartmentID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForGroupID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNationalID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForInsuranceNo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForInsuranceDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDrivingLicense)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEndDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBankName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAccountNo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForContractType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForContractPeriod)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForContactEndDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDateOfRecruitment)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForJobID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFingerprintCode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBirthDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBirthPlace)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNationality)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForMaritalStatus)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForMilitarilyStatus)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForPhone)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAddress)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		private global::System.ComponentModel.IContainer components = null;

		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		private global::System.Windows.Forms.BindingSource employeeBindingSource;

		private global::DevExpress.XtraEditors.TextEdit CodeTextEdit;

		private global::DevExpress.XtraEditors.TextEdit NameTextEdit;

		private global::DevExpress.XtraEditors.ImageComboBoxEdit GenderImageComboBoxEdit;

		private global::DevExpress.XtraEditors.TextEdit BranchTextEdit;

		private global::DevExpress.XtraEditors.TextEdit DepartmentTextEdit;

		private global::DevExpress.XtraEditors.TextEdit GroupTextEdit;

		private global::DevExpress.XtraEditors.TextEdit NationalIDTextEdit;

		private global::DevExpress.XtraEditors.TextEdit InsuranceNoTextEdit;

		private global::DevExpress.XtraEditors.DateEdit InsuranceDateDateEdit;

		private global::DevExpress.XtraEditors.TextEdit DrivingLicenseTextEdit;

		private global::DevExpress.XtraEditors.DateEdit EndDateDateEdit;

		private global::DevExpress.XtraEditors.TextEdit BankNameTextEdit;

		private global::DevExpress.XtraEditors.TextEdit AccountNoTextEdit;

		private global::DevExpress.XtraEditors.DateEdit BirthDateDateEdit;

		private global::DevExpress.XtraEditors.TextEdit BirthPlaceTextEdit;

		private global::DevExpress.XtraEditors.TextEdit NationalityTextEdit;

		private global::DevExpress.XtraEditors.ImageComboBoxEdit MaritalStatusImageComboBoxEdit;

		private global::DevExpress.XtraEditors.ImageComboBoxEdit MilitarilyStatusImageComboBoxEdit;

		private global::DevExpress.XtraEditors.TextEdit PhoneTextEdit;

		private global::DevExpress.XtraEditors.TextEdit EmailTextEdit;

		private global::DevExpress.XtraEditors.TextEdit AddressTextEdit;

		private global::DevExpress.XtraEditors.TextEdit FingerprintCodeTextEdit;

		private global::DevExpress.XtraEditors.ImageComboBoxEdit ContractTypeImageComboBoxEdit;

		private global::DevExpress.XtraEditors.TextEdit ContractPeriodTextEdit;

		private global::DevExpress.XtraEditors.DateEdit ContactEndDateDateEdit;

		private global::DevExpress.XtraEditors.DateEdit DateOfRecruitmentDateEdit;

		private global::DevExpress.XtraEditors.TextEdit JobTextEdit;

		private global::DevExpress.XtraEditors.TextEdit AbsenceRegulationTextEdit;

		private global::DevExpress.XtraEditors.TextEdit DelayRegulationTextEdit;

		private global::DevExpress.XtraEditors.TextEdit OverTimeRegulationTextEdit;

		private global::DevExpress.XtraEditors.TextEdit SalaryRegulationTextEdit;

		private global::DevExpress.XtraEditors.CheckEdit AutoAlternateShiftCheckEdit;

		private global::DevExpress.XtraEditors.DateEdit FristShiftDateDateEdit;

		private global::DevExpress.XtraEditors.TextEdit ShiftAlternatingPeriodTextEdit;

		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;

		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;

		private global::DevExpress.XtraLayout.TabbedControlGroup tabbedControlGroup1;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup5;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForBirthDate;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForBirthPlace;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForNationality;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForMaritalStatus;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForMilitarilyStatus;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForPhone;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForEmail;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForAddress;

		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem7;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForName;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForGender;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForBranch;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDepartment;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDepartmentID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForGroup;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForGroupID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForCode;

		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem4;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup3;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForNationalID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForInsuranceNo;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForInsuranceDate;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDrivingLicense;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForEndDate;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForBankName;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForAccountNo;

		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem5;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup4;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForContractType;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForContractPeriod;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForContactEndDate;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDateOfRecruitment;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForJob;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForJobID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForFingerprintCode;

		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem6;

		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup6;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForAbsenceRegulation;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForAbsenceRegistionID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDelayRegulation;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDelayRegulationID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForOverTimeRegulation;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForOverTimeRegulationID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForSalaryRegulation;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForSalaryRegulationID;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForAutoAlternateShift;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForShiftAlternatingPeriod;

		private global::DevExpress.XtraLayout.LayoutControlItem ItemForFristShiftDate;

		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem3;

		private global::DevExpress.XtraEditors.LookUpEdit DepartmentIDLookUpEdit;

		private global::DevExpress.XtraEditors.LookUpEdit GroupIDLookUpEdit;

		private global::DevExpress.XtraEditors.LookUpEdit JobIDLookUpEdit;

		private global::DevExpress.XtraEditors.LookUpEdit AbsenceRegistionIDLookUpEdit;

		private global::DevExpress.XtraEditors.LookUpEdit DelayRegulationIDLookUpEdit;

		private global::DevExpress.XtraEditors.LookUpEdit SalaryRegulationIDLookUpEdit;

		private global::DevExpress.XtraEditors.LookUpEdit OverTimeRegulationIDLookUpEdit;
	}
}
