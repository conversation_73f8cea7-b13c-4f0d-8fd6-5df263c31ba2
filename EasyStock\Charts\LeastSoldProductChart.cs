﻿using DevExpress.XtraCharts;
using EasyStock.Charts.Models;
using EasyStock.Controller;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;

namespace EasyStock.Charts
{
    internal class LeastSoldProductChart : BaseChart
    {
        public LeastSoldProductChart()
        {
            Series MostSellingProductSeries = new Series("", ViewType.Doughnut);
            MostSellingProductSeries.ArgumentDataMember = "ProductName";
            MostSellingProductSeries.ValueScaleType = ScaleType.Numerical;
            MostSellingProductSeries.ValueDataMembersSerializable = "Quantity";
            MostSellingProductSeries.Label.TextPattern = "{VP:P0}";
            MostSellingProductSeries.LegendTextPattern = "{A}: {VP:P0}";
            MostSellingProductSeries.TopNOptions.ShowOthers = true;
            MostSellingProductSeries.TopNOptions.Enabled = true;
            MostSellingProductSeries.TopNOptions.Count = 10;
            ((DoughnutSeriesLabel)MostSellingProductSeries.Label).Position = PieSeriesLabelPosition.TwoColumns;
            ((DoughnutSeriesLabel)MostSellingProductSeries.Label).ResolveOverlappingMode = ResolveOverlappingMode.Default;
            ((DoughnutSeriesLabel)MostSellingProductSeries.Label).ResolveOverlappingMinIndent = 5;
            MostSellingProductSeries.ShowInLegend = true;
            base.AnimationStartMode = ChartAnimationMode.OnDataChanged;
            base.Series.Clear();
            base.Series.Add(MostSellingProductSeries);
            base.Height = 400;
        }

        public override string Caption
        {
            get
            {
                return "Les articles les moins vendus ce mois-ci";
            }
        }
        public override Color Color
        {
            get
            {
                return ColorTranslator.FromHtml("#4e342e");
            }
        }
        public override async Task<object> QueryData()
        {
            ERPDataContext db = new ERPDataContext();
            object dataSource = null;
            await Task.Run(delegate
            {
                dataSource = (from x in db.ProductTransactions
                              where (int)x.TransactionState == 2
                              select x into log
                              from item in from x in db.Products
                                           where x.ID == log.ProductID
                                           select x
                              where (int)log.Type == 4
                              select new { log.Quantity, item.Name, log.Price } into x
                              group x by x.Name into x
                              select new TopSoldProductsChartModel
                              {
                                  ProductName = x.Key,
                                  Quantity = (double?)x.Sum(c => c.Quantity)??0.00,
                                  Value = (double?)x.Sum(c => c.Quantity * c.Price) ?? 0.00
                              } into x
                              orderby x.Quantity
                              select x).Take(10).ToList();
            });
            return dataSource;
        }
    }
}
