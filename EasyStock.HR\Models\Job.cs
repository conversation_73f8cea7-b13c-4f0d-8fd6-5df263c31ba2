﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    public class Job : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Numéro")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Nom")]
        [StringLength(150)]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public string Name
        {
            get
            {
                return this.name;
            }
            set
            {
                base.SetProperty<string>(ref this.name, value, "Name");
            }
        }

        private int id;

        private string name;
    }
}
