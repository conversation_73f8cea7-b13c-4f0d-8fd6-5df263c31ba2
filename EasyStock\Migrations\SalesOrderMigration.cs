﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class SalesOrderMigration : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(SalesOrderMigration));

        string IMigrationMetadata.Id => "202107311724350_SalesOrderMigration";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.SalesOrderDetails", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                SalesOrderID = c.Int(false),
                ProductID = c.Int(false),
                UnitID = c.Int(false),
                Factor = c.Double(false),
                ColorID = c.Int(),
                SizeID = c.Int(),
                Expire = c.DateTime(),
                Quantity = c.Double(false),
                Price = c.Double(false),
                ProductCode = c.String(),
                Discount = c.Double(false),
                DiscountPercentage = c.Double(false),
                Tax = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.SalesOrders", t => t.SalesOrderID).Index(t => t.SalesOrderID);
            CreateTable("dbo.SalesOrders", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Code = c.String(),
                SourceID = c.Int(),
                SourceType = c.Int(false),
                BranchID = c.Int(false),
                Date = c.DateTime(false),
                Notes = c.String(),
                ExpireDate = c.DateTime(),
                DelevairyDate = c.DateTime(),
                CustomerID = c.Int(false),
                Discount = c.Double(false),
                OtherExpenses = c.Double(false),
                Tax = c.Double(false),
                Total = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Customers", t => t.CustomerID).Index(t => t.CustomerID);
        }

        public override void Down()
        {
            DropForeignKey("dbo.SalesOrderDetails", "SalesOrderID", "dbo.SalesOrders");
            DropForeignKey("dbo.SalesOrders", "CustomerID", "dbo.Customers");
            DropIndex("dbo.SalesOrders", new string[1] { "CustomerID" });
            DropIndex("dbo.SalesOrderDetails", new string[1] { "SalesOrderID" });
            DropTable("dbo.SalesOrders");
            DropTable("dbo.SalesOrderDetails");
        }
    }
}
