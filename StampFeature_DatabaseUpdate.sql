-- إضافة ميزة الطابع الجبائي إلى قاعدة البيانات
-- Stamp Feature Database Update Script

-- 1. إضافة عمود الطابع الجبائي إلى جدول PayDetails
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PayDetails]') AND name = 'StampAmount')
BEGIN
    ALTER TABLE [dbo].[PayDetails] 
    ADD [StampAmount] FLOAT NOT NULL DEFAULT 0.0
    PRINT 'تم إضافة عمود StampAmount إلى جدول PayDetails'
END
ELSE
BEGIN
    PRINT 'عمود StampAmount موجود بالفعل في جدول PayDetails'
END

-- 2. إضافة إعدادات الطابع الجبائي إلى جدول SystemSettings
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[SystemSettings]') AND name = 'StampCalculationMode')
BEGIN
    ALTER TABLE [dbo].[SystemSettings] 
    ADD [StampCalculationMode] INT NOT NULL DEFAULT 1 -- AlgerianLaw = 1
    PRINT 'تم إضافة عمود StampCalculationMode إلى جدول SystemSettings'
END
ELSE
BEGIN
    PRINT 'عمود StampCalculationMode موجود بالفعل في جدول SystemSettings'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[SystemSettings]') AND name = 'StampAmount')
BEGIN
    ALTER TABLE [dbo].[SystemSettings] 
    ADD [StampAmount] FLOAT NOT NULL DEFAULT 0.0
    PRINT 'تم إضافة عمود StampAmount إلى جدول SystemSettings'
END
ELSE
BEGIN
    PRINT 'عمود StampAmount موجود بالفعل في جدول SystemSettings'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[SystemSettings]') AND name = 'StampAccount')
BEGIN
    ALTER TABLE [dbo].[SystemSettings] 
    ADD [StampAccount] INT NOT NULL DEFAULT 0
    PRINT 'تم إضافة عمود StampAccount إلى جدول SystemSettings'
END
ELSE
BEGIN
    PRINT 'عمود StampAccount موجود بالفعل في جدول SystemSettings'
END

-- 3. تحديث القيم الافتراضية للإعدادات الموجودة
UPDATE [dbo].[SystemSettings] 
SET [StampCalculationMode] = 1, -- القانون الجزائري
    [StampAmount] = 0.0,
    [StampAccount] = 0
WHERE [StampCalculationMode] IS NULL OR [StampCalculationMode] = 0

PRINT 'تم تحديث الإعدادات الافتراضية للطابع الجبائي'

-- 4. إنشاء فهرس على عمود StampAmount في PayDetails (اختياري للأداء)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[PayDetails]') AND name = 'IX_PayDetails_StampAmount')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_PayDetails_StampAmount] 
    ON [dbo].[PayDetails] ([StampAmount])
    PRINT 'تم إنشاء فهرس على عمود StampAmount'
END

-- 5. إضافة تعليقات على الأعمدة الجديدة
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'مبلغ الطابع الجبائي المحسوب تلقائياً', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'PayDetails', 
    @level2type = N'COLUMN', @level2name = N'StampAmount'

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'نمط حساب الطابع الجبائي: 0=مبلغ ثابت، 1=القانون الجزائري', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'SystemSettings', 
    @level2type = N'COLUMN', @level2name = N'StampCalculationMode'

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'مبلغ الطابع الجبائي الثابت (يستخدم مع النمط الثابت)', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'SystemSettings', 
    @level2type = N'COLUMN', @level2name = N'StampAmount'

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'رقم الحساب المحاسبي للطابع الجبائي', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'SystemSettings', 
    @level2type = N'COLUMN', @level2name = N'StampAccount'

PRINT 'تم إضافة التعليقات على الأعمدة الجديدة'

-- 6. إنشاء view لعرض تفاصيل الدفع مع الطابع الجبائي
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[PayDetailsWithStamp]'))
    DROP VIEW [dbo].[PayDetailsWithStamp]

CREATE VIEW [dbo].[PayDetailsWithStamp] AS
SELECT 
    pd.*,
    CASE 
        WHEN pd.MethodType = 1 THEN 'Espèces avec timbre'  -- DrawerWithStamp
        WHEN pd.MethodType = 0 THEN 'Espèces sans timbre'  -- Drawer
        WHEN pd.MethodType = 2 THEN 'Virement bancaire'    -- Bank
        WHEN pd.MethodType = 3 THEN 'Sur compte'           -- Account
        WHEN pd.MethodType = 4 THEN 'Carte de paiement'   -- PayCard
        WHEN pd.MethodType = 5 THEN 'Reçu de caisse'      -- CashNote
        ELSE 'Autre'
    END AS MethodTypeName,
    (pd.Amount + pd.StampAmount) AS TotalAmountWithStamp,
    CASE 
        WHEN pd.StampAmount > 0 THEN 'Oui'
        ELSE 'Non'
    END AS HasStamp
FROM PayDetails pd

PRINT 'تم إنشاء view PayDetailsWithStamp'

PRINT '=== تم الانتهاء من تحديث قاعدة البيانات لميزة الطابع الجبائي ==='