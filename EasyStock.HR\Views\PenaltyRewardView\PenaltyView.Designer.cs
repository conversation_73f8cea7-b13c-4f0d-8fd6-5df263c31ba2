﻿namespace EasyStock.HR.Views.PenaltyRewardView
{
	// Token: 0x0200004E RID: 78
	public partial class PenaltyView : global::EasyStock.HR.MainViews.MasterView
	{
		// Token: 0x060002F3 RID: 755 RVA: 0x000374F8 File Offset: 0x000356F8
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060002F4 RID: 756 RVA: 0x00037530 File Offset: 0x00035730
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.noOfPenaltyValue = new DevExpress.XtraEditors.LabelControl();
            this.noOfPenaltylbl = new DevExpress.XtraEditors.LabelControl();
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.PenaltybindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.DateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.NoOfDaysSpinEdit = new DevExpress.XtraEditors.SpinEdit();
            this.AmountSpinEdit = new DevExpress.XtraEditors.SpinEdit();
            this.RemarksMemoEdit = new DevExpress.XtraEditors.MemoEdit();
            this.calcTypeRadioGroup = new DevExpress.XtraEditors.RadioGroup();
            this.EmployeeIdLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.emptySpaceItem3 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForcalcType = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForNoOfDays = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForAmount = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForRemarks = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForEmployeeId = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.PenaltybindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NoOfDaysSpinEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.AmountSpinEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.RemarksMemoEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.calcTypeRadioGroup.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmployeeIdLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForcalcType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNoOfDays)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAmount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForRemarks)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmployeeId)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).BeginInit();
            this.SuspendLayout();
            // 
            // dataLayoutControl1
            // 
            this.dataLayoutControl1.Controls.Add(this.noOfPenaltyValue);
            this.dataLayoutControl1.Controls.Add(this.noOfPenaltylbl);
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.DateDateEdit);
            this.dataLayoutControl1.Controls.Add(this.NoOfDaysSpinEdit);
            this.dataLayoutControl1.Controls.Add(this.AmountSpinEdit);
            this.dataLayoutControl1.Controls.Add(this.RemarksMemoEdit);
            this.dataLayoutControl1.Controls.Add(this.calcTypeRadioGroup);
            this.dataLayoutControl1.Controls.Add(this.EmployeeIdLookUpEdit);
            this.dataLayoutControl1.DataSource = this.PenaltybindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 26);
            this.dataLayoutControl1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(686, 316);
            this.dataLayoutControl1.TabIndex = 4;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            // 
            // noOfPenaltyValue
            // 
            this.noOfPenaltyValue.Location = new System.Drawing.Point(326, 68);
            this.noOfPenaltyValue.Name = "noOfPenaltyValue";
            this.noOfPenaltyValue.Size = new System.Drawing.Size(69, 13);
            this.noOfPenaltyValue.StyleController = this.dataLayoutControl1;
            this.noOfPenaltyValue.TabIndex = 15;
            // 
            // noOfPenaltylbl
            // 
            this.noOfPenaltylbl.Location = new System.Drawing.Point(249, 68);
            this.noOfPenaltylbl.Name = "noOfPenaltylbl";
            this.noOfPenaltylbl.Size = new System.Drawing.Size(73, 13);
            this.noOfPenaltylbl.StyleController = this.dataLayoutControl1;
            this.noOfPenaltylbl.TabIndex = 14;
            this.noOfPenaltylbl.Text = "N° de pénalités";
            // 
            // IDTextEdit
            // 
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.PenaltybindingSource, "ID", true));
            this.IDTextEdit.Location = new System.Drawing.Point(107, 44);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.IDTextEdit.Properties.MaskSettings.Set("mask", "N0");
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(288, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 4;
            // 
            // PenaltybindingSource
            // 
            this.PenaltybindingSource.DataSource = typeof(EasyStock.HR.Models.PenaltyReward);
            // 
            // DateDateEdit
            // 
            this.DateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.PenaltybindingSource, "Date", true));
            this.DateDateEdit.EditValue = null;
            this.DateDateEdit.Location = new System.Drawing.Point(107, 92);
            this.DateDateEdit.Name = "DateDateEdit";
            this.DateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.DateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DateDateEdit.Size = new System.Drawing.Size(288, 20);
            this.DateDateEdit.StyleController = this.dataLayoutControl1;
            this.DateDateEdit.TabIndex = 6;
            // 
            // NoOfDaysSpinEdit
            // 
            this.NoOfDaysSpinEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.PenaltybindingSource, "NoOfDays", true));
            this.NoOfDaysSpinEdit.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.NoOfDaysSpinEdit.Location = new System.Drawing.Point(107, 144);
            this.NoOfDaysSpinEdit.Name = "NoOfDaysSpinEdit";
            this.NoOfDaysSpinEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.NoOfDaysSpinEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.NoOfDaysSpinEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.NoOfDaysSpinEdit.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.NoOfDaysSpinEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.NoOfDaysSpinEdit.Properties.MaskSettings.Set("mask", "N0");
            this.NoOfDaysSpinEdit.Size = new System.Drawing.Size(288, 20);
            this.NoOfDaysSpinEdit.StyleController = this.dataLayoutControl1;
            this.NoOfDaysSpinEdit.TabIndex = 8;
            // 
            // AmountSpinEdit
            // 
            this.AmountSpinEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.PenaltybindingSource, "Amount", true));
            this.AmountSpinEdit.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.AmountSpinEdit.Location = new System.Drawing.Point(107, 168);
            this.AmountSpinEdit.Name = "AmountSpinEdit";
            this.AmountSpinEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.AmountSpinEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.AmountSpinEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.AmountSpinEdit.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.AmountSpinEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.AmountSpinEdit.Properties.MaskSettings.Set("mask", "F");
            this.AmountSpinEdit.Size = new System.Drawing.Size(288, 20);
            this.AmountSpinEdit.StyleController = this.dataLayoutControl1;
            this.AmountSpinEdit.TabIndex = 9;
            // 
            // RemarksMemoEdit
            // 
            this.RemarksMemoEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.PenaltybindingSource, "Remarks", true));
            this.RemarksMemoEdit.Location = new System.Drawing.Point(107, 192);
            this.RemarksMemoEdit.Name = "RemarksMemoEdit";
            this.RemarksMemoEdit.Size = new System.Drawing.Size(288, 65);
            this.RemarksMemoEdit.StyleController = this.dataLayoutControl1;
            this.RemarksMemoEdit.TabIndex = 10;
            // 
            // calcTypeRadioGroup
            // 
            this.calcTypeRadioGroup.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.PenaltybindingSource, "calcType", true));
            this.calcTypeRadioGroup.Location = new System.Drawing.Point(107, 116);
            this.calcTypeRadioGroup.Name = "calcTypeRadioGroup";
            this.calcTypeRadioGroup.Properties.Appearance.Options.UseTextOptions = true;
            this.calcTypeRadioGroup.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.calcTypeRadioGroup.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Default;
            this.calcTypeRadioGroup.Size = new System.Drawing.Size(288, 24);
            this.calcTypeRadioGroup.StyleController = this.dataLayoutControl1;
            this.calcTypeRadioGroup.TabIndex = 12;
            // 
            // EmployeeIdLookUpEdit
            // 
            this.EmployeeIdLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.PenaltybindingSource, "EmployeeId", true));
            this.EmployeeIdLookUpEdit.Location = new System.Drawing.Point(107, 68);
            this.EmployeeIdLookUpEdit.Name = "EmployeeIdLookUpEdit";
            this.EmployeeIdLookUpEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.EmployeeIdLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.EmployeeIdLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.EmployeeIdLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.EmployeeIdLookUpEdit.Properties.NullText = "";
            this.EmployeeIdLookUpEdit.Size = new System.Drawing.Size(138, 20);
            this.EmployeeIdLookUpEdit.StyleController = this.dataLayoutControl1;
            this.EmployeeIdLookUpEdit.TabIndex = 16;
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(686, 316);
            this.Root.TextVisible = false;
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.emptySpaceItem3,
            this.emptySpaceItem1,
            this.layoutControlGroup2});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(666, 296);
            // 
            // emptySpaceItem3
            // 
            this.emptySpaceItem3.AllowHotTrack = false;
            this.emptySpaceItem3.Location = new System.Drawing.Point(399, 0);
            this.emptySpaceItem3.Name = "emptySpaceItem3";
            this.emptySpaceItem3.Size = new System.Drawing.Size(267, 261);
            this.emptySpaceItem3.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 261);
            this.emptySpaceItem1.MinSize = new System.Drawing.Size(104, 24);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(666, 35);
            this.emptySpaceItem1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlGroup2
            // 
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForcalcType,
            this.ItemForNoOfDays,
            this.ItemForAmount,
            this.ItemForRemarks,
            this.ItemForDate,
            this.ItemForID,
            this.layoutControlItem1,
            this.ItemForEmployeeId,
            this.layoutControlItem2});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(399, 261);
            this.layoutControlGroup2.Text = "Informations";
            // 
            // ItemForcalcType
            // 
            this.ItemForcalcType.Control = this.calcTypeRadioGroup;
            this.ItemForcalcType.Location = new System.Drawing.Point(0, 72);
            this.ItemForcalcType.Name = "ItemForcalcType";
            this.ItemForcalcType.Size = new System.Drawing.Size(375, 28);
            this.ItemForcalcType.TextSize = new System.Drawing.Size(79, 13);
            // 
            // ItemForNoOfDays
            // 
            this.ItemForNoOfDays.Control = this.NoOfDaysSpinEdit;
            this.ItemForNoOfDays.Location = new System.Drawing.Point(0, 100);
            this.ItemForNoOfDays.Name = "ItemForNoOfDays";
            this.ItemForNoOfDays.Size = new System.Drawing.Size(375, 24);
            this.ItemForNoOfDays.TextSize = new System.Drawing.Size(79, 13);
            // 
            // ItemForAmount
            // 
            this.ItemForAmount.Control = this.AmountSpinEdit;
            this.ItemForAmount.Location = new System.Drawing.Point(0, 124);
            this.ItemForAmount.MaxSize = new System.Drawing.Size(375, 24);
            this.ItemForAmount.MinSize = new System.Drawing.Size(375, 24);
            this.ItemForAmount.Name = "ItemForAmount";
            this.ItemForAmount.Size = new System.Drawing.Size(375, 24);
            this.ItemForAmount.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForAmount.TextSize = new System.Drawing.Size(79, 13);
            // 
            // ItemForRemarks
            // 
            this.ItemForRemarks.Control = this.RemarksMemoEdit;
            this.ItemForRemarks.Location = new System.Drawing.Point(0, 148);
            this.ItemForRemarks.MaxSize = new System.Drawing.Size(375, 69);
            this.ItemForRemarks.MinSize = new System.Drawing.Size(375, 69);
            this.ItemForRemarks.Name = "ItemForRemarks";
            this.ItemForRemarks.Size = new System.Drawing.Size(375, 69);
            this.ItemForRemarks.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForRemarks.StartNewLine = true;
            this.ItemForRemarks.TextSize = new System.Drawing.Size(79, 13);
            // 
            // ItemForDate
            // 
            this.ItemForDate.Control = this.DateDateEdit;
            this.ItemForDate.Location = new System.Drawing.Point(0, 48);
            this.ItemForDate.Name = "ItemForDate";
            this.ItemForDate.Size = new System.Drawing.Size(375, 24);
            this.ItemForDate.TextSize = new System.Drawing.Size(79, 13);
            // 
            // ItemForID
            // 
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(375, 24);
            this.ItemForID.TextSize = new System.Drawing.Size(79, 13);
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.noOfPenaltylbl;
            this.layoutControlItem1.Location = new System.Drawing.Point(225, 24);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(77, 24);
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            // 
            // ItemForEmployeeId
            // 
            this.ItemForEmployeeId.Control = this.EmployeeIdLookUpEdit;
            this.ItemForEmployeeId.Location = new System.Drawing.Point(0, 24);
            this.ItemForEmployeeId.Name = "ItemForEmployeeId";
            this.ItemForEmployeeId.Size = new System.Drawing.Size(225, 24);
            this.ItemForEmployeeId.TextSize = new System.Drawing.Size(79, 13);
            // 
            // layoutControlItem2
            // 
            this.layoutControlItem2.Control = this.noOfPenaltyValue;
            this.layoutControlItem2.Location = new System.Drawing.Point(302, 24);
            this.layoutControlItem2.Name = "layoutControlItem2";
            this.layoutControlItem2.Size = new System.Drawing.Size(73, 24);
            this.layoutControlItem2.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem2.TextVisible = false;
            // 
            // PenaltyView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(686, 366);
            this.Controls.Add(this.dataLayoutControl1);
            this.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.Name = "PenaltyView";
            this.Text = "PenaltyView";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.PenaltybindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NoOfDaysSpinEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.AmountSpinEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.RemarksMemoEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.calcTypeRadioGroup.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.EmployeeIdLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForcalcType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNoOfDays)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAmount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForRemarks)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForEmployeeId)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x040004B6 RID: 1206
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x040004B7 RID: 1207
		private global::System.Windows.Forms.BindingSource PenaltybindingSource;

		// Token: 0x040004B8 RID: 1208
		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		// Token: 0x040004B9 RID: 1209
		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		// Token: 0x040004BA RID: 1210
		private global::DevExpress.XtraEditors.DateEdit DateDateEdit;

		// Token: 0x040004BB RID: 1211
		private global::DevExpress.XtraEditors.SpinEdit NoOfDaysSpinEdit;

		// Token: 0x040004BC RID: 1212
		private global::DevExpress.XtraEditors.SpinEdit AmountSpinEdit;

		// Token: 0x040004BD RID: 1213
		private global::DevExpress.XtraEditors.MemoEdit RemarksMemoEdit;

		// Token: 0x040004BE RID: 1214
		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		// Token: 0x040004BF RID: 1215
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		// Token: 0x040004C0 RID: 1216
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		// Token: 0x040004C1 RID: 1217
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDate;

		// Token: 0x040004C2 RID: 1218
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForcalcType;

		// Token: 0x040004C3 RID: 1219
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForNoOfDays;

		// Token: 0x040004C4 RID: 1220
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForAmount;

		// Token: 0x040004C5 RID: 1221
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForRemarks;

		// Token: 0x040004C6 RID: 1222
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;

		// Token: 0x040004C7 RID: 1223
		private global::DevExpress.XtraEditors.RadioGroup calcTypeRadioGroup;

		// Token: 0x040004C8 RID: 1224
		private global::DevExpress.XtraEditors.LabelControl noOfPenaltylbl;

		// Token: 0x040004C9 RID: 1225
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem3;

		// Token: 0x040004CA RID: 1226
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;

		// Token: 0x040004CB RID: 1227
		private global::DevExpress.XtraEditors.LabelControl noOfPenaltyValue;

		// Token: 0x040004CC RID: 1228
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem2;

		// Token: 0x040004CD RID: 1229
		private global::DevExpress.XtraEditors.LookUpEdit EmployeeIdLookUpEdit;

		// Token: 0x040004CE RID: 1230
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForEmployeeId;

		// Token: 0x040004CF RID: 1231
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;
	}
}
