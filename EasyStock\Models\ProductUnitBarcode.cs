﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
	[DisplayName("Code-barres des unités d'articles")]
	public class ProductUnitBarcode
	{
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		[ReadOnly(true)]
		[Display(Name = "Code")]
		public int ID { get; set; }

		[Display(Name = "Unité")]
		[Required]
		public ProductUnit Unit { get; set; }

		[Display(Name = "Unité ID")]
		public int UnitID { get; set; }

		[NotMapped]
		[ReadOnly(true)]
		[Display(Name = "Article")]
		public Product Product
		{
			get
			{
				return this.Unit.Product;
			}
		}

		[NotMapped]
		[ReadOnly(true)]
		[Display(Name = "Article Code")]
		public int ProductID
		{
			get
			{
				ProductUnit unit = this.Unit;
				return (unit != null) ? unit.ProductID : 0;
			}
		}

		[Required(ErrorMessage = "Vous devez saisir un numéro de code-barres")]
		[StringLength(50)]
		[Display(Name = "Code-barres")]
		public string Barcode { get; set; }
	}
}
