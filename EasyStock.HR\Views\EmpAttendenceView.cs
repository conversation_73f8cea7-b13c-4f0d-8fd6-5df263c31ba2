﻿using DevExpress.Data.Linq;
using EasyStock.HR.BLL;
using EasyStock.HR.MainViews;
using System;

namespace EasyStock.HR.Views
{
    public partial class EmpAttendenceView : MasterView
    {
        public static EmpAttendenceView Instance
        {
            get
            {
                bool flag = EmpAttendenceView.instance == null || EmpAttendenceView.instance.IsDisposed;
                if (flag)
                {
                    EmpAttendenceView.instance = new EmpAttendenceView();
                }
                return EmpAttendenceView.instance;
            }
        }

        public EmpAttendenceView()
        {
            this.context = new HRDataContext();
            this.InitializeComponent();
        }

        private Type ReportModelType { get; set; }

        public string KeyExpression { get; set; } = "ID";

        public override void RefreshData()
        {
            this.FeedbackSource = new EntityInstantFeedbackSource();
            this.FeedbackSource.DesignTimeElementType = this.ReportModelType;
            this.FeedbackSource.KeyExpression = this.KeyExpression;
            this.FeedbackSource.GetQueryable += this.FeedbackSource_GetQueryable;
            this.gridControl1.DataSource = this.FeedbackSource;
            base.RefreshData();
        }

        private void FeedbackSource_GetQueryable(object sender, GetQueryableEventArgs e)
        {
            e.QueryableSource = EmpAttendanceBLL.CreateAll(DateTime.Today);
            e.Tag = this.context;
        }

        private static EmpAttendenceView instance;

        private HRDataContext context;

        private EntityInstantFeedbackSource FeedbackSource;
    }
}
