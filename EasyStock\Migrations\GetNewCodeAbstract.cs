﻿using System;
using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
	// Token: 0x020004B9 RID: 1209
	[GeneratedCode("EntityFramework.Migrations", "6.5.1")]
	public sealed class GetNewCodeAbstract : DbMigration, IMigrationMetadata
	{
		// Token: 0x060023F1 RID: 9201 RVA: 0x0001290E File Offset: 0x00010B0E
		public override void Up()
		{
			base.AlterColumn("dbo.ContractorAbstracts", "Code", (ColumnBuilder c) => c.String(new bool?(false), null, null, null, null, null, null, null, null), null);
		}

		// Token: 0x060023F2 RID: 9202 RVA: 0x00012942 File Offset: 0x00010B42
		public override void Down()
		{
			base.AlterColumn("dbo.ContractorAbstracts", "Code", (ColumnBuilder c) => c.Int(new bool?(false), false, null, null, null, null, null), null);
		}

		// Token: 0x17000BC7 RID: 3015
		// (get) Token: 0x060023F3 RID: 9203 RVA: 0x001F5658 File Offset: 0x001F3858
		string IMigrationMetadata.Id
		{
			get
			{
				return "202108191129551_GetNewCodeAbstract";
			}
		}

		// Token: 0x17000BC8 RID: 3016
		// (get) Token: 0x060023F4 RID: 9204 RVA: 0x001EFA00 File Offset: 0x001EDC00
		string IMigrationMetadata.Source
		{
			get
			{
				return null;
			}
		}

		// Token: 0x17000BC9 RID: 3017
		// (get) Token: 0x060023F5 RID: 9205 RVA: 0x001F5670 File Offset: 0x001F3870
		string IMigrationMetadata.Target
		{
			get
			{
				return this.Resources.GetString("Target");
			}
		}

		// Token: 0x04002B63 RID: 11107
		private readonly ResourceManager Resources = new ResourceManager(typeof(GetNewCodeAbstract));
	}
}
