﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class outGoingBillEntity : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(outGoingBillEntity));

        string IMigrationMetadata.Id => "202107171224081_outGoingBillEntity";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.OutgoingBill", (ColumnBuilder c) => new
            {
                ID = c.Int(false),
                SourceID = c.Int(),
                SourceType = c.Int(false),
                PersonalID = c.Int(false),
                PersonalType = c.Int(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Bills", t => t.ID).Index(t => t.ID);
        }

        public override void Down()
        {
            DropForeignKey("dbo.OutgoingBill", "ID", "dbo.Bills");
            DropIndex("dbo.OutgoingBill", new string[1] { "ID" });
            DropTable("dbo.OutgoingBill");
        }
    }
}
