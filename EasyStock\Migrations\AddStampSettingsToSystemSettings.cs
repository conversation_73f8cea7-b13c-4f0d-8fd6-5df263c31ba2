namespace EasyStock.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddStampSettingsToSystemSettings : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.SystemSettings", "StampCalculationMode", c => c.Int(nullable: false, defaultValue: 1)); // AlgerianLaw = 1
            AddColumn("dbo.SystemSettings", "StampAmount", c => c.Double(nullable: false, defaultValue: 0.0));
            AddColumn("dbo.SystemSettings", "StampAccount", c => c.Int(nullable: false, defaultValue: 0));
        }
        
        public override void Down()
        {
            DropColumn("dbo.SystemSettings", "StampAccount");
            DropColumn("dbo.SystemSettings", "StampAmount");
            DropColumn("dbo.SystemSettings", "StampCalculationMode");
        }
    }
}