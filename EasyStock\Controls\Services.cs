﻿using CustomControls.Models;
using System.Collections.Generic;
using System.Linq;

namespace EasyStock.Controls
{
    internal static class Services
    {
        public static ICollection<Product> GetProductsByCategory(this ICollection<Product> products, int categoryID)
        {
            bool flag = products != null;
            ICollection<Product> result;
            if (flag)
            {
                result = (from x in products
                          where x.CategoryId == categoryID
                          select x).ToList<Product>();
            }
            else
            {
                result = null;
            }
            return result;
        }

        public static ICollection<Product> GetProductsByCategory(this ICollection<Product> products, Category category)
        {
            return products.GetProductsByCategory(category.id);
        }

        public static ICollection<Category> GetDummyCategoryData()
        {
            return new List<Category>
            {
                new Category
                {
                    id = 1,
                    name = "Category 1"
                },
                new Category
                {
                    id = 2,
                    name = "Category 2"
                },
                new Category
                {
                    id = 3,
                    name = "Category 3"
                },
                new Category
                {
                    id = 4,
                    name = "Category 4"
                },
                new Category
                {
                    id = 5,
                    name = "Category 5"
                }
            };
        }

        public static ICollection<Product> GetDummyProductData()
        {
            return new List<Product>
            {
                new Product
                {
                    ID = 1,
                    CategoryId = 1,
                    Name = "Products 1 Category 1",
                    Price = 1.5
                },
                new Product
                {
                    ID = 2,
                    CategoryId = 1,
                    Name = "Products 2 Category 1",
                    Price = 1.5
                },
                new Product
                {
                    ID = 3,
                    CategoryId = 1,
                    Name = "Products 3 Category 1",
                    Price = 1.5
                },
                new Product
                {
                    ID = 4,
                    CategoryId = 1,
                    Name = "Products 4 Category 1",
                    Price = 1.5
                },
                new Product
                {
                    ID = 5,
                    CategoryId = 1,
                    Name = "Products 5 Category 1",
                    Price = 1.5
                },
                new Product
                {
                    ID = 1,
                    CategoryId = 2,
                    Name = "Products 1 Category 2",
                    Price = 1.5
                },
                new Product
                {
                    ID = 2,
                    CategoryId = 2,
                    Name = "Products 2 Category 2",
                    Price = 1.5
                },
                new Product
                {
                    ID = 3,
                    CategoryId = 2,
                    Name = "Products 3 Category 2",
                    Price = 1.5
                },
                new Product
                {
                    ID = 4,
                    CategoryId = 2,
                    Name = "Products 4 Category 2",
                    Price = 1.5
                },
                new Product
                {
                    ID = 5,
                    CategoryId = 2,
                    Name = "Products 5 Category 2",
                    Price = 1.5
                },
                new Product
                {
                    ID = 1,
                    CategoryId = 3,
                    Name = "Products 1 Category 3",
                    Price = 1.5
                },
                new Product
                {
                    ID = 2,
                    CategoryId = 3,
                    Name = "Products 2 Category 3",
                    Price = 1.5
                },
                new Product
                {
                    ID = 3,
                    CategoryId = 3,
                    Name = "Products 3 Category 3",
                    Price = 1.5
                },
                new Product
                {
                    ID = 4,
                    CategoryId = 3,
                    Name = "Products 4 Category 3",
                    Price = 1.5
                },
                new Product
                {
                    ID = 5,
                    CategoryId = 3,
                    Name = "Products 5 Category 3",
                    Price = 1.5
                },
                new Product
                {
                    ID = 1,
                    CategoryId = 4,
                    Name = "Products 1 Category 4",
                    Price = 1.5
                },
                new Product
                {
                    ID = 2,
                    CategoryId = 4,
                    Name = "Products 2 Category 4",
                    Price = 1.5
                },
                new Product
                {
                    ID = 3,
                    CategoryId = 4,
                    Name = "Products 3 Category 4",
                    Price = 1.5
                },
                new Product
                {
                    ID = 4,
                    CategoryId = 4,
                    Name = "Products 4 Category 4",
                    Price = 1.5
                },
                new Product
                {
                    ID = 5,
                    CategoryId = 4,
                    Name = "Products 5 Category 4",
                    Price = 1.5
                },
                new Product
                {
                    ID = 1,
                    CategoryId = 5,
                    Name = "Products 1 Category 5",
                    Price = 1.5
                },
                new Product
                {
                    ID = 2,
                    CategoryId = 5,
                    Name = "Products 2 Category 5",
                    Price = 1.5
                },
                new Product
                {
                    ID = 3,
                    CategoryId = 5,
                    Name = "Products 3 Category 5",
                    Price = 1.5
                },
                new Product
                {
                    ID = 4,
                    CategoryId = 5,
                    Name = "Products 4 Category 5",
                    Price = 1.5
                },
                new Product
                {
                    ID = 5,
                    CategoryId = 5,
                    Name = "Products 5 Category 5",
                    Price = 1.5
                }
            };
        }
    }
}
