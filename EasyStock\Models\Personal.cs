﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [Table("Personals")]
    public abstract class Personal : BaseNotifyPropertyChangedModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [ReadOnly(true)]
        [Display(Name = "Code")]
        public int ID { get; set; }

        [StringLength(400)]
        [Required(ErrorMessage = "Ce champ est requis")]
        [Display(Name = "Nom")]
        public string Name { get; set; }

        [Display(Name = "Catégorie")]
        public PersonalGroup Group { get; set; }

        [Display(Name = "Code de catégorie")]
        public int? GroupID { get; set; }

        [Display(Name = "Ville")]
        [StringLength(250)]
        public string City { get; set; }

        [StringLength(250)]
        [Display(Name = "Adresse")]
        public string Address { get; set; }

        [StringLength(50)]
        [Display(Name = "Téléphone")]
        public string Phone { get; set; }

        [Display(Name = "Mobile")]
        [StringLength(50)]
        public string Mobile { get; set; }

        [Display(Name = "Email")]
        [StringLength(250)]
        public string EMail { get; set; }

        [Display(Name = "Numéro de compte bancaire")]
        public string BankAccount { get; set; }

        [Display(Name = "Lié à un autre compte")]
        [DefaultValue(false)]
        public bool LinkedToAccount { get; set; }

        [Display(Name = "Compte")]
        public Account Account { get; set; }

        [Display(Name = "Numéro de compte")]
        public int AccountID { get; set; }

        [Display(Name = "N.I.F")]
        public string TaxFileNumber { get; set; }
        [Display(Name = "N.I.S")]
        public string NIS { get; set; }
        [Display(Name = "RC N°")]
        public string RC { get; set; }
        [Display(Name = "ART")]
        public string ART { get; set; }


        [Display(Name = "Succursale")]
        public Branch Branch
        {
            get
            {
                return this.branch;
            }
            set
            {
                base.SetProperty<Branch>(ref this.branch, value, "Branch");
            }
        }

        [Display(Name = "Branche")]
        public int? BranchID
        {
            get
            {
                return this.branchID;
            }
            set
            {
                base.SetProperty<int?>(ref this.branchID, value, "BranchID");
            }
        }

        private Branch branch;

        private int? branchID;
    }
}
