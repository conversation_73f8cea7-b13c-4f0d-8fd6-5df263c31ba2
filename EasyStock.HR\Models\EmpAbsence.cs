﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    [DisplayColumn("Absences")]
    public class EmpAbsence : BaseNotifyPropertyChangedModel
    {
        private int id;


        private int empid;

        private DateTime fromdate;

        private DateTime todate;

        private int duration;

        private string notes;

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return id;
            }
            set
            {
                SetProperty(ref id, value, "ID");
            }
        }

        [Display(Name = "Employé")]
        [Range(1, int.MaxValue, ErrorMessage = "*")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int EmpID
        {
            get
            {
                return empid;
            }
            set
            {
                SetProperty(ref empid, value, "EmpID");
            }
        }

        [Display(Name = "Type d'absence")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public EAbsenceType AbsenceType { get; set; }

        [Display(Name = "Date de début")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public DateTime FromDate
        {
            get
            {
                return fromdate;
            }
            set
            {
                SetProperty(ref fromdate, value, "FromDate");
            }
        }

        [Display(Name = "Date de fin")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public DateTime ToDate
        {
            get
            {
                return todate;
            }
            set
            {
                SetProperty(ref todate, value, "ToDate");
            }
        }

        [Display(Name = "Congés payés")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public EAbsencePaid AbsencePaid { get; set; }

        [Display(Name = "Durée")]
        public int Duration
        {
            get
            {
                return duration;
            }
            set
            {
                SetProperty(ref duration, value, "Duration");
            }
        }

        [Display(Name = "Notes")]
        public string Notes
        {
            get
            {
                return notes;
            }
            set
            {
                SetProperty(ref notes, value, "Notes");
            }
        }
    }
}
