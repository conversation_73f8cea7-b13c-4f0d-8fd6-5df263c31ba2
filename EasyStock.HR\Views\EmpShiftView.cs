﻿using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using EasyStock.Common;
using EasyStock.HR.BLL;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class EmpShiftView : MasterView
    {
        public static EmpShiftView Instance
        {
            get
            {
                bool flag = EmpShiftView.instance == null || EmpShiftView.instance.IsDisposed;
                if (flag)
                {
                    EmpShiftView.instance = new EmpShiftView();
                }
                return EmpShiftView.instance;
            }
        }

        public EmpShift shift
        {
            get
            {
                return this.empShiftBindingSource.Current as EmpShift;
            }
            set
            {
                this.empShiftBindingSource.DataSource = value;
            }
        }

        public EmpShiftView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            this.context = new HRDataContext();
            base.Shown += this.EmpShiftView_Shown;
            this.FromDateDateEdit.EditValueChanged += this.FromDateDateEdit_EditValueChanged;
            this.ToDateDateEdit.EditValueChanged += this.ToDateDateEdit_EditValueChanged;
        }

        private void ToDateDateEdit_EditValueChanged(object sender, EventArgs e)
        {
            this.getDuration();
        }

        private void FromDateDateEdit_EditValueChanged(object sender, EventArgs e)
        {
            this.getDuration();
        }

        private void getDuration()
        {
            int days = (this.shift.ToDate - this.shift.FromDate).Days + 1;
            this.shift.Duration = days;
        }

        private void EmpShiftView_Shown(object sender, EventArgs e)
        {
            bool flag = this.shift == null;
            if (flag)
            {
                this.New();
            }
        }

        public override void New()
        {
            this.shift = new EmpShift
            {
                FromDate = this.context.GetServerTime(),
                ToDate = this.context.GetServerTime()
            };
            base.New();
        }

        public void GoTo(int id)
        {
            EmpShift sourceDepr = this.context.EmpShifts.SingleOrDefault((EmpShift x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.shift = sourceDepr;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable");
            }
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.getDuration();
                this.context.EmpShifts.AddOrUpdate(new EmpShift[]
                {
                    this.shift
                });
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.EmpIDTextEdit.Properties.DataSource = EmployeeBLL.GetActive();
            this.EmpIDTextEdit.Properties.DisplayMember = "Name";
            this.EmpIDTextEdit.Properties.ValueMember = "ID";
            this.EmpIDTextEdit.Properties.NullText = "";
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("ID", "Code"));
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Nom"));
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("PayPeriod", "Période de paie"));
            base.RefreshData();
        }

        public override void Delete()
        {
        }

        private static EmpShiftView instance;

        private HRDataContext context;
    }
}
