﻿using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using EasyStock.Common;
using EasyStock.HR.BLL;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class EmpAbsenceView : MasterView
    {
        public static EmpAbsenceView Instance
        {
            get
            {
                bool flag = EmpAbsenceView.instance == null || EmpAbsenceView.instance.IsDisposed;
                if (flag)
                {
                    EmpAbsenceView.instance = new EmpAbsenceView();
                }
                return EmpAbsenceView.instance;
            }
        }

        public EmpAbsence absence
        {
            get
            {
                return this.empAbsenceBindingSource.Current as EmpAbsence;
            }
            set
            {
                this.empAbsenceBindingSource.DataSource = value;
            }
        }

        public EmpAbsenceView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            this.context = new HRDataContext();
            this.FromDateDateEdit.EditValueChanged += this.FromDateDateEdit_EditValueChanged;
            this.ToDateDateEdit.EditValueChanged += this.ToDateDateEdit_EditValueChanged;
            base.Shown += this.EmpAbsenceView_Shown;
        }

        private void ToDateDateEdit_EditValueChanged(object sender, EventArgs e)
        {
            this.getDuration();
        }

        private void FromDateDateEdit_EditValueChanged(object sender, EventArgs e)
        {
            this.getDuration();
        }

        private void EmpAbsenceView_Shown(object sender, EventArgs e)
        {
            bool flag = this.absence == null;
            if (flag)
            {
                this.New();
            }
        }

        public override void New()
        {
            this.absence = new EmpAbsence
            {
                FromDate = this.context.GetServerTime(),
                ToDate = this.context.GetServerTime()
            };
            base.New();
        }

        private void getDuration()
        {
            int days = (this.absence.ToDate - this.absence.FromDate).Days + 1;
            this.absence.Duration = days;
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                this.getDuration();
                base.DisableValidation(this.dataLayoutControl1);
                this.context.EmpAbsences.AddOrUpdate(new EmpAbsence[]
                {
                    this.absence
                });
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.EmpIDTextEdit.Properties.DataSource = EmployeeBLL.GetActive();
            this.EmpIDTextEdit.Properties.DisplayMember = "Name";
            this.EmpIDTextEdit.Properties.ValueMember = "ID";
            this.EmpIDTextEdit.Properties.NullText = "";
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("ID", "Code"));
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Nom"));
            this.EmpIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("PayPeriod", "Période de paiement"));
            base.RefreshData();
        }

        public void GoTo(int id)
        {
            EmpAbsence sourceDepr = this.context.EmpAbsences.SingleOrDefault((EmpAbsence x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.absence = sourceDepr;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable");
            }
        }

        public override void Delete()
        {
        }

        private static EmpAbsenceView instance;

        private HRDataContext context;
    }
}
