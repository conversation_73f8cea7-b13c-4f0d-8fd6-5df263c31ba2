﻿<XtraSerializer version="1.0" application="View">
  <property name="#LayoutVersion" />
  <property name="#LayoutScaleFactor">@1,Width=1@1,Height=1</property>
  <property name="OptionsView" isnull="true" iskey="true">
    <property name="NewItemRowPosition">Top</property>
    <property name="ShowGroupPanel">false</property>
  </property>
  <property name="ActiveFilterEnabled">true</property>
  <property name="Columns" iskey="true" value="10">
    <property name="Item1" isnull="true" iskey="true">
      <property name="Name">colAmount</property>
      <property name="VisibleIndex">3</property>
      <property name="Visible">true</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item2" isnull="true" iskey="true">
      <property name="Name">colMethodType</property>
      <property name="VisibleIndex">1</property>
      <property name="Visible">true</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item3" isnull="true" iskey="true">
      <property name="Name">colMethodID</property>
      <property name="VisibleIndex">2</property>
      <property name="Visible">true</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item4" isnull="true" iskey="true">
      <property name="Name">colCode</property>
      <property name="VisibleIndex">0</property>
      <property name="Visible">true</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item5" isnull="true" iskey="true">
      <property name="Name">colCurrancyID</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item6" isnull="true" iskey="true">
      <property name="Name">colCurrancyRate</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item7" isnull="true" iskey="true">
      <property name="Name">colLocalAmount</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item8" isnull="true" iskey="true">
      <property name="Name">colNotes</property>
      <property name="VisibleIndex">4</property>
      <property name="Visible">true</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item9" isnull="true" iskey="true">
      <property name="Name">colInsertDate</property>
      <property name="VisibleIndex">5</property>
      <property name="Visible">true</property>
      <property name="Width">75</property>
      <property name="MinWidth">20</property>
      <property name="MaxWidth">0</property>
    </property>
    <property name="Item10" isnull="true" iskey="true">
      <property name="Name">ColDelete</property>
      <property name="VisibleIndex">6</property>
      <property name="Visible">true</property>
      <property name="Width">25</property>
      <property name="MinWidth">25</property>
      <property name="MaxWidth">25</property>
    </property>
  </property>
  <property name="GroupSummary" iskey="true" value="0" />
  <property name="ActiveFilterString" />
  <property name="GroupSummarySortInfoState" />
  <property name="FindFilterText" />
  <property name="FindPanelVisible">false</property>
</XtraSerializer>