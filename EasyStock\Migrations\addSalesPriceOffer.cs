﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{
    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class addSalesPriceOffer : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(addSalesPriceOffer));

        string IMigrationMetadata.Id => "202106281241589_addSalesPriceOffer";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.SalesPriceOfferDetails", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                SalesPriceOfferID = c.Int(false),
                ProductID = c.Int(false),
                UnitID = c.Int(false),
                Factor = c.Double(false),
                ColorID = c.Int(),
                SizeID = c.Int(),
                Expire = c.DateTime(),
                Quantity = c.Double(false),
                PriceOfferType = c.Int(false),
                Price = c.Double(false),
                ProductCode = c.String(),
                Discount = c.Double(false),
                DiscountPercentage = c.Double(false),
                Tax = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.SalesPriceOffers", t => t.SalesPriceOfferID).Index(t => t.SalesPriceOfferID);
            CreateTable("dbo.SalesPriceOffers", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                Code = c.String(),
                BranchID = c.Int(false),
                Date = c.DateTime(false),
                Notes = c.String(),
                ExpireDate = c.DateTime(),
                DelevairyDate = c.DateTime(),
                CustomerID = c.Int(false),
                Discount = c.Double(false),
                OtherExpenses = c.Double(false),
                Tax = c.Double(false),
                Total = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.Customers", t => t.CustomerID).Index(t => t.CustomerID);
        }

        public override void Down()
        {
            DropForeignKey("dbo.SalesPriceOfferDetails", "SalesPriceOfferID", "dbo.SalesPriceOffers");
            DropForeignKey("dbo.SalesPriceOffers", "CustomerID", "dbo.Customers");
            DropIndex("dbo.SalesPriceOffers", new string[1] { "CustomerID" });
            DropIndex("dbo.SalesPriceOfferDetails", new string[1] { "SalesPriceOfferID" });
            DropTable("dbo.SalesPriceOffers");
            DropTable("dbo.SalesPriceOfferDetails");
        }
    }
}
