﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    [DisplayName("Employé")]
    public class Employee : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Numéro")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Code")]
        [StringLength(50)]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public string Code
        {
            get
            {
                return this.code;
            }
            set
            {
                base.SetProperty<string>(ref this.code, value, "Code");
            }
        }

        [Display(Name = "Nom")]
        [StringLength(150)]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public string Name
        {
            get
            {
                return this.name;
            }
            set
            {
                base.SetProperty<string>(ref this.name, value, "Name");
            }
        }

        [Display(Name = "Photo")]
        public byte[] Photo
        {
            get
            {
                return this.photo;
            }
            set
            {
                base.SetProperty<byte[]>(ref this.photo, value, "Photo");
            }
        }

        [Display(Name = "Succursale")]
        public int? Branch
        {
            get
            {
                return this.branch;
            }
            set
            {
                base.SetProperty<int?>(ref this.branch, value, "Branch");
            }
        }

        [Display(Name = "Département")]
        public Department Department
        {
            get
            {
                return this.depart;
            }
            set
            {
                base.SetProperty<Department>(ref this.depart, value, "Department");
            }
        }

        [Display(Name = "Département")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int? DepartmentID
        {
            get
            {
                return this.departID;
            }
            set
            {
                base.SetProperty<int?>(ref this.departID, value, "DepartmentID");
            }
        }

        [Display(Name = "Totalة")]
        public Group Group
        {
            get
            {
                return this.group;
            }
            set
            {
                base.SetProperty<Group>(ref this.group, value, "Group");
            }
        }

        [Display(Name = "Groupe")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int? GroupID
        {
            get
            {
                return this.groupID;
            }
            set
            {
                base.SetProperty<int?>(ref this.groupID, value, "GroupID");
            }
        }

        [Display(Name = "Poste")]
        public Job Job
        {
            get
            {
                return this.job;
            }
            set
            {
                base.SetProperty<Job>(ref this.job, value, "Job");
            }
        }

        [Display(Name = "Poste")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int? JobID
        {
            get
            {
                return this.jobID;
            }
            set
            {
                base.SetProperty<int?>(ref this.jobID, value, "JobID");
            }
        }

        [Display(Name = " Genre")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public GenderType Gender { get; set; }

        [Display(Name = "Numéro National")]
        [StringLength(50)]
        public string NationalID
        {
            get
            {
                return this.nationalid;
            }
            set
            {
                base.SetProperty<string>(ref this.nationalid, value, "NationalID");
            }
        }

        [Display(Name = "Numéro d'Assurance")]
        [StringLength(50)]
        public string InsuranceNo
        {
            get
            {
                return this.insuranceNo;
            }
            set
            {
                base.SetProperty<string>(ref this.insuranceNo, value, "InsuranceNo");
            }
        }
        [Display(Name = "Date d'Assurance")]

        public DateTime? InsuranceDate
        {
            get
            {
                return this.insurancedate;
            }
            set
            {
                base.SetProperty<DateTime?>(ref this.insurancedate, value, "InsuranceDate");
            }
        }

        [Display(Name = "Permis de Conduire")]
        [StringLength(50)]
        public string DrivingLicense
        {
            get
            {
                return this.drivingLicense;
            }
            set
            {
                base.SetProperty<string>(ref this.drivingLicense, value, "DrivingLicense");
            }
        }

        [Display(Name = "Date de Fin")]
        public DateTime? EndDate
        {
            get
            {
                return this.endDate;
            }
            set
            {
                base.SetProperty<DateTime?>(ref this.endDate, value, "EndDate");
            }
        }

        [Display(Name = "Nom de la Banque")]
        public string BankName
        {
            get
            {
                return this.bankName;
            }
            set
            {
                base.SetProperty<string>(ref this.bankName, value, "BankName");
            }
        }

        [Display(Name = "Numéro de Compte")]
        public string AccountNo
        {
            get
            {
                return this.accountNo;
            }
            set
            {
                base.SetProperty<string>(ref this.accountNo, value, "AccountNo");
            }
        }

        [Display(Name = "Date de Naissance")]
        public DateTime? BirthDate
        {
            get
            {
                return this.birthDate;
            }
            set
            {
                base.SetProperty<DateTime?>(ref this.birthDate, value, "BirthDate");
            }
        }

        [Display(Name = "Lieu de Naissance")]
        public Reference BirthPlace
        {
            get
            {
                return this.birthPlace;
            }
            set
            {
                base.SetProperty<Reference>(ref this.birthPlace, value, "BirthPlace");
            }
        }

        [Display(Name = "Lieu de Naissance")]
        public int? BirthPlaceId
        {
            get
            {
                return this.birthPlaceId;
            }
            set
            {
                base.SetProperty<int?>(ref this.birthPlaceId, value, "BirthPlaceId");
            }
        }

        [Display(Name = "Nationalité")]
        [StringLength(20)]
        public Reference Nationality
        {
            get
            {
                return this.nationality;
            }
            set
            {
                base.SetProperty<Reference>(ref this.nationality, value, "Nationality");
            }
        }

        [Display(Name = "Nationalité")]
        public int? NationalityId
        {
            get
            {
                return this.nationalityId;
            }
            set
            {
                base.SetProperty<int?>(ref this.nationalityId, value, "NationalityId");
            }
        }

        [Display(Name = "Religion")]
        [StringLength(20)]
        public Reference Religion
        {
            get
            {
                return this.religion;
            }
            set
            {
                base.SetProperty<Reference>(ref this.religion, value, "Religion");
            }
        }

        [Display(Name = "Religion")]
        public int? ReligionId
        {
            get
            {
                return this.religionId;
            }
            set
            {
                base.SetProperty<int?>(ref this.religionId, value, "ReligionId");
            }
        }

        [Display(Name = "Qualification")]
        [StringLength(20)]
        public Reference Qualification
        {
            get
            {
                return this.qualification;
            }
            set
            {
                base.SetProperty<Reference>(ref this.qualification, value, "Qualification");
            }
        }

        [Display(Name = "Qualification")]
        public int? QualificationId
        {
            get
            {
                return this.qualificationId;
            }
            set
            {
                base.SetProperty<int?>(ref this.qualificationId, value, "QualificationId");
            }
        }

        [Display(Name = "État de l'employé")]
        [ReadOnly(true)]
        public EmpState EmpState { get; set; }

        [Display(Name = "État Civil")]
        public MaritalStatus MaritalStatus { get; set; }

        [Display(Name = "État Militaire")]
        public MilitarilyStatus MilitarilyStatus { get; set; }

        [Display(Name = "Téléphone")]
        [StringLength(20)]
        public string Phone
        {
            get
            {
                return this.phone;
            }
            set
            {
                base.SetProperty<string>(ref this.phone, value, "Phone");
            }
        }

        [Display(Name = "Email")]
        [StringLength(50)]
        public string Email
        {
            get
            {
                return this.email;
            }
            set
            {
                base.SetProperty<string>(ref this.email, value, "Email");
            }
        }

        [Display(Name = "Adresse")]
        [StringLength(150)]
        public string Address
        {
            get
            {
                return this.address;
            }
            set
            {
                base.SetProperty<string>(ref this.address, value, "Address");
            }
        }
        [Display(Name = "Type de contrat")]
        public ContractTypes ContractType
        {
            get
            {
                return this.contracttype;
            }
            set
            {
                base.SetProperty<ContractTypes>(ref this.contracttype, value, "ContractType");
            }
        }

        [Display(Name = "Durée du contrat")]
        public string ContractPeriod
        {
            get
            {
                return this.contractperiod;
            }
            set
            {
                base.SetProperty<string>(ref this.contractperiod, value, "ContractPeriod");
            }
        }
        [Display(Name = "Date de fin de contrat")]
        public DateTime? ContactEndDate
        {
            get
            {
                return this.contactEndDate;
            }
            set
            {
                base.SetProperty<DateTime?>(ref this.contactEndDate, value, "ContactEndDate");
            }
        }

        [Display(Name = "Date de recrutement")]
        public DateTime? DateOfRecruitment
        {
            get
            {
                return this.dateOfRecruitment;
            }
            set
            {
                base.SetProperty<DateTime?>(ref this.dateOfRecruitment, value, "DateOfRecruitment");
            }
        }

        [Display(Name = "Code de l'empreinte")]
        public int FingerprintCode
        {
            get
            {
                return this.fingerprintcode;
            }
            set
            {
                base.SetProperty<int>(ref this.fingerprintcode, value, "FingerprintCode");
            }
        }

        [Display(Name = "Réglement sur les absences")]
        public AbsenceRegulation AbsenceRegulation
        {
            get
            {
                return this.absenceRegulation;
            }
            set
            {
                base.SetProperty<AbsenceRegulation>(ref this.absenceRegulation, value, "AbsenceRegulation");
            }
        }

        [Display(Name = "Réglement sur les absences")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int? AbsenceRegistionID
        {
            get
            {
                return this.abcenceregistionID;
            }
            set
            {
                base.SetProperty<int?>(ref this.abcenceregistionID, value, "AbsenceRegistionID");
            }
        }
        [Display(Name = "Réglement sur les absences")]
        public OvertimeAndDelayRegulation DelayRegulation
        {
            get
            {
                return this.delayRegulation;
            }
            set
            {
                base.SetProperty<OvertimeAndDelayRegulation>(ref this.delayRegulation, value, "DelayRegulation");
            }
        }

        [Display(Name = "Réglement sur les retards")]

        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int? DelayRegulationID
        {
            get
            {
                return this.delayRegulationID;
            }
            set
            {
                base.SetProperty<int?>(ref this.delayRegulationID, value, "DelayRegulationID");
            }
        }

        [Display(Name = "Réglement sur les heures supplémentaires")]
        public OvertimeAndDelayRegulation OverTimeRegulation
        {
            get
            {
                return this.orverTimeRegulation;
            }
            set
            {
                base.SetProperty<OvertimeAndDelayRegulation>(ref this.orverTimeRegulation, value, "OverTimeRegulation");
            }
        }

        [Display(Name = "Réglement sur les heures supplémentaires")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int? OverTimeRegulationID
        {
            get
            {
                return this.orverTimeRegulationID;
            }
            set
            {
                base.SetProperty<int?>(ref this.orverTimeRegulationID, value, "OverTimeRegulationID");
            }
        }

        [Display(Name = "Première équipe")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int? FristShiftId
        {
            get
            {
                return this.fristShiftId;
            }
            set
            {
                base.SetProperty<int?>(ref this.fristShiftId, value, "FristShiftId");
            }
        }

        public Shift FirstShift { get; set; }

        [Display(Name = "Deuxième équipe")]
        public int? SecondShiftId
        {
            get
            {
                return this.secondShiftId;
            }
            set
            {
                base.SetProperty<int?>(ref this.secondShiftId, value, "SecondShiftId");
            }
        }

        public Shift SecondShift { get; set; }

        [Display(Name = "Période de paie")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public ESalaryPeriod PayPeriod { get; set; }

        [Display(Name = "Salaire de base")]
        public double SalaryBasic
        {
            get
            {
                return this.salaryBasic;
            }
            set
            {
                base.SetProperty<double>(ref this.salaryBasic, value, "SalaryBasic");
            }
        }

        [Display(Name = "Salaire variable")]
        public double SalaryVariable
        {
            get
            {
                return this.salaryVariable;
            }
            set
            {
                base.SetProperty<double>(ref this.salaryVariable, value, "SalaryVariable");
            }
        }

        [Display(Name = "Valeur par jour")]
        public double DayValue
        {
            get
            {
                return this.dayValue;
            }
            set
            {
                base.SetProperty<double>(ref this.dayValue, value, "DayValue");
            }
        }

        [Display(Name = "Valeur par heure")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double HourValue
        {
            get
            {
                return this.hourValue;
            }
            set
            {
                base.SetProperty<double>(ref this.hourValue, value, "HourValue");
            }
        }

        [Display(Name = "Compte des dépenses")]
        public int? ExpensesAccountId
        {
            get
            {
                return this.expensesAccountId;
            }
            set
            {
                base.SetProperty<int?>(ref this.expensesAccountId, value, "ExpensesAccountId");
            }
        }
        [Display(Name = "Compte des produits")]
        public int? AccruedAccountId
        {
            get
            {
                return this.accruedAccountId;
            }
            set
            {
                base.SetProperty<int?>(ref this.accruedAccountId, value, "AccruedAccountId");
            }
        }

        [Display(Name = "Calculer l'impôt sur le revenu automatiquement")]
        public bool CalcIncomeTax
        {
            get
            {
                return this.calcIncomeTax;
            }
            set
            {
                base.SetProperty<bool>(ref this.calcIncomeTax, value, "CalcIncomeTax");
            }
        }

        [Display(Name = "Tableau des avantages")]
        public BindingList<EmpSalaryExtension> BenefitList { get; set; }

        [Display(Name = "Tableau des déductions")]
        public BindingList<EmpSalaryExtension> DeductionList { get; set; }

        public Employee()
        {
            this.BenefitList = new BindingList<EmpSalaryExtension>();
        }

        private int id;

        private string code;

        private string name;

        private byte[] photo;

        private int? branch;

        private Department depart;

        private int? departID;

        private Group group;

        private int? groupID;

        private Job job;

        private int? jobID;

        private string nationalid;

        private string insuranceNo;

        private DateTime? insurancedate;

        private string drivingLicense;

        private DateTime? endDate;

        private string bankName;

        private string accountNo;

        private DateTime? birthDate;

        private Reference birthPlace;

        private int? birthPlaceId;

        private Reference nationality;

        private int? nationalityId;

        private Reference religion;

        private int? religionId;

        private Reference qualification;

        private int? qualificationId;

        private string phone;

        private string email;

        private string address;

        private ContractTypes contracttype;

        private string contractperiod;

        private DateTime? contactEndDate;

        private DateTime? dateOfRecruitment;

        private int fingerprintcode;

        private AbsenceRegulation absenceRegulation;

        private int? abcenceregistionID;

        private OvertimeAndDelayRegulation delayRegulation;

        private int? delayRegulationID;

        private OvertimeAndDelayRegulation orverTimeRegulation;

        private int? orverTimeRegulationID;

        private int? fristShiftId;

        private int? secondShiftId;

        private double salaryBasic;

        private double salaryVariable;

        private double dayValue;

        private double hourValue;

        private int? expensesAccountId;

        private int? accruedAccountId;

        private bool calcIncomeTax;
    }
}
