using System;
using EasyStock.Classes;

namespace EasyStock.Test
{
    /// <summary>
    /// ملف اختبار بسيط لحساب الطابع الجبائي
    /// </summary>
    class TestStampCalculation
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== اختبار حساب الطابع الجبائي ===");
            Console.WriteLine();

            // اختبار مبالغ مختلفة
            double[] testAmounts = { 200, 300, 500, 1000, 5000, 30000, 50000, 100000, 150000 };

            foreach (double amount in testAmounts)
            {
                try
                {
                    double stampAmount = StampCalculator.CalculateStampAmount(amount);
                    string description = StampCalculator.GetStampCalculationDescription(amount);
                    bool requiresStamp = StampCalculator.RequiresStamp(amount);

                    Console.WriteLine($"المبلغ: {amount:N2} دج");
                    Console.WriteLine($"الطابع الجبائي: {stampAmount:F2} دج");
                    Console.WriteLine($"يتطلب طابع: {(requiresStamp ? "نعم" : "لا")}");
                    Console.WriteLine($"التفاصيل: {description}");
                    Console.WriteLine(new string('-', 50));
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في حساب المبلغ {amount}: {ex.Message}");
                }
            }

            Console.WriteLine("اضغط أي مفتاح للخروج...");
            Console.ReadKey();
        }
    }
}