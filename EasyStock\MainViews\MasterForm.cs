﻿using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraLayout;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.Models;
using EasyStock.Views;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace EasyStock.MainViews
{
    public partial class MasterForm : Common.MainViews.MasterForm
    {
        internal string ScreenName
        {
            get
            {
                return string.Join<char>("", from x in this.Text
                                             where !char.IsDigit(x) && !new char[]
                                             {
                    ',',
                    '-'
                                             }.Contains(x)
                                             select x);
            }
        }
        public MasterForm()
        {
            this.btn_Log.ItemClick += this.btn_Log_ItemClick;
        }
        public override void MasterForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            this.SaveGridLayoutIfFound(CurrentSession.CurrentUser.ID.ToString());
            base.MasterForm_FormClosing(sender, e);
        }

        public override void MasterForm_Load(object sender, EventArgs e)
        {
            base.MasterForm_Load(sender, e);
            User currentUser = CurrentSession.CurrentUser;
            this.ApplyUserSettings((currentUser != null) ? currentUser.SettingsProfile : null);
            User currentUser2 = CurrentSession.CurrentUser;
            int? num;
            int? num2;
            if (currentUser2 == null)
            {
                num = null;
                num2 = num;
            }
            else
            {
                num2 = new int?(currentUser2.ID);
            }
            num = num2;
            this.RestoreGridLayoutIfFound((num != null) ? num.GetValueOrDefault().ToString() : null);
        }

        public NavigationObject NavigationObject
        {
            get
            {
                bool flag = this._navigationObject == null;
                if (flag)
                {
                    object tag = base.Tag;
                    bool flag2;
                    int id = 0;
                    if (tag is int)
                    {
                        id = (int)tag;
                        flag2 = true;
                    }
                    else
                    {
                        flag2 = false;
                    }
                    bool flag3 = flag2;
                    if (flag3)
                    {
                        this._navigationObject = NavigationObjects.AllObjects.FirstOrDefault((NavigationObject x) => x.ID == id);
                    }
                    else
                    {
                        this._navigationObject = (from x in NavigationObjects.AllObjects
                                                  where x.Form != null
                                                  select x).FirstOrDefault((NavigationObject x) => x.Form.Name == base.Name);
                    }
                }
                return this._navigationObject;
            }
        }
        public UserAccessProfileDetail Profile
        {
            get
            {
                bool flag = this._profile == null && this.NavigationObject != null;
                if (flag)
                {
                    using (new ERPDataContext())
                    {
                        this._profile = (from x in CurrentSession.CurrentUser.AccessProfile.Details
                                         where x.ObjectId == this.NavigationObject.ID
                                         select x).FirstOrDefault<UserAccessProfileDetail>();
                    }
                }
                return this._profile;
            }
        }

        private void LogAction(WindowActions action)
        {
            LayoutControl layoutControl = base.Controls.OfType<LayoutControl>().FirstOrDefault<LayoutControl>();
            bool flag = layoutControl != null;
            if (flag)
            {
                IEnumerable<BaseEdit> controls = layoutControl.Controls.OfType<BaseEdit>();
                BaseEdit baseEdit = (from x in controls
                                     where x.Name.StartsWith("Code")
                                     select x).FirstOrDefault<BaseEdit>();
                string entityCode;
                if (baseEdit == null)
                {
                    entityCode = null;
                }
                else
                {
                    object editValue = baseEdit.EditValue;
                    entityCode = ((editValue != null) ? editValue.ToString() : null);
                }
                this.EntityCode = entityCode;
                BaseEdit baseEdit2 = (from x in controls
                                      where x.Name.StartsWith("ID")
                                      select x).FirstOrDefault<BaseEdit>();
                object id = (baseEdit2 != null) ? baseEdit2.EditValue : null;
                bool flag2 = id != null;
                if (flag2)
                {
                    int IdValue = 0;
                    bool flag3;
                    if (id is int)
                    {
                        IdValue = (int)id;
                        flag3 = true;
                    }
                    else
                    {
                        flag3 = false;
                    }
                    bool flag4 = flag3;
                    if (flag4)
                    {
                        this.EntityID = IdValue;
                    }
                    else
                    {
                        int.TryParse(id.ToString(), out this.EntityID);
                    }
                }
            }
            ERPDataContext.SaveTrackedItems(this.EntityID, this.EntityCode, this.ScreenName, base.GetType().Name, action, CurrentSession.CurrentUser.ID);
        }

        public virtual void ApplyUserSettings(UserSettingsProfile profile)
        {
        }

        private void btn_Log_ItemClick(object sender, ItemClickEventArgs e)
        {
            base.Name = base.GetType().Name;
            bool flag = CurrentSession.CurrentUser.Type == UserType.User && !CurrentSession.CurrentUser.AccessProfile.CanOpenWindow(this);
            if (flag)
            {
                XtraMessageBox.Show("Vous n'avez pas les autorisations nécessaires", "Autorisation Insuffisante", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
            else
            {
                LayoutControl layoutControl = base.Controls.OfType<LayoutControl>().FirstOrDefault<LayoutControl>();
                bool flag2 = layoutControl != null;
                if (flag2)
                {
                    IEnumerable<BaseEdit> controls = layoutControl.Controls.OfType<BaseEdit>();
                    BaseEdit baseEdit = (from x in controls
                                         where x.Name.StartsWith("ID")
                                         select x).FirstOrDefault<BaseEdit>();
                    object id = (baseEdit != null) ? baseEdit.EditValue : null;
                    bool flag3 = id != null;
                    if (flag3)
                    {
                        int IdValue = 0;
                        bool flag4;
                        if (id is int)
                        {
                            IdValue = (int)id;
                            flag4 = true;
                        }
                        else
                        {
                            flag4 = false;
                        }
                        bool flag5 = flag4;
                        if (flag5)
                        {
                            this.EntityID = IdValue;
                        }
                        else
                        {
                            int.TryParse(id.ToString(), out this.EntityID);
                        }
                        UserLogView.Instance.Close();
                        UserLogView.Instance.GoTo(base.GetType().Name, this.EntityID);
                        UserLogView.Instance.ShowDialog();
                    }
                }
            }
        }
        public override bool CheckAction(WindowActions actions)
        {
            return CurrentSession.CurrentUser.Type == UserType.Administrator || this.Profile.CheckAction(actions);
        }
        public override void Save()
        {
            bool flag = CurrentSession.CurrentUser.SettingsProfile.PrintAfterSave && base.IsNew;
            if (flag)
            {
                this.btn_Print.PerformClick();
            }
            base.Save();
        }

        public override void Delete()
        {
            this.LogAction(WindowActions.Delete);
            base.Delete();
        }
        public override void Saved()
        {
            this.LogAction(base.IsNew ? WindowActions.Add : WindowActions.Edit);
        }
        public override void BeforeSave()
        {
            ERPDataContext.ClearTrackedItems();
        }
        public override void AfterPrint()
        {
            this.LogAction(WindowActions.Print);
        }

        internal int EntityID = 0;

        internal string EntityCode = null;

        public NavigationObject _navigationObject;

        private UserAccessProfileDetail _profile;
    }
}
