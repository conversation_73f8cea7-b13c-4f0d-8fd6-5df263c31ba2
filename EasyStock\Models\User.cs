﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
	[DisplayName("Utilisateurs")]
	public class User : BaseNotifyPropertyChangedModel
	{
		[Display(Name = "N°")]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID
		{
			get
			{
				return this._Id;
			}
			set
			{
				base.SetProperty<int>(ref this._Id, value, "ID");
			}
		}

		[Display(Name = "Nom")]
		[Required(ErrorMessage = "Ce champ est obligatoire")]
		public string Name
		{
			get
			{
				return this.name;
			}
			set
			{
				base.SetProperty<string>(ref this.name, value, "Name");
			}
		}

		[Display(Name = "Nom d'utilisateur")]
		[Required(ErrorMessage = "Ce champ est obligatoire")]
		public string UserName
		{
			get
			{
				return this.userName;
			}
			set
			{
				base.SetProperty<string>(ref this.userName, value, "UserName");
			}
		}

		[Required(ErrorMessage = "Ce champ est obligatoire")]
		[Display(Name = "Mot de passe")]
		public string Password
		{
			get
			{
				return this.passWord;
			}
			set
			{
				base.SetProperty<string>(ref this.passWord, value, "Password");
			}
		}

		[Required(ErrorMessage = "Ce champ est obligatoire")]
		[Display(Name = "Type d'utilisateur")]
		public UserType Type
		{
			get
			{
				return this.type;
			}
			set
			{
				base.SetProperty<UserType>(ref this.type, value, "Type");
			}
		}

		[Display(Name = "Modèle d'accès")]
		[Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
		public int AccessProfileID
		{
			get
			{
				return this.accessProfileID;
			}
			set
			{
				base.SetProperty<int>(ref this.accessProfileID, value, "AccessProfileID");
			}
		}

		[Display(Name = "")]
		public UserAccessProfile AccessProfile
		{
			get
			{
				return this.accessProfile;
			}
			set
			{
				base.SetProperty<UserAccessProfile>(ref this.accessProfile, value, "AccessProfile");
			}
		}

		[Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
		[Display(Name = "Modèle des paramètres")]
		public int SettingsProfileID
		{
			get
			{
				return this.settingsProfileID;
			}
			set
			{
				base.SetProperty<int>(ref this.settingsProfileID, value, "SettingsProfileID");
			}
		}

		[Display(Name = "")]
		public UserSettingsProfile SettingsProfile
		{
			get
			{
				return this.settingsProfile;
			}
			set
			{
				base.SetProperty<UserSettingsProfile>(ref this.settingsProfile, value, "SettingsProfile");
			}
		}

		[Display(Name = "Notes")]
		public string Notes
		{
			get
			{
				return this.notes;
			}
			set
			{
				base.SetProperty<string>(ref this.notes, value, "Notes");
			}
		}

		private int _Id;

		private string name;

		private string userName;

		private string passWord;

		private UserType type;

		private int accessProfileID;

		private UserAccessProfile accessProfile;

		private int settingsProfileID;

		private UserSettingsProfile settingsProfile;

		private string notes;
	}
}
