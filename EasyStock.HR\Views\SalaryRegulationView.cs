﻿using DevExpress.XtraEditors;
using EasyStock.Common;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.HR.Views
{
    public partial class SalaryRegulationView : MasterView
    {
        public static SalaryRegulationView Instance
        {
            get
            {
                bool flag = SalaryRegulationView.instance == null || SalaryRegulationView.instance.IsDisposed;
                if (flag)
                {
                    SalaryRegulationView.instance = new SalaryRegulationView();
                }
                return SalaryRegulationView.instance;
            }
        }

        public SalaryRegulation Regulation
        {
            get
            {
                return this.salaryRegulationBindingSource.Current as SalaryRegulation;
            }
            set
            {
                this.salaryRegulationBindingSource.DataSource = value;
            }
        }

        public SalaryRegulationView()
        {
            this.InitializeComponent();
            base.DisableValidation(this.dataLayoutControl1);
            this.context = new HRDataContext();
            this.gridView1.AddEditButton(new EventHandler(this.RepositoryItemButtonEdit1_Click));
            this.gridView1.SetAlternatingColors();
            base.Shown += this.SalaryRegulationView_Shown;
        }

        private void SalaryRegulationView_Shown(object sender, EventArgs e)
        {
            bool flag = this.Regulation == null;
            if (flag)
            {
                this.New();
            }
        }

        private void RepositoryItemButtonEdit1_Click(object sender, EventArgs e)
        {
            SalaryRegulation row = this.gridView1.GetRow(this.gridView1.FocusedRowHandle) as SalaryRegulation;
            bool flag = row != null;
            if (flag)
            {
                this.GoTo(row.ID);
            }
        }

        public void GoTo(int id)
        {
            SalaryRegulation sourceDepr = this.context.SalaryRegulations.SingleOrDefault((SalaryRegulation x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.Regulation = sourceDepr;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document est introuvable");
            }
        }

        public override void New()
        {
            this.Regulation = new SalaryRegulation();
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.context.SalaryRegulations.AddOrUpdate(new SalaryRegulation[]
                {
                    this.Regulation
                });
                this.context.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.gridControl1.DataSource = this.context.SalaryRegulations.ToList<SalaryRegulation>();
            base.RefreshData();
        }

        public override void Delete()
        {
        }

        private static SalaryRegulationView instance;

        private HRDataContext context;
    }
}
