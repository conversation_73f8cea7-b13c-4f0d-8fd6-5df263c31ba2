﻿using System.CodeDom.Compiler;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Builders;
using System.Data.Entity.Migrations.Infrastructure;
using System.Resources;

namespace EasyStock.Migrations
{

    [GeneratedCode("EntityFramework.Migrations", "6.5.1")]
    public sealed class DrawerPeriodModels : DbMigration, IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(DrawerPeriodModels));

        string IMigrationMetadata.Id => "202103061330231_DrawerPeriodModels";

        string IMigrationMetadata.Source => null;

        string IMigrationMetadata.Target => Resources.GetString("Target");

        public override void Up()
        {
            CreateTable("dbo.DrawerPeriods", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                PeriodStart = c.DateTime(false),
                PeriodEnd = c.DateTime(),
                OpeningBalance = c.Double(false),
                ClosingBalance = c.Double(false),
                ActualBalance = c.Double(false),
                DifferenceAccountID = c.Int(),
                PeriodUserID = c.Int(false),
                ClosingPeriodUserID = c.Int(),
                DrawerID = c.Int(false),
                ClosingDrwerID = c.Int(),
                TransferdBalance = c.Double(false),
                RemainingBalance = c.Double(false)
            }).PrimaryKey(t => t.ID);
            CreateTable("dbo.DrawerPeriodTransSummeryItems", (ColumnBuilder c) => new
            {
                ID = c.Int(false, identity: true),
                DrawerPeriodID = c.Int(false),
                ProcessType = c.Int(false),
                ProcessCount = c.Int(false),
                ProcessSum = c.Double(false)
            }).PrimaryKey(t => t.ID).ForeignKey("dbo.DrawerPeriods", t => t.DrawerPeriodID).Index(t => t.DrawerPeriodID);
        }

        public override void Down()
        {
            DropForeignKey("dbo.DrawerPeriodTransSummeryItems", "DrawerPeriodID", "dbo.DrawerPeriods");
            DropIndex("dbo.DrawerPeriodTransSummeryItems", new string[1] { "DrawerPeriodID" });
            DropTable("dbo.DrawerPeriodTransSummeryItems");
            DropTable("dbo.DrawerPeriods");
        }
    }
}
