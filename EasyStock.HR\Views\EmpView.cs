﻿using DevExpress.Utils.Extensions;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using EasyStock.Common;
using EasyStock.HR.BLL;
using EasyStock.HR.MainViews;
using EasyStock.HR.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.Entity;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace EasyStock.HR.Views
{
    public partial class EmpView : MasterView
    {
        public static EmpView Instance
        {
            get
            {
                bool flag = EmpView.instance == null || EmpView.instance.IsDisposed;
                if (flag)
                {
                    EmpView.instance = new EmpView();
                }
                return EmpView.instance;
            }
        }

        private BindingList<EmpSalaryExtension> Deduction
        {
            get
            {
                return this.grdDeduction.DataSource as BindingList<EmpSalaryExtension>;
            }
            set
            {
                this.grdDeduction.DataSource = value;
            }
        }

        private BindingList<EmpSalaryExtension> Benefits
        {
            get
            {
                return this.grdBenefits.DataSource as BindingList<EmpSalaryExtension>;
            }
            set
            {
                this.grdBenefits.DataSource = value;
            }
        }

        public Employee emp
        {
            get
            {
                return this.EmpBindingSource.Current as Employee;
            }
            set
            {
                this.EmpBindingSource.DataSource = value;
            }
        }

        private void grdBenefits_EmbeddedNavigator_ButtonClick(object sender, NavigatorButtonClickEventArgs e)
        {
            bool flag = e.Button.ButtonType == NavigatorButtonType.Append;
            if (flag)
            {
                e.Handled = true;
            }
        }

        public EmpView()
        {
            this.InitializeComponent();
            this.btn_Print.Visibility = BarItemVisibility.Always;
            this.btn_Print.Enabled = true;
            base.DisableValidation(this.dataLayoutControl1);
            this.viewDeduction.SetAlternatingColors();
            base.Shown += this.EmpView_Shown;
            this.Text = "Données de l'employé";
            this.BindingLookupControls();
            this.PhotoPictureEdit.EditValueChanged += this.PhotoPictureEdit_EditValueChanged;
            this.InitializeGrid();
        }

        private void EmpView_Shown(object sender, EventArgs e)
        {
            bool flag = this.emp == null;
            if (flag)
            {
                this.New();
            }
        }

        public void GoTo(int id)
        {
            Employee employee = EmployeeBLL.Get(id);
            bool flag = employee != null;
            if (flag)
            {
                this.emp = employee;
                this.DetailsBen = new HRDataContext();
                this.DetailsDed = new HRDataContext();
                (from x in this.DetailsBen.EmpSalaryExtensions
                 where x.EmpId == this.emp.ID && (int)x.Type == 0
                 select x).Load();
                this.grdBenefits.DataSource = this.DetailsBen.EmpSalaryExtensions.Local.ToBindingList<EmpSalaryExtension>();
                (from x in this.DetailsDed.EmpSalaryExtensions
                 where x.EmpId == this.emp.ID && (int)x.Type == 1
                 select x).Load();
                this.grdDeduction.DataSource = this.DetailsDed.EmpSalaryExtensions.Local.ToBindingList<EmpSalaryExtension>();
                bool flag2 = this.emp.EmpState == EmpState.leftWork;
                if (flag2)
                {
                    this.labelControl1.Text = "Départ du travail";

                }
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("L'employé n'existe pas");
            }
        }

        public override void New()
        {
            this.emp = new Employee();
            this.DetailsBen = new HRDataContext();
            this.DetailsDed = new HRDataContext();
            (from x in this.DetailsBen.EmpSalaryExtensions
             where x.EmpId == this.emp.ID && (int)x.Type == 0
             select x).Load();
            this.grdBenefits.DataSource = this.DetailsBen.EmpSalaryExtensions.Local.ToBindingList<EmpSalaryExtension>();
            (from x in this.DetailsDed.EmpSalaryExtensions
             where x.EmpId == this.emp.ID && (int)x.Type == 1
             select x).Load();
            this.grdDeduction.DataSource = this.DetailsDed.EmpSalaryExtensions.Local.ToBindingList<EmpSalaryExtension>();
            base.New();
        }

        public override void Save()
        {
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                (this.grdBenefits.DataSource as BindingList<EmpSalaryExtension>).ForEach(delegate (EmpSalaryExtension d)
                {
                    d.Employee = null;
                    d.EmpId = this.emp.ID;
                    d.Type = ExtensionType.Benefit;
                });
                (this.grdDeduction.DataSource as BindingList<EmpSalaryExtension>).ForEach(delegate (EmpSalaryExtension d)
                {
                    d.Employee = null;
                    d.EmpId = this.emp.ID;
                    d.Type = ExtensionType.Deduction;
                });
                int res = EmployeeBLL.AddOrUpdate(this.emp, this.Benefits, this.Deduction);
                base.Save();
            }
        }

        public override void RefreshData()
        {
            this.New();
            base.RefreshData();
        }

        public override void Delete()
        {
            bool flag = this.emp == null || this.emp.ID == 0;
            if (!flag)
            {
                int res = EmployeeBLL.Delete(this.emp.ID);
                bool flag2 = res > 0;
                if (flag2)
                {
                    base.Delete();
                }
            }
        }

        public void BindingLookupControls()
        {
            // Configure GroupIDTextEdit
            this.GroupIDTextEdit.Properties.DataSource = GroupBLL.GetAll();
            this.GroupIDTextEdit.Properties.DisplayMember = "Name";
            this.GroupIDTextEdit.Properties.ValueMember = "ID";
            this.GroupIDTextEdit.Properties.NullText = "";
            this.GroupIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("ID", "Code"));
            this.GroupIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Groupe"));

            // Configure DepartmentIDTextEdit
            this.DepartmentIDTextEdit.Properties.DataSource = DepartmentBLL.GetAll();
            this.DepartmentIDTextEdit.Properties.DisplayMember = "Name";
            this.DepartmentIDTextEdit.Properties.ValueMember = "ID";
            this.DepartmentIDTextEdit.Properties.NullText = "";
            this.DepartmentIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Nom"));
            this.DepartmentIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("MangerName", "Responsable"));

            // Configure JobIDTextEdit
            this.JobIDTextEdit.Properties.DataSource = JobBLL.GetAll();
            this.JobIDTextEdit.Properties.DisplayMember = "Name";
            this.JobIDTextEdit.Properties.ValueMember = "ID";
            this.JobIDTextEdit.Properties.NullText = "";
            this.JobIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Fonction"));

            // Configure BranchTextEdit
            this.BranchTextEdit.Properties.DataSource = ExternalBLL.Branches;
            this.BranchTextEdit.Properties.DisplayMember = "Name";
            this.BranchTextEdit.Properties.ValueMember = "ID";
            this.BranchTextEdit.Properties.NullText = "";
            this.BranchTextEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Succursale"));

            // Configure FristShiftIdTextEdit
            this.FristShiftIdTextEdit.Properties.DataSource = ShiftBLL.GetAll();
            this.FristShiftIdTextEdit.Properties.DisplayMember = "Name";
            this.FristShiftIdTextEdit.Properties.ValueMember = "ID";
            this.FristShiftIdTextEdit.Properties.NullText = "";
            this.FristShiftIdTextEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Quart"));

            // Configure SecondShiftIdTextEdit
            this.SecondShiftIdTextEdit.Properties.DataSource = ShiftBLL.GetAll();
            this.SecondShiftIdTextEdit.Properties.DisplayMember = "Name";
            this.SecondShiftIdTextEdit.Properties.ValueMember = "ID";
            this.SecondShiftIdTextEdit.Properties.NullText = "";
            this.SecondShiftIdTextEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Quart"));

            // Configure AbsenceRegistionIDTextEdit
            this.AbsenceRegistionIDTextEdit.Properties.DataSource = AbsenceRegulationBLL.GetAll();
            this.AbsenceRegistionIDTextEdit.Properties.DisplayMember = "Name";
            this.AbsenceRegistionIDTextEdit.Properties.ValueMember = "ID";
            this.AbsenceRegistionIDTextEdit.Properties.NullText = "";
            this.AbsenceRegistionIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Règlement d'absence"));

            // Configure DelayRegulationIDTextEdit
            this.DelayRegulationIDTextEdit.Properties.DataSource = OvertimeAndDelayRegulationBLL.GetAll(EType.Delay);
            this.DelayRegulationIDTextEdit.Properties.DisplayMember = "Name";
            this.DelayRegulationIDTextEdit.Properties.ValueMember = "ID";
            this.DelayRegulationIDTextEdit.Properties.NullText = "";
            this.DelayRegulationIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Règlement de retard"));

            // Configure OverTimeRegulationIDTextEdit
            this.OverTimeRegulationIDTextEdit.Properties.DataSource = OvertimeAndDelayRegulationBLL.GetAll(EType.Overtime);
            this.OverTimeRegulationIDTextEdit.Properties.DisplayMember = "Name";
            this.OverTimeRegulationIDTextEdit.Properties.ValueMember = "ID";
            this.OverTimeRegulationIDTextEdit.Properties.NullText = "";
            this.OverTimeRegulationIDTextEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Règlement de heures supplémentaires"));

            // Configure ExpensesAccountIdLookUpEdit
            this.ExpensesAccountIdLookUpEdit.Properties.DataSource = ExternalBLL.Accounts;
            this.ExpensesAccountIdLookUpEdit.Properties.DisplayMember = "AccName";
            this.ExpensesAccountIdLookUpEdit.Properties.ValueMember = "ID";
            this.ExpensesAccountIdLookUpEdit.Properties.NullText = "";
            this.ExpensesAccountIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("AccNo", "Numéro de compte"));
            this.ExpensesAccountIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("AccName", "Nom du compte"));

            // Configure AccruedAccountIdLookUpEdit
            this.AccruedAccountIdLookUpEdit.Properties.DataSource = ExternalBLL.Accounts;
            this.AccruedAccountIdLookUpEdit.Properties.DisplayMember = "AccName";
            this.AccruedAccountIdLookUpEdit.Properties.ValueMember = "ID";
            this.AccruedAccountIdLookUpEdit.Properties.NullText = "";
            this.AccruedAccountIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("AccNo", "Numéro de compte"));
            this.AccruedAccountIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("AccName", "Nom du compte"));

            // Configure NationalityIdLookUpEdit
            this.NationalityIdLookUpEdit.Properties.DataSource = ReferenceBLL.GetAll(ReferenceType.Nationality);
            this.NationalityIdLookUpEdit.Properties.DisplayMember = "Name";
            this.NationalityIdLookUpEdit.Properties.ValueMember = "ID";
            this.NationalityIdLookUpEdit.Properties.NullText = "";
            this.NationalityIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Nationalité"));
            this.NationalityIdLookUpEdit.Properties.TextEditStyle = TextEditStyles.Standard;
            this.NationalityIdLookUpEdit.ProcessNewValue += delegate (object sender, ProcessNewValueEventArgs e)
            {
                this.TextEdit_ProcessNewValue(sender, e, ReferenceType.Nationality);
            };

            // Configure BirthCountryIdLookUpEdit
            this.BirthCountryIdLookUpEdit.Properties.DataSource = ReferenceBLL.GetAll(ReferenceType.Country);
            this.BirthCountryIdLookUpEdit.Properties.DisplayMember = "Name";
            this.BirthCountryIdLookUpEdit.Properties.ValueMember = "ID";
            this.BirthCountryIdLookUpEdit.Properties.NullText = "";
            this.BirthCountryIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Pays d'origine"));
            this.BirthCountryIdLookUpEdit.Properties.TextEditStyle = TextEditStyles.Standard;
            this.BirthCountryIdLookUpEdit.ProcessNewValue += delegate (object sender, ProcessNewValueEventArgs e)
            {
                this.TextEdit_ProcessNewValue(sender, e, ReferenceType.Country);
            };

            // Configure ReligionIdLookUpEdit
            this.ReligionIdLookUpEdit.Properties.DataSource = ReferenceBLL.GetAll(ReferenceType.Religion);
            this.ReligionIdLookUpEdit.Properties.DisplayMember = "Name";
            this.ReligionIdLookUpEdit.Properties.ValueMember = "ID";
            this.ReligionIdLookUpEdit.Properties.NullText = "";
            this.ReligionIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Religion"));
            this.ReligionIdLookUpEdit.Properties.TextEditStyle = TextEditStyles.Standard;
            this.ReligionIdLookUpEdit.ProcessNewValue += delegate (object sender, ProcessNewValueEventArgs e)
            {
                this.TextEdit_ProcessNewValue(sender, e, ReferenceType.Religion);
            };

            // Configure QualificationIdLookUpEdit
            this.QualificationIdLookUpEdit.Properties.DataSource = ReferenceBLL.GetAll(ReferenceType.Qualification);
            this.QualificationIdLookUpEdit.Properties.DisplayMember = "Name";
            this.QualificationIdLookUpEdit.Properties.ValueMember = "ID";
            this.QualificationIdLookUpEdit.Properties.NullText = "";
            this.QualificationIdLookUpEdit.Properties.Columns.Add(new LookUpColumnInfo("Name", "Qualification"));
            this.QualificationIdLookUpEdit.Properties.TextEditStyle = TextEditStyles.Standard;
            this.QualificationIdLookUpEdit.ProcessNewValue += delegate (object sender, ProcessNewValueEventArgs e)
            {
                this.TextEdit_ProcessNewValue(sender, e, ReferenceType.Qualification);
            };

            // Configure repoExp
            this.repoExp.DataSource = SalaryExtensionBLL.GetByType(ExtensionType.Benefit);
            this.repoExp.DisplayMember = "Name";
            this.repoExp.ValueMember = "ID";
            this.repoExp.NullText = "";
            this.repoExp.Columns.Add(new LookUpColumnInfo("Name", "Article"));
            this.repoExp.Columns.Add(new LookUpColumnInfo("CalculationType", "Période de paie"));

            // Configure repoded
            this.repoded.DataSource = SalaryExtensionBLL.GetByType(ExtensionType.Deduction);
            this.repoded.DisplayMember = "Name";
            this.repoded.ValueMember = "ID";
            this.repoded.NullText = "";
            this.repoded.Columns.Add(new LookUpColumnInfo("Name", "Article"));
            this.repoded.Columns.Add(new LookUpColumnInfo("CalculationType", "Période de paie"));

        }

        private void TextEdit_ProcessNewValue(object sender, ProcessNewValueEventArgs e, ReferenceType referenceType)
        {
            bool flag = (string)e.DisplayValue != string.Empty;
            if (flag)
            {
                List<Reference> list = (sender as LookUpEdit).Properties.DataSource as List<Reference>;
                Reference reference;
                if (list == null)
                {
                    reference = null;
                }
                else
                {
                    reference = list.FirstOrDefault((Reference x) => x.ID == 0);
                }
                Reference obj = reference;
                bool flag2 = obj == null;
                if (flag2)
                {
                    obj = new Reference
                    {
                        Name = e.DisplayValue.ToString(),
                        RefType = referenceType
                    };
                    list.Add(obj);
                }
                else
                {
                    obj.Name = e.DisplayValue.ToString();
                }
                (sender as LookUpEdit).Properties.DataSource = list;
                bool flag3 = this.emp != null;
                if (flag3)
                {
                    switch (referenceType)
                    {
                        case ReferenceType.Country:
                            this.emp.BirthPlace = obj;
                            break;
                        case ReferenceType.Nationality:
                            this.emp.Nationality = obj;
                            break;
                        case ReferenceType.Religion:
                            this.emp.Religion = obj;
                            break;
                        case ReferenceType.Qualification:
                            this.emp.Qualification = obj;
                            break;
                    }
                }
                e.Handled = true;
            }
        }

        private void btnAddImg_Click(object sender, EventArgs e)
        {
            this.PhotoPictureEdit.LoadImage();
        }

        private void btnRemoveImg_Click(object sender, EventArgs e)
        {
            this.PhotoPictureEdit.Image = null;
        }

        private void PhotoPictureEdit_EditValueChanged(object sender, EventArgs e)
        {
            bool flag = this.PhotoPictureEdit.EditValue is Image;
            if (flag)
            {
                MemoryStream ms = new MemoryStream();
                this.PhotoPictureEdit.Image.Save(ms, ImageFormat.Jpeg);
                byte[] arr = ms.ToArray();
                this.emp.Photo = arr;
            }
        }

        private void InitializeGrid()
        {
            this.viewBenefits.Appearance.TopNewRow.Options.UseBackColor = true;
            this.viewBenefits.OptionsDetail.EnableMasterViewMode = false;
            this.viewBenefits.NewItemRowText = "Cliquez ici pour ajouter un nouvel article";
            this.viewBenefits.OptionsView.NewItemRowPosition = NewItemRowPosition.Top;
            this.viewBenefits.InvalidRowException += this.viewBenefits_InvalidRowException;
            this.viewBenefits.ValidateRow += this.viewBenefits_ValidateRow;
            this.viewBenefits.CellValueChanged += this.ViewBenefits_CellValueChanged;
            this.viewBenefits.InitNewRow += this.ViewBenefits_InitNewRow;
            this.viewDeduction.Appearance.TopNewRow.Options.UseBackColor = true;
            this.viewDeduction.OptionsDetail.EnableMasterViewMode = false;
            this.viewDeduction.NewItemRowText = "Cliquez ici pour ajouter un nouvel article";
            this.viewDeduction.OptionsView.NewItemRowPosition = NewItemRowPosition.Top;
            this.viewDeduction.InvalidRowException += this.viewDeduction_InvalidRowException;
            this.viewDeduction.ValidateRow += this.viewDeduction_ValidateRow;
            this.viewDeduction.CellValueChanged += this.ViewDeduction_CellValueChanged;
            this.viewDeduction.InitNewRow += this.ViewDeduction_InitNewRow;
        }

        private void ViewDeduction_InitNewRow(object sender, InitNewRowEventArgs e)
        {
            GridView view = sender as GridView;
            EmpSalaryExtension row = view.GetRow(e.RowHandle) as EmpSalaryExtension;
            bool flag = row != null;
            if (flag)
            {
                row.EmpId = (this.EmpBindingSource.Current as Employee).ID;
            }
        }

        private void ViewBenefits_InitNewRow(object sender, InitNewRowEventArgs e)
        {
            GridView view = sender as GridView;
            EmpSalaryExtension row = view.GetRow(e.RowHandle) as EmpSalaryExtension;
            bool flag = row != null;
            if (flag)
            {
                row.EmpId = (this.EmpBindingSource.Current as Employee).ID;
            }
        }

        private void ViewDeduction_CellValueChanged(object sender, CellValueChangedEventArgs e)
        {
            GridView view = sender as GridView;
            EmpSalaryExtension row = this.viewDeduction.GetRow(e.RowHandle) as EmpSalaryExtension;
            bool flag = row == null;
            if (!flag)
            {
                string fieldName = e.Column.FieldName;
                string a = fieldName;
                if (a == "SalaryExtensionId")
                {
                    bool flag2 = row.SalaryExtensionId == 0;
                    if (flag2)
                    {
                        view.DeleteRow(e.RowHandle);
                    }
                    else
                    {
                        row.Value = 1.0;
                    }
                }
            }
        }

        private void ViewBenefits_CellValueChanged(object sender, CellValueChangedEventArgs e)
        {
            GridView view = sender as GridView;
            EmpSalaryExtension row = this.viewBenefits.GetRow(e.RowHandle) as EmpSalaryExtension;
            bool flag = row == null;
            if (!flag)
            {
                string fieldName = e.Column.FieldName;
                string a = fieldName;
                if (a == "SalaryExtensionId")
                {
                    bool flag2 = row.SalaryExtensionId == 0;
                    if (flag2)
                    {
                        view.DeleteRow(e.RowHandle);
                    }
                    else
                    {
                        row.Value = 1.0;
                    }
                }
            }
        }

        private void viewDeduction_ValidateRow(object sender, ValidateRowEventArgs e)
        {
            GridView view = sender as GridView;
            EmpSalaryExtension row = view.GetRow(e.RowHandle) as EmpSalaryExtension;
            bool flag = row == null || row.SalaryExtensionId == 0;
            if (flag)
            {
                e.Valid = false;
            }
            else
            {
                bool flag2 = row.Value < 0.0001;
                if (flag2)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Value"], "La valeur doit être supérieure à 0.0001 ");
                }
            }
        }

        private void viewDeduction_InvalidRowException(object sender, InvalidRowExceptionEventArgs e)
        {
            bool flag;
            if (e.Row != null)
            {
                EmpSalaryExtension row = e.Row as EmpSalaryExtension;
                flag = (row != null && row.SalaryExtensionId == 0);
            }
            else
            {
                flag = true;
            }
            bool flag2 = flag;
            if (flag2)
            {
                GridView view = sender as GridView;
                view.DeleteRow(e.RowHandle);
                e.ExceptionMode = ExceptionMode.Ignore;
            }
            else
            {
                e.ExceptionMode = ExceptionMode.NoAction;
            }
        }

        private void viewBenefits_ValidateRow(object sender, ValidateRowEventArgs e)
        {
            GridView view = sender as GridView;
            EmpSalaryExtension row = view.GetRow(e.RowHandle) as EmpSalaryExtension;
            bool flag = row == null || row.SalaryExtensionId == 0;
            if (flag)
            {
                e.Valid = false;
            }
            else
            {
                bool flag2 = row.Value < 0.0001;
                if (flag2)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Value"], "La valeur doit être supérieure à 0.0001 ");
                }
            }
        }

        private void viewBenefits_InvalidRowException(object sender, InvalidRowExceptionEventArgs e)
        {
            bool flag;
            if (e.Row != null)
            {
                EmpSalaryExtension row = e.Row as EmpSalaryExtension;
                flag = (row != null && row.SalaryExtensionId == 0);
            }
            else
            {
                flag = true;
            }
            bool flag2 = flag;
            if (flag2)
            {
                GridView view = sender as GridView;
                view.DeleteRow(e.RowHandle);
                e.ExceptionMode = ExceptionMode.Ignore;
            }
            else
            {
                e.ExceptionMode = ExceptionMode.NoAction;
            }
        }

        private void GridView1_CellValueChanging(object sender, CellValueChangedEventArgs e)
        {
            GridView view = sender as GridView;
            bool flag = view == null;
            if (!flag)
            {
                bool flag2 = e.Column.FieldName == "SalaryExtensionId";
                if (flag2)
                {
                    string type = "";
                    string Calc = "";
                    bool flag3 = !string.IsNullOrEmpty(e.Value.ToString());
                    if (flag3)
                    {
                        SalaryExtension obj = SalaryExtensionBLL.Get((int)e.Value);
                        type = ((obj != null) ? obj.Type.GetDisplayName() : null);
                        Calc = ((obj != null) ? obj.CalculationType.GetDisplayName() : null);
                    }
                    GridColumn colType = (from col in view.Columns
                                          where col.FieldName == "Type"
                                          select col).FirstOrDefault<GridColumn>();
                    GridColumn colCalc = (from col in view.Columns
                                          where col.FieldName == "CalculationType"
                                          select col).FirstOrDefault<GridColumn>();
                    view.SetRowCellValue(e.RowHandle, colType, type);
                    view.SetRowCellValue(e.RowHandle, colCalc, Calc);
                    view.SetFocusedRowCellValue(colType, type);
                    view.SetFocusedRowCellValue(colCalc, Calc);
                }
            }
        }

        private void GridView1_ShownEditor(object sender, EventArgs e)
        {
            GridView view = sender as GridView;
            List<GridColumn> cols = view.VisibleColumns.ToList<GridColumn>();
            foreach (GridColumn item in cols)
            {
                bool flag = item.FieldName == "Type" || item.FieldName == "CalculationType";
                if (flag)
                {
                    item.OptionsColumn.ReadOnly = true;
                    item.OptionsColumn.AllowEdit = false;
                }
            }
        }

        private void GridView1_CustomRowCellEdit(object sender, CustomRowCellEditEventArgs e)
        {
        }

        private void GridControl1_ProcessGridKey(object sender, KeyEventArgs e)
        {
            GridControl gridControl = sender as GridControl;
            GridView view = gridControl.FocusedView as GridView;
            bool flag = e.KeyCode == Keys.Return;
            if (flag)
            {
                GridColumn focusedColumn = (gridControl.FocusedView as ColumnView).FocusedColumn;
                int focusedRowHandle = (gridControl.FocusedView as ColumnView).FocusedRowHandle;
                bool flag2 = view.FocusedColumn.FieldName == "SalaryExtensionId";
                if (flag2)
                {
                    this.GridControl1_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                }
                else
                {
                    bool flag3 = view.FocusedColumn.FieldName == "Value";
                    if (flag3)
                    {
                        view.AddNewRow();
                        view.FocusedColumn.FieldName = "SalaryExtensionId";
                    }
                    else
                    {
                        view.FocusedRowHandle = focusedRowHandle + 1;
                        view.FocusedColumn.FieldName = "SalaryExtensionId";
                    }
                }
                e.Handled = true;
            }
            else
            {
                bool flag4 = e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift;
                if (flag4)
                {
                    bool flag5 = view.FocusedColumn.VisibleIndex < view.VisibleColumns.Count;
                    if (flag5)
                    {
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex + 1];
                    }
                    e.Handled = true;
                }
                else
                {
                    bool flag6 = e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control;
                    if (flag6)
                    {
                        view.DeleteSelectedRows();
                    }
                }
            }
        }

        private void GridView1_InitNewRow(object sender, InitNewRowEventArgs e)
        {
            GridView view = sender as GridView;
        }

        private void GridView1_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
        }

        private void GridView1_ValidateRow(object sender, ValidateRowEventArgs e)
        {
            GridView view = sender as GridView;
        }

        private void GridView1_CustomUnboundColumnData(object sender, CustomColumnDataEventArgs e)
        {
            EmpSalaryExtension row = e.Row as EmpSalaryExtension;
            bool isGetData = e.IsGetData;
            if (!isGetData)
            {
                bool isSetData = e.IsSetData;
                if (isSetData)
                {
                }
            }
        }

        private static EmpView instance;

        private HRDataContext DetailsBen = new HRDataContext();

        private HRDataContext DetailsDed = new HRDataContext();
    }
}
