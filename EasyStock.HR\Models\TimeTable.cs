﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.HR.Models
{
    [DisplayColumn("Tableau horaire")]
    public class TimeTable : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Nom")]
        [StringLength(150)]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public string Name
        {
            get
            {
                return this.name;
            }
            set
            {
                base.SetProperty<string>(ref this.name, value, "Name");
            }
        }

        [Display(Name = "Date de présence")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public TimeSpan AttendanceTime
        {
            get
            {
                return this.attendencetime;
            }
            set
            {
                base.SetProperty<TimeSpan>(ref this.attendencetime, value, "AttendanceTime");
            }
        }

        [Display(Name = "Marge de début de présence")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int AttendanceStart
        {
            get
            {
                return this.attendencestart;
            }
            set
            {
                base.SetProperty<int>(ref this.attendencestart, value, "AttendanceStart");
            }
        }

        [Display(Name = "Marge de fin de présence")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int AttendanceEnd
        {
            get
            {
                return this.attendenceend;
            }
            set
            {
                base.SetProperty<int>(ref this.attendenceend, value, "AttendanceEnd");
            }
        }

        [Display(Name = "Jour de départ")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public TimeTable.LeaveDayType LeaveDay { get; set; }

        [Display(Name = "Date de départ")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public TimeSpan LeaveTime
        {
            get
            {
                return this.leavetime;
            }
            set
            {
                base.SetProperty<TimeSpan>(ref this.leavetime, value, "LeaveTime");
            }
        }

        [Display(Name = "Marge de début de départ")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int LeaveStart
        {
            get
            {
                return this.leavestart;
            }
            set
            {
                base.SetProperty<int>(ref this.leavestart, value, "LeaveStart");
            }
        }

        [Display(Name = "Marge de fin de départ")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int LeaveEnd
        {
            get
            {
                return this.leaveend;
            }
            set
            {
                base.SetProperty<int>(ref this.leaveend, value, "LeaveEnd");
            }
        }

        private int id;

        private string name;

        private TimeSpan attendencetime;

        private int attendencestart;

        private int attendenceend;

        private TimeSpan leavetime;

        private int leavestart;

        private int leaveend;

        public enum LeaveDayType
        {
            [Display(Name = "Même jour")]
            Same,
            [Display(Name = "Jour suivant")]
            Next
        }
    }
}