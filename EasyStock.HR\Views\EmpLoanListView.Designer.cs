﻿namespace EasyStock.HR.Views
{
	public partial class EmpLoanListView : global::EasyStock.HR.MainViews.MasterView
	{
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.empLoanBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBranchID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repobranch = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.colDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colAccountNo = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repoAccount = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.colEmpID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repoEmp = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.employeeBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.colBalance = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTotalAmount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colInstalmentNo = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colNotes = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.empLoanBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repobranch)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoAccount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoEmp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.employeeBindingSource)).BeginInit();
            this.SuspendLayout();
            this.gridControl1.DataSource = this.empLoanBindingSource;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 26);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repoEmp,
            this.repoAccount,
            this.repobranch});
            this.gridControl1.Size = new System.Drawing.Size(800, 400);
            this.gridControl1.TabIndex = 4;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            this.empLoanBindingSource.DataSource = typeof(EasyStock.HR.Models.EmpLoan);
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colID,
            this.colBranchID,
            this.colDate,
            this.colAccountNo,
            this.colEmpID,
            this.colBalance,
            this.colTotalAmount,
            this.colInstalmentNo,
            this.colNotes});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.colID.FieldName = "ID";
            this.colID.Name = "colID";
            this.colID.OptionsColumn.ReadOnly = true;
            this.colID.Visible = true;
            this.colID.VisibleIndex = 0;
            this.colBranchID.Caption = "Succursale";
            this.colBranchID.ColumnEdit = this.repobranch;
            this.colBranchID.FieldName = "BranchID";
            this.colBranchID.Name = "colBranchID";
            this.colBranchID.Visible = true;
            this.colBranchID.VisibleIndex = 1;
            this.repobranch.AutoHeight = false;
            this.repobranch.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repobranch.Name = "repobranch";
            this.colDate.FieldName = "Date";
            this.colDate.Name = "colDate";
            this.colDate.Visible = true;
            this.colDate.VisibleIndex = 2;
            this.colAccountNo.ColumnEdit = this.repoAccount;
            this.colAccountNo.FieldName = "AccountNo";
            this.colAccountNo.Name = "colAccountNo";
            this.colAccountNo.Visible = true;
            this.colAccountNo.VisibleIndex = 3;
            this.repoAccount.AutoHeight = false;
            this.repoAccount.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repoAccount.Name = "repoAccount";
            this.colEmpID.ColumnEdit = this.repoEmp;
            this.colEmpID.FieldName = "EmpID";
            this.colEmpID.Name = "colEmpID";
            this.colEmpID.Visible = true;
            this.colEmpID.VisibleIndex = 4;
            this.repoEmp.AutoHeight = false;
            this.repoEmp.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repoEmp.DataSource = this.employeeBindingSource;
            this.repoEmp.DisplayMember = "Name";
            this.repoEmp.Name = "repoEmp";
            this.repoEmp.ValueMember = "ID";
            this.employeeBindingSource.DataSource = typeof(EasyStock.HR.Models.Employee);
            this.colBalance.FieldName = "Balance";
            this.colBalance.Name = "colBalance";
            this.colBalance.Visible = true;
            this.colBalance.VisibleIndex = 5;
            this.colTotalAmount.FieldName = "TotalAmount";
            this.colTotalAmount.Name = "colTotalAmount";
            this.colTotalAmount.Visible = true;
            this.colTotalAmount.VisibleIndex = 6;
            this.colInstalmentNo.FieldName = "InstalmentNo";
            this.colInstalmentNo.Name = "colInstalmentNo";
            this.colInstalmentNo.Visible = true;
            this.colInstalmentNo.VisibleIndex = 7;
            this.colNotes.FieldName = "Notes";
            this.colNotes.Name = "colNotes";
            this.colNotes.Visible = true;
            this.colNotes.VisibleIndex = 8;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.gridControl1);
            this.Name = "EmpLoanListView";
            this.Text = "Liste des avances";
            this.Controls.SetChildIndex(this.gridControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.empLoanBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repobranch)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoAccount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoEmp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.employeeBindingSource)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		private global::System.ComponentModel.IContainer components = null;

		private global::DevExpress.XtraGrid.GridControl gridControl1;

		private global::System.Windows.Forms.BindingSource empLoanBindingSource;

		private global::DevExpress.XtraGrid.Views.Grid.GridView gridView1;

		private global::DevExpress.XtraGrid.Columns.GridColumn colID;

		private global::DevExpress.XtraGrid.Columns.GridColumn colBranchID;

		private global::DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repobranch;

		private global::DevExpress.XtraGrid.Columns.GridColumn colDate;

		private global::DevExpress.XtraGrid.Columns.GridColumn colAccountNo;

		private global::DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repoAccount;

		private global::DevExpress.XtraGrid.Columns.GridColumn colEmpID;

		private global::DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repoEmp;

		private global::System.Windows.Forms.BindingSource employeeBindingSource;

		private global::DevExpress.XtraGrid.Columns.GridColumn colBalance;

		private global::DevExpress.XtraGrid.Columns.GridColumn colTotalAmount;

		private global::DevExpress.XtraGrid.Columns.GridColumn colInstalmentNo;

		private global::DevExpress.XtraGrid.Columns.GridColumn colNotes;
	}
}
